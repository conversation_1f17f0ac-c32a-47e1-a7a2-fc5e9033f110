package org.springblade.common.eventCode;

import org.springblade.common.utils.IotContextUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class IotApplicationEventImpl implements ApplicationListener<ApplicationReadyEvent> {

    private static AtomicBoolean init = new AtomicBoolean(false);
    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        if(init.get()) {
            return ;
        }
        init.compareAndSet(false, true);

        Collection<IotApplicationEvent> events = IotContextUtils.getBeansCollectionOfType(IotApplicationEvent.class);

        if(events != null && events.size() > 0) {
            events.stream().sorted((t,o)->{
                if(t.getOrder()>o.getOrder()) {
                    return 1;
                } else if(t.getOrder() == o.getOrder()) {
                    return 0;
                }
                return -1;
            }).forEach(e->{
                e.onApplicationEvent();
            });
        }
    }

    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}

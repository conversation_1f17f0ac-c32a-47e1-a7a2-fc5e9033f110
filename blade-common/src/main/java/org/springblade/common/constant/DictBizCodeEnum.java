package org.springblade.common.constant;

import lombok.Getter;

@Getter
public enum DictBizCodeEnum {
	/**
	 * 字典
	 */
    WF_SCHEDULE("wf_schedule","代理商工作流节点"),
    AGENT_PROJECT_TYPE("agent_project_type", "项目类型"),
	INFRASTRUCTURE("infrastructure", "电力基础设施"),
	INFRASTRUCTURE_CONNECTION_TYPE("infrastructure_connection_type", "基础设施连接类型"),
	ROOF_TYPE("roof_type", "屋顶类型"),
	MOUNTING_STRUCTURE("mounting_structure", "安装结构"),
	PANEL_TYPE("panel_type", "板类型"),
	PANEL_ORIENTATION("panel_orientation", "面板定向"),
	INVERTER_TYPE("inverter_type", "智能能量变换器类型"),
	AGENT_EHS_TOOLS_MACHINERY_TYPE("agent_ehs_tools_machinery_type","工具设备类型"),
	AGENT_ITEM_BASE_PACKAGE("agent_item_base_package","代理商物料基础包"),
	AGENT_SKU_DEVICE_TYPE("sku_device_type","物料类型"),
	AGENT_ITEM_FINAL_PRICE_RULE("agent_item_final_price_rule","最终价格计算规则"),
	AGENT_EXCLUDE_ITEM("exclude_item","需要排除的item编码"),
	AGENT_SEPARATE_ITEM_PRICE("separate_item_price","需要单独计算价格物料"),
	SKU_QUOTE_DEVICE_ITEMS_A("sku_quote_device_items_a","报价单设备清单A"),
	SKU_QUOTE_DEVICE_ITEMS_B("sku_quote_device_items_b","报价单设备清单B"),
	SYSTEM_DEFAULT_CONFIGURATION("system_default_configuration","系统默认参数配置"),
	AGENT_ORDER_TYPE("agent_order_type","epc订单类型"),
	AGENT_MAINT_FEE_TYPE("agent_maint_fee_type","epc维保费用类型"),
	AGENT_MAINT_QUOTATION_EMAIL_CONTENT("agent_maint_quotation_email_content","epc维保上门费用清单邮件模板"),
	AGENT_MAINT_POP_EMAIL_CONTENT("agent_maint_pop_email_content","epc维保上门费用缴费证明邮件模板"),
	FINANCE_SD_DEFAULT_EMAIL("finance_sd_default_email","财务和客户默认邮箱code"),
	AGENT_FINANCE_DEFAULT_EMAIL("finance","财务邮箱key"),
	AGENT_SD_DEFAULT_EMAIL("SD","客服邮箱key"),
	AGENT_MAINT_WORKFLOW_NODE_CODE("wf_schedule_maintenance","维保工作流节点快码"),
	AGENT_SKU_STOCK_LOG_TYPE("agent_sku_stock_log_type","库存日志类型"),
	CLIENT_DEVICE_MODE_TYPE("device_mode_type","设备类型"),
	DEVICE_ALARM_MAPPING_CONFIG("device_alarm_mapping_config","告警等级&推送信息配置"),
	DEVICE_TYPE("device_type","设备类型"),
	DEVICE_MODE_TYPE("device_mode_type","智能能量变换器设备类型"),
	BATTERY_MODE_TYPE("battery_mode_type","储能包设备类型"),
	AGENT_DEVICE_ITEM_STATUS("agent_device_item_status","智墅管家设备物料状态"),
	DEVICE_CLIENT_GUARDIAN_TYPE("client_guardian_type","安全卫士设备类型"),
	CLIENT_GUARDIAN_CONSERVE("client_guardian_conserve","节能字典"),
	CLIENT_GUARDIAN_CONSERVE_COAL("conserve_coal","节约标准煤"),
	CLIENT_GUARDIAN_CONSERVE_TREE("conserve_tree","保护森林树木"),
	CLIENT_GUARDIAN_CONSERVE_CO2("conserve_co2","减少CO2排放"),
	CLIENT_GUARDIAN_WEATHER_SUNNY("client_guardian_weather_sunny","天气晴天对应编码"),
	CLIENT_LAZZEN_GUARDIAN_TYPE("client_lazzen_guardian_type","良信安全卫士类型"),
	ENERGY_STORAGE_2_BY_DEVICE_MODEL("energy_storage_2_by_device_model","配置设备型号展示储能包2数据"),
	;
    private final String dictCode;
    private final String remark;
    DictBizCodeEnum(String dictCode, String remark) {
        this.dictCode = dictCode;
        this.remark = remark;
    }
}

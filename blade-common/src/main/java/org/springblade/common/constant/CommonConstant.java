/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.common.constant;

import java.math.BigDecimal;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Long TOP_PARENT_ID = 0L;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";

	/**
	 * 未封存状态值
	 */
	Integer NOT_SEALED_ID = 0;

	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "Aa123456";

	/**
	 * 默认密码参数值
	 */
	String DEFAULT_PARAM_PASSWORD = "account.initPassword";

	/**
	 * 默认排序字段
	 */
	String SORT_FIELD = "sort";

	/**
	 * 数据权限类型
	 */
	Integer DATA_SCOPE_CATEGORY = 1;

	/**
	 * 接口权限类型
	 */
	Integer API_SCOPE_CATEGORY = 2;

	String APPLICATION_CLIENT_NAME = "skyworth-client";

	String APPLICATION_TOOLKIT_NAME = "skyworth-toolkit";

	String APPLICATION_AGENT_NAME = "skyworth-agent";

	String APPLICATION_PORTABLE_NAME = "skyworth-portable";
	/**
	 * 系统当前语言
	 */
	String CURRENT_LANGUAGE_ZH = "zh";
	/**
	 * 创维公司
	 */
	String SKY_WORTH = "skyworth";

	String SYMBOL_QUESTION_MARK = "?";
	/**
	 * 语言：英文
	 */
	String CURRENT_LANGUAGE_EN = "en";

	String CURRENT_LANGUAGE_DE = "de";

	String SKYWORTH_SEQUENCE_PREFIX = "redis_seq_";

	String SKYWORTH_SEQUENCE_DATE_FORMAT = "yyyyMMdd";

	String LOGIN_TYPE = "login_type";
	String LOGIN_TYPE_TELEPHONE = "telephone";
	String LOGIN_TYPE_EMAIL = "email";
	//手机注册
	String REGISTER_PHONE = "1";
	String REGISTER_EMAIL = "2";
	// 手机号码格式
	final String PHONE_FORMAT = "\\d+";
	String PRE_FIX_FIRST_PLANT_DEVICE_RELATION = "first:plant:device:";

	String PRE_FIX_FIRST_PLANT_DEVICE_RELATION_NET = "first:net:plant:device:";

	String CLIENT_TENANT_ID = "111111";
	String PORTABLE_TENANT_ID = "222222";
	// 单个储能包额定容量
	BigDecimal SINGLE_BATTERY_RATED_CAPACITY = new BigDecimal("5.12");


	/**
	 * 邮箱审批通知主题
	 */
	String SUBJECT = "SKYWORTH Financial Contribution List";

	/**
	 * 邮箱审批通知模板
	 */
	String CONTENT = "Dear  %s <br>" +
		" Thank you for placing an order in our company.<br>" +
		" The main purpose of this email is to inform you of the <br>" +
		" payment,and the accompanying documents are the details <br>" +
		" of the fee list.<br>" +
		" Please arrange for payment to be made within 15 days.<br>" +
		" If you still have any questions, please feel free to contact<br>" +
		" our staff .<br><br>" +
		" Thanks again,<br>" +
		" Skyworth ";


	//app类别
	String APP_EPC_TYPE = "1";

	//代理商类别
	String AGENCY_TYPE = "3";

	//安装商(施工人员)类别
	String INSTALL_TYPE = "1";

	//电气工程师类别
	String ELECTRIC_TYPE = "2";


	//安装商代理商电气工程师租户ID
	String AGENT_TENANT_ID = "888888";
	// 内部角色
	String ROLE_TYPE_INNER = "inner";
	String ROLE_TYPE_OUT = "out";
	// app注册类型，agent 为代理商app
	String APP_TYPE_AGENT = "agent";
	String APP_TYPE_PORTABLE = "portable";
	String APP_TYPE_CLIENT = "client";
	String DEFAULT_VALUE_MINUS_ONE = "-1";
	// 是否包含内部角色表示
	String USER_ROLE_INNER_FLAG = "innerRoleFlag";

	int REST_FUL_RESULT_SUCCESS = 200;
	// 分配账号
	String USER_FROM_ALLOCATION = "allocation";
	String USER_FROM_REGISTER_WEB = "registerWeb";
	String SIGN_ADD = "+";

	String PHONE_DELETE_USER_COMMON_KEY = "phone:delete:user:";


	//指派施工任务按钮状态
	String MODIFY_STATUS = "modify";

	String DISABLE_STATUS = "disable";

	String PASS_STATUS = "pass";


	//代理商内部标识
	String INTERIOR_TYPE = "skyworth";

	String BLANK = "";
	// 用户信息发生改变时记录
	String BLADE_USER_CHANGE_PRE = "blade:user:change:pre:";

	String DEFAULT_TIME_ZONE = "time_zone";

	String PROCESS_DEFINITION_KEY = "zsButler_installation_process";

	String PROCESS_MAINTENANCE_KEY = "epc_maintenance";

	/**
	 * 终端卫士实时数据topic
	 * */
	public static String TERMINAL_WARDS_TOPIC="security_guard_realTime";

	/**
	 * 终端卫士实时数据Key
	 * */
	public static String TERMINAL_WARDS_KEY="realTimeData";


	/**
	 * 探测器实时数据
	 * */
	public static String BIZ_REALTIME_DATA="42";

	/**
	 * 卡号数据
	 * */
	public static final String SIM_NUMBER="04";

	/**
	 * 探测器发送报警阀值数据
	 * */
	public static final String BIZ_ALARM_THRESHOLD="43";

	/**
	 * 功率设置和定时开关机设置上传
	 * */
	public static final String BIZ_POWER_SETTING="44";

	/**
	 * 业务码
	 * */
	public static final String BIZ_TYPE="bizType";

	public static final String BIZ_TYPE_DELETE_DEVICE="1";
	public static final String BIZ_TYPE_ISSUE_DATA="2";
	public static final String LAZZEN_BIZ_TYPE_ISSUE_SWITCH="0400";

	/**
	 * 终端卫士名称
	 * */
	public static final String TERMINAL_WARDS_NAME="guardian";

	String FLAG_Y = "Y";
	String FLAG_N = "N";
	String FLAG_NA = "NA";
	// 取消、单据审批扣减库存时分布式锁，锁定方法
	String REDIS_SKU_STOCK_LOCK = "agent:sku:stock:lock";
	String SYMBOL_COMMA=",";

	int REST_RESULT_FAIL = 400;

	String APPLICATION_NETTY_NAME = "skyworth-netty";

	String KEY_NAME="overcurrentAlarmThreshold";
	// 一度电价格
	BigDecimal ONE_DEGREE_ELECTRIC_CHARGE_PRICE = new BigDecimal("0.66");

	String MQTT_SHARE_KEY = "$share/";
	// 良信
	String MQTT_LAZZEN_KEY = "lazzen/";
	String LAZZEN_MQTT_COMMON_CONSUMER_TOPIC = "LN/DZCloud/NDB5E/DataChange/+"; // 98D863B91366000000
	String LAZZEN_KAFKA_COMMON_CONSUMER_TOPIC = "lazzen_gateway_common_topic";
	String LAZZEN_COMMON_CONSUMER_KEY = "lazzenConsumer";
	String LAZZEN_COMMON_CONSUMER_GROUP_ID = "lazzenConsumerGroupId";
	// 良信请求id前缀
	String LAZZEN_REQUEST_ID_PREFIX = "lazzen:request:id:prefix:";

	// 天技
	String MQTT_TENTECH_KEY = "tentech/";
	// 天技设备推送topic，  采集器的发布主题格式为：c/33/{采集器SN} 如： c/33/ABCDE12345
	String TENTECH_MQTT_COMMON_CONSUMER_TOPIC = "c/33/+";

	// 系统用户账号
	String COMMON_SYSTEM_USER_ACCOUNT = "system";
	String RESOURCE_ENCRYPTED_KEY = "sms:encrypted:key:";
	String RESOURCE_BLACKLIST_PREFIX = "sms:blackIp:";
	String RESOURCE_REQUEST_COUNT_PREFIX = "sms:rate:limit:request:";
	// 默认时区
	String COMMON_DEFAULT_TIME_ZONE = "UTC+08:00";
	String COMMON_UTC_TIME_ZONE = "UTC-00:00";
	// 日起始时间
	String COMMON_DAY_START_TIME = " 00:00:00";
	// 日结束时间
	String COMMON_DAY_END_TIME = " 23:59:59";
	// 系统自动恢复(德语)
	String COMMON_SYSTEM_AUTO_RECOVERY_DE = "Automatische Wiederherstellung nach Systemüberprüfung";
	// 系统自动恢复(英语)
	String COMMON_SYSTEM_AUTO_RECOVERY_EN = "Automatic recovery after system check";
	String AUTO_ADJUST = "autoAdjust";
	// 客户端管理员
	String COMMON_CLIENT_ADMIN = "client_admin";
	String OPERATE_TYPE_INSERT = "insert";
	String OPERATE_TYPE_UPDATE  = "update";
}

package org.springblade.common.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum RoleCodeEnum {
	ADMIN("admin", "代理商管理员", "007", ""),
	SITE_TECHNICIAN("Site Technician", "施工人员", "008", "302"),
	INSPECTOR("Inspector", "安全检查员", "009", "303"),
	ROLLOUT_MANAGER("Rollout Manager", "创维交付经理", "016", ""),
	ROLLOUT_MANAGER_DISTRIBUTOR("Rollout Manager Distributor", "代理商交付经理", "018", "304"),
	// 设备端 对应 epc角色
	CLIENT_SITE_TECHNICIAN("Site Technician client", "施工人员", "302", "008"),
	CLIENT_INSPECTOR("Inspector client", "安全检查员", "303", "009"),
	CLIENT_ROLLOUT_MANAGER_DISTRIBUTOR("Rollout Manager Distributor client", "代理商交付经理", "304", "018"),
	CLIENT_COMMON_ROLE("client_user", "client_user", "301", "");

	final String roleEn;
	final String roleCn;
	final String roleCode;
	// 设备端 和 epc 角色编码映射
	final String roleCodeMapping;

	RoleCodeEnum(String roleEn, String roleCn, String roleCode, String roleCodeMapping) {
		this.roleEn = roleEn;
		this.roleCn = roleCn;
		this.roleCode = roleCode;
		this.roleCodeMapping = roleCodeMapping;
	}

	public static List<String> getClientRoleCodeByAgentRoleCode(Set<String> agentCode) {
		List<String> result = new ArrayList<>();
		RoleCodeEnum[] values = RoleCodeEnum.values();
		for (RoleCodeEnum roleCodeEnum : values) {
			if (agentCode.contains(roleCodeEnum.getRoleCode())) {
				result.add(roleCodeEnum.getRoleCodeMapping());
			}
		}
		return result;
	}

	public static String getClientRoleCodeByAgentRoleCode(String agentCode) {
		RoleCodeEnum[] values = RoleCodeEnum.values();
		for (RoleCodeEnum roleCodeEnum : values) {
			if (agentCode.contains(roleCodeEnum.getRoleCode())) {
				return roleCodeEnum.getRoleCodeMapping();
			}
		}
		return "";
	}

	// 获取epc的角色 在 client中的编码
	public static List<String> getClientRoleCode4Agent() {
		List<String> result = new ArrayList<>();
		result.add(RoleCodeEnum.CLIENT_SITE_TECHNICIAN.getRoleCode());
		result.add(RoleCodeEnum.CLIENT_INSPECTOR.getRoleCode());
		result.add(RoleCodeEnum.CLIENT_ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode());
		return result;
	}

	// 获取epc的角色 在 client中的编码
	public static List<String> getAgentRoleCode() {
		List<String> result = new ArrayList<>();
		result.add(RoleCodeEnum.SITE_TECHNICIAN.getRoleCode());
		result.add(RoleCodeEnum.CLIENT_INSPECTOR.getRoleCode());
		result.add(RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode());
		return result;
	}
}

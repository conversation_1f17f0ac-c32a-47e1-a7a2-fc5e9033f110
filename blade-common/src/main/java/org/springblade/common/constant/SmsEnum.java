package org.springblade.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SmsEnum {
	// 登录
	ONE("1", "5808024", "5877480"),
	// 注册
	TWO("2", "5808022", "5877478"),
	// 重置密码
	THREE("3", "5808320", "5877474"),
	// 更新手机
	FOUR("4", "5813870", "5877484"),
	// 注销账号
	FIVE("5", "5814628", "5877482");

	final String smsType;
	final String smsTemplateEn;
	final String smsTemplateCn;

	SmsEnum(String smsType, String smsTemplateEn, String smsTemplateCn) {
		this.smsType = smsType;
		this.smsTemplateEn = smsTemplateEn;
		this.smsTemplateCn = smsTemplateCn;
	}

	public static String getSmsTemplate(String smsType, String language) {
		SmsEnum[] values = SmsEnum.values();
		for (SmsEnum smsEnum : values) {
			if (smsEnum.smsType.equals(smsType)) {
				if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
					return smsEnum.smsTemplateCn;
				} else {
					return smsEnum.smsTemplateEn;
				}
			}
		}
		return "";
	}
}

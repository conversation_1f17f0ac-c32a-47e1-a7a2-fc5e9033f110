package org.springblade.common.constant;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description 实体类字段对应常量
 * @create-time 2023/9/27 13:25:58
 */
public interface EntityFieldConstant {
	String MPPT_1_POWER = "mppt1Power";
	String MPPT_2_POWER = "mppt2Power";
	String MPPT_3_POWER = "mppt3Power";
	String MPPT_4_POWER = "mppt4Power";
	String PHASE_A_POWER = "phaseAPower";
	String PHASE_B_POWER = "phaseBPower";
	String PHASE_C_POWER = "phaseCPower";
	String PHASE_R_WATT_OF_GRID = "phaseRWattOfGrid";
	String PHASE_S_WATT_OF_GRID = "phaseSWattOfGrid";
	String PHASE_T_WATT_OF_GRID = "phaseTWattOfGrid";
	String DEVICE_DATETIME = "deviceDateTime";


	String DEVICE_DATETIME_FOR_CAL = "deviceDateTimeForCal";
	String INSTALL_DATE = "installDate";
	String QUALITY_GUARANTEE_YEAR = "qualityGuaranteeYear";
	String INSTALL_TEAM = "installTeam";
	String INSTALL_TEAM_ID = "installTeamId";
	String CREATE_USER_ACCOUNT = "createUserAccount";
	String EXIT_FACTORY_DATE = "exitFactoryDate";
	String COUNTRY_CODE = "countryCode";
	String PROVINCE_CODE = "provinceCode";
	String CITY_CODE = "cityCode";
	String COUNTRY_NAME = "countryName";
	String PROVINCE_NAME = "provinceName";
	String CITY_NAME = "cityName";
	String BATTERY_VOLTAGE = "batteryVoltage";
	String BATTERY_CURRENT = "batteryCurrent";
	String BATTERY_MAXIMUMCELL_VOLTAGE = "batteryMaximumCellVoltage";
	String BATTERY_MINIMUMCELL_VOLTAGE = "batteryMinimumCellVoltage";
	String BATTERY_MAXIMUMCELL_TEMPERATURE = "batteryMaximumCellTemperature";
	String BATTERY_MINIMUMCELL_TEMPERATURE = "batteryMinimumCellTemperature";
	String BATTERY_POWER = "batteryPower";
	String BATTERY_SOC = "batterySoc";
	String BATTERY_POWER_SUM = "batteryPowerSum";
	String TOTAL_DATE = "totalDate";
	String BATTERY_DAILY_CHARGE_ENERGY = "batteryDailyChargeEnergy";
	String BATTERY_DAILY_DISCHARGE_ENERGY = "batteryDailyDischargeEnergy";
	String BATTERY_ACCUMULATED_CHARGE_ENERGY = "batteryAccumulatedChargeEnergy";
	String BATTERY_ACCUMULATED_DISCHARGE_ENERGY = "batteryAccumulatedDischargeEnergy";
	String BATTERY_DAILY_CHARGE_ENERGY_PARALLEL = "batteryDailyChargeEnergyParallel";
	String BATTERY_DAILY_DISCHARGE_ENERGY_PARALLEL = "batteryDailyDischargeEnergyParallel";
	String PLANT_ID = "plantId";
	String DEVICE_SERIAL_NUMBER = "deviceSerialNumber";
	String DEVICE_TYPE = "deviceType";
	String SECURITY_GUARD_SERIAL_NUMBER = "securityGuardSerialNumber";
	String GATEWAY_UNIQUE_NUMBER = "gatewayUniqueNumber";
	String SET_ITEM_BIG_TYPE = "setItemBigType";
}

package org.springblade.common.constant;

import org.springblade.core.tool.api.IResultCode;

import java.util.ArrayList;

/**
 * 国际化
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public enum I18nMsgCode implements IResultCode {
	SKYWORTH_COMMON_ERROR_900000(900000, "System Error", "系统错误",""),
	SKYWORTH_CLIENT_PLANT_100001(100001, "The Plant Name : [{}] is occupied.", "该电站名称[{}]已被占用。",""),
	SKYWORTH_CLIENT_PLANT_100002(100002, "The Inverter Serial Number : [{}] is occupied.", "该智能能量变换器序列号[{}]已被占用。",""),
	SKYWORTH_CLIENT_PLANT_100003(100003, "The Inverter Serial Number : [{}] doesn't exist.", "该智能能量变换器序列号[{}]不存在。",""),
	SKYWORTH_SYSTEM_USER_100004(100004, "The Password can not be empty.", "密码不能为空。",""),
	SKYWORTH_SYSTEM_USER_100005(100005, "Wrong Email Format.", "邮箱格式错误。",""),
	SKYWORTH_SYSTEM_USER_100006(100006, "Wrong Verification Code", "验证码错误。",""),
	SKYWORTH_SYSTEM_USER_100007(100007, "The User Name : [{}] is occupied.", "用户名[{}]已被占用。",""),
	SKYWORTH_CLIENT_PLANT_100008(100008, "Energy Station Name, Country and Detailed Address can not be empty.", "电站名称、国家、详细地址不能为空。",""),
	SKYWORTH_SYSTEM_USER_100009(100009, "The New Password and Confirm Password are not the same.", "新密码和确认密码不一致。",""),
	SKYWORTH_SYSTEM_USER_100010(100010, "The Previous Password is wrong.", "原密码错误。",""),
	SKYWORTH_SYSTEM_INVERTER_100011(100011, "Energy Mode setting failed.", "设置能量模式失败。",""),
	SKYWORTH_CLIENT_INVERTER_100012(100012, "Energy Mode setting timed out.", "设置能量模式超时。",""),
	SKYWORTH_CLIENT_USER_100013(100013, "The Username or Password is wrong.", "用户名或密码错误。",""),
	SKYWORTH_CLIENT_USER_100014(100014, "Login time expired, please login again.", "登录超时，请重新登录。",""),
	SKYWORTH_CLIENT_USER_100015(100015, "The Tenant id is null, please check.", "租户ID为空，请检查。",""),
	SKYWORTH_CLIENT_USER_100016(100016, "Failed to login for too many times, please wait", "登录超时，请稍后重试。",""),
	SKYWORTH_CLIENT_INVERTER_100017(100017, "The inverter is not connected to WiFi yet.", "智能能量变换器未连接WiFi，请稍后重试。",""),
	SKYWORTH_CLIENT_INVERTER_100018(100018, "The inverter is offline.", "智能能量变换器离线，请检查网络连接。",""),
	SKYWORTH_CLIENT_INVERTER_100019(100019, "The inverter is alarming.", "智能能量变换器报警，请检查智能能量变换器状态。",""),
	SKYWORTH_SYSTEM_USER_100020(100020, "The login type is wrong.", "登录类型错误。",""),
	SKYWORTH_SYSTEM_USER_100021(100021, "Failed to login in. Please Contact the system admin.", "登录失败，请联系系统管理员。",""),
	SKYWORTH_CLIENT_PLANT_100022(100022, "The Energy Station is deleted,please refresh this page.", "电站已删除，请刷新页面。",""),
	SKYWORTH_CLIENT_INVERTER_100023(100023, "The inverter is alarming.", "智能能量变换器报警，请检查智能能量变换器状态。",""),
	SKYWORTH_CLIENT_INVERTER_100024(100024, "The inverter setting failed.", "设置智能能量变换器失败。",""),
	SKYWORTH_CLIENT_INVERTER_100025(100025, "the inverter setting timed out.", "设置智能能量变换器超时。",""),
	SKYWORTH_CLIENT_PLANT_100026(100026, "The Energy Station id can not be empty.", "电站ID不能为空。",""),
	SKYWORTH_CLIENT_INVERTER_100027(100027, "Inverter settings failed.", "设置智能能量变换器失败。",""),
	SKYWORTH_CLIENT_PLANT_100028(100028, "The Energy Station has been deleted.","电站已删除，请刷新页面。",""),
	SKYWORTH_SYSTEM_USER_100100(100100, "The phone format is wrong.", "手机格式错误。",""),
	SKYWORTH_SYSTEM_USER_100101(100101, "The User id can not be empty.", "用户ID不能为空。",""),
	SKYWORTH_RESOURCE_SMS_100102(100102, "The SMS type can not be empty.", "短信类型不能为空。",""),
	SKYWORTH_RESOURCE_SMS_100103(100103, "The Phone or Dialling Code can not be empty.", "手机或区号不能为空。",""),
	SKYWORTH_SYSTEM_USER_100104(100104, "The User ID can not be empty.", "用户ID不能为空。",""),
	SKYWORTH_SYSTEM_USER_100105(100105, "The Register Type is wrong.", "注册类型错误。",""),
	SKYWORTH_SYSTEM_USER_100106(100106, "The Password is wrong.", "密码错误。",""),
	SKYWORTH_SYSTEM_USER_100107(100107, "The Phone already existed.", "手机已存在。",""),
	SKYWORTH_RESOURCE_SMS_100108(100108, "The Phone is wrong.", "手机号码格式错误。",""),
	SKYWORTH_RESOURCE_SMS_100109(100109, "SMS other error.", "短信其他错误。",""),
	SKYWORTH_RESOURCE_SMS_100110(100110, "SMS sent. Please wait and check your phone.", "短信发送成功，请等待并检查手机。",""),
	SKYWORTH_CLIENT_INVERTER_100111(100111, "The battery SN is occupied.", "储能包SN已使用。",""),
	SKYWORTH_RESOURCE_SMS_100112(100112, "Phone number format is incorrect, please confirm.", "手机号码格式错误，请确认。",""),
	SKYWORTH_SYSTEM_USER_100113(100113, "The user has not registered, please register before login.", "用户未注册，请先注册。",""),
	SKYWORTH_AGENT_SKU_100033(100033, "the sku code exist.", "SKU编码已存在。",""),
	SKYWORTH_AGENT_SKU_100029(100029, "the sku name , quantity can not empty.", "SKU名称、数量不能为空。",""),
	SKYWORTH_AGENT_SKU_100030(100030, "the sku name , device type, company ,standards, unit ,price can not empty.", "SKU名称、设备类型、厂家、规格、单位、单价不能为空。",""),
	SKYWORTH_SYSTEM_USER_100031(100031, "the user is not exist.", "用户不存在。",""),
	SKYWORTH_CLIENT_PLANT_100032(100032, "the user had not create plant.", "用户未创建电站。",""),
	SKYWORTH_SYSTEM_USER_100133(100133, "the agentNumber can not empty.", "代理商编号不能为空。",""),
	SKYWORTH_SYSTEM_USER_100134(100134, "The agent code entered is incorrect. Please re-enter it!", "代理商编号输入错误，请重新输入！",""),
	SKYWORTH_SYSTEM_ROLE_100135(100135, "the role can not empty.", "角色不能为空。",""),
	SKYWORTH_SYSTEM_USER_100136(100136, "the agent number is not exits.", "代理商编号不存在。",""),
	SKYWORTH_SYSTEM_USER_100137(100137, "The same phone number exceeds the (3 items/1 HOUR) frequency limit.", "手机号发送验证码次数超过限制，请1小时后再试。",""),
	SKYWORTH_SYSTEM_USER_100114(100114, "You have sent SMS verification codes too many times.Please try again 1 hour later.", "您发送短信验证码次数过多，请1小时后再试。",""),
	SKYWORTH_CLIENT_DEVICE_100115(100115, "OTA upgrade timed out. Please try it later.", "OTA升级超时，请稍后重试。",""),
	SKYWORTH_CLIENT_DEVICE_100116(100116, "The device is offline.", "设备离线，请检查网络连接。",""),
	SKYWORTH_CLIENT_DEVICE_100117(100117, "The device does not exist.", "设备不存在。",""),
	SKYWORTH_CLIENT_DEVICE_100118(100118, "The device is being upgraded, please try again later.", "设备正在升级，请稍后重试。",""),
	SKYWORTH_CLIENT_USER_100119(100119, "This account doesn't exist, please register.", "账号不存在，请先注册。",""),
	SKYWORTH_CLIENT_PLANT_100120(100120, "The plant is not exist", "电站不存在。",""),
	SKYWORTH_CLIENT_PLANT_100121(100121, "Installer Company can not be empty.", "安装单位不能为空。",""),
	SKYWORTH_CLIENT_PLANT_100122(100122, "Installer can not be empty.", "安装人员不能为空。",""),
	SKYWORTH_CLIENT_DEVICE_100123(100123, "Setup success.", "设置成功。","Erfolgreiche Einrichtung"),
	SKYWORTH_CLIENT_DEVICE_100124(100124, "The number of parallel inverters for this model is limited to 2.", "该型号的智能能量变换器并机数量限制为2个。","Die anzahl der revers-generatoren für den typ ist auf zwei beschränkt."),
	SKYWORTH_CLIENT_DEVICE_100125(100125, "Sorry,only the same model of inverter can be bound to one station.", "只有同一型号的智能能量变换器可以连接到同一个电站。","Nur ein typ Von widerwandler kann mit demselben kraftwerk verbunden werden."),
	SKYWORTH_CLIENT_GUARDIAN_100126(100126, "The device Serial Number : [{}] doesn't exist.", "该设备序列号[{}]不存在。",""),
	SKYWORTH_CLIENT_GUARDIAN_100127(100127, "The device Serial Number : [{}] is occupied.", "该设备序列号[{}]已被占用。",""),
	SKYWORTH_CLIENT_BATTERY_100128(100128, "SN error! Please confirm whether to add the energy storage devices provided by the manufacturer", "SN错误！请确认是否继续添加本厂商配套的储能包设备。",""),
	SKYWORTH_CLIENT_BATTERY_100129(100129, "Only energy storage packs of the same capacity and model can be added.", "只能添加容量与型号相同的储能包",""),
	SKYWORTH_CLIENT_GUARDIAN_100130(100130, "The device Serial Number : [{}] is incorrect", "该设备序列号[{}]不正确。",""),
	SKYWORTH_CLIENT_DEVICE_100131(100131, "The device does not match the scene and cannot be added.",
		"设备与场景不符，不能添加。","Das Gerät passt nicht zur Szene und kann nicht hinzugefügt werden."),
	;

	int code;
	String message;

	String messageZh;
	String messageDe;

	I18nMsgCode(int code, String message,String messageZh, String messageDe) {
		this.code = code;
		this.message = message;
		this.messageZh = messageZh;
	    this.messageDe = messageDe;

	}

	@Override
	public String getMessage() {
		return message;
	}

	@Override
	public int getCode() {
		return code;
	}

	public String getMessageZh() {
		return messageZh;
	}

	public String getMessageDe() {
		return messageDe;
	}

	public String autoGetMessage(String language) {
		if (language.equals(CommonConstant.CURRENT_LANGUAGE_ZH)) {
			return messageZh;
		} else if (language.equals(CommonConstant.CURRENT_LANGUAGE_DE)){
			return messageDe;
		}else {
			return message;
		}
	}


	public static void main(String[] args) {
		ArrayList<Object> ens = new ArrayList<>();
		ArrayList<Object> zhs = new ArrayList<>();
		ArrayList<Object> des = new ArrayList<>();
		I18nMsgCode[] values = I18nMsgCode.values();
		for (I18nMsgCode value : values) {
			ens.add(value.getMessage());
			zhs.add(value.getMessageZh());
			des.add(value.getMessageDe());
		}
		System.out.println(ens);
		System.out.println(zhs);
		System.out.println(des);
	}
}

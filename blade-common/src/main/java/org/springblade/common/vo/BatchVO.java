/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.springblade.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-28 14:13
 **/
@Data
public class BatchVO<T> implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 批量新增附件对象
	 */
	private List<T> addList;
	/**
	 * 批量修改附件对象
	 */
	private List<T> updateList;
	/**
	 * 批量删除附件对象
	 */
	private List<T> deleteList;
}

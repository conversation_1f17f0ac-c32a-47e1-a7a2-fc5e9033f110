package org.springblade.common.sink;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class DorisSinkService {

	public static final char UNDER_LINE = '_';

	@Autowired
	private DorisConfig dorisConfig;


    Map<String, DataSink> tableNameSink = new ConcurrentHashMap<>();


	public static DataSink getDataSink(Map<String, String> map) {
        String storageType = map.get("storageType");
        if ("doris".equals(storageType)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(DorisSink.URL, map.get("dorisWriteUrl").replaceAll("tableName", map.get("tableName")));
            jsonObject.put(DorisSink.USER_NAME, map.get("userName"));
            jsonObject.put(DorisSink.PASSWORD, map.get("password"));
            jsonObject.put(DorisSink.LABEL_PRE, map.get("tableName"));
            return new DorisSink(jsonObject, Integer.parseInt(map.get("cacheCount")), Long.parseLong(map.get("cacheTime")));
        }
        return null;
    }

    public synchronized DataSink createDorisSink(String tableName) {
        DataSink sink = tableNameSink.get(tableName);
        if (sink != null) {
            return sink;
        }
		//读取配置文件
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(DorisSink.URL, dorisConfig.getServiceUrl().replaceAll("tableName", tableName));
        jsonObject.put(DorisSink.USER_NAME, dorisConfig.getUserName());
        jsonObject.put(DorisSink.PASSWORD, dorisConfig.getPassword());
        jsonObject.put(DorisSink.LABEL_PRE, tableName);
        DorisSink dorisSink = new DorisSink(jsonObject, Integer.parseInt(dorisConfig.getCacheSize()), Long.parseLong(dorisConfig.getCacheTime()));
        tableNameSink.put(tableName, dorisSink);
        return dorisSink;
    }

    public void closeDorisSink(String tableName) {
        DataSink dataSink = tableNameSink.get(tableName);
        if (dataSink != null) dataSink.close();
    }

    private DataSink dataSinkFactory(String tableName) {
        DataSink dataSink = tableNameSink.get(tableName);
        if (dataSink == null) {
            dataSink = createDorisSink(tableName);
        }
        return dataSink;
    }

    public void write(Object object, String tableName) {
        DataSink dataSink = dataSinkFactory(tableName);
        dataSink.write(object);
    }

    public void write(List<JSONObject> list, String tableName) {
        DataSink dataSink = dataSinkFactory(tableName);
        dataSink.write(list);
    }

    public void writes(List<Object> list, String tableName) {
        DataSink dataSink = dataSinkFactory(tableName);
        dataSink.writes(list);
    }

    public List<JSONObject> dorisFormat(List<Map<String, Object>> data) {
        List<JSONObject> result = new ArrayList<>();
        data.forEach(map -> {
            JSONObject jsonObject = new JSONObject();
            map.entrySet().forEach(entry -> {
                String key = entry.getKey();
                Object value = entry.getValue();
                String newKey = camelTounderLine(key);
                jsonObject.put(newKey, value);
            });
            result.add(jsonObject);
        });
        return result;
    }

	/**
	 * 将驼峰转换成"_"(userId -> user_id)
	 *
	 * @param param
	 * @return
	 */
	public static String camelTounderLine(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if (Character.isUpperCase(c)) {
				sb.append(UNDER_LINE);
				sb.append(Character.toLowerCase(c));
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

}

package org.springblade.common.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultRedirectStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;

public class DorisSink implements DataSink {
    private static final Logger logger = LoggerFactory.getLogger(DorisSink.class);
    public static final String USER_NAME = "userName";
    public static final String URL = "url";
    public static final String PASSWORD = "password";
    public static final String LABEL_PRE = "labelPre";
    private String userName;
    private String passwd;
    private String url;
    private String labelPre;
    private JSONObject properties;
    private ArrayBlockingQueue<Object> cacheData;
    private int cacheSize;
    private Long cacheTime;
    private boolean isAsyncWrite;
    private volatile boolean isStop = false;
    private Thread thread;
    private ThreadPoolExecutor threadPoolExecutor;

    public DorisSink(JSONObject params, int cacheSize, Long cacheTime) {
        this.cacheSize = cacheSize;
        this.cacheTime = cacheTime;
        this.isAsyncWrite = isAsyncWrite;
        threadPoolExecutor =
                new ThreadPoolExecutor(
                        5,
                        5,
                        5,
                        TimeUnit.SECONDS,
                        new ArrayBlockingQueue<>(5),
                        Executors.defaultThreadFactory(),
                        new ThreadPoolExecutor.DiscardOldestPolicy());

        cacheData = new ArrayBlockingQueue<>(cacheSize * 2);
        this.userName = params.getString(USER_NAME);
        //http://172.28.17.212:8030/api/test/table1/_stream_load
        this.url = params.getString(URL);
        this.passwd = params.getString(PASSWORD);
        this.labelPre = params.getString(LABEL_PRE);
        this.properties = params;
        final HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setRedirectStrategy(new DefaultRedirectStrategy() {
                    @Override
                    protected boolean isRedirectable(String method) {
                        return true;
                    }
                });

        thread = new Thread(() -> {
            List<Object> list = new ArrayList<>();
            Date lastWriteTime = new Date();
            int failCnt = 0;
            while (!isStop) {
                try {
                    Date currentTime = new Date();
                    if (list.size() >= cacheSize || currentTime.getTime() - lastWriteTime.getTime() > cacheTime) {
                        if (!list.isEmpty()) {
                            List<Object> writeList = new ArrayList<>();
                            writeList.addAll(list);
                            writeAndFlush(writeList, true);
                            lastWriteTime = currentTime;
                            list.clear();
                        }
                    }
                    Object poll = cacheData.poll();
                    if (poll != null) {
                        list.add(poll);
                    } else {
                        Thread.sleep(1000L);

                    }
                } catch (Exception e) {
                    logger.warn(e.getMessage(), e);
                    failCnt++;
                    if (failCnt >= 3) {
                        failCnt = 0;
                        list.clear();
                    }
                    logger.warn("doris数据导入失败");
                    try {
                        Thread.sleep(3000L);
                    } catch (InterruptedException interruptedException) {
                        interruptedException.printStackTrace();
                    }
                }

            }
            threadPoolExecutor.shutdown();
            if (!cacheData.isEmpty()) {
                while (!cacheData.isEmpty()) {
                    list.add(cacheData.poll());
                }
            }
            if (!list.isEmpty()) {
                writeAndFlush(list, false);
                list.clear();
            }
        });
        thread.start();
    }

    public void commit() {

    }


    public void writeAndFlush(List<Object> list, boolean isAsync) {
        if (isAsync) {
            CompletableFuture.runAsync(() -> writeAndFlushAsync(list), threadPoolExecutor).exceptionally(
                    e -> {
                        logger.error(e.getMessage(), e);
                        return null;
                    });
        } else {
            writeAndFlushAsync(list);
        }

    }

    private void writeAndFlushAsync(List<Object> list) {
        try {
            final HttpClientBuilder httpClientBuilder = HttpClients.custom()
                    .setRedirectStrategy(new DefaultRedirectStrategy() {
                        @Override
                        protected boolean isRedirectable(String method) {
                            return true;
                        }
                    });
            CloseableHttpClient httpclient = httpClientBuilder.build();
            HttpPut httpPut = new HttpPut(url);
            httpPut.removeHeaders(HttpHeaders.CONTENT_LENGTH);
            httpPut.removeHeaders(HttpHeaders.TRANSFER_ENCODING);
            String label = labelPre + UUID.randomUUID();
            httpPut.setHeader("Expect", "100-continue");
            httpPut.setHeader("label", label);//导入标签
            httpPut.setHeader("two_phase_commit", "false");
            httpPut.setHeader("format", "json");
            httpPut.setHeader("strip_outer_array", "true");//导入array
            httpPut.setHeader("Authorization", getBasicAuthHeader(userName, passwd));
            String jsonStr = JSONObject.toJSONString(list);
            ByteArrayEntity byteArrayEntity = new ByteArrayEntity(jsonStr.getBytes(StandardCharsets.UTF_8));
            httpPut.setEntity(byteArrayEntity);
            httpPut.setConfig(RequestConfig.custom().setRedirectsEnabled(true).build());
            try (CloseableHttpResponse resp = httpclient.execute(httpPut)) {
                HttpEntity entity = resp.getEntity();
                JSONObject result = JSON.parseObject(EntityUtils.toString(entity));
                String status = result.getString("Status");
                if (!"Success".equals(status)) {
                    logger.warn("doris数据导入失败，doris接口返回结果：" + result.toJSONString());
                    throw new DataSinkException("doris data import failure," + url + "," + result.toJSONString());
                }
            }
        } catch (IOException e) {
            logger.warn(e.getMessage(), e);
            throw new DataSinkException("doris interface access exception!");
        }
    }

    private void checkStop() {
        if (isStop) {
            throw new RuntimeException("Data source is closed");
        }
    }

    @Override
    public void write(List<JSONObject> list) {
        checkStop();
        if (list == null) return;
        for (Object o : list) {
            try {
                cacheData.put(o);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void writes(List<Object> list) {
        checkStop();
        if (list == null) return;
        for (Object o : list) {
            try {
                cacheData.put(o);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void write(Object obj) {
        checkStop();
        try {
            cacheData.put(obj);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private String getBasicAuthHeader(String username, String password) {
        String auth = username + ":" + password;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
        return new StringBuilder("Basic ").append(new String(encodedAuth)).toString();
    }

    @Override
    public void close() {
        isStop = true;
    }

}

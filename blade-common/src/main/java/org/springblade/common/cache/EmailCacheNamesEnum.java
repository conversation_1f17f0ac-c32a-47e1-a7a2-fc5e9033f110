package org.springblade.common.cache;

import java.util.Objects;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024年4月9日 17:01:54
 */
public enum EmailCacheNamesEnum {
	EMAIL_REGISTER_CAPTCHA_SUFFIX("0","email:register:"),
	EMAIL_FORGET_PASSWORD_CAPTCHA_SUFFIX("1","email:forget:password:"),
	EMAIL_DELETE_CAPTCHA_SUFFIX("2","email:delete:"),
	EMAIL_LOGIN_CAPTCHA_SUFFIX("3","email:login:"),
	EMAIL_BIND_CAPTCHA_SUFFIX("4","email:bind:");


	String type;
	String cacheNameSuffix;

	public String getType() {
		return type;
	}

	public String getCacheNameSuffix() {
		return cacheNameSuffix;
	}

	EmailCacheNamesEnum(String type, String cacheNameSuffix){
		this.type = type;
		this.cacheNameSuffix = cacheNameSuffix;
	}

	public static String getCacheNameSuffix(String type) {
		String result = null;
		for (EmailCacheNamesEnum s : values()) {
			if (Objects.equals(s.getType(), type)) {
				result = s.getCacheNameSuffix();
				break;
			}
		}
		return result;
	}

}

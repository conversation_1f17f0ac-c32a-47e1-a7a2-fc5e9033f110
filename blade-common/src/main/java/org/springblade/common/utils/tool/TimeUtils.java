package org.springblade.common.utils.tool;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class TimeUtils {

	/**
	 * 根据时间戳转化为date
	 * */
	public static Date  getDateForTimeStamp(long timestamp) throws ParseException {
		Date date = new Date(timestamp);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    String str= formatter.format(date);
		return formatter.parse(str);
	}


	public static String  getStringForTimeStamp(long timestamp) throws ParseException {
		Date date = new Date(timestamp);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.format(date);
	}

	public static String getCurrentTime(){
		Date date=new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.format(date);
	}

	public static Date getCurrentTimeForDate() throws ParseException {
		Date date=new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		return formatter.parse(formatter.format(date));
	}

	public static Date getCurrentTimeForDate(String str) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		return formatter.parse(str);
	}

	public static String getCurrentTimeToDateString(String str) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		return formatter.format(str);
	}

	public static long getDateLongTime(String str) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.parse(str).getTime();
	}

	public static String generateRequestId() {
		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = dateFormat.format(date);
		String randomString = getRandomNum(10);
		return dateString.concat(randomString+"server");
	}

	public static String getTimeByStr(String str){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date;
		try {
			date = format.parse(str);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}

		return format.format(date);
	}

	public static Date getCurrentTimeByDate(String str) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return formatter.parse(str);
	}

	/**
	 * 获取随机数
	 *
	 * @param num
	 * @return
	 */
	public static String getRandomNum(int num) {
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < num; i++) {
			stringBuilder.append((int) (10 * (Math.random())));
		}
		return stringBuilder.toString();
	}

}

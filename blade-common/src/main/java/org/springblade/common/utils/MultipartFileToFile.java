package org.springblade.common.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MultipartFileToFile {
	/**
	 * 　　* MultipartFile 转 File
	 * 　　*
	 * 　　* @param file
	 * 　　* @throws Exception
	 *
	 */
	public static File multipartFileToFile(MultipartFile file) throws Exception {
		File toFile = null;
		if (file.isEmpty() || file.getSize() <= 0) {
			file = null;
		} else {
			InputStream ins = null;
			ins = file.getInputStream();
			toFile = new File(Objects.requireNonNull(file.getOriginalFilename()));
			inputStreamToFile(ins, toFile);
			ins.close();
		}
		return toFile;
	}

	/**
	 * 　　* 获取流文件
	 * 　　*
	 * 　　* @param file 文件
	 * 　　* @throws Exception 异常
	 *
	 */

	private static void inputStreamToFile(InputStream ins, File file) {
		try {
			OutputStream os = Files.newOutputStream(file.toPath());
			int bytesRead = 0;
			byte[] buffer = new byte[8192];
			while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
			os.close();
			ins.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 　　* 删除本地临时文件
	 * 　　* @param file 文件
	 *
	 */
	public static void deleteTempFile(File file) {
		if (file != null) {
			File del = new File(file.toURI());
			del.delete();
		}
	}
}


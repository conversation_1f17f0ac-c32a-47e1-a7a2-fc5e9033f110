package org.springblade.common.utils;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * 上下文工具类
 *
 */
@Component
public class IotContextUtils implements ApplicationContextAware {

	/**
	 * 上下文
	 */
	private static ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext context) throws BeansException {
		applicationContext = context;
	}

	public static ApplicationContext getApplicationContext() {
		return applicationContext;
	}

	public static Object getBean(String beanName) {
		return applicationContext.getBean(beanName);
	}


	public static <T> T getBean(String beanName, Class<T> requiredType) {
		return applicationContext.getBean(beanName, requiredType);
	}

	// 获取指定bean对象
	public static <T> T getBeanByClazz(Class<T> beanClass) {
		if (applicationContext == null) {
			throw new IllegalStateException("ApplicationContext has not been initialized.");
		}
		return applicationContext.getBean(beanClass);
	}

	public static boolean containsBean(String beanName) {
		return applicationContext.containsBean(beanName);
	}

	public static <T> Map<String, T> getBeansOfType(Class<T> type) {
		return applicationContext.getBeansOfType(type);
	}

	public static <T> Collection<T> getBeansCollectionOfType(Class<T> type) {
		Map<String, T> map = applicationContext.getBeansOfType(type);
		if (CollectionUtils.isNullOrEmpty(map.values())) {
			return Collections.emptyList();
		}
		return map.values();
	}

}

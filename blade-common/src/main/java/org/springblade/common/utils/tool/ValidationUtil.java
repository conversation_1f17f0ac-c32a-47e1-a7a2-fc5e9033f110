package org.springblade.common.utils.tool;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public class ValidationUtil {
	private ValidationUtil() {
	}

	public static boolean isNotEmpty(Object obj) {
		return !isEmpty(obj);
	}

	public static boolean isEmpty(Object obj) {
		if (null == obj) {
			return true;
		} else if (obj instanceof String) {
			return isEmpty(((String)obj).trim());
		} else if (obj instanceof Collection) {
			return ((Collection)obj).isEmpty();
		} else if (obj instanceof List) {
			return ((List)obj).isEmpty();
		} else if (obj instanceof Map) {
			return ((Map)obj).isEmpty();
		} else if (obj instanceof String[]) {
			return ((String[])((String[])obj)).length == 0;
		} else if (obj instanceof Long[]) {
			return ((Long[])((Long[])obj)).length == 0;
		} else if (obj instanceof StringBuilder) {
			return obj.toString().length() == 0;
		} else {
			return false;
		}
	}

	private static boolean isEmpty(String str) {
		boolean ref = false;
		if (null == str) {
			ref = true;
		}

		if ("".equals(str)) {
			ref = true;
		}

		if ("undefined".equals(str)) {
			ref = true;
		}

		if ("null".equals(str)) {
			ref = true;
		}

		return ref;
	}

	public static boolean isPort(String port) {
		String reg = "^[1-9]$|(^[1-9][0-9]$)|(^[1-9][0-9][0-9]$)|(^[1-9][0-9][0-9][0-9]$)|(^[1-6][0-5][0-5][0-3][0-5]$)";
		return port.matches(reg);
	}

	public static boolean isNumber(String num) {
		String reg = "^\\d+$";
		return num.matches(reg);
	}
}

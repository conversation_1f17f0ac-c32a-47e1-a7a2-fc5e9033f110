package org.springblade.common.utils;


import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @desc AES 加解密工具类
 * @date 2021/2/7 0007 11:28
 **/
public class AESUtils {

	public static String MODE = "AES/ECB/PKCS5Padding";
	public static String KEY_ALGORITHM = "AES";
	public static String CHARSET = "utf-8";
	private static final int KEY_SIZE = 128;


	/**
	 * 获取密钥
	 *
	 * @return
	 * @throws Exception
	 */
	private static Key getKey() throws Exception {
		// 实例
		KeyGenerator kg = KeyGenerator.getInstance(KEY_ALGORITHM);
		// AES
		kg.init(KEY_SIZE);
		// 生成密钥
		SecretKey secretKey = kg.generateKey();
		return secretKey;
	}


	/**
	 * 加密
	 *
	 * @param content 内容
	 * @param key     秘钥
	 * @return 加密后的数据
	 */
	public static String encrypt(String content, String key) throws Exception {
		// 新建Cipher 类
		Cipher cipher = Cipher.getInstance(MODE);
		// 初始化秘钥
		SecretKeySpec sks = new SecretKeySpec(key.getBytes(CHARSET), KEY_ALGORITHM);
		// 初始化加密类
		cipher.init(Cipher.ENCRYPT_MODE, sks);
		// 进行加密
		byte[] encrypt = cipher.doFinal(content.getBytes());
		// 这一步非必须，是因为二进制数组不方便传输，所以加密的时候才进行base64编码
		encrypt = Base64.getEncoder().encode(encrypt);
		// 转成字符串返回
		return new String(encrypt, CHARSET);
	}

	/**
	 * 解密数据
	 *
	 * @param content 内容
	 * @param key     秘钥
	 * @return 数据
	 */
	public static String decrypt(String content, String key) throws Exception{
		// 替换base64里的换行,这一步也非必须，只是有些情况base64里会携带换行符导致解码失败
		content = content.replaceAll("[\\n\\r]", "");
		// base64 解码，跟上面的编码对称
		byte[] data = Base64.getDecoder().decode(content.getBytes(CHARSET));
		// 新建Cipher 类
		Cipher cipher = Cipher.getInstance(MODE);
		// 初始化秘钥
		SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(CHARSET), KEY_ALGORITHM);
		// 初始化类
		cipher.init(Cipher.DECRYPT_MODE, keySpec);
		// 解密
		byte[] result = cipher.doFinal(data);
		// 返回解密后的内容
		return new String(result);
	}


}

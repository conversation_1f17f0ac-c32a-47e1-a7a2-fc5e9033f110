package org.springblade.common.utils;

import java.io.IOException;
import java.io.InputStream;

/**
 * 文件帮助类
 *
 * @description:
 * @author: SDT50545
 * @since: 2024-03-01 17:11
 **/
public class FileUtil {
	/**
	 * 校验和
	 * @param inputStream 入参
	 * @return long
	 * <AUTHOR>
	 * @since 2024/3/6 13:30
	 **/
	public static long calculateChecksum(InputStream inputStream) throws IOException {
		int bytesRead;
		long sum = 0;
		while ((bytesRead = inputStream.read()) != -1) {
			long unsignedInt = Integer.toUnsignedLong(bytesRead);
			sum += unsignedInt;
		}
		return sum & 0xFFFFFFFFL;
	}
}

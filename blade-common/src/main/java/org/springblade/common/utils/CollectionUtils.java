package org.springblade.common.utils;

import java.util.Collection;
import java.util.Map;

/**
 * 集合工具类
 *
 *
 */
public final class CollectionUtils {

	private CollectionUtils() {
	}

	/**
	 * 判断空集合
	 * @param collection
	 * @return
	 */
	public static boolean isNullOrEmpty(Collection<? extends Object> collection) {
		return collection == null || collection.isEmpty();
	}

	/**
	 * 判断空数组
	 * @param array
	 * @return
	 */
	public static boolean isNullOrEmpty(Object[] array) {
		return array == null || array.length == 0;
	}

	/**
	 * 判断空Map
	 * @param map
	 * @return
	 */
	public static boolean isNullOrEmpty(Map<?, ?> map) {
		return map == null || map.size() == 0;
	}

	/**
	 * 转为String
	 * @param map
	 * @return
	 */
	public static String toString(Collection<? extends Object> collection) {
		String str = "";
		if (collection != null && !collection.isEmpty()) {
			for(Object s : collection) {
				str = str.concat(s.toString()).concat(";");
			}
		}
		return str;
	}
}

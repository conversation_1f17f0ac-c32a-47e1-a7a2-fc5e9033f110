package org.springblade.common.utils.tool;

import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.PrintWriter;
import java.io.StringWriter;

public class ExceptionUtil {public ExceptionUtil() {
}

	public static String getExceptionMessage(Throwable e) {
		StringWriter sw = new StringWriter();
		e.printStackTrace(new PrintWriter(sw, true));
		return sw.toString();
	}

	public static String getRootErrorMseeage(Exception e) {
		Throwable root1 = ExceptionUtils.getRootCause(e);
		Throwable root = (root1 == null ? e : root1);
		if (root == null) {
			return "";
		} else {
			String msg = ((Throwable)root).getMessage();
			return msg == null ? "null" : StringUtils.defaultString(msg);
		}
	}
}

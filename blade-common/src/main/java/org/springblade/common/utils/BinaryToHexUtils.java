package org.springblade.common.utils;

import java.math.BigInteger;

/**
 * 进制转换
 * */
public class BinaryToHexUtils {


	/**
	 * 16进制转二进制
	 * */
	public static String hexToBinary(String hexString) {
		int decimal = Integer.parseInt(hexString, 16);
		return Integer.toBinaryString(decimal);
	}

	/**
	 * 16进制转10进制
	 * */
	public static int hexToDecimal(String hexString) {
		return Integer.parseInt(hexString, 16);
	}

	/**
	 * 10进制转16进制
	 * */
	public static String decimalToHex(int decimal) {
		return Integer.toHexString(decimal);
	}

	/**
	 * 二进制转10进制
	 * */
	public static int binaryToDecimal(String binaryString) {
		return Integer.parseInt(binaryString, 2);
	}

	/**
	 * 10进制转二进制
	 * */
	public static String decimalToBinary(int decimal) {
		return Integer.toBinaryString(decimal);
	}

	/**
	 *  16进制转Ascii
	 * */
	public static char hexToAscii(String hex){
		int decimal = Integer.parseInt(hex, 16);
		return (char) decimal;
	}

	/**
	 * 16进制转Ascii
	 * */
	public static String hex16ToAscii(String hexString){
		StringBuilder sb = new StringBuilder();

		for (int i = 0; i < hexString.length(); i += 2) {
			String hex = hexString.substring(i, i + 2);
			int decimal = Integer.parseInt(hex, 16);
			sb.append((char) decimal);
		}
		return sb.toString();

	}

	/**
	 * 俩个16进制相加
	 * */
	public static String HexadecimalAddition(String hex1,String hex2){
		// 将两个16进制数转换为十进制
		int dec1 = Integer.parseInt(hex1, 16);
		int dec2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		int sum = dec1 + dec2-1;
		return Integer.toHexString(sum).toUpperCase();
	}

	/**
	 * 俩个16进制相加 获取长度
	 * */
	public static String HexadecimalAdditionLen(String hex1,String hex2){
		// 将两个16进制数转换为十进制
		int dec1 = Integer.parseInt(hex1, 16);
		int dec2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		int sum = dec1 + dec2;
		return Integer.toHexString(sum).toUpperCase();
	}

	/**
	 * 俩个16进制相减
	 * */
	public static String HexadecimalSubtract(String hex2,String hex1){

		// 将十六进制数转换为整数
		int num1 = Integer.parseInt(hex1, 16);
		int num2 = Integer.parseInt(hex2, 16);

		// 执行减法运算
		int result = num2 - num1;

		// 将结果转换回十六进制表示
		return Integer.toHexString(result).toUpperCase();

	}

	/**
	 * 俩个16进制相减
	 * */
	public static int HexadecimalSubtractLen(String hex2,String hex1){
		// 将两个16进制数转换为十进制
		int num1 = Integer.parseInt(hex1, 16);
		int num2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		return  num2 - num1;
	}

	/**
	 * 16进制数据转10进制数据有符号位
	 * */
	public static int hexToDecimalForSymbol(String hexString) {
		BigInteger number = new BigInteger(hexString, 16);
	    return number.intValue();
	}


	/**
	 * 二进制转字符串
	 * */
	public static String binaryToText(String binary) {
		StringBuilder stringBuilder = new StringBuilder();
		int length = binary.length();

		for (int i = 0; i < length; i += 8) {
			String binaryByte = binary.substring(i, Math.min(i + 8, length));
			int decimal = Integer.parseInt(binaryByte, 2);
			char character = (char) decimal;
			stringBuilder.append(character);
		}

		return stringBuilder.toString();
	}

	public static String hexToBinaryByRepairZero(String hexNumber) {
		String binaryNumber = hexToBinary2(hexNumber);
		return padWithZeros(binaryNumber, 16);
	}

	private static String hexToBinary2(String hexNumber) {
		StringBuilder binaryNumber = new StringBuilder();
		for (int i = 0; i < hexNumber.length(); i++) {
			char hexDigit = hexNumber.charAt(i);
			int decimalValue = Character.digit(hexDigit, 16);
			String binaryDigit = Integer.toBinaryString(decimalValue);
			binaryNumber.append(padWithZeros(binaryDigit, 4));
		}
		return binaryNumber.toString();
	}

	public static String padWithZeros(String binaryNumber, int length) {
		int numZerosToAdd = length - binaryNumber.length();
		StringBuilder paddedBinaryNumber = new StringBuilder();
		for (int i = 0; i < numZerosToAdd; i++) {
			paddedBinaryNumber.append('0');
		}
		paddedBinaryNumber.append(binaryNumber);
		return paddedBinaryNumber.toString();
	}

	/**
	 * 根据传进来的位数进行数据补0
	 * */
	public static String binaryComplement(String hexString, int bitLength) {
		int decimal = Integer.parseInt(hexString, 16);
		String res= Integer.toBinaryString(decimal);
        return String.format("%" + bitLength + "s", res).replace(' ', '0');
	}

	/**
	 * 10进制转16进制不足俩位补0
	 * */
	public static String toTwoDigitHex(int number) {
		return String.format("%02X", number);
	}

	/**
	 * 10进制转16进制不足四位补0
	 * */
	public static String toFourDigitHex(int number,int length) {
		return String.format("%0"+length+"X", number);
	}

	/**
	 * 二进制转16进制,不足len补0,超过len不补0
	 * */
	public static String binaryToHex(String binaryString,int len) {
		int decimal = Integer.parseInt(binaryString, 2);
		return toFourDigitHex(decimal,len);
	}

	/**
	 * 时分秒拆分成HHMMSS
	 * */
	public static String timeSplit(String time) {
		String[] timeArray = time.split(":");
		StringBuilder sb = new StringBuilder();
		for (String s : timeArray) {
			sb.append(s);
		}
		return sb.toString();
	}


	/**
	 * 前端传递过来的可能是1,3,5,7 。 代表周一、周三、周五、日。需要转换为1,0,1,0,1,0,1
	 * @param general
	 * @return
	 */
	public static String generalSetToBinarySet(String general) {
		if (general == null || general.isEmpty()) {
			return "0,0,0,0,0,0,0";
		}

		// 初始化一个长度为7的二进制字符串，全部填充为0
		StringBuilder binarySet = new StringBuilder("0,0,0,0,0,0,0");

		// 将字符串按逗号拆分成数组
		String[] days = general.split(",");

		for (String day : days) {
			try {
				int index = Integer.parseInt(day.trim()) - 1;
				if (index >= 0 && index < 7) {
					// 将二进制字符串转换为字符数组
					char[] chars = binarySet.toString().toCharArray();
					// 修改指定位置的字符
					chars[index * 2] = '1';
					// 重新组合成字符串
					binarySet = new StringBuilder(new String(chars));
				}
			} catch (NumberFormatException e) {
				continue;
			}
		}

		return binarySet.toString();
	}


	/**
	 * 0,1,0,1,0,1,0 转换为=》2,4,6 让前端方便展示
	 * @param binary
	 * @return
	 */
	public static String binarySetToGeneralSet(String binary) {
		if (binary == null || binary.isEmpty()) {
			return ""; // 如果输入为空、为空字符串或长度不符合要求，直接返回空字符串
		}

		// 初始化一个StringBuilder来存储结果
		StringBuilder result = new StringBuilder();

		// 将输入字符串转换为每7位一组的形式
		String binaryStr = binary.replaceAll(",", "").trim();
		if (binaryStr.length() != 7) {
			return ""; // 长度不为7，直接返回空字符串
		}

		// 遍历二进制字符串，从左向右
		for (int i = 0; i < binaryStr.length(); i++) {
			char c = binaryStr.charAt(i); // 从左向右读取字符
			if (c == '1') {
				int day = i + 1; // 计算对应的星期几
				result.append(day).append(",");
			}
		}

		// 删除最后多余的逗号
		if (result.length() > 0) {
			result.deleteCharAt(result.length() - 1);
		}

		return result.toString();
	}

	/**
	 *  十六进制加长，如：088E + 1= 088F
	 * @param hex 原始16进制数
	 * @param length 需要增加的步长
	 * @return 新的16进制数
	 */
	public static String hexAddLengthToHex(String hex,int length) {
		int decimalValue = BinaryToHexUtils.hexToDecimal(hex);
		decimalValue += length;
		return String.format("%04X", decimalValue);
	}
}

package org.springblade.common.excel;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.tool.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 校验excel工具类
 */
@Slf4j
public class ExcelImportValidateUtil {

    public static void validateDataNotNull(Object object, StringBuilder validDataNotNullResult, int rows) {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            //设置可访问
            field.setAccessible(true);
            //属性的值
            Object fieldValue = null;
            try {
                fieldValue = field.get(object);
            } catch (IllegalAccessException e) {
                log.error("excel error : {}" ,e.getMessage());
                continue;
            }
            boolean isExcelValid = field.isAnnotationPresent(ExcelNotNullValidate.class);
            if (isExcelValid && fieldValue == null) {
                String columnName = field.getAnnotation(ExcelNotNullValidate.class).message();
                log.error("excel must not null : {}" ,columnName);
                validDataNotNullResult.append("the ").append(rows).append(" row of  title name : ").append(columnName).append(" must not null;");
            }

        }
    }

    public static <T> StringBuilder validateDuplicateData(List<T> list) {
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(p -> {
            Field[] fields = p.getClass().getDeclaredFields();
            StringBuilder sb = new StringBuilder();
            for (Field field : fields) {
                boolean isExcelValid  = field.isAnnotationPresent(ExcelBusinessUniqueValidate.class);
                if(!isExcelValid) {
                    continue;
                }
                ExcelBusinessUniqueValidate annotation = field.getAnnotation(ExcelBusinessUniqueValidate.class);
                boolean uniqueFlag = annotation.uniqueFlag();
                field.setAccessible(true);
                Object fieldValue = null;
                try {
                    fieldValue = field.get(p);
                } catch (IllegalAccessException e) {
                    log.error("excel error : {}", e.getMessage());
                    continue;
                }
                if(uniqueFlag) {
                    sb.append(fieldValue).append(" , ");
                }
            }
            return sb.toString();
        }, Collectors.counting()));
        StringBuilder result = new StringBuilder();
		log.info("validateDuplicateData map : {}", map);
        for(Map.Entry<String, Long> entry : map.entrySet()) {
            // 统计有重复数据
            if(StringUtils.isNotEmpty(entry.getKey()) && entry.getValue() > 1) {
                result.append(entry.getKey()).append(";");
            }
        }
        if(StringUtils.isNotEmpty(result)) {
            result.append(" duplicate, please check.");
        }
        return result;
    }
}

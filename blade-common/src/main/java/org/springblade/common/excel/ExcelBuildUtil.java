package org.springblade.common.excel;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR> - x<PERSON><PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/10 13:35:13
 */
public class ExcelBuildUtil {
	private static final ReentrantLock lock = new ReentrantLock();

	public static List<List<String>> buildI18HeadList(Set<String> column) {
		lock.lock();
		try {
			List<List<String>> list = new ArrayList<>();
			for (String col : column) {
				List<String> head = new ArrayList<>();
				head.add(col);
				list.add(head);
			}
			return list;
		} finally {
			lock.unlock();
		}
	}
}

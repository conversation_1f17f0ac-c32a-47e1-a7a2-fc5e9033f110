package org.springblade.common.excel;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * 导入excel入口方法
 */
public interface ExcelImportServiceInterface<T> {
     String imporExcel(MultipartFile file, Set<String> columnSet,
                             Class<T> excelVO, BiFunction<List<T> ,Boolean,String> function);

     /**
      *  导入多列头
      * @param file 待导入excel
      * @param headerListSet 多个列头集合  [[{基本信息},{sn}],[{基本信息},{type}]]
      * @param excelVO excel映射字段
      * @param function 最终保存方法
      * @return  错误提示
      */
     String importMultipleHeaderExcel(MultipartFile file, List<List<String>> headerListSet,
                       Class<T> excelVO, BiFunction<List<T> ,Boolean,String> function);
}

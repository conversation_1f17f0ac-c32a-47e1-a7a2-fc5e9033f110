package org.springblade.common.excel;

import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * 导入excle实现
 */

@Slf4j
public abstract class ExcelImportServiceAbstract<T> implements ExcelImportServiceInterface<T> {
    //校验方法
    public abstract String validateDataEffective(List<T> dataList);

    public String imporExcel(MultipartFile file, Set<String> columnSet,
                             Class<T> excelVO, BiFunction<List<T> ,Boolean,String> function) {
        try {
            ExitFactoryInfoListener<T> excel = new ExitFactoryInfoListener<>(columnSet);
            List<T> list = EasyExcel.read(file.getInputStream(), excelVO,
                    excel).doReadAllSync();
            if(CollectionUtils.isNullOrEmpty(list)) {
                return "excel data can not null";
            }
            StringBuilder validColumnResult = excel.validColumnResult;
            // 列头是否正确
            if (StringUtils.isNotEmpty(validColumnResult)) {
                return validColumnResult.toString();
            }
            StringBuilder validDataResult = excel.validDataNotNullResult;
            // 数据是否必填
            if (StringUtils.isNotEmpty(validDataResult)) {
                return validDataResult.toString();
            }
            // 校验excel中是否存在重复数据
            StringBuilder duplicateData = ExcelImportValidateUtil.validateDuplicateData(list);
            if (StringUtils.isNotEmpty(duplicateData)) {
                return duplicateData.toString();
            }
            // 数据正确性校验
            String effectiveResult = this.validateDataEffective(list);
            if (StringUtils.isNotEmpty(effectiveResult)) {
                return effectiveResult;
            }
            return function.apply(list,true);
        } catch (IOException e) {
            log.error("error : {}", e.getMessage());
            return "error";
        }
    }

    public String importMultipleHeaderExcel(MultipartFile file, List<List<String>> headerListSet,
                                     Class<T> excelVO, BiFunction<List<T> ,Boolean,String> function) {
        try {
            ExitFactoryInfoListener<T> excel = new ExitFactoryInfoListener<>(headerListSet);
            List<T> list = EasyExcel.read(file.getInputStream(), excelVO,
                    excel).headRowNumber(headerListSet.size()).doReadAllSync();
            if(CollectionUtils.isNullOrEmpty(list)) {
                return "excel data can not null";
            }
            StringBuilder validColumnResult = excel.validColumnResult;
            // 列头是否正确
            if (StringUtils.isNotEmpty(validColumnResult)) {
                return validColumnResult.toString();
            }
            StringBuilder validDataResult = excel.validDataNotNullResult;
            // 数据是否必填
            if (StringUtils.isNotEmpty(validDataResult)) {
                return validDataResult.toString();
            }
            // 校验excel中是否存在重复数据
            StringBuilder duplicateData = ExcelImportValidateUtil.validateDuplicateData(list);
            if (StringUtils.isNotEmpty(duplicateData)) {
                return duplicateData.toString();
            }
            // 数据正确性校验
            String effectiveResult = this.validateDataEffective(list);
            if (StringUtils.isNotEmpty(effectiveResult)) {
                return effectiveResult;
            }
            return function.apply(list,true);
        } catch (IOException e) {
            log.error("error : {}", e.getMessage());
            return "error";
        }
    }
}

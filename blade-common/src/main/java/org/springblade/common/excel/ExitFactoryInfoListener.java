package org.springblade.common.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadHolder;
import com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.tool.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 校验excel 列头是否正确
 */
@Slf4j
public class ExitFactoryInfoListener<T> extends AnalysisEventListener<T> {
     private Set<String> columnSet;

	private List<List<String>> headerListSet;

	// 当前excel列头行数
	private Integer currentHeaderRow = 0;
     public StringBuilder validColumnResult = new StringBuilder();

    public StringBuilder validDataNotNullResult = new StringBuilder();

    public ExitFactoryInfoListener(Set<String> columnSet) {
        this.columnSet = columnSet;
    }

	public ExitFactoryInfoListener(List<List<String>> headerListSet) {
		this.headerListSet = headerListSet;
	}
    private int rows = 0;
    @Override
    public  void invoke(T o, AnalysisContext analysisContext) {
        if(StringUtils.isNotEmpty(validColumnResult.toString())) {
            log.info("valid header had error");
            return;
        }
        log.info("invoke ");
        rows ++;
        ExcelImportValidateUtil.validateDataNotNull(o,validDataNotNullResult,rows);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("doAfterAllAnalysed");
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("valid header :{}", JSON.toJSONString(headMap));
		if (headerListSet != null) {
			ReadHolder holder = context.currentReadHolder();
			if (holder instanceof XlsxReadSheetHolder) {
				if(currentHeaderRow >= headerListSet.size()) {
					return;
				}
				List<String> headerSet = headerListSet.get(currentHeaderRow);
				currentHeaderRow++;
				if (headerSet.size() != headMap.size()) {
					validColumnResult.append("the excel title number is not correct ;");
				}
				StringBuilder columnValid = new StringBuilder();
				for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
					if(!headerSet.contains(entry.getValue())) {
						columnValid.append(entry.getValue()).append(", ");
					}
				}
				if(StringUtils.isNotEmpty(columnValid.toString())) {
					validColumnResult.append("the excel title : ").append(columnValid).append(" is not correct ");
				}
			}
		} else {
			this.singleHeader(headMap);
		}

    }

	private void singleHeader(Map<Integer, String> headMap) {
		if (columnSet.size() != headMap.size()) {
			validColumnResult.append("the excel title number is not correct ;");
		}
		StringBuilder columnValid = new StringBuilder();
		for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
			if(!columnSet.contains(entry.getValue())) {
				columnValid.append(entry.getValue()).append(", ");
			}
		}
		if(StringUtils.isNotEmpty(columnValid.toString())) {
			validColumnResult.append("the excel title : ").append(columnValid).append(" is not correct ");
		}
	}
}

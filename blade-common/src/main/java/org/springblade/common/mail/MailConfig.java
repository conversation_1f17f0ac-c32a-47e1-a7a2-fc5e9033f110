package org.springblade.common.mail;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Properties;


//@Component
//@PropertySource({"classpath:mail.properties"})
@ConfigurationProperties(prefix="mailappskyworth")
@Component
@Data
public class MailConfig {
	private String host;
	private String port;
	private String auth;
	private String starttlsEnable;
	private String username;
	private String password;
	private String subject;
//	private static MailConfig mailConfig;
//	private MailConfig(){
//	}
//	public static MailConfig  getMailConfig() throws IOException {
//		if(mailConfig == null) {
//			Resource resource = new ClassPathResource("mail.properties");
//			Properties properties = PropertiesLoaderUtils.loadProperties(resource);
//			properties.list(System.out);
//			String property = properties.getProperty("mailAppSkyworth.host");
//			mailConfig = new MailConfig();
//			mailConfig.setHost(property);
//			mailConfig.setPort(properties.getProperty("mailAppSkyworth.port"));
//			mailConfig.setAuth(properties.getProperty("mailAppSkyworth.auth"));
//			mailConfig.setStarttlsEnable(properties.getProperty("mailAppSkyworth.starttlsEnable"));
//			mailConfig.setUsername(properties.getProperty("mailAppSkyworth.username"));
//			mailConfig.setPassword(properties.getProperty("mailAppSkyworth.password"));
//			mailConfig.setSubject(properties.getProperty("mailAppSkyworth.subject"));
//		}
//		return mailConfig;
//	}

}

package org.springblade.common.mq.kafka.config;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.common.utils.tool.ValidationUtil;

import javax.validation.Valid;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

public class KafkaAssignParalleDataSource extends ParallelDataSource{private static final Logger log = LoggerFactory.getLogger(KafkaAssignParalleDataSource.class);
	public static final String ALL_THREAD_CNT = "allThreadCnt";
	public static final String CURRENT_NUMBER = "currentNumber";
	private int allThreadCnt;
	private int currentNumber;
	private Long durationMillions = 1000L;
	private KafkaConsumer<String, String> consumer;
	private Map<String, List<PartitionInfo>> partitionInfoMap = new HashMap();
	private List<PartitionInfo> currentAsignPartitionInfo = new ArrayList();
	private List<List<PartitionInfo>> allPartitinoInfo = new ArrayList();
	private JSONObject properties;

	public KafkaAssignParalleDataSource(JSONObject kafkaProperties) {
		this.properties = kafkaProperties;
	}

	public void initDataSource() {
		String allThreadCntStr = this.properties.getString("allThreadCnt");
		String currentNumberStr = this.properties.getString("currentNumber");
		this.allThreadCnt = Integer.parseInt(allThreadCntStr);
		this.currentNumber = Integer.parseInt(currentNumberStr);
		JSONArray topicStr = this.properties.getJSONArray("topics");
		String bootstrapServers = this.properties.getString("isoc.kafka.bootstrapServer");
		String groupId = this.properties.getString("isoc.kafka.consumer.group.id");
		if (topicStr != null && topicStr.size() != 0) {
			if (bootstrapServers == null) {
				throw new RuntimeException("kafka brokerList Cluster address not set");
			} else if (StringUtils.isBlank(allThreadCntStr)) {
				throw new RuntimeException("The total number of threads is not set!");
			} else if (StringUtils.isBlank(currentNumberStr)) {
				throw new RuntimeException("Thread number is not set!");
			} else {
				Properties properties = new Properties();
				properties.put("bootstrap.servers", bootstrapServers);
				properties.put("group.id", groupId != null ? groupId : UUID.randomUUID().toString());
				properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
				properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
				properties.put("enable.auto.commit", "false");
				properties.put("auto.offset.reset", "latest");
				properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
				properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
				if (this.properties.containsKey("properties")) {
					Iterator<Map.Entry<String, Object>> iterator = this.properties.getJSONObject("properties").entrySet().iterator();

					while(iterator.hasNext()) {
						Map.Entry<String, Object> entry = (Map.Entry)iterator.next();
						if ("durationMillions".equals(entry.getKey())) {
							this.durationMillions = (Long)entry.getValue();
						} else {
							properties.put(entry.getKey(), entry.getValue());
						}
					}
				}
				this.consumer = new KafkaConsumer(properties);
				List<String> topics=new ArrayList<>();
				for(int i = 0; i < topicStr.size(); ++i) {
					String topic = topicStr.getString(i);
					topics.add(topic);
				}
				consumer.subscribe(topics); // 订阅要消费的主题
			}
		} else {
			throw new RuntimeException("Kafka topic not assign!");
		}
	}

	private void assignCurrentThreadPartitions(JSONArray topics) {
		for(int i = 0; i < topics.size(); ++i) {
			String topic = topics.getString(i);
			List<PartitionInfo> partitionInfos = this.consumer.partitionsFor(topic);
			this.partitionInfoMap.put(topic, partitionInfos);
			this.allPartitinoInfo.add(partitionInfos);
		}

		List<PartitionInfo> soartPartition = this.sortParition(this.allPartitinoInfo);
		int partitionCnt = soartPartition.size() / this.allThreadCnt;
		int overPartitionCnt = soartPartition.size() % this.allThreadCnt;
		int startParition = this.currentNumber * partitionCnt;
		int endPartition = this.currentNumber * partitionCnt + partitionCnt - 1;

		for(int i = startParition; i <= endPartition; ++i) {
			this.currentAsignPartitionInfo.add(soartPartition.get(i));
		}

		if (overPartitionCnt != 0 && this.currentNumber < overPartitionCnt) {
			this.currentAsignPartitionInfo.add(soartPartition.get(partitionCnt * this.allThreadCnt + this.currentNumber));
		}

	}

	private List<PartitionInfo> sortParition(List<List<PartitionInfo>> allPartitionInfo) {
		List<PartitionInfo> list = new ArrayList();
		if (allPartitionInfo.size() == 1) {
			list.addAll((Collection)allPartitionInfo.get(0));
			return list;
		} else {
			int max = 0;

			int i;
			for(i = 0; i < allPartitionInfo.size(); ++i) {
				if (max < ((List)allPartitionInfo.get(i)).size()) {
					max = ((List)allPartitionInfo.get(i)).size();
				}
			}

			for(i = 0; i < max; ++i) {
				for(int j = 0; j < allPartitionInfo.size(); ++j) {
					List<PartitionInfo> partitionInfos = (List)allPartitionInfo.get(j);
					if (i < partitionInfos.size()) {
						list.add(partitionInfos.get(i));
					}
				}
			}

			return list;
		}
	}

	public List<JSONObject> getData() {
		ConsumerRecords<String, String> records = this.consumer.poll(Duration.ofMillis(this.durationMillions));
		if(ValidationUtil.isNotEmpty(records)&&!records.isEmpty()){
			Iterator<ConsumerRecord<String, String>> iterator = records.iterator();
			ArrayList<ConsumerRecord<String, String>> consumerRecords = new ArrayList();
			iterator.forEachRemaining(consumerRecords::add);
			List<JSONObject> collect = (List)consumerRecords.stream().map((record) -> {
				JSONObject jsonObj = new JSONObject();
				jsonObj.put("line", record.value());
				jsonObj.put("topic", record.topic());
				jsonObj.put("partition", record.partition());
				jsonObj.put("offset", record.offset());
				return jsonObj;
			}).collect(Collectors.toList());
			return collect;
		}else {
			return new ArrayList<>();
		}

	}

	public void commit() {
		this.consumer.commitSync();
	}

	public void commit(Map<TopicPartition, OffsetAndMetadata> offsets) {
		this.consumer.commitSync(offsets);
	}

	public void close() {
		this.consumer.wakeup();
		this.consumer.close();
	}
}

package org.springblade.common.mq.kafka.config;

import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;

import java.util.List;
import java.util.Map;

public abstract class ParallelDataSource implements IKafkaDataSource{

	public ParallelDataSource() {
	}

	public abstract void initDataSource();

	public abstract List<JSONObject> getData();

	public abstract void commit();

	public abstract void commit(Map<TopicPartition, OffsetAndMetadata> var1);

	public abstract void close();

}

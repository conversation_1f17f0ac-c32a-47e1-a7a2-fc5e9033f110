spring:
    redis:
        host: *************
        port: 6379
        password: skyworth@123
    datasource:
        dynamic:
            primary: master #设置默认的数据源或者数据源组,默认值即为master
            strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
            datasource:
                master:
                    url: jdbc:mysql://*************:3306/bladex?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&tinyInt1isBit=false&allowMultiQueries=true&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
                    username: root
                    password: skyworth@123
                slave:
                    url: ***************************************************************************
                    username: root
                    password:
        driver-class-name: com.mysql.cj.jdbc.Driver
        druid:
            validation-query: select 1

#spring配置
#spring:
#  redis:
#    ##redis 单机环境配置
#    host: *************
#    port: 6379
#    password: skyworth@123
#    database: 0
#    ssl: false
#    ##redis 集群环境配置
#    #cluster:
#    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
#    #  commandTimeout: 5000
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    #driver-class-name: org.postgresql.Driver
#    #driver-class-name: oracle.jdbc.OracleDriver
#    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#    #driver-class-name: dm.jdbc.driver.DmDriver
#    #driver-class-name: com.yashandb.jdbc.Driver
#    druid:
#      # MySql、PostgreSQL、SqlServer、DaMeng校验
#      validation-query: select 1
#      # Oracle、YashanDB校验
#      #oracle: true
#      #validation-query: select 1 from dual

#项目模块集中配置
blade:
  #分布式锁配置
  lock:
    enabled: false
    address: redis://*************:6379
  #多团队协作服务配置
  loadbalancer:
    #开启配置
    enabled: true
    #负载均衡优先调用的ip段
    prior-ip-pattern:
      - 192.168.0.*
      - 127.0.0.1
  #通用开发生产环境数据库地址(特殊情况可在对应的子工程里配置覆盖)
#  datasource:
#    dev:
#      # MySql
#      url: jdbc:mysql://*************:3306/bladex?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&tinyInt1isBit=false&allowMultiQueries=true&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
#      username: root
#      password: skyworth@123
      # PostgreSQL
      #url: ***************************************
      #username: postgres
      #password: 123456
      # Oracle
      #url: *************************************
      #username: BLADEX
      #password: BLADEX
      # SqlServer
      #url: ***************************************************
      #username: bladex
      #password: bladex
      # DaMeng
      #url: jdbc:dm://127.0.0.1:5236/BLADEX?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
      #username: BLADEX
      #password: BLADEX
      # YashanDB
      #url: **********************************
      #username: BLADEX
      #password: BLADEX
azure-blob:
    defaultEndpointsProtocol: https
    accountName: energystorage1
    accountKey: ****************************************************************************************
    endpointSuffix: core.windows.net
    blobEndpoint: https://energystorage1.blob.core.windows.net/
    queueEndpoint: https://energystorage1.queue.core.windows.net/
    tableEndpoint: https://energystorage1.table.core.windows.net/

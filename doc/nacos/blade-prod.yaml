#spring配置
spring:
  redis:
    ##redis 单机环境配置
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    host: *************
    port: 3379
    password:
    database: 0
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
      #driver-class-name: org.postgresql.Driver
      #driver-class-name: oracle.jdbc.OracleDriver
      #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #driver-class-name: dm.jdbc.driver.DmDriver
      #driver-class-name: com.yashandb.jdbc.Driver
    druid:
      # MySql、PostgreSQL、SqlServer、DaMeng校验
      validation-query: select 1
      # Oracle、YashanDB校验
      #oracle: true
      #validation-query: select 1 from dual

#项目模块集中配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##将docker脚本部署的redis服务映射为宿主机ip
    ##生产环境推荐使用阿里云高可用redis服务并设置密码
    address: redis://*************:3379
  #通用开发生产环境数据库地址(特殊情况可在对应的子工程里配置覆盖)
  datasource:
    prod:
      url: jdbc:mysql://*************:3306/bladex?useSSL=false&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&transformedBitIsBoolean=true&tinyInt1isBit=false&allowMultiQueries=true&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
      username: root
      password: root
azure-blob:
    defaultEndpointsProtocol: https
    accountName: energystorage1
    accountKey: ****************************************************************************************
    endpointSuffix: core.windows.net
    blobEndpoint: https://energystorage1.blob.core.windows.net/
    queueEndpoint: https://energystorage1.queue.core.windows.net/
    tableEndpoint: https://energystorage1.table.core.windows.net/

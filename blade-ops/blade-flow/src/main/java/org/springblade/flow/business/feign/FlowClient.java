/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.feign;

import lombok.AllArgsConstructor;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.core.vo.ApprovalVO;
import org.springblade.flow.core.vo.HistoryVO;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 流程远程调用实现类
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
public class FlowClient implements IFlowClient {

	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final TaskService taskService;
	private final FlowBusinessService flowBusinessService;


	@Override
	@PostMapping(START_PROCESS_INSTANCE_BY_ID)
	public R<BladeFlow> startProcessInstanceById(String processDefinitionId, String businessKey, @RequestBody Map<String, Object> variables) {
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionId, businessKey, variables);
		// 组装流程通用类
		BladeFlow flow = new BladeFlow();
		flow.setProcessInstanceId(processInstance.getId());
		flow.setCreateTime(processInstance.getStartTime());
		return R.data(flow);
	}

	@Override
	@PostMapping(START_PROCESS_INSTANCE_BY_KEY)
	public R<BladeFlow> startProcessInstanceByKey(String processDefinitionKey, String businessKey, @RequestBody Map<String, Object> variables) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(AuthUtil.getUserId().toString());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
		// 组装流程通用类
		BladeFlow flow = new BladeFlow();
		flow.setProcessInstanceId(processInstance.getId());
		return R.data(flow);
	}

	@Override
	@PostMapping(COMPLETE_TASK)
	public R<BladeFlow> completeTask(String taskId, String processInstanceId, String comment, @RequestBody Map<String, Object> variables) {
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		// 非空判断
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		// 完成任务
		taskService.complete(taskId, variables);
		return R.success("流程提交成功");
	}

	@Override
	@GetMapping(TASK_VARIABLE)
	public R<Object> taskVariable(String taskId, String variableName) {
		return R.data(taskService.getVariable(taskId, variableName));
	}

	@Override
	@GetMapping(TASK_VARIABLES)
	public R<Map<String, Object>> taskVariables(String taskId) {
		return R.data(taskService.getVariables(taskId));
	}

	@Override
	@GetMapping(SELECT_HISTORY_PROCESS_BY_ID)
	public R<List<HistoryVO>> getTaskHistory(String processInstanceId,String orderType) {
		String tempOrderType = "";
		if (StringUtils.isEmpty(orderType)) {
			tempOrderType = "guardian";
		}
		return R.data(flowBusinessService.getTaskHistory(processInstanceId,tempOrderType));
	}

	@Override
	@PostMapping(START_EPC_PROCESS)
	public R<OrderFlowVO> startOrderProcess(String businessId, String flowId) {
		return R.data(flowBusinessService.startOrderProcess(businessId, flowId));
	}

	@Override
	@PostMapping(COMPLETE_EPC_TASK)
	public R<ApprovalVO> completeOrderTask(@Validated OrderFlowDTO orderFlowDTO, String flowId) {
		if (orderFlowDTO == null) {
			return R.success("data is null");
		}
		return flowBusinessService.completeOrderTask(orderFlowDTO, flowId);
	}


}

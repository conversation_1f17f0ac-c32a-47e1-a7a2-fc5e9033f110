package org.springblade.flow.engine.utils;

import org.flowable.task.api.Task;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.emun.MaintenanceTaskEnum;
import org.springblade.flow.core.vo.ApprovalVO;

import java.util.Map;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-17 09:25
 **/
public class FlowMaintenanceFunction {
	public static ApprovalVO getOrderFlowVO(Map<String, Object> map) {
		Task task = (Task) map.get("task");
		String siteEngineer = FlowFunction.objectToString(map.get("siteEngineer"));
		String electrician = FlowFunction.objectToString(map.get("electrician"));
		String siteEngineerName = FlowFunction.objectToString(map.get("siteEngineerName"));
		String electricianName = FlowFunction.objectToString(map.get("electricianName"));
		String roleCodes = FlowFunction.objectToString(map.get("roleCodes"));
		String roleNames = FlowFunction.objectToString(map.get("roleNames"));
		String taskAbbreviationName = FlowFunction.objectToString(map.get("taskAbbreviationName"));
		ApprovalVO flow = new ApprovalVO();
		flow.setCreateTime(task.getCreateTime());
		flow.setTaskName(taskAbbreviationName);
		flow.setTaskId(task.getId());
		MaintenanceTaskEnum taskEnum = MaintenanceTaskEnum.of(taskAbbreviationName);
		if (taskEnum != null) {
			switch (taskEnum.getType()) {
				//踏勘及业主确认
				case 2:
					//物料签收
				case 4:
					//施工安全信息提交
				case 5:
					//质检信息提交
				case 7:
					//业主验收
				case 10:
					if (StringUtil.isNotBlank(siteEngineer) && StringUtil.isNotBlank(electrician)) {
						flow.setAssignee(siteEngineer + "," + electrician);
						flow.setAssigneeName(siteEngineerName + "," + electricianName);
					} else if (StringUtil.isNotBlank(electrician) && StringUtil.isBlank(siteEngineer)) {
						flow.setAssignee(electrician);
						flow.setAssigneeName(electricianName);
					} else if (StringUtil.isNotBlank(siteEngineer) && StringUtil.isBlank(electrician)) {
						flow.setAssignee(siteEngineer);
						flow.setAssigneeName(siteEngineerName);
					}
					break;
				//施工审核上传临时COC
				case 9:
					if (StringUtil.isNotBlank(siteEngineer) && StringUtil.isNotBlank(electrician)) {
						flow.setAssignee(siteEngineer + "," + electrician + "," + roleCodes);
						flow.setAssigneeName(siteEngineerName + "," + electricianName + "," + roleNames);
					} else if (StringUtil.isNotBlank(electrician) && StringUtil.isBlank(siteEngineer)) {
						flow.setAssignee(electrician + "," + roleCodes);
						flow.setAssigneeName(electricianName + "," + roleNames);
					} else if (StringUtil.isNotBlank(siteEngineer) && StringUtil.isBlank(electrician)) {
						flow.setAssignee(siteEngineer + "," + roleCodes);
						flow.setAssigneeName(siteEngineerName + "," + roleNames);
					}
					break;
				default:
					flow.setAssignee(roleCodes);
					flow.setAssigneeName(roleNames);
					break;
			}
		} else {
			flow.setAssignee(roleCodes);
			flow.setAssigneeName(roleNames);
		}
		return flow;
	}

	public static boolean check(Map<String, Object> map) {
		String taskName = (String) map.get("taskName");
		boolean flag = (boolean) map.get("flag");
		Object siteEngineer = map.get("siteEngineer");
		Object electrician = map.get("electrician");
		boolean isNull = false;
		MaintenanceTaskEnum taskEnum = MaintenanceTaskEnum.of(taskName);
		if (taskEnum != null) {
			switch (taskEnum.getType()) {
					//指派踏勘任务
				case 1:
					//指派施工任务
				case 3:
					if (flag) {
						isNull = (Func.isEmpty(siteEngineer)
							&& Func.isEmpty(electrician));
					}
					break;
					//物料签收
				case 4:
					//施工安全信息审核
				case 6:
					//施工审核上传临时COC
				case 9:
					isNull = (Func.isEmpty(siteEngineer)
						&& Func.isEmpty(electrician));
					break;
				/*//质检信息审核
				case 8:
					isNull = Func.isEmpty(electrician);
					break;*/
				default:
					break;
			}
		}
		return isNull;
	}
}

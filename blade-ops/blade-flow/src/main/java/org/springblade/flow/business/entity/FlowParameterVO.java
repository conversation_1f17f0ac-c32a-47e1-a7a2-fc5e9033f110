package org.springblade.flow.business.entity;

import lombok.Data;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.flow.core.vo.ApprovalVO;
import org.springblade.flow.engine.utils.FlowFunction;
import org.springblade.flow.engine.utils.FlowMaintenanceFunction;
import org.springblade.flow.engine.utils.FlowRole;

import java.io.Serializable;
import java.util.Map;
import java.util.function.Function;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-16 09:55
 **/
@Data
public class FlowParameterVO implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * 流程id
	 */
	private String flowId;
	/**
	 * 流程节点数据字典
	 */
	private String dictFlowKey;
	/**
	 * 业务主键id
	 */
	private String businessId;
	/**
	 * 拥有流程创建角色code
	 */
	private String initCreateRoleCodes;
	/**
	 * 流程节点名称和处理角色code映射关系
	 */
	private Map<String, String> roleCodeAndNameMap;
	/**
	 * check方法
	 */
	private Function<Map<String, Object>, Boolean> checkFunction;
	/**
	 * 流程返回信息方法
	 */
	private Function<Map<String, Object>, ApprovalVO> flowResultFunction;


	public FlowParameterVO(String flowId) {
		switch (flowId) {
			case CommonConstant.PROCESS_DEFINITION_KEY:
				this.flowId = flowId;
				this.dictFlowKey = DictBizCodeEnum.WF_SCHEDULE.getDictCode();
				this.roleCodeAndNameMap = FlowRole.getRole();
				this.checkFunction = FlowFunction::check;
				this.flowResultFunction = FlowFunction::getOrderFlowVO;
				this.initCreateRoleCodes = "007,012,016,018";
				break;
			case CommonConstant.PROCESS_MAINTENANCE_KEY:
				this.flowId = flowId;
				this.dictFlowKey = DictBizCodeEnum.AGENT_MAINT_WORKFLOW_NODE_CODE.getDictCode();
				this.roleCodeAndNameMap = FlowRole.getMaintenanceRole();
				this.checkFunction = FlowMaintenanceFunction::check;
				this.flowResultFunction = FlowMaintenanceFunction::getOrderFlowVO;
				this.initCreateRoleCodes = "007,012,016";
				break;
			default:
		}
	}

}

package org.springblade.flow.business.emun;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TaskEnum {

	/**
	 * 指派踏勘任务
	 */
	SURVEY_WO_ASSIGNMENT(1, "surveyWoAssign","role"),

	/**
	 * 踏勘及业主确认
	 */
	SURVEY_LL_VERIFICATION(2, "surveyLlVerification","user"),

	/**
	 * 屋顶设计
	 */
	SITE_DESIGN(3, "siteDesign","role"),

	/**
	 * 设计审核
	 */
	DESIGN_VERIFICATION(4, "designVerification","role"),
	/**
	 * 发送报价单
	 */
	QUOTES(5, "quotes","role"),
	/**
	 *审核报价单
	 */
	QUOTES_VERIFICATION(6, "quotesVerification","role"),
	/**
	 *设备发货
	 */
	EQUIPMENT_DELIVERY(7, "equipmentDelivery","role"),
	/**
	 *物料签收
	 */
	EQUIPMENT_SIGNING(8, "equipmentSigning","user"),
	/**
	 *指派施工任务
	 */
	ASSIGN_TASKS(9, "assignTasks","role"),

	/**
	 * 施工安全信息提交
	 */
	EHS_SUBMISSION(10, "ehsSubmission","user"),

	/**
	 * 施工安全信息审核 EHS Verification
	 */
	EHS_Verification(11, "ehsVerification","role"),

	/**
	 * 质检信息提交
	 */
	QC_SUBMISSION(12, "qcSubmission","user"),

	/**
	 * 质检信息审核 QC Verification
	 */
	Q_Verification(13, "qcVerification","role"),

	/**
	 * 交付经理验收
	 */
	ROLLOUT_ACCEPTANCE(14, "rolloutAcceptance","role"),

	/**
	 * 业主验收
	 */
	FAC(15, "fac","user"),

	/**
	 * 尾款支付
	 */
	BALANCE_PAYMENT(16, "balancePayment","role"),

	/**
	 * 转维保
	 */
	OFFICIAL_COC(17, "maintenance","role")
	;

	/**
	 * 类型
	 */
	private final int type;

	/**
	 * 描述
	 */
	private final String description;

	/**
	 * 审批人类型
	 */
	private final String typeOfApprove;


	public static TaskEnum of(String taskName) {
		if (taskName == null) {
			return null;
		}
		TaskEnum[] values = TaskEnum.values();
		for (TaskEnum taskEnum : values) {
			if (Objects.equals(taskEnum.description, taskName)) {
				return taskEnum;
			}
		}
		return null;
	}

}

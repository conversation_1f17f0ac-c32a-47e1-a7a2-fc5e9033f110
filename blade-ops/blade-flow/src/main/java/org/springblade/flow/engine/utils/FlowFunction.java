package org.springblade.flow.engine.utils;

import lombok.extern.slf4j.Slf4j;
import org.flowable.task.api.Task;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.business.emun.TaskEnum;
import org.springblade.flow.core.vo.ApprovalVO;

import java.util.Map;
import java.util.Optional;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-16 15:25
 **/
@Slf4j
public class FlowFunction {
	public static boolean check(Map<String, Object> map) {
		String taskName = (String) map.get("taskName");
		Object siteEngineer = map.get("siteEngineer");
		boolean isNull = false;
		TaskEnum taskEnum = TaskEnum.of(taskName);
		if (taskEnum != null) {
			switch (taskEnum.getType()) {
				//指派踏勘任务
				case 1:
					//设备发货
				case 7:
					//指派施工任务
				case 9:
					//施工安全信息审核
				case 11:
					//交付经理验收
				case 14:
					isNull = Func.isEmpty(siteEngineer);
					break;
				default:
					break;
			}
		}
		return isNull;
	}


	public static String objectToString(Object object) {
		return Optional.ofNullable(object)
			.map(Object::toString)
			.orElse("");
	}


	public static ApprovalVO getOrderFlowVO(Map<String, Object> map) {
		Task task = (Task) map.get("task");
		String siteEngineer = objectToString(map.get("siteEngineer"));
		String siteEngineerName = objectToString(map.get("siteEngineerName"));
		String roleCodes = objectToString(map.get("roleCodes"));
		String roleNames = objectToString(map.get("roleNames"));
		String taskAbbreviationName = objectToString(map.get("taskAbbreviationName"));
		ApprovalVO flow = new ApprovalVO();
		flow.setCreateTime(task.getCreateTime());
		flow.setTaskName(taskAbbreviationName);
		flow.setTaskId(task.getId());
		TaskEnum taskEnum = TaskEnum.of(taskAbbreviationName);
		if (taskEnum != null) {
			//设置审批人类型
			flow.setTypeOfApprove(taskEnum.getTypeOfApprove());
			switch (taskEnum.getType()) {
				//踏勘及业主确认
				case 2:
					//物料签收
				case 8:
					//指派施工任务
				case 10:
					//施工安全信息提交
				case 12:
					//业主验收
				case 15:
					flow.setAssignee(siteEngineer);
					flow.setAssigneeName(siteEngineerName);
					break;
				default:
					flow.setAssignee(roleCodes);
					flow.setAssigneeName(roleNames);
					break;
			}
		} else {
			flow.setAssignee(roleCodes);
			flow.setAssigneeName(roleNames);
		}
		return flow;
	}


}

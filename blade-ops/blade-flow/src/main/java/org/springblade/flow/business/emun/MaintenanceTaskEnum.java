package org.springblade.flow.business.emun;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-17 15:12
 **/
@Getter
@AllArgsConstructor
public enum MaintenanceTaskEnum {

	/**
	 * 指派踏勘任务
	 */
	Survey_WO_Assignment(1, "surveyWoAssign"),

	/**
	 * 踏勘及业主确认
	 */
	SURVEY_LL_VERIFICATION(2, "surveyLlVerification"),

	/**
	 * 指派施工任务
	 */
	Installation_WO_Assignment(3, "installWoAssignment"),

	/**
	 * 物料签收
	 */
	MATERIAL_COLLECTION_SIGN(4, "materialCollection"),

	/**
	 * 施工安全信息提交
	 */
	EHS_SUBMISSION(5, "ehsSubmission"),

	/**
	 * 施工安全信息审核 EHS Verification
	 */
	EHS_Verification(6, "ehsVerification"),

	/**
	 * 质检信息提交
	 */
	QC_SUBMISSION(7, "qcSubmission"),

	/**
	 * 质检信息审核 QC Verification
	 */
	QC_Verification(8, "qcVerification"),
	/**
	 * 电路合格证书生成
	 */
	COC_Creation(9, "cocCreation"),
	/**
	 * 业主验收
	 */
	FAC(10, "fac")
	;

	/**
	 * 类型
	 */
	private final int type;

	/**
	 * 描述
	 */
	private final String description;


	public static MaintenanceTaskEnum of(String taskName) {
		if (taskName == null) {
			return null;
		}
		MaintenanceTaskEnum[] values = MaintenanceTaskEnum.values();
		for (MaintenanceTaskEnum taskEnum : values) {
			if (Objects.equals(taskEnum.description, taskName)) {
				return taskEnum;
			}
		}
		return null;
	}
}

package org.springblade.flow.business.process.impl;

import liquibase.repackaged.org.apache.commons.text.StringEscapeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.OrderStatusConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.constant.FlowConstant;
import org.springblade.flow.business.emun.TaskEnum;
import org.springblade.flow.business.entity.FlowParameterVO;
import org.springblade.flow.business.process.BusinessProcessMethodService;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.dto.PurviewDTO;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.core.vo.ApprovalVO;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springblade.flow.core.vo.PurviewVO;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-15 13:31
 **/
@Service
@AllArgsConstructor
@Slf4j
public class AgentInstallationProcessServiceImpl implements BusinessProcessMethodService<FlowParameterVO> {
	private final ISysClient iSysClient;
	private final IdentityService identityService;
	private final RuntimeService runtimeService;
	private final TaskService taskService;
	private final IDictBizClient dictBizClient;
	private final IAgentClient agentClient;
	private final IUserSearchClient userSearchClient;
	private final static List<String> PROCESS_END_STATE = new ArrayList<>();

	static {
		// 订单关闭
		PROCESS_END_STATE.add(OrderStatusConstants.ORDER_STATUS_CLOSED);
		// 订单转维保
		PROCESS_END_STATE.add(OrderStatusConstants.END_NODE);
	}

	/**
	 * 启动流程
	 *
	 * @param flowParameterVO 入参
	 * @return OrderFlowVO
	 * <AUTHOR>
	 * @since 2024/4/16 10:41
	 **/
	@Override
	public OrderFlowVO startOrderProcess(FlowParameterVO flowParameterVO) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		Map<String, String> map = getFlowKey(flowParameterVO.getDictFlowKey(), dictBizClient);
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(flowParameterVO.getFlowId(), flowParameterVO.getBusinessId());
		// 获取流程活动任务
		Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).active().singleResult();
		// 组装流程通用类
		OrderFlowVO flow = new OrderFlowVO();
		String taskName = map.get(task.getName());
		flow.setProcessInstanceId(processInstance.getId());
		flow.setCreateTime(processInstance.getStartTime());
		flow.setTaskName(taskName);
		flow.setTaskId(task.getId());
		Map<String, String> roleMap = flowParameterVO.getRoleCodeAndNameMap();
		flow.setAssignee(roleMap.get(taskName));
		flow.setAssigneeName(getRoleName(roleMap.get(taskName), iSysClient));
		return flow;
	}


	/**
	 * 审批&拒绝流程
	 *
	 * @param orderFlowDTO    业务对象
	 * @param flowParameterVO 入参
	 * @return R<ApprovalVO>
	 * <AUTHOR>
	 * @since 2024/4/16 10:41
	 **/
	@Override
	public R<ApprovalVO> completeOrderTask(OrderFlowDTO orderFlowDTO, FlowParameterVO flowParameterVO) {
		// 增加评论
		String taskId = orderFlowDTO.getTaskId();
		String taskName = orderFlowDTO.getTaskName();
		String processInstanceId = orderFlowDTO.getProcessInstanceId();
		boolean flag = orderFlowDTO.getFlag();
		Map<String, Object> variables = orderFlowDTO.getVariables();
		// 非空判断
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		Object siteEngineer = variables.get("siteEngineer");
		Object comment = variables.get("comment");
		Map<String, Object> map = new HashMap<>(3);
		map.put("taskName", taskName);
		map.put("flag", flag);
		map.put("siteEngineer", siteEngineer);
		//指定人的节点传参校验
		if (flowParameterVO.getCheckFunction().apply(map)) {
			return R.fail("siteEngineer cannot be empty.");
		}
		//查询一下当前节点ID 节点名称 是否和用户传的匹配
		boolean isEqual = false;
		Task taskBefore = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
		log.info("completeOrderTask get taskBefore ");
		if (taskBefore != null) {
			//String submitTaskId = orderFlowDTO.getTaskId();
			log.info("completeOrderTask get paramsCurrentTaskId : {} ",taskId );
			log.info("completeOrderTask get DbTaskBeforeId : {}, taskName : {}", taskBefore.getId(),taskBefore.getName());
			if (StringUtil.isNoneBlank(taskBefore.getId()) && StringUtil.isNoneBlank(taskId)) {
				isEqual = taskBefore.getId().equals(taskId);
			}
			if (!isEqual) {
				return R.fail("Process mismatch ！taskId is: " + taskBefore.getId() + " taskName is: " + taskBefore.getName());
			}
		} else {
			return R.fail("Process has ended");
		}
		//驳回时审批意见必填
		if (!flag && Func.isEmpty(comment)) {
			return R.fail("comment cannot be empty.");
		}
		//流程节点操作人
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		//特殊字符转义处理
		String comments = StringEscapeUtils.unescapeHtml4((String) comment);
		//pass时审批 设置标识ok
		if (flag) {
			taskService.addComment(taskId, processInstanceId, "flag", "ok");
		}
		taskService.addComment(taskId, processInstanceId, comments);
		// 完成任务
		taskService.complete(taskId, variables);
		// 获取返回信息
		R<ApprovalVO> flowParameterResultVO = getApprovalVO(flowParameterVO, processInstanceId, siteEngineer);
		if (flowParameterResultVO != null) {
			return flowParameterResultVO;
		}
		return R.success("Process has ended");
	}

	/**
	 * 返回工作流明细参数
	 *
	 * @param flowParameterVO   不同工作流流程对应的参数对象
	 * @param processInstanceId 流程实例id
	 * @param siteEngineer      施工人员id
	 * @return R<ApprovalVO>
	 * <AUTHOR>
	 * @since 2024/4/17 16:14
	 **/
	@Nullable
	private R<ApprovalVO> getApprovalVO(FlowParameterVO flowParameterVO, String processInstanceId, Object siteEngineer) {
		//获取流转后流程活动任务
		Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).active().singleResult();
		if (task != null) {
			List<Long> userIds = new ArrayList<>();
			if (Func.isNotEmpty(siteEngineer)) {
				userIds.add((long) siteEngineer);
			}
			String siteEngineerName = "";
			if (userIds.size() > BizConstant.NUMBER_ZERO) {
				List<User> userList = userSearchClient.listByUserIds(userIds).getData();
				if (Func.isNotEmpty(siteEngineer)) {
					siteEngineerName = Optional.ofNullable(userList).orElse(new ArrayList<>()).stream().filter(a ->
						a.getId() == (long) siteEngineer
					).findFirst().map(User::getRealName).get();
				}
			}
			Map<String, String> flowKeyMap = this.getFlowKey(flowParameterVO.getDictFlowKey(), dictBizClient);
			// task名称简称-和数据字典key对应
			String taskAbbreviationName = flowKeyMap.get(task.getName());
			Map<String, String> roleCodeMap = flowParameterVO.getRoleCodeAndNameMap();
			String roleCodes = roleCodeMap.get(taskAbbreviationName);
			String roleNames = getRoleName(roleCodes, iSysClient);
			Map<String, Object> conditionMap = new HashMap<>(6);
			conditionMap.put("task", task);
			conditionMap.put("siteEngineer", siteEngineer);
			conditionMap.put("siteEngineerName", siteEngineerName);
			conditionMap.put("roleCodes", roleCodes);
			conditionMap.put("roleNames", roleNames);
			conditionMap.put("taskAbbreviationName", taskAbbreviationName);
			return R.data(flowParameterVO.getFlowResultFunction().apply(conditionMap));
		}
		return null;
	}

	/**
	 * 获取当前用户是否对该节点存在编辑权限
	 *
	 * @param purviewDTO 业务对象
	 * @return PurviewVO
	 * <AUTHOR>
	 * @since 2024/4/16 10:41
	 **/
	@Override
	public PurviewVO getPurview(PurviewDTO purviewDTO, FlowParameterVO flowParameterVO) {
		//创维交付经理  016 系统管理员 拥有所有节点的编辑权限
		PurviewVO purviewVO = new PurviewVO();
		boolean flag;
		purviewVO.setFlag(false);
		String userId = String.valueOf(AuthUtil.getUserId());
		//useId为空时 框架默认-1
		if (FlowConstant.MINUS_ONE.equals(userId)) {
			return purviewVO;
		}
		//根据userId查询 用户包含的所有角色
		List<String> codes = iSysClient.findUserRoleInfoByUserId(userId).getData().stream()
			.map(Role::getRoleCode).collect(Collectors.toList());
		//用户角色不为空
		if (CollectionUtil.isNotEmpty(codes)) {
			//创建订单时 订单号为空 Rollout Manager Distributor/SD
			if (StringUtil.isBlank(purviewDTO.getOrderId())) {
				purviewVO.setFlag(check(codes, flowParameterVO.getInitCreateRoleCodes()));
				return purviewVO;
			}
			OrderWorkFlowDTO orderWorkFlow = new OrderWorkFlowDTO();
			orderWorkFlow.setOrderId(Long.valueOf(purviewDTO.getOrderId()));
			OrderWorkFlowVO orderWorkFlowVO = agentClient.getWorkFlowDataByOrderId(orderWorkFlow).getData();
			if (orderWorkFlowVO == null) {
				return purviewVO;
			}
			//通过订单号查询当前订单节点和处理人信息
			String roleType = orderWorkFlowVO.getWfCurrentType();
			String taskName = orderWorkFlowVO.getWfCurrentStatus();
			String aggeName = orderWorkFlowVO.getWfCurrentRole();
			String conditionTaskName = purviewDTO.getTaskName();
			if (StringUtil.isNoneBlank(taskName)) {
				//订单状态为关闭 办结 不能编辑
				if (PROCESS_END_STATE.contains(taskName)) {
					return purviewVO;
				}
				if (StringUtil.isNoneBlank(roleType) && StringUtil.isNoneBlank(aggeName)) {
					if (taskName.equals(StringEscapeUtils.unescapeHtml4(conditionTaskName))) {
						//当前节点处理是人 匹配用户ID相同可以编辑
						//当前节点处理是角色 匹配用户角色相同可以编辑
						//超级管理员 创维交付经理 在当前节点都可以编辑
						flag = (check(codes, "007,016") || (roleType.equals(OrderStatusConstants.CURRENT_USER) && aggeName.contains(userId)) ||
							(roleType.equals(OrderStatusConstants.CURRENT_ROLE) && check(codes, aggeName)) || (roleType.equals(OrderStatusConstants.CURRENT_USER_ROLE) && (check(codes, aggeName) || aggeName.contains(userId))));
						purviewVO.setFlag(flag);
						return purviewVO;
					}else if(TaskEnum.EQUIPMENT_SIGNING.getDescription().equals(taskName) && TaskEnum.EQUIPMENT_DELIVERY.getDescription().equals(conditionTaskName)){
						// 特殊节点，流程停留在equipmentDelivery节点，equipmentSigning节点也能编辑
						purviewVO.setFlag(check(codes, "007,016"));
						return purviewVO;
					}
				}
			}
		}
		return purviewVO;
	}

}

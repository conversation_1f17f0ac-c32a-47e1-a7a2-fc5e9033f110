package org.springblade.flow.engine.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class FlowRole {

	public static Map<String, String> getRole() {
		Map<String, String> map = new HashMap<>(16);
		map.put("surveyWoAssign", "012,016");
		map.put("surveyLlVerification", "008");
		map.put("siteDesign", "010");
		map.put("designVerification", "017");
		map.put("quotes", "018");
		map.put("quotesVerification", "011");
		map.put("equipmentDelivery", "016");
		map.put("equipmentSigning", "008");
		map.put("assignTasks", "012,016");
		map.put("ehsSubmission", "008");
		map.put("ehsVerification", "009");
		map.put("qcSubmission", "008");
		map.put("qcVerification", "009");
		map.put("rolloutAcceptance", "016");
		map.put("fac", "008");
		map.put("balancePayment", "011");
		map.put("maintenance", "012");
		return map;
	}

	public static Map<String, String> getMaintenanceRole() {
		Map<String, String> map = new HashMap<>(16);
		map.put("warrantyVerification", "012");
		map.put("calloutQuotation", "011,012");
		map.put("calloutPop", "011,012");
		map.put("surveyDate", "012");
		map.put("surveyWoAssign", "016,018");
		map.put("surveyLlVerification", "008,009");
		map.put("surveyReview", "016,018");
		map.put("siteDesign", "010");
		map.put("designVerification", "017");
		map.put("stockConfirmation", "015");
		map.put("essQuotation", "011,012");
		map.put("essPop", "011,012");
		map.put("installationDate", "012");
		map.put("installWoAssignment", "016,018");
		map.put("materialCollection", "008,009");
		map.put("ehsSubmission", "008,009");
		map.put("ehsVerification", "013");
		map.put("qcSubmission", "008,009");
		map.put("qcVerification", "014");
		map.put("cocCreation", "014,016,018");
		map.put("fac", "008,009");
		return map;
	}
}


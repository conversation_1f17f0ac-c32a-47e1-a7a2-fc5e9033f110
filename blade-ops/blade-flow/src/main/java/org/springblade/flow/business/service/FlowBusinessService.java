/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.dto.PurviewDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.vo.*;

import java.util.List;


/**
 * 流程业务类
 *
 * <AUTHOR>
 */
public interface FlowBusinessService {

	/**
	 * 流程待签列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectClaimPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程待办列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectTodoPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程已发列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectSendPage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 流程办结列表
	 *
	 * @param page      分页工具
	 * @param bladeFlow 流程类
	 * @return
	 */
	IPage<BladeFlow> selectDonePage(IPage<BladeFlow> page, BladeFlow bladeFlow);

	/**
	 * 完成任务
	 *
	 * @param leave 请假信息
	 * @return boolean
	 */
	boolean completeTask(BladeFlow leave);

	/**
	 * 审批历史记录
	 *
	 * @param processInstanceId 流程实例ID
	 * @return List<BladeFlow>
	 */
	List<HistoryVO> getTaskHistory(String processInstanceId,String orderType);

	/**
	 * 启动EPC流程 Initiator
	 *
	 * @param businessId 业务id
	 * @param flowId     流程ID
	 * @return OrderFlowVO
	 */
	OrderFlowVO startOrderProcess(String businessId, String flowId);

	/**
	 * 流程流转
	 *
	 * @param orderFlowDTO 流程类
	 * @param flowId       流程ID
	 * @return OrderFlowVO
	 */
	R<ApprovalVO> completeOrderTask(OrderFlowDTO orderFlowDTO, String flowId);

	/**
	 * 最新审批记录
	 *
	 * @param processInstanceId 流程实例ID
	 * @return HistoryVO
	 */
	HistoryVO getNewest(String processInstanceId,String OrderType);

	/**
	 * 审批流程图
	 *
	 * @param orderId 流程实例ID
	 * @return List<HistoryVO>
	 */
	List<ViewVO> getFlowView(String orderId,String orderType);

	/**
	 * 页面能否编辑
	 *
	 * @param purviewDTO 流程模板数据
	 * @return Boolean
	 */
	PurviewVO getPurview(PurviewDTO purviewDTO);
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.flow.business.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;

import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.OrderStatusConstants;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.business.constant.FlowConstant;
import org.springblade.flow.business.entity.FlowParameterVO;
import org.springblade.flow.business.process.BusinessProcessMethodService;
import org.springblade.flow.business.service.FlowBusinessService;
import org.springblade.flow.core.constant.ProcessConstant;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.dto.PurviewDTO;
import org.springblade.flow.core.entity.BladeFlow;
import org.springblade.flow.core.utils.TaskUtil;
import org.springblade.flow.core.vo.*;
import org.springblade.flow.engine.constant.FlowEngineConstant;
import org.springblade.flow.engine.entity.FlowProcess;
import org.springblade.flow.engine.utils.FlowCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程业务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class FlowBusinessServiceImpl implements FlowBusinessService {

	private final TaskService taskService;
	private final HistoryService historyService;
	private final IUserClient iUserClient;
	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final ISysClient iSysClient;
	private final IAgentClient iAgentClient;
	private final IDictBizClient bizClient;
	private final IUserSearchClient iUserSearchClient;
	private static final String USR_TASK = "userTask";
	private final BusinessProcessMethodService<FlowParameterVO> businessProcessMethodService;

	@Override
	public IPage<BladeFlow> selectClaimPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		String taskGroup = TaskUtil.getCandidateGroup();
		List<BladeFlow> flowList = new LinkedList<>();

		// 个人等待签收的任务
		TaskQuery claimUserQuery = taskService.createTaskQuery().taskCandidateUser(taskUser)
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 定制流程等待签收的任务
		TaskQuery claimRoleWithTenantIdQuery = taskService.createTaskQuery().taskTenantId(AuthUtil.getTenantId()).taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		// 通用流程等待签收的任务
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, claimUserQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);
		buildFlowTaskList(bladeFlow, flowList, claimRoleWithoutTenantIdQuery, FlowEngineConstant.STATUS_CLAIM);

		// 计算总数
		long count = claimUserQuery.count() + claimRoleWithTenantIdQuery.count() + claimRoleWithoutTenantIdQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectTodoPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		// 已签收的任务
		TaskQuery todoQuery = taskService.createTaskQuery().taskAssignee(taskUser).active()
			.includeProcessVariables().orderByTaskCreateTime().desc();

		// 构建列表数据
		buildFlowTaskList(bladeFlow, flowList, todoQuery, FlowEngineConstant.STATUS_TODO);

		// 计算总数
		long count = todoQuery.count();
		// 设置页数
		page.setSize(count);
		// 设置总数
		page.setTotal(count);
		// 设置数据
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectSendPage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricProcessInstanceQuery historyQuery = historyService.createHistoricProcessInstanceQuery().startedBy(taskUser).orderByProcessInstanceStartTime().desc();

		if (bladeFlow.getCategory() != null) {
			historyQuery.processDefinitionCategory(bladeFlow.getCategory());
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			historyQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			historyQuery.startedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			historyQuery.startedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricProcessInstance> historyList = historyQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));

		historyList.forEach(historicProcessInstance -> {
			BladeFlow flow = new BladeFlow();
			// historicProcessInstance
			flow.setCreateTime(historicProcessInstance.getStartTime());
			flow.setEndTime(historicProcessInstance.getEndTime());
			flow.setVariables(historicProcessInstance.getProcessVariables());
			String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
			if (businessKey.length > 1) {
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}
			flow.setHistoryActivityName(historicProcessInstance.getName());
			flow.setProcessInstanceId(historicProcessInstance.getId());
			flow.setHistoryProcessInstanceId(historicProcessInstance.getId());
			// ProcessDefinition
			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessInstanceId(historicProcessInstance.getId());
			// HistoricTaskInstance
			List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
			if (Func.isNotEmpty(historyTasks)) {
				HistoricTaskInstance historyTask = historyTasks.iterator().next();
				flow.setTaskId(historyTask.getId());
				flow.setTaskName(historyTask.getName());
				flow.setTaskDefinitionKey(historyTask.getTaskDefinitionKey());
			}
			// Status
			if (historicProcessInstance.getEndActivityId() != null) {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
			} else {
				flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});

		// 计算总数
		long count = historyQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public IPage<BladeFlow> selectDonePage(IPage<BladeFlow> page, BladeFlow bladeFlow) {
		String taskUser = TaskUtil.getTaskUser();
		List<BladeFlow> flowList = new LinkedList<>();

		HistoricTaskInstanceQuery doneQuery = historyService.createHistoricTaskInstanceQuery().taskAssignee(taskUser).finished()
			.includeProcessVariables().orderByHistoricTaskInstanceEndTime().desc();

		if (bladeFlow.getCategory() != null) {
			doneQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			doneQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			doneQuery.taskCompletedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			doneQuery.taskCompletedBefore(bladeFlow.getEndDate());
		}

		// 查询列表
		List<HistoricTaskInstance> doneList = doneQuery.listPage(Func.toInt((page.getCurrent() - 1) * page.getSize()), Func.toInt(page.getSize()));
		doneList.forEach(historicTaskInstance -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(historicTaskInstance.getId());
			flow.setTaskDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
			flow.setTaskName(historicTaskInstance.getName());
			flow.setAssignee(historicTaskInstance.getAssignee());
			flow.setCreateTime(historicTaskInstance.getCreateTime());
			flow.setExecutionId(historicTaskInstance.getExecutionId());
			flow.setHistoryTaskEndTime(historicTaskInstance.getEndTime());
			flow.setVariables(historicTaskInstance.getProcessVariables());

			FlowProcess processDefinition = FlowCache.getProcessDefinition(historicTaskInstance.getProcessDefinitionId());
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));

			flow.setProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			flow.setHistoryProcessInstanceId(historicTaskInstance.getProcessInstanceId());
			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance((historicTaskInstance.getProcessInstanceId()));
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
				if (historicProcessInstance.getEndActivityId() != null) {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_FINISHED);
				} else {
					flow.setProcessIsFinished(FlowEngineConstant.STATUS_UNFINISHED);
				}
			}
			flow.setStatus(FlowEngineConstant.STATUS_FINISH);
			flowList.add(flow);
		});
		// 计算总数
		long count = doneQuery.count();
		// 设置总数
		page.setTotal(count);
		page.setRecords(flowList);
		return page;
	}

	@Override
	public boolean completeTask(BladeFlow flow) {
		String taskId = flow.getTaskId();
		String processInstanceId = flow.getProcessInstanceId();
		String comment = Func.toStr(flow.getComment(), ProcessConstant.PASS_COMMENT);
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		// 创建变量
		Map<String, Object> variables = flow.getVariables();
		if (variables == null) {
			variables = Kv.create();
		}
		variables.put(ProcessConstant.PASS_KEY, flow.isPass());
		// 完成任务
		taskService.complete(taskId, variables);
		return true;
	}


	/**
	 * 构建流程
	 *
	 * @param bladeFlow 流程通用类
	 * @param flowList  流程列表
	 * @param taskQuery 任务查询类
	 * @param status    状态
	 */
	private void buildFlowTaskList(BladeFlow bladeFlow, List<BladeFlow> flowList, TaskQuery taskQuery, String status) {
		if (bladeFlow.getCategory() != null) {
			taskQuery.processCategoryIn(Func.toStrList(bladeFlow.getCategory()));
		}
		if (bladeFlow.getProcessDefinitionName() != null) {
			taskQuery.processDefinitionName(bladeFlow.getProcessDefinitionName());
		}
		if (bladeFlow.getBeginDate() != null) {
			taskQuery.taskCreatedAfter(bladeFlow.getBeginDate());
		}
		if (bladeFlow.getEndDate() != null) {
			taskQuery.taskCreatedBefore(bladeFlow.getEndDate());
		}
		taskQuery.list().forEach(task -> {
			BladeFlow flow = new BladeFlow();
			flow.setTaskId(task.getId());
			flow.setTaskDefinitionKey(task.getTaskDefinitionKey());
			flow.setTaskName(task.getName());
			flow.setAssignee(task.getAssignee());
			flow.setCreateTime(task.getCreateTime());
			flow.setClaimTime(task.getClaimTime());
			flow.setExecutionId(task.getExecutionId());
			flow.setVariables(task.getProcessVariables());

			HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(task.getProcessInstanceId());
			if (Func.isNotEmpty(historicProcessInstance)) {
				String[] businessKey = Func.toStrArray(StringPool.COLON, historicProcessInstance.getBusinessKey());
				flow.setBusinessTable(businessKey[0]);
				flow.setBusinessId(businessKey[1]);
			}

			FlowProcess processDefinition = FlowCache.getProcessDefinition(task.getProcessDefinitionId());
			flow.setCategory(processDefinition.getCategory());
			flow.setCategoryName(FlowCache.getCategoryName(processDefinition.getCategory()));
			flow.setProcessDefinitionId(processDefinition.getId());
			flow.setProcessDefinitionName(processDefinition.getName());
			flow.setProcessDefinitionKey(processDefinition.getKey());
			flow.setProcessDefinitionVersion(processDefinition.getVersion());
			flow.setProcessInstanceId(task.getProcessInstanceId());
			flow.setStatus(status);
			flowList.add(flow);
		});
	}

	/**
	 * 获取历史流程
	 *
	 * @param processInstanceId 流程实例id
	 * @return HistoricProcessInstance
	 */
	private HistoricProcessInstance getHistoricProcessInstance(String processInstanceId) {
		return historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
	}

	/**
	 * 审批历史记录
	 *
	 * @param processInstanceId 流程实例ID
	 * @return List<OrderFlowVO>
	 */
	@Override
	public List<HistoryVO> getTaskHistory(String processInstanceId,String OrderType) {
		if (StringUtil.isBlank(processInstanceId)) {
			return new ArrayList<>();
		}
		List<HistoryVO> historyList = new ArrayList<>();
		List<BladeFlow> bladeFlows = getHistory(processInstanceId);
		bladeFlows.forEach(flow -> {
			HistoryVO historyVO = new HistoryVO();
			BeanUtils.copyProperties(flow, historyVO);
			if (historyVO.getPass()) {
				historyVO.setColor(FlowConstant.GREEN);
				//最终节点的时间+1s用于排序
				if (StringUtil.isBlank(historyVO.getAssigneeName())) {
					historyVO.setCreateTime(new Date(historyVO.getCreateTime().getTime() + 1000));
				}
			} else if (StringUtil.isBlank(historyVO.getComment())) {
				historyVO.setColor(FlowConstant.YELLOW);
				//进行节点的时间+1s用于排序
				historyVO.setCreateTime(new Date(historyVO.getCreateTime().getTime() + 1000));
			} else {
				if("guardian".equals(OrderType)) {
					//流程节点为 Payment Confirmation Balance Payment
					if (FlowConstant.LL_INFO_CONFIRMATION.equals(historyVO.getTaskName()) || FlowConstant.PAYMENT_CONFIRMATION.equals(historyVO.getTaskName())) {
						historyVO.setColor(FlowConstant.BLUE);
					} else {
						historyVO.setColor(FlowConstant.RED);
					}
				} else {
					//流程节点为 Payment Confirmation Balance Payment
					if (FlowConstant.MAINT_CANCEL_SKYWORTH_CALLOUT_POP.equals(historyVO.getTaskName()) || FlowConstant.MAINT_CANCEL_SKYWORTH_ESS_POP.equals(historyVO.getTaskName())) {
						historyVO.setColor(FlowConstant.BLUE);
					} else {
						historyVO.setColor(FlowConstant.RED);
					}
				}

			}
			historyList.add(historyVO);
		});
		List<HistoryVO> historyNew = historyList.stream().sorted(Comparator.comparing(HistoryVO::getCreateTime))
			.collect(Collectors.toList());
		//流程图中包含取消流程 蓝色 则最后一个节点无色
		boolean b = historyNew.stream().anyMatch(m -> m.getColor().equals(FlowConstant.BLUE));
		if (b) {
			historyNew.remove(historyList.size() - 1);
		}
		return historyNew;

	}

	private List<BladeFlow> getHistory(String processInstanceId) {
		List<BladeFlow> bladeFlows = new ArrayList<>();
		//获取历史任务
		List<HistoricActivityInstance> listHistory = historyService.createHistoricActivityInstanceQuery()
			.processInstanceId(processInstanceId)
			.orderByActivityId()
			.desc().list();
		listHistory.forEach(hi -> {
			if (StringUtils.equals(USR_TASK, hi.getActivityType())
				|| FlowEngineConstant.START_EVENT.equals(hi.getActivityType())
				|| FlowEngineConstant.END_EVENT.equals(hi.getActivityType())) {
				BladeFlow flow = new BladeFlow();
				flow.setTaskName(hi.getActivityName());
				flow.setTaskId(hi.getTaskId());
				if (hi.getEndTime() == null) {
					flow.setCreateTime(hi.getStartTime());
				} else {
					flow.setCreateTime(hi.getEndTime());
				}
				//开始节点、结束节点默认通过
				if (FlowEngineConstant.START_EVENT.equals(hi.getActivityType()) || FlowEngineConstant.END_EVENT.equals(hi.getActivityType())) {
					flow.setFlag("ok");
				}
				//流程启动人
				if (FlowEngineConstant.START_EVENT.equals(hi.getActivityType())) {
					List<HistoricProcessInstance> processInstanceList = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).orderByProcessInstanceStartTime().asc().list();
					if (!processInstanceList.isEmpty()) {
						if (StringUtil.isNotBlank(processInstanceList.get(0).getStartUserId())) {
							String taskUser = processInstanceList.get(0).getStartUserId();
							flow.setAssignee(String.valueOf(TaskUtil.getUserId(taskUser)));
						}
					}
				} else {
					flow.setAssignee(String.valueOf(TaskUtil.getUserId(hi.getAssignee())));
				}

				bladeFlows.add(flow);
			}
		});
		//获取历史意见
		List<Comment> comments = taskService.getProcessInstanceComments(processInstanceId);
		bladeFlows.forEach(bla -> comments.stream()
			.filter(comment -> "comment".equals(comment.getType())).filter(m -> Objects.equals(m.getTaskId(), bla.getTaskId()))
			.findFirst().map(m -> {
				bla.setAssignee(String.valueOf(TaskUtil.getUserId(m.getUserId())));
				bla.setComment(m.getFullMessage());
				return bla;
			}));
		//获取流程流转用户ID集合
		List<Long> userIds = bladeFlows.stream().map(BladeFlow::getAssignee).map(Long::valueOf).distinct().collect(Collectors.toList());
		//获取用户信息
		Map<Long, String> userMap = iUserSearchClient.listAllByUserIds(userIds).getData().stream().collect(Collectors.toMap(User::getId, User::getRealName));
		bladeFlows.forEach(bladeFlow -> {
			//取到用户对应的realName
			bladeFlow.setAssigneeName(userMap.get(Func.toLong(bladeFlow.getAssignee())));
		});
		//获取审批标识
		bladeFlows.forEach(bla -> comments.stream().filter(comment -> "flag".equals(comment.getType()))
			.filter(m -> Objects.equals(m.getTaskId(), bla.getTaskId()))
			.findFirst().map(m -> {
				bla.setFlag(((CommentEntityImpl) m).getMessage());
				return bla;
			}));
		return bladeFlows;
	}

	@Override
	public OrderFlowVO startOrderProcess(String businessId, String flowId) {
		FlowParameterVO flowParameterVO = new FlowParameterVO(flowId);
		flowParameterVO.setBusinessId(businessId);
		return businessProcessMethodService.startOrderProcess(flowParameterVO);
	}

	/**
	 * 多个角色获取多个ID
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<ApprovalVO> completeOrderTask(OrderFlowDTO orderFlowDTO, String flowId) {
		// 增加评论
		FlowParameterVO flowParameterVO = new FlowParameterVO(flowId);
		return businessProcessMethodService.completeOrderTask(orderFlowDTO, flowParameterVO);
	}


	@Override
	public HistoryVO getNewest(String processInstanceId,String OrderType) {
		List<HistoryVO> historyList = getTaskHistory(processInstanceId,OrderType);
		if (CollectionUtil.isEmpty(historyList)) {
			return null;
		}
		return historyList.get(historyList.size() - 2);
	}


	@Override
	public List<ViewVO> getFlowView(String orderId, String orderType) {
		String workNodeBizDictCode = DictBizCodeEnum.WF_SCHEDULE.getDictCode();
		if ("epc_maintenance".equals(orderType)) {
			workNodeBizDictCode = DictBizCodeEnum.AGENT_MAINT_WORKFLOW_NODE_CODE.getDictCode();
		}
		//获取字典表中流程节点名称
		Map<String, String> map = getFlowKey(workNodeBizDictCode);
		List<DictBiz> bizList = getFlowSort(workNodeBizDictCode);
		List<ViewVO> voList = new ArrayList<>();
		bizList.forEach(biz -> {
			if (!OrderStatusConstants.ORDER_STATUS_CLOSED.equals(biz.getDictKey())) {
				ViewVO vo = new ViewVO();
				vo.setTaskName(biz.getDictValue());
				vo.setTaskNameKey(biz.getDictKey());
				voList.add(vo);
			}
		});
		/*//去掉数据库配置的cancel
		if (voList.size() == FlowConstant.TWENTY_TWO) {
			voList.remove(voList.size() - 1);
		}*/
		if (StringUtil.isNoneBlank(orderId)) {
			OrderWorkFlowDTO orderWorkFlow = new OrderWorkFlowDTO();
			orderWorkFlow.setOrderId(Long.valueOf(orderId));
			OrderWorkFlowVO orderWorkFlowVO = iAgentClient.getWorkFlowDataByOrderId(orderWorkFlow).getData();
			List<HistoryVO> listHistory = getTaskHistory(orderWorkFlowVO.getWfInstanceId(),orderType);
			Map<String, List<HistoryVO>> groupListMap = listHistory.stream().collect(Collectors.groupingBy(HistoryVO::getTaskName));
			listHistory = groupListMap.values().stream().map(lists -> lists.get(lists.size() - 1))
				.sorted(Comparator.comparing(HistoryVO::getCreateTime))
				.collect(Collectors.toList());
			//获取审批日志
			List<ViewVO> vos = new ArrayList<>();
			listHistory.forEach(history -> {
				ViewVO vo = new ViewVO();
				BeanUtils.copyProperties(history, vo);
				vos.add(vo);
			});
			//更新流程图节点信息
			voList.forEach(bla -> vos.stream()
				.filter(m -> Objects.equals(bla.getTaskNameKey(), map.get(m.getTaskName())))
				.findFirst().map(m -> {
					bla.setColor(m.getColor());
					bla.setAssignee(m.getAssignee());
					bla.setPass(m.getPass());
					bla.setTaskId(m.getTaskId());
					bla.setAssigneeName(m.getAssigneeName());
					bla.setCreateTime(m.getCreateTime());
					return bla;
				}));
			// 历史审批节点为空，则直接返回数据字典节点
			if (CollectionUtil.isEmpty(listHistory)) {
				return voList;
			}
			//跨节点驳回 中间的节点也变为红色
			// 判断流程图中最后一个节点是否为驳回节点 如果是则驳回节点到当前节点都为红色
			int k = vos.size() - 1;
			if (StringUtil.isNoneBlank(voList.get(k).getColor())) {
				if (voList.get(k).getColor().equals(FlowConstant.RED)) {
					//获取黄色节点的下标
					int j = 0;
					for (int i = 0; i < voList.size(); i++) {
						if (FlowConstant.YELLOW.equals(voList.get(i).getColor()) || FlowConstant.BLUE.equals(voList.get(i).getColor())) {
							j = i;
							break;
						}
					}
					if (k - j > 1) {
						//截取到黄色节点到红色节点之间的数据【)
						voList.subList(j + 1, k)
							.forEach(viewVO -> viewVO.setColor(FlowConstant.RED));
					}
				}
			}
		}
		return voList;
	}

	@Override
	public PurviewVO getPurview(PurviewDTO purviewDTO) {
		return businessProcessMethodService.getPurview(purviewDTO, new FlowParameterVO(purviewDTO.getFlowId()));
	}


	private Map<String, String> getFlowKey(String workNodeBizDictCode) {
		List<DictBiz> bizList = bizClient.getListByLang(workNodeBizDictCode, "en").getData();
		return bizList.stream().collect(HashMap::new, (stringStringHashMap, dictBiz) -> stringStringHashMap.put(dictBiz.getDictValue(), dictBiz.getDictKey()), HashMap::putAll);
	}

	private List<DictBiz> getFlowSort(String workNodeBizDictCode) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		return bizClient.getListByLang(workNodeBizDictCode, currentLanguage).getData();
	}
}

package org.springblade.flow.business.process;

import cn.hutool.core.text.StrPool;
import org.jetbrains.annotations.Nullable;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.dto.PurviewDTO;
import org.springblade.flow.core.vo.ApprovalVO;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springblade.flow.core.vo.PurviewVO;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务流程公共方法
 *
 * @description:
 * @author: SDT50545
 * @since: 2024-04-15 10:44
 **/
public interface BusinessProcessMethodService<T> {
	/**
	 * 启动流程
	 *
	 * @param t 入参
	 * @return OrderFlowVO
	 * <AUTHOR>
	 * @since 2024/4/15 11:33
	 **/
	OrderFlowVO startOrderProcess(T t);

	/**
	 * 审批流程
	 *
	 * @param orderFlowDTO 订单详情
	 * @param t            入参
	 * @return R
	 */
	R<ApprovalVO> completeOrderTask(OrderFlowDTO orderFlowDTO, T t);

	/**
	 * 查询流程图
	 *
	 * @param purviewDTO 参数
	 * @param t          枚举
	 * @return 流程图
	 */
	PurviewVO getPurview(PurviewDTO purviewDTO, T t);

	/**
	 * 根据角色编码查询角色名称
	 *
	 * @param roleCode   角色编码
	 * @param iSysClient 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2024/4/16 10:38
	 **/
	@Nullable
	default String getRoleName(String roleCode, ISysClient iSysClient) {
		if (StringUtil.isNoneBlank(roleCode)) {
			if (roleCode.contains(StrPool.COMMA)) {
				StringBuilder str = new StringBuilder();
				String[] result = roleCode.split(StrPool.COMMA);
				for (int i = 0; i < result.length; i++) {
					if (i != 0) {
						str.append(StrPool.COMMA);
					}
					str.append(iSysClient.getRoleInfoByCode(result[i]).getData().getRoleName());
				}
				return str.toString();
			}
			return iSysClient.getRoleInfoByCode(roleCode).getData().getRoleName();
		}
		return null;
	}

	/**
	 * 通过数据字典编码查询数据字典
	 *
	 * @param code      字典编码
	 * @param bizClient 入参
	 * @return Map<String, String>
	 * <AUTHOR>
	 * @since 2024/4/16 10:39
	 **/
	default Map<String, String> getFlowKey(String code, IDictBizClient bizClient) {
		List<DictBiz> bizList = bizClient.getListByLang(code, CommonConstant.CURRENT_LANGUAGE_EN).getData();
		return bizList.stream().collect(HashMap::new, (stringStringHashMap, dictBiz) -> stringStringHashMap.put(dictBiz.getDictValue(), dictBiz.getDictKey()), HashMap::putAll);
	}

	/**
	 * 匹配角色
	 *
	 * @param roleIds              角色ids
	 * @param currentNodeRoleCodes 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2024/4/16 10:47
	 **/
	default Boolean check(List<String> roleIds, String currentNodeRoleCodes) {
		for (String roleId : roleIds) {
			if (currentNodeRoleCodes.contains(roleId)) {
				return true;
			}
		}
		return false;
	}

}

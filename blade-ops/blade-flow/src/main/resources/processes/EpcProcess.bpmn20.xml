<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:flowable="http://flowable.org/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:activiti="http://activiti.org/bpmn" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" targetNamespace="http://bpmn.io/schema/bpmn">
    <process id="epc_x8Mx3NyGBw3r85SRKTNeys8QMA7sTwGb" name="EPC_HXzBKbbRJSw6Z4cPRYmWKfHzptGBTnWs" isExecutable="true">
        <documentation>EPC订单审核</documentation>
        <startEvent id="startEvent_1" name="Create WO">
            <documentation>创建订单</documentation>
            <outgoing>Flow_07mv3yb</outgoing>
        </startEvent>
        <sequenceFlow id="Flow_07mv3yb" name="submit" sourceRef="startEvent_1" targetRef="Activity_1ropoed">
            <documentation>提交订单</documentation>
        </sequenceFlow>
        <userTask id="Activity_1ropoed" name="LL Info Confirmation">
            <documentation>房源信息确认</documentation>
            <incoming>Flow_07mv3yb</incoming>
            <incoming>Flow_14hmt5l</incoming>
            <incoming>Flow_1436u89</incoming>
            <outgoing>Flow_11np8mc</outgoing>
            <outgoing>Flow_08zeazx</outgoing>
        </userTask>
        <userTask id="Activity_0vc0uk0" name="Survey WO Assignment">
            <documentation>指派踏勘任务</documentation>
            <incoming>Flow_11np8mc</incoming>
            <outgoing>Flow_0tgsulx</outgoing>
            <outgoing>Flow_14hmt5l</outgoing>
        </userTask>
        <sequenceFlow id="Flow_11np8mc" name="pass" sourceRef="Activity_1ropoed" targetRef="Activity_0vc0uk0">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1s0gi5o" name="Survey &#38; LL Verification" flowable:candidateUsers="${siteEngineer},${electrician}">
            <documentation>踏勘及业主确认</documentation>
            <incoming>Flow_0tgsulx</incoming>
            <outgoing>Flow_0hjtxbj</outgoing>
            <outgoing>Flow_1436u89</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0tgsulx" name="pass" sourceRef="Activity_0vc0uk0" targetRef="Activity_1s0gi5o">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_09hqtz4" name="Site Design">
            <documentation>屋顶设计</documentation>
            <incoming>Flow_0hjtxbj</incoming>
            <incoming>Flow_1dekbsn</incoming>
            <incoming>Flow_0la3jey</incoming>
            <incoming>Flow_1vukrm4</incoming>
            <outgoing>Flow_1cj7zsy</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0hjtxbj" name="pass" sourceRef="Activity_1s0gi5o" targetRef="Activity_09hqtz4">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_0adec5l" name="Design Verification" flowable:candidateGroups="TechnicalLeader">
            <documentation>设计审核</documentation>
            <incoming>Flow_1cj7zsy</incoming>
            <outgoing>Flow_1ha62tn</outgoing>
            <outgoing>Flow_0la3jey</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1cj7zsy" name="pass" sourceRef="Activity_09hqtz4" targetRef="Activity_0adec5l">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_157ampr" name="Stock Confirmation" flowable:candidateGroups="WarehouseManager">
            <documentation>库存审核</documentation>
            <incoming>Flow_1ha62tn</incoming>
            <outgoing>Flow_1rpyash</outgoing>
            <outgoing>Flow_1vukrm4</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1ha62tn" name="pass" sourceRef="Activity_0adec5l" targetRef="Activity_157ampr">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1mcfhhl" name="Quotes" flowable:candidateGroups="Finance,SD">
            <documentation>费用清单上传及通知</documentation>
            <incoming>Flow_1rpyash</incoming>
            <outgoing>Flow_1dupe67</outgoing>
            <outgoing>Flow_1dekbsn</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1rpyash" name="pass" sourceRef="Activity_157ampr" targetRef="Activity_1mcfhhl">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_10oq67u" name="Payment Confirmation" flowable:candidateGroups="Finance,SD">
            <documentation>缴费确认</documentation>
            <incoming>Flow_1dupe67</incoming>
            <outgoing>Flow_1ypx1ya</outgoing>
            <outgoing>Flow_1b92i29</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1dupe67" name="pass" sourceRef="Activity_1mcfhhl" targetRef="Activity_10oq67u">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_14exwn7" name="Installation Date" flowable:candidateGroups="SD">
            <documentation>确认施工日期</documentation>
            <incoming>Flow_1ypx1ya</incoming>
            <outgoing>Flow_10q14fe</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1ypx1ya" name="pass" sourceRef="Activity_10oq67u" targetRef="Activity_14exwn7">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1j8jzk5" name="Installation WO Assignment" flowable:candidateGroups="RolloutManagerDistributor,RolloutManager">
            <documentation>指派施工任务</documentation>
            <incoming>Flow_10q14fe</incoming>
            <outgoing>Flow_06xe8hu</outgoing>
        </userTask>
        <sequenceFlow id="Flow_10q14fe" name="pass" sourceRef="Activity_14exwn7" targetRef="Activity_1j8jzk5">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_0i8g5t0" name="Material Collection &#38; Sign" flowable:assignee="${siteEngineer}">
            <documentation>物料签收</documentation>
            <incoming>Flow_06xe8hu</incoming>
            <outgoing>Flow_1fnby3r</outgoing>
        </userTask>
        <sequenceFlow id="Flow_06xe8hu" name="pass" sourceRef="Activity_1j8jzk5" targetRef="Activity_0i8g5t0">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_12gzjtj" name="EHS Submission" flowable:assignee="${siteEngineer}">
            <documentation>施工安全信息提交</documentation>
            <incoming>Flow_1fnby3r</incoming>
            <incoming>Flow_0qxnnsr</incoming>
            <incoming>Flow_0phzwag</incoming>
            <outgoing>Flow_03ous9o</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1fnby3r" name="pass" sourceRef="Activity_0i8g5t0" targetRef="Activity_12gzjtj">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_0je9e22" name="EHS Verification" flowable:candidateGroups="EHS">
            <documentation>施工安全信息审核</documentation>
            <incoming>Flow_03ous9o</incoming>
            <outgoing>Flow_0ruuqz5</outgoing>
            <outgoing>Flow_0qxnnsr</outgoing>
        </userTask>
        <sequenceFlow id="Flow_03ous9o" name="pass" sourceRef="Activity_12gzjtj" targetRef="Activity_0je9e22">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_0t3jk4g" name="QC Submission" flowable:assignee="${siteEngineer}">
            <documentation>质检信息提交</documentation>
            <incoming>Flow_0ruuqz5</incoming>
            <incoming>Flow_1o0ajnx</incoming>
            <outgoing>Flow_122iylg</outgoing>
            <outgoing>Flow_0phzwag</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0ruuqz5" name="pass" sourceRef="Activity_0je9e22" targetRef="Activity_0t3jk4g">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_0sq64ws" name="QC Verification" flowable:candidateGroups="QC">
            <documentation>质检信息审核</documentation>
            <incoming>Flow_122iylg</incoming>
            <outgoing>Flow_0bz9lzq</outgoing>
            <outgoing>Flow_1o0ajnx</outgoing>
        </userTask>
        <sequenceFlow id="Flow_122iylg" name="pass" sourceRef="Activity_0t3jk4g" targetRef="Activity_0sq64ws">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_03eowuo" name="COC Creation" flowable:assignee="${electrician}">
            <documentation>临时COC上传</documentation>
            <incoming>Flow_0bz9lzq</incoming>
            <outgoing>Flow_0b50pul</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0bz9lzq" name="pass" sourceRef="Activity_0sq64ws" targetRef="Activity_03eowuo">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1ryqk5f" name="FAC" flowable:assignee="${siteEngineer}">
            <documentation>业主验收</documentation>
            <incoming>Flow_0b50pul</incoming>
            <outgoing>Flow_14f376o</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0b50pul" name="pass" sourceRef="Activity_03eowuo" targetRef="Activity_1ryqk5f">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_03npj3i" name="Balance Payment" flowable:candidateGroups="Finance">
            <documentation>尾款缴费确认</documentation>
            <incoming>Flow_14f376o</incoming>
            <outgoing>Flow_1x9bnf8</outgoing>
        </userTask>
        <sequenceFlow id="Flow_14f376o" name="pass" sourceRef="Activity_1ryqk5f" targetRef="Activity_03npj3i">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1a3eom6" name="Released COC" flowable:assignee="${electrician}" flowable:candidateGroups="SD">
            <documentation>正式COC上传</documentation>
            <incoming>Flow_1x9bnf8</incoming>
            <outgoing>Flow_0ly36li</outgoing>
        </userTask>
        <sequenceFlow id="Flow_1x9bnf8" name="pass" sourceRef="Activity_03npj3i" targetRef="Activity_1a3eom6">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <endEvent id="Event_0s9g19q" name="End">
            <documentation>结束</documentation>
            <incoming>Flow_1b92i29</incoming>
            <incoming>Flow_08zeazx</incoming>
            <incoming>Flow_0r3mw03</incoming>
        </endEvent>
        <sequenceFlow id="Flow_1b92i29" name="cancel" sourceRef="Activity_10oq67u" targetRef="Event_0s9g19q">
            <documentation>取消</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='cancel'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_08zeazx" name="cancel" sourceRef="Activity_1ropoed" targetRef="Event_0s9g19q">
            <documentation>取消</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='cancel'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_0ly36li" name="pass" sourceRef="Activity_1a3eom6" targetRef="Activity_1sh4x8n">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_1o0ajnx" name="reject" sourceRef="Activity_0sq64ws" targetRef="Activity_0t3jk4g">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_0qxnnsr" name="reject" sourceRef="Activity_0je9e22" targetRef="Activity_12gzjtj">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_1dekbsn" name="reject" sourceRef="Activity_1mcfhhl" targetRef="Activity_09hqtz4">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_0la3jey" name="reject" sourceRef="Activity_0adec5l" targetRef="Activity_09hqtz4">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_1vukrm4" name="reject" sourceRef="Activity_157ampr" targetRef="Activity_09hqtz4">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_0phzwag" name="reject" sourceRef="Activity_0t3jk4g" targetRef="Activity_12gzjtj">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_14hmt5l" name="reject" sourceRef="Activity_0vc0uk0" targetRef="Activity_1ropoed">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_1436u89" name="reject" sourceRef="Activity_1s0gi5o" targetRef="Activity_1ropoed">
            <documentation>驳回</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='reject'}</conditionExpression>
        </sequenceFlow>
        <userTask id="Activity_1sh4x8n" name="Maintenance" flowable:candidateGroups="SD">
            <documentation>转维保</documentation>
            <incoming>Flow_0ly36li</incoming>
            <outgoing>Flow_0r3mw03</outgoing>
        </userTask>
        <sequenceFlow id="Flow_0r3mw03" name="pass" sourceRef="Activity_1sh4x8n" targetRef="Event_0s9g19q">
            <documentation>通过</documentation>
            <conditionExpression xsi:type="tFormalExpression">${outcome=='pass'}</conditionExpression>
        </sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_flow">
        <bpmndi:BPMNPlane id="BPMNPlane_flow" bpmnElement="epc_x8Mx3NyGBw3r85SRKTNeys8QMA7sTwGb">
            <bpmndi:BPMNEdge id="Flow_0r3mw03_di" bpmnElement="Flow_0r3mw03">
                <di:waypoint x="980" y="670" />
                <di:waypoint x="980" y="624" />
                <di:waypoint x="950" y="624" />
                <di:waypoint x="950" y="578" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="953" y="606" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1436u89_di" bpmnElement="Flow_1436u89">
                <di:waypoint x="860" y="178" />
                <di:waypoint x="860" y="130" />
                <di:waypoint x="540" y="130" />
                <di:waypoint x="540" y="178" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="686" y="112" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_14hmt5l_di" bpmnElement="Flow_14hmt5l">
                <di:waypoint x="650" y="240" />
                <di:waypoint x="590" y="240" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="606" y="222" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0phzwag_di" bpmnElement="Flow_0phzwag">
                <di:waypoint x="570" y="880" />
                <di:waypoint x="570" y="830" />
                <di:waypoint x="960" y="830" />
                <di:waypoint x="960" y="880" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="751" y="812" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1vukrm4_di" bpmnElement="Flow_1vukrm4">
                <di:waypoint x="1340" y="178" />
                <di:waypoint x="1340" y="130" />
                <di:waypoint x="1020" y="130" />
                <di:waypoint x="1020" y="178" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1166" y="112" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0la3jey_di" bpmnElement="Flow_0la3jey">
                <di:waypoint x="1130" y="240" />
                <di:waypoint x="1070" y="240" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1086" y="222" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1dekbsn_di" bpmnElement="Flow_1dekbsn">
                <di:waypoint x="1290" y="400" />
                <di:waypoint x="1020" y="400" />
                <di:waypoint x="1020" y="258" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1141" y="382" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0qxnnsr_di" bpmnElement="Flow_0qxnnsr">
                <di:waypoint x="800" y="940" />
                <di:waypoint x="910" y="940" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="841" y="922" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1o0ajnx_di" bpmnElement="Flow_1o0ajnx">
                <di:waypoint x="400" y="940" />
                <di:waypoint x="520" y="940" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="446" y="922" width="28" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ly36li_di" bpmnElement="Flow_0ly36li">
                <di:waypoint x="850" y="710" />
                <di:waypoint x="930" y="710" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="863" y="692" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_08zeazx_di" bpmnElement="Flow_08zeazx">
                <di:waypoint x="540" y="258" />
                <di:waypoint x="540" y="560" />
                <di:waypoint x="932" y="560" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="540" y="406" width="32" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1b92i29_di" bpmnElement="Flow_1b92i29">
                <di:waypoint x="1290" y="560" />
                <di:waypoint x="968" y="560" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1116" y="542" width="32" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1x9bnf8_di" bpmnElement="Flow_1x9bnf8">
                <di:waypoint x="700" y="710" />
                <di:waypoint x="750" y="710" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="713" y="692" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_14f376o_di" bpmnElement="Flow_14f376o">
                <di:waypoint x="550" y="710" />
                <di:waypoint x="600" y="710" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="563" y="692" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0b50pul_di" bpmnElement="Flow_0b50pul">
                <di:waypoint x="400" y="710" />
                <di:waypoint x="450" y="710" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="413" y="692" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0bz9lzq_di" bpmnElement="Flow_0bz9lzq">
                <di:waypoint x="350" y="880" />
                <di:waypoint x="350" y="750" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="353" y="812" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_122iylg_di" bpmnElement="Flow_122iylg">
                <di:waypoint x="520" y="900" />
                <di:waypoint x="400" y="900" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="448" y="882" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ruuqz5_di" bpmnElement="Flow_0ruuqz5">
                <di:waypoint x="700" y="920" />
                <di:waypoint x="620" y="920" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="648" y="902" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_03ous9o_di" bpmnElement="Flow_03ous9o">
                <di:waypoint x="910" y="900" />
                <di:waypoint x="800" y="900" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="843" y="882" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fnby3r_di" bpmnElement="Flow_1fnby3r">
                <di:waypoint x="1090" y="920" />
                <di:waypoint x="1010" y="920" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1038" y="902" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_06xe8hu_di" bpmnElement="Flow_06xe8hu">
                <di:waypoint x="1290" y="920" />
                <di:waypoint x="1190" y="920" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1228" y="902" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_10q14fe_di" bpmnElement="Flow_10q14fe">
                <di:waypoint x="1340" y="770" />
                <di:waypoint x="1340" y="880" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1343" y="822" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ypx1ya_di" bpmnElement="Flow_1ypx1ya">
                <di:waypoint x="1340" y="600" />
                <di:waypoint x="1340" y="690" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1344" y="642" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1dupe67_di" bpmnElement="Flow_1dupe67">
                <di:waypoint x="1340" y="440" />
                <di:waypoint x="1340" y="520" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1344" y="477" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1rpyash_di" bpmnElement="Flow_1rpyash">
                <di:waypoint x="1340" y="258" />
                <di:waypoint x="1340" y="360" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1345" y="306" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ha62tn_di" bpmnElement="Flow_1ha62tn">
                <di:waypoint x="1230" y="218" />
                <di:waypoint x="1290" y="218" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1249" y="200" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1cj7zsy_di" bpmnElement="Flow_1cj7zsy">
                <di:waypoint x="1070" y="200" />
                <di:waypoint x="1130" y="200" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1089" y="182" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0hjtxbj_di" bpmnElement="Flow_0hjtxbj">
                <di:waypoint x="910" y="218" />
                <di:waypoint x="970" y="218" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="929" y="200" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0tgsulx_di" bpmnElement="Flow_0tgsulx">
                <di:waypoint x="750" y="218" />
                <di:waypoint x="810" y="218" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="769" y="200" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_11np8mc_di" bpmnElement="Flow_11np8mc">
                <di:waypoint x="590" y="200" />
                <di:waypoint x="650" y="200" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="609" y="182" width="24" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07mv3yb_di" bpmnElement="Flow_07mv3yb">
                <di:waypoint x="368" y="218" />
                <di:waypoint x="490" y="218" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="413" y="200" width="34" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="BPMNShape_startEvent_1" bpmnElement="startEvent_1">
                <dc:Bounds x="332" y="200" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="323" y="243" width="56" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1ropoed_di" bpmnElement="Activity_1ropoed">
                <dc:Bounds x="490" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0vc0uk0_di" bpmnElement="Activity_0vc0uk0">
                <dc:Bounds x="650" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1s0gi5o_di" bpmnElement="Activity_1s0gi5o">
                <dc:Bounds x="810" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_09hqtz4_di" bpmnElement="Activity_09hqtz4">
                <dc:Bounds x="970" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0adec5l_di" bpmnElement="Activity_0adec5l">
                <dc:Bounds x="1130" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_157ampr_di" bpmnElement="Activity_157ampr">
                <dc:Bounds x="1290" y="178" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1mcfhhl_di" bpmnElement="Activity_1mcfhhl">
                <dc:Bounds x="1290" y="360" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_10oq67u_di" bpmnElement="Activity_10oq67u">
                <dc:Bounds x="1290" y="520" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_14exwn7_di" bpmnElement="Activity_14exwn7">
                <dc:Bounds x="1290" y="690" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1j8jzk5_di" bpmnElement="Activity_1j8jzk5">
                <dc:Bounds x="1290" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0i8g5t0_di" bpmnElement="Activity_0i8g5t0">
                <dc:Bounds x="1090" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_12gzjtj_di" bpmnElement="Activity_12gzjtj">
                <dc:Bounds x="910" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0je9e22_di" bpmnElement="Activity_0je9e22">
                <dc:Bounds x="700" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0t3jk4g_di" bpmnElement="Activity_0t3jk4g">
                <dc:Bounds x="520" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0sq64ws_di" bpmnElement="Activity_0sq64ws">
                <dc:Bounds x="300" y="880" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_03eowuo_di" bpmnElement="Activity_03eowuo">
                <dc:Bounds x="300" y="670" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1ryqk5f_di" bpmnElement="Activity_1ryqk5f">
                <dc:Bounds x="450" y="670" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_03npj3i_di" bpmnElement="Activity_03npj3i">
                <dc:Bounds x="600" y="670" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1a3eom6_di" bpmnElement="Activity_1a3eom6">
                <dc:Bounds x="750" y="670" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_0s9g19q_di" bpmnElement="Event_0s9g19q">
                <dc:Bounds x="932" y="542" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="941" y="512" width="21" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1sh4x8n_di" bpmnElement="Activity_1sh4x8n">
                <dc:Bounds x="930" y="670" width="100" height="80" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>

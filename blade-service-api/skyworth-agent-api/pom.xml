<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>blade-service-api</artifactId>
        <groupId>org.springblade</groupId>
        <version>3.1.1.RELEASE</version>
    </parent>
    <groupId>org.skyworth.ess</groupId>
    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-common</artifactId>
        </dependency>
    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>skyworth-agent-api</artifactId>
    <name>${project.artifactId}</name>
    <version>${bladex.project.version}</version>
    <packaging>jar</packaging>


</project>

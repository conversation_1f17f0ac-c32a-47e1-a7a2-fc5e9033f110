/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 代理商用户信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@TableName("agent_user_info")
@ApiModel(value = "AgentUserInfo对象", description = "代理商用户信息")
@EqualsAndHashCode(callSuper = true)
public class AgentUserInfoEntity extends SkyWorthEntity {

	/**
	 * 代理商id
	 */
	@ApiModelProperty(value = "代理商id")
	private Long agentId;
	/**
	 * 用户类型:施工人员:0/电工:1
	 */
	@ApiModelProperty(value = "用户类型:施工人员:constructor/电工:electrician")
	private String userType;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long userId;
	/**
	 * 用户图片业务主键
	 */
	@ApiModelProperty(value = "用户图片业务主键")
	private Long userImgBizKey;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 用户名字
	 */
	@ApiModelProperty(value = "用户名字")
	@TableField(exist = false)
	private String userName;

	/**
	 * 电话号码
	 */
	@ApiModelProperty(value = "电话号码")
	@TableField(exist = false)
	private String phone;

	/**
	 * 用户邮箱
	 */
	@ApiModelProperty(value = "用户邮箱")
	@TableField(exist = false)
	private String email;

	/**
	 * 用户邮箱
	 */
	@ApiModelProperty(value = "部门id")
	@TableField(exist = false)
	private Long deptId;

}

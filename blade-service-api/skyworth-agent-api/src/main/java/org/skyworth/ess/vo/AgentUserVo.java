package org.skyworth.ess.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运维人员信息
 * */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentUserVo{

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 真实名称
	 */
	@ApiModelProperty(value = "真实名称")
	private String realName;

	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String phone;

	/**
	 * 区号
	 */
	@ApiModelProperty(value = "区号")
	private String phoneDiallingCode;


	/**
	 * 账号
	 */
	@ApiModelProperty(value = "账号")
	private String account;


	/**
	 * 昵称
	 */
	@ApiModelProperty(value = "昵称")
	private String name;
}

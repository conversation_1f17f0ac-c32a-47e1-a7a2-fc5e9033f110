package org.skyworth.ess.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单表管理列表 视图实体类
 * org.skyworth.ess.order.vo
 *
 * <AUTHOR>
 * @since 2023/10/27
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class AgentCompanyVO {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	/**
	 * 代理商编号
	 */
	@ApiModelProperty(value = "代理商编号")
	private String agentNumber;


	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "部门Id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long deptId;


}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 订单流程关系表 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("business_order_work_flow")
@ApiModel(value = "OrderWorkFlow对象", description = "订单流程关系表")
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderWorkFlowEntity extends TenantEntity {

	/**
	 * 订单表id
	 */
	@ApiModelProperty(value = "订单表id")
	private Long orderId;
	/**
	 * 工作流实例id
	 */
	@ApiModelProperty(value = "工作流实例id")
	private String wfInstanceId;
	/**
	 * 任务id
	 */
	@ApiModelProperty(value = "任务id")
	private String taskId;
	/**
	 * 上一步审批状态（1:通过；2：不通过；3：待审批）
	 */
	@ApiModelProperty(value = "上一步审批状态（1:通过；2：不通过；3：待审批）")
	private Integer auditStatus;
	/**
	 * 工作流当前状态/订单进度
	 */
	@ApiModelProperty(value = "工作流当前状态/订单进度")
	private String wfCurrentStatus;
	/**
	 * 当前处理节点类型
	 */
	@ApiModelProperty(value = "当前处理节点类型")
	private String wfCurrentType;
	/**
	 * 当前处理节点角色或人
	 */
	@ApiModelProperty(value = "当前处理节点角色或人")
	private String wfCurrentRole;
	/**
	 * 当前处理节点角色或人名称
	 */
	@ApiModelProperty(value = "当前处理节点角色或人名称")
	private String wfCurrentRoleName;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	/*@ApiModelProperty(value = "ehs最新版本时间")
	private String ehsVersion;*/

//	@ApiModelProperty(value = "库存管理员审批通过时间")
//	private Date warehouseApproveCreateTime;
	// 订单类型
	@TableField(exist = false)
	private String orderType;
}

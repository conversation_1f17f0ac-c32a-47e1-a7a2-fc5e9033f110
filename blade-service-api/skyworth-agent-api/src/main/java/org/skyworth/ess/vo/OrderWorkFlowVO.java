/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.entity.OrderWorkFlowEntity;

import java.math.BigDecimal;


/**
 * 订单流程关系表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderWorkFlowVO extends OrderWorkFlowEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 角色类型
	 */
	private String roleType;
	/**
	 * 登录人部门id
	 */
	private String userDeptId;
	/**
	 * 安装商人员id
	 */
	private Long installUserId;
	/**
	 * 工作流当前状态
	 */
	private String wfCurrentStatus;
	// 物料包类型
	private String itemBasePackage;
	// 物料包价格
	private BigDecimal itemBasePackagePrice;
	// 最终价格，优化价
	private BigDecimal itemFinalTotalPrice;
}

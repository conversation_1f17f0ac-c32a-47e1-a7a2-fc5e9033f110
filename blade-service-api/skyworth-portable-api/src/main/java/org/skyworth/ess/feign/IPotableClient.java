/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.skyworth.ess.feign;

import com.alibaba.fastjson.JSONObject;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 设备软件版本信息表 Feign接口类
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@FeignClient(
	value = CommonConstant.APPLICATION_PORTABLE_NAME,
	fallback = PortableClientCallBack.class
)
public interface IPotableClient {

	String API_PREFIX = "/portable";
	String PORTABLE_DEVICE_POSITION = API_PREFIX + "/deviceLocation";

	/**
	 * 修改人员行政区域的同时，刷新用户设备的所属行政区域
	 *
	 * @param jsonObject 入参
	 * @return R
	 * <AUTHOR>
	 * @since 2023/9/22 16:36
	 **/
	@PostMapping(PORTABLE_DEVICE_POSITION)
	R<Boolean> maintenanceDeviceLocation(@RequestBody JSONObject jsonObject);

}

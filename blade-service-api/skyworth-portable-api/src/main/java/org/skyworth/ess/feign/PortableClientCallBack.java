/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.feign;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-26 14:06
 **/
@Component
public class PortableClientCallBack implements IPotableClient {


	@Override
	public R<Boolean> maintenanceDeviceLocation(JSONObject jsonObject) {
		return R.fail("修改设备信息表人员所属行政区划失败");
	}
}

package org.springblade.system.entity;

import lombok.Getter;

/**
 * 流水号枚举
 *
 * @description: 流水号枚举
 * @author: SDT50545
 * @since: 2023-11-13 16:44
 **/
@Getter
public enum RedisSeqEnums {
	/**
	 * 订单号流水号
	 */
	ORDER_NO_SEQ("orderNoSeq", "", 6, 1),
	/**
	 * 代理商编号
	 */
	AGENT_NO_SEQ("agentNumberSeq", "D", 8, 1),
	/**
	 * 维保订单编号
	 */
	ORDER_MAINTENANCE_NO_SEQ("orderMaintenanceNoSeq", "", 6, 1),
	;
	/**
	 * 业务类型
	 */
	private final String bizTag;

	/**
	 * 最开头 字母前缀
	 */
	private final String prefixAbc;

	/**
	 * 长度
	 */
	private final Integer length;

	/**
	 * 是否每日刷新，1 刷新  0 不刷新
	 */
	private final Integer refresh;

	RedisSeqEnums(String bizTag, String prefixAbc, Integer length, Integer refresh) {
		this.bizTag = bizTag;
		this.prefixAbc = prefixAbc;
		this.length = length;
		this.refresh = refresh;
	}
}

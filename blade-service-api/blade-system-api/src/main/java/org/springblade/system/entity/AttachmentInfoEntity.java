/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 附件表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@TableName("attachment_info")
@ApiModel(value = "AttachmentInfo对象", description = "附件表")
@EqualsAndHashCode(callSuper = true)
public class AttachmentInfoEntity extends TenantEntity {

	/**
	 * 附件名称
	 */
	@ApiModelProperty(value = "附件名称")
	private String fileName;
	/**
	 * 附件地址
	 */
	@ApiModelProperty(value = "附件地址")
	private String fileUrl;
	/**
	 * 附件域名
	 */
	@ApiModelProperty(value = "附件在blob的路径")
	private String azureKey;

	@ApiModelProperty(value = "缩略图在blob的路径")
	private String thumbnailAzureKey;
	/**
	 * 缩略图地址，针对图片
	 */
	@ApiModelProperty(value = "缩略图地址，针对图片")
	private String thumbnailUrl;
	/**
	 * 附件cdn地址
	 */
	@ApiModelProperty(value = "附件cdn地址")
	private String cdnUrl;
	/**
	 * 业务主键
	 */
	@ApiModelProperty(value = "业务主键")
	private Long businessId;
	/**
	 * 附件大小
	 */
	@ApiModelProperty(value = "附件大小")
	private Long attachSize;
	/**
	 * 容器container
	 */
	@ApiModelProperty(value = "容器container")
	private String container;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 修改人账号
	 */
	@ApiModelProperty(value = "修改人账号")
	private String updateUserAccount;
	/**
	 * 顺序
	 */
	@ApiModelProperty(value = "顺序")
	private Integer sort;
	/**
	 * 原图sas签名
	 */
	@ApiModelProperty(value = "原图sas签名")
	@TableField(exist = false)
	private String sasToken;
	/**
	 * 缩略图sas签名
	 */
	@ApiModelProperty(value = "缩略图sas签名")
	@TableField(exist = false)
	private String thumbnailSasToken;

}

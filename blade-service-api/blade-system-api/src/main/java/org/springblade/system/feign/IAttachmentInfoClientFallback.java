/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.springblade.system.feign;

import org.springblade.common.vo.BatchVO;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-10-20 16:22
 **/
@Component
public class IAttachmentInfoClientFallback implements IAttachmentInfoClient {
	@Override
	public R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIds(List<Long> businessIds) {
		return R.fail("findByBusinessIds-获取数据失败");
	}

	@Override
	public R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIdsNoTent(List<Long> businessIds) {
		return R.fail("findByBusinessIdsNoTent-获取数据失败");
	}

	@Override
	public R saveAndUpdate(BatchVO<AttachmentInfoEntity> batchVO) {
		throw new RuntimeException("save attachment fail");
	}
}

package org.springblade.flow.core.dto;

import lombok.Data;
import org.springblade.common.constant.CommonConstant;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 编辑权限 的相关参数对象
 *
 * <AUTHOR>
 */
@Data
public class PurviewDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单表ID
	 */
	private String orderId;
	/**
	 * 任务编号名称
	 */
	@NotBlank(message = "taskName  cannot be empty.")
	private String taskName;
	/**
	 * 流程id,增加默认值，防止旧版本调用报错
	 */
	private String flowId = CommonConstant.PROCESS_DEFINITION_KEY;
}

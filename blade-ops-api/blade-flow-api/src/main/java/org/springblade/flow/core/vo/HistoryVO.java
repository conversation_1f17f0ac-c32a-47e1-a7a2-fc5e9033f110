package org.springblade.flow.core.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HistoryVO  extends ApprovalVO implements Serializable{
	private static final long serialVersionUID = 1L;
	/**
    * 是否通过标识
    *
    */
	private Boolean pass;
	/**
	 * 任务意见
	 */
	private String comment;

	/**
	 * 流程图节点颜色
	 */
	private String color;
}

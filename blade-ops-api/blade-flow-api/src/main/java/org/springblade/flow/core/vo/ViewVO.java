package org.springblade.flow.core.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * <AUTHOR>
 * 流程图展示类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ViewVO extends ApprovalVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 流程图节点颜色
	 */
	private String color;
	/**
	 * 是否通过标识
	 *
	 */
	private Boolean pass;

	/**
	 * 流程图节点key
	 */
	private String taskNameKey;
}

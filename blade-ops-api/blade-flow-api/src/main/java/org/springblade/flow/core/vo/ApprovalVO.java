package org.springblade.flow.core.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
public class ApprovalVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 任务编号
	 */
	private String taskId;
	/**
	 * 任务名称
	 */
	private String taskName;
	/**
	 * 任务执行人编号
	 */
	private String assignee;
	/**
	 * 任务执行人名称
	 */
	private String assigneeName;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 审批人类型 user;role;user/role
	 */
	private String typeOfApprove;
}

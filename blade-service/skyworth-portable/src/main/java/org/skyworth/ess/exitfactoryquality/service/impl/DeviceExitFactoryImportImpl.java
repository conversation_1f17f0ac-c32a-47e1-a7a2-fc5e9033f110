package org.skyworth.ess.exitfactoryquality.service.impl;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.skyworth.ess.constant.DictBizCodeEnum;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactoryImportExcel;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DeviceExitFactoryImportImpl extends ExcelImportServiceAbstract<ExitFactoryImportExcel> {
    private final IDictBizClient dictBizClient;
    @Override
    public String validateDataEffective(List<ExitFactoryImportExcel> dataList) {
        R<Map<String, List<DictBiz>>> mapR = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
                DictBizCodeEnum.PORTABLE_COMPANY.getCode()));
        List<DictBiz> deviceTypeList = mapR.getData().get(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
        List<DictBiz> companyList = mapR.getData().get(DictBizCodeEnum.PORTABLE_COMPANY.getCode());
        if (CollectionUtil.isEmpty(companyList) || CollectionUtil.isEmpty(deviceTypeList)) {
            throw new BusinessException("portable.device.exit.factory.import.dict.is.not.exist");
        }
        StringBuilder stringBuilder = this.validateCompany(dataList, companyList);
        stringBuilder.append(this.validateNetType(dataList, deviceTypeList));
        stringBuilder.append(this.validateColumnFormat(dataList));
        if(StringUtil.isNotBlank(stringBuilder.toString())) {
            throw new BusinessException("portable.device.exit.factory.import.dict.name.is.error",stringBuilder.toString());
        }
        return stringBuilder.toString();
    }

    private StringBuilder validateColumnFormat(List<ExitFactoryImportExcel> dataList) {
        StringBuilder sb = new StringBuilder();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        for(int i = 0; i < dataList.size(); i++) {
            ExitFactoryImportExcel exitFactoryImportExcel = dataList.get(i);
            String qualityGuaranteeYear = exitFactoryImportExcel.getQualityGuaranteeYear();
            String exitFactoryDate = exitFactoryImportExcel.getExitFactoryDate();
            try {
                int year = Integer.parseInt(qualityGuaranteeYear);
            }catch (Exception e) {
                sb.append(qualityGuaranteeYear).append(",");
            }
            try {
                Date date = formatter.parse(exitFactoryImportExcel.getExitFactoryDate());
            }catch (Exception e) {
                sb.append(exitFactoryDate).append(",");
            }
        }
        return sb;
    }

    private  StringBuilder validateNetType(List<ExitFactoryImportExcel> dataList, List<DictBiz> dictData) {
        StringBuilder sb = new StringBuilder();
        for(ExitFactoryImportExcel vo : dataList) {
            String netType = vo.getDeviceType();
            boolean flag = false;
            for(DictBiz biz : dictData) {
                if(biz.getDictValue().equalsIgnoreCase(netType)) {
                    vo.setDeviceType(biz.getDictKey());
                    flag = true;
                }
            }
            if(!flag) {
                sb.append(netType).append(",");
            }
        }
        return sb;
    }

    private  StringBuilder validateCompany(List<ExitFactoryImportExcel> dataList, List<DictBiz> dictData) {
        StringBuilder sb = new StringBuilder();
        for(ExitFactoryImportExcel vo : dataList) {
            String company = vo.getCompany();
            boolean flag = false;
            for(DictBiz biz : dictData) {
                if(biz.getDictValue().equalsIgnoreCase(company)) {
                    vo.setCompany(biz.getDictKey());
                    flag = true;
                }
            }
            if(!flag) {
                sb.append(company).append(",");
            }
        }
        return sb;
    }
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.google.common.base.Joiner;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactoryBaseColumnEnum;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactoryImportExcel;
import org.skyworth.ess.exitfactoryquality.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.util.SkyworthFileUtil;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.valid.ValidGroups;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

/**
 * 设备出厂信息 控制器
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/deviceExitFactoryInfo")
@Api(value = "设备出厂信息", tags = "设备出厂信息接口")
@Slf4j
public class DeviceExitFactoryInfoController extends BladeController {

	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final ExcelImportServiceInterface<ExitFactoryImportExcel> deviceExitFactoryImportImpl;
	/**
	 * 设备出厂信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceExitFactoryInfo")
	public R<DeviceExitFactoryInfoVO> detail(@RequestParam("id") Long id) {
		return R.data(deviceExitFactoryInfoService.queryDeviceById(id));
	}

	/**
	 * 设备出厂信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceExitFactoryInfo")
	public R<IPage<DeviceExitFactoryInfoVO>> page(DeviceExitFactoryInfoVO deviceExitFactoryInfo, Query query) {
		query.setDescs("create_time");
		IPage<DeviceExitFactoryInfoVO> pages = deviceExitFactoryInfoService.selectDeviceExitFactoryInfoPage(Condition.getPage(query), deviceExitFactoryInfo);
		return R.data(pages);
	}

	public static void main(String[] args) throws JsonProcessingException {

	}

	/**
	 * 设备出厂信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceExitFactoryInfo")
	public R<Boolean> save(@Validated(value= ValidGroups.AddGroup.class) @RequestBody DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		return R.data(deviceExitFactoryInfoService.saveDevice(deviceExitFactoryInfo));
	}

	/**
	 * 设备出厂信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceExitFactoryInfo")
	public R update(@Valid @RequestBody DeviceExitFactoryInfoEntity deviceExitFactoryInfo) {
		return R.status(deviceExitFactoryInfoService.updateDeviceById(deviceExitFactoryInfo));
	}

	/**
	 * 设备出厂信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntities = deviceExitFactoryInfoService.listByIds(Func.toLongList(ids));
		if(CollectionUtil.isNotEmpty(deviceExitFactoryInfoEntities)) {
			List<String> collect = deviceExitFactoryInfoEntities.stream().filter(p-> p.getStatus() == 1).map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			if(CollectionUtil.isNotEmpty(collect)) {
				String join = Joiner.on(",").join(collect);
				throw new BusinessException("portable.device.exit.factory.deviceSerialNumber.is.used", join);
			}
			return R.status(deviceExitFactoryInfoService.deleteLogic(Func.toLongList(ids)));
		}
		return R.status(true);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-deviceExitFactoryInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入deviceExitFactoryInfo")
	public void exportDeviceExitFactoryInfo(@ApiIgnore @RequestParam Map<String, Object> deviceExitFactoryInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceExitFactoryInfoEntity> queryWrapper = Condition.getQueryWrapper(deviceExitFactoryInfo, DeviceExitFactoryInfoEntity.class);
		queryWrapper.lambda().eq(DeviceExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		deviceExitFactoryInfoService.exportDeviceExitFactoryInfo(queryWrapper,response);
//		ExcelUtil.export(response, "设备出厂信息数据" + DateUtil.time(), "设备出厂信息数据表", list, DeviceExitFactoryInfoExcel.class);
	}

	private void outputZip2Response(HttpServletResponse response, String baseDir, Map<Long, String> imgKeyMapDeviceSn, String imageZipDir) {
		ZipOutputStream zos = null;
		try {
			log.info("exitFactoryExportExcel outputZip2Response ->");
			response.setContentType("application/zip");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			String fileName = URLEncoder.encode("质保管理", StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".zip");
			zos = new ZipOutputStream(response.getOutputStream());
			// 将excel写入response，目录为 ： 20240127165124_18665860502/deviceExitFactoryInfo.xlsx

			// 将不同SN的本地图片压缩包写入response 目录为：日期_账号\imageZip\sn.zip
			Collection<String> values = imgKeyMapDeviceSn.values();
			for (String sn : values) {
				String zipName = sn + ".zip";// 下载的压缩包中图片压缩包名称
				SkyworthFileUtil.createZipFile4Response(zipName, imageZipDir + zipName, zos);
			}
			log.info("exitFactoryExportExcel outputZip2Response -> end ");
		} catch (IOException e) {
			log.error("outputZip2Response error :  ", e);
		} finally {
			if (zos != null) {
				try {
					zos.closeEntry();
					zos.close();
				} catch (IOException e) {
					log.error("outputZip2Response close error : ", e);
				}
			}
		}
	}
	@GetMapping("/import-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导入模板")
	public void exportImportantEventTemplate(HttpServletResponse response) {
		deviceExitFactoryInfoService.exportImportantEventTemplate(response);
//		ExcelUtil.export(response, "template", "exitFactoryInfo", list, ExitFactoryImportExcel.class);
	}

	@PostMapping("/import-add-exitFactory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "新增导入信息", notes = "传入excel")
	public R importAddDviceExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<ExitFactoryImportExcel>, Boolean, String> fun = deviceExitFactoryInfoService::importAddExcel;
		String currentLanguage = CommonUtil.getCurrentLanguage();
		String result = deviceExitFactoryImportImpl.importMultipleHeaderExcel(file, ExitFactoryBaseColumnEnum.getMultipleHeader(currentLanguage),
				ExitFactoryImportExcel.class, fun);
		if(StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	@PostMapping("/import-modify-exitFactory")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "修改导入出厂信息", notes = "传入excel")
	public R importModifyDviceExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<ExitFactoryImportExcel>, Boolean, String> fun = deviceExitFactoryInfoService::importModifyExcel;
		String currentLanguage = CommonUtil.getCurrentLanguage();
		String result = deviceExitFactoryImportImpl.importMultipleHeaderExcel(file, ExitFactoryBaseColumnEnum.getMultipleHeader(currentLanguage),
				ExitFactoryImportExcel.class, fun);
		if(StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}
}

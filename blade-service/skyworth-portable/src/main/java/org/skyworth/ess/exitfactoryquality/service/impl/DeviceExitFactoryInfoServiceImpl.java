/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.skyworth.ess.constant.DictBizCodeEnum;

import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.excel.*;
import org.skyworth.ess.exitfactoryquality.mapper.DeviceExitFactoryInfoMapper;
import org.skyworth.ess.exitfactoryquality.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.exitfactoryquality.wrapper.DeviceExitFactoryInfoWrapper;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;
import org.skyworth.ess.util.SkyworthFileUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.FunctionSetName;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

/**
 * 设备出厂信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeviceExitFactoryInfoServiceImpl extends BaseServiceImpl<DeviceExitFactoryInfoMapper, DeviceExitFactoryInfoEntity> implements IDeviceExitFactoryInfoService {
	private final IDictBizClient dictBizClient;
	private final IUserSearchClient userSearchClient;
	private final IAttachmentInfoClient attachmentInfoClient;
	private final ThreadPoolExecutor commonThreadPool;

	final String baseImageZipDir = "imageZip/";

	final String baseImageDir = "imageBase/";

	@Override
	public IPage<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage<DeviceExitFactoryInfoVO> page, DeviceExitFactoryInfoVO deviceExitFactoryInfo) {
		List<DeviceExitFactoryInfoVO> deviceExitFactoryInfoVOList = baseMapper.selectDeviceExitFactoryInfoPage(page, deviceExitFactoryInfo);
		R<Map<String, List<DictBiz>>> mapR = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
			DictBizCodeEnum.PORTABLE_COMPANY.getCode(), DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode()));
		List<Long> userIdList = deviceExitFactoryInfoVOList.stream().map(DeviceExitFactoryInfoVO::getBuyerUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		R<List<User>> userResult = userSearchClient.listByUserIds(userIdList);

		List<DictBiz> deviceTypeList = mapR.getData().get(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
		List<DictBiz> companyList = mapR.getData().get(DictBizCodeEnum.PORTABLE_COMPANY.getCode());
		List<DictBiz> buyChannelList = mapR.getData().get(DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		for (DeviceExitFactoryInfoVO vo : deviceExitFactoryInfoVOList) {
			this.setDictValue(deviceTypeList, currentLanguage, vo.getDeviceType(), vo::setDeviceTypeName);
			this.setDictValue(companyList, currentLanguage, vo.getCompany(), vo::setCompanyName);
			this.setDictValue(buyChannelList, currentLanguage, vo.getBuyChannel(), vo::setBuyChannelName);
			this.setUserInfo(userResult, vo.getBuyerUserId(), vo::setBuyerUserName, vo::setBuyerUserPhone);
			if (vo.getBuyDate() != null) {
				vo.setQualityGuaranteeEndDate(vo.getBuyDate().plusYears(vo.getQualityGuaranteeYear()));
			}
		}
		return page.setRecords(deviceExitFactoryInfoVOList);
	}

	private void setDictValue(List<DictBiz> dictBizList, String currentLanguage, String businessKey, FunctionSetName<String> function) {
		if (CollectionUtil.isNotEmpty(dictBizList)) {
			dictBizList.forEach(p -> {
				if (p.getLanguage().equals(currentLanguage) && p.getDictKey().equals(businessKey)) {
					function.setName(p.getDictValue());
				}
			});
		}
	}

	private void setUserInfo(R<List<User>> userResult, Long buyerUserId, FunctionSetName<String> functionUserName, FunctionSetName<String> functionUserPhone) {
		if (userResult.getCode() != CommonConstant.REST_FUL_RESULT_SUCCESS || CollectionUtil.isEmpty(userResult.getData())) {
			return;
		}
		List<User> userList = userResult.getData();
		for (User user : userList) {
			if (user.getId().equals(buyerUserId)) {
				functionUserName.setName(user.getRealName());
				functionUserPhone.setName(user.getPhone());
				break;
			}
		}
	}

	private Pair<List<DeviceExitFactoryInfoEntity>, Map<Long, List<AttachmentInfoEntity>>> queryExportData(List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoList) throws ExecutionException, InterruptedException, TimeoutException {
		List<Long> userIdList = deviceExitFactoryInfoList.stream().map(DeviceExitFactoryInfoEntity::getBuyerUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		List<Long> imgKeyList = deviceExitFactoryInfoList.stream().map(DeviceExitFactoryInfoEntity::getBuyCertificateImgBizKey).filter(Objects::nonNull).collect(Collectors.toList());

		Future<R<Map<String, List<DictBiz>>>> futureMapR = commonThreadPool.submit(() -> {
			log.info("exitFactoryExportExcel getDictBiz -> ");
			R<Map<String, List<DictBiz>>> mapR = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
				DictBizCodeEnum.PORTABLE_COMPANY.getCode(), DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode()));
			log.info("exitFactoryExportExcel getDictBiz -> end");
			return mapR;
		});
		Future<R<List<User>>> futureUser = commonThreadPool.submit(() -> {
			log.info("exitFactoryExportExcel listByUserIds -> ");
			R<List<User>> listR = new R<>();
			if (CollectionUtil.isNotEmpty(userIdList)) {
				listR = userSearchClient.listByUserIds(userIdList);
			}
			log.info("exitFactoryExportExcel listByUserIds -> end");
			return listR;
		});
		Future<R<Map<Long, List<AttachmentInfoEntity>>>> futureAttach = commonThreadPool.submit(() -> {
			log.info("exitFactoryExportExcel findAttachmentInfo -> ");
			R<Map<Long, List<AttachmentInfoEntity>>> byBusinessIds = attachmentInfoClient.findByBusinessIds(imgKeyList);
			log.info("exitFactoryExportExcel findAttachmentInfo -> end ");
			return byBusinessIds;
		});

		R<Map<String, List<DictBiz>>> mapR = futureMapR.get(3, TimeUnit.MINUTES);
		R<List<User>> userResult = futureUser.get(3, TimeUnit.MINUTES);
		R<Map<Long, List<AttachmentInfoEntity>>> attachR = futureAttach.get(3, TimeUnit.MINUTES);
//      R<Map<String, List<DictBiz>>> mapR = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
//		DictBizCodeEnum.PORTABLE_COMPANY.getCode(), DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode()));
//		R<List<User>> userResult = userSearchClient.listByUserIds(userIdList);
//		R<Map<Long, List<AttachmentInfoEntity>>> result = attachmentInfoClient.findByBusinessIds(imgKeyList);

		Map<Long, List<AttachmentInfoEntity>> data = attachR.getData();

		List<DictBiz> deviceTypeList = mapR.getData().get(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
		List<DictBiz> companyList = mapR.getData().get(DictBizCodeEnum.PORTABLE_COMPANY.getCode());
		List<DictBiz> buyChannelList = mapR.getData().get(DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode());
		String currentLanguage = CommonUtil.getCurrentLanguage();

		for (DeviceExitFactoryInfoEntity entity : deviceExitFactoryInfoList) {
			this.setDictValue(deviceTypeList, currentLanguage, entity.getDeviceType(), entity::setDeviceTypeName);
			this.setDictValue(companyList, currentLanguage, entity.getCompany(), entity::setCompanyName);
			this.setDictValue(buyChannelList, currentLanguage, entity.getBuyChannel(), entity::setBuyChannelName);
			this.setUserInfo(userResult, entity.getBuyerUserId(), entity::setBuyerUserName, entity::setBuyerUserPhone);
			if (entity.getBuyDate() != null) {
				entity.setQualityGuaranteeEndDate(entity.getBuyDate().plusYears(entity.getQualityGuaranteeYear()));
			}

		}

		return Pair.of(deviceExitFactoryInfoList, data);
	}

	final String EXPORT_EXCEL_NAME = "deviceExitFactoryInfo.xlsx";

	@Override
	public void exportDeviceExitFactoryInfo(Wrapper<DeviceExitFactoryInfoEntity> queryWrapper, HttpServletResponse response) {
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoList = baseMapper.exportDeviceExitFactoryInfo(queryWrapper);
		try {
			// 转换需要导出的数据
			Pair<List<DeviceExitFactoryInfoEntity>, Map<Long, List<AttachmentInfoEntity>>> listMapPair = this.queryExportData(deviceExitFactoryInfoList);
			List<DeviceExitFactoryInfoEntity> exportData = listMapPair.getLeft();
			Map<Long, List<AttachmentInfoEntity>> attachData = listMapPair.getRight();
			// 创建临时目录存储远程图片和压缩包、excel
			BladeUser user = AuthUtil.getUser();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
			String baseDir = formatter.format(LocalDateTime.now()) + "_" + user.getAccount() + "/";
			String imageZipDir = baseDir + baseImageZipDir;
			String imageDir = baseDir + baseImageDir;
			SkyworthFileUtil.createDir(baseDir, imageZipDir, imageDir);
			List<Future<?>> futureList = new ArrayList<>();
			// 生成本地excel
			Future<?> createExcelFuture = this.createLocalExportExcel(baseDir, exportData);
			futureList.add(createExcelFuture);

			Map<Long, String> imgKeyMapDeviceSn = deviceExitFactoryInfoList.stream().filter(p -> p.getBuyCertificateImgBizKey() != null)
				.collect(HashMap::new, (map, excel) -> map.put(excel.getBuyCertificateImgBizKey(), excel.getDeviceSerialNumber()), HashMap::putAll);
			// 压缩图片
			futureList.addAll(this.zipImage(attachData, imageDir, imgKeyMapDeviceSn, imageZipDir));
			for (Future<?> future : futureList) {
				future.get(3, TimeUnit.MINUTES);
			}
			this.outputZip2Response(response, baseDir, imgKeyMapDeviceSn, imageZipDir);
		} catch (Exception e) {
			log.error("exportDeviceExitFactoryInfo ： ", e);
		}
	}


	private List<Future<?>> zipImage(Map<Long, List<AttachmentInfoEntity>> attachData, String imageDir, Map<Long, String> imgKeyMapDeviceSn,
									 String imageZipDir) {
		List<Future<?>> futureList = new ArrayList<>();
		for (Map.Entry<Long, List<AttachmentInfoEntity>> entry : attachData.entrySet()) {
			Future<?> submit = commonThreadPool.submit(() -> {
				log.info("exitFactoryExportExcel zipImage -> ");
				String snImageDir = imageDir + imgKeyMapDeviceSn.get(entry.getKey());
				SkyworthFileUtil.mkdirOrDeleteFile(snImageDir);
				List<AttachmentInfoEntity> attachList = entry.getValue();
				// 将同一个 SN下的 图片 从图片服务器 下载到本地目录， 目录名为 : 日期_账号\imageBase\sn
				for (AttachmentInfoEntity attach : attachList) {
					String fileUrl = attach.getFileUrl();
					String sasToken = attach.getSasToken();
					String imageName = snImageDir + "/" + getFileNameFromUrl(attach.getAzureKey());
					log.info("imageName : {}", imageName);
					log.info("url : {}", fileUrl + "?" + sasToken);
					SkyworthFileUtil.createFileByUrl(fileUrl + "?" + sasToken, imageName);
				}
				String imageZipPathName = imageZipDir + imgKeyMapDeviceSn.get(entry.getKey());
				// 将上面下载的sn下的图片目录压缩成一个zip包， 目录名为 : 日期_账号\imageZip\sn.zip
				SkyworthFileUtil.createZipFile4Directory(imageZipPathName, imageDir + imgKeyMapDeviceSn.get(entry.getKey()));
				log.info("exitFactoryExportExcel zipImage -> end");
			});
			futureList.add(submit);
		}
		return futureList;
	}

	private Future<?> createLocalExportExcel(String baseDir, List<DeviceExitFactoryInfoEntity> exportData) {
		return commonThreadPool.submit(() -> {
			log.info("exitFactoryExportExcel createLocalExportExcel -> ");
			List<ExitFactoryBaseExcel> baseInfoList = new ArrayList<>();
			List<ExitFactoryActivationExcel> activationList = new ArrayList<>();
			List<ExitFactoryBuyExcel> buyList = new ArrayList<>();
			// 组装 3 个sheet页的对象数据
			for (DeviceExitFactoryInfoEntity entity : exportData) {
				ExitFactoryBaseExcel baseInfo = new ExitFactoryBaseExcel();
				BeanUtil.copy(entity, baseInfo);
				this.setDate(baseInfo::setExitFactoryDate, entity.getExitFactoryDate());
				baseInfoList.add(baseInfo);

				ExitFactoryActivationExcel activationInfo = new ExitFactoryActivationExcel();
				BeanUtil.copy(entity, activationInfo);
				this.setDate(activationInfo::setActivationDate, entity.getActivationDate());
				this.setDate(activationInfo::setQualityGuaranteeBeginDate, entity.getQualityGuaranteeBeginDate());
				this.setDate(activationInfo::setQualityGuaranteeEndDate, entity.getQualityGuaranteeEndDate());
				activationList.add(activationInfo);

				ExitFactoryBuyExcel buyInfo = new ExitFactoryBuyExcel();
				BeanUtil.copy(entity, buyInfo);
				this.setDate(buyInfo::setBuyDate, entity.getBuyDate());
				buyList.add(buyInfo);
			}

			String currentLanguage = CommonUtil.getCurrentLanguage();
			// 获取 3个sheet 页的 列头
			List<List<String>> baseHeaderList = this.getColumn(currentLanguage, ExitFactoryBaseColumnEnum::getColumnAndSheetEnum, ExitFactorySheetEnum.activation);
			List<List<String>> activationHeaderList = this.getColumn(currentLanguage, ExitFactoryActivationColumnEnum::getColumnAndSheetEnum, ExitFactorySheetEnum.activation);
			List<List<String>> buyHeaderList = this.getColumn(currentLanguage, ExitFactoryBuyColumnEnum::getColumnAndSheetEnum, ExitFactorySheetEnum.buy);
			// 生成excel
			ExcelWriter excelWriter = EasyExcel.write(baseDir + EXPORT_EXCEL_NAME).build();
			// 设置sheet页名称
			WriteSheet baseSheet = EasyExcel.writerSheet(ExitFactorySheetEnum.getColumnByCode(currentLanguage, ExitFactorySheetEnum.base))
				.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(baseHeaderList).build();
			WriteSheet activationSheet = EasyExcel.writerSheet(ExitFactorySheetEnum.getColumnByCode(currentLanguage, ExitFactorySheetEnum.activation)).
				registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(activationHeaderList).build();
			WriteSheet buySheet = EasyExcel.writerSheet(ExitFactorySheetEnum.getColumnByCode(currentLanguage, ExitFactorySheetEnum.buy)).
				registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(buyHeaderList).build();
			// 写入数据
			excelWriter.write(baseInfoList, baseSheet);
			excelWriter.write(activationList, activationSheet);
			excelWriter.write(buyList, buySheet);
			excelWriter.finish();
			log.info("exitFactoryExportExcel createLocalExportExcel -> end");
		});
	}

	private List<List<String>> getColumn(String currentLanguage, Function<String, Map<String, ExitFactorySheetEnum>> function, ExitFactorySheetEnum sheetEnum) {
		List<List<String>> headerList = new ArrayList<>();
		Map<String, ExitFactorySheetEnum> column = function.apply(currentLanguage);
		for (Map.Entry<String, ExitFactorySheetEnum> entry : column.entrySet()) {
			List<String> head = new ArrayList<>();
			if (entry.getValue().equals(ExitFactorySheetEnum.base)) {
				head.add(ExitFactorySheetEnum.getColumnByCode(currentLanguage, ExitFactorySheetEnum.base));
			} else {
				head.add(ExitFactorySheetEnum.getColumnByCode(currentLanguage, sheetEnum));
			}
			head.add(entry.getKey());
			headerList.add(head);
		}
		return headerList;
	}

	private void outputZip2Response(HttpServletResponse response, String baseDir, Map<Long, String> imgKeyMapDeviceSn, String imageZipDir) {
		ZipOutputStream zos = null;
		try {
			log.info("exitFactoryExportExcel outputZip2Response ->");
			response.setContentType("application/zip");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			String fileName = URLEncoder.encode("质保管理", StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".zip");
			zos = new ZipOutputStream(response.getOutputStream());
			// 将excel写入response，目录为 ： 20240127165124_18665860502/deviceExitFactoryInfo.xlsx
			SkyworthFileUtil.createZipFile4Response(EXPORT_EXCEL_NAME, baseDir + EXPORT_EXCEL_NAME, zos);
			// 将不同SN的本地图片压缩包写入response 目录为：日期_账号\imageZip\sn.zip
			Collection<String> values = imgKeyMapDeviceSn.values();
			for (String sn : values) {
				String zipName = sn + ".zip";// 下载的压缩包中图片压缩包名称
				SkyworthFileUtil.createZipFile4Response(zipName, imageZipDir + zipName, zos);
			}
			log.info("exitFactoryExportExcel outputZip2Response -> end ");
		} catch (IOException e) {
			log.error("outputZip2Response error :  ", e);
		} finally {
			if (zos != null) {
				try {
					zos.closeEntry();
					zos.close();
				} catch (IOException e) {
					log.error("outputZip2Response close error : ", e);
				}
			}
		}
	}

	private void setDate(FunctionSetName<String> functionSetName, LocalDate localDate) {
		if (localDate != null) {
			functionSetName.setName(localDate.toString());
		}
	}

	private static String getFileNameFromUrl(String url) {
		int lastSlashIndex = url.lastIndexOf('/');
		if (lastSlashIndex != -1 && lastSlashIndex < url.length() - 1) {
			return url.substring(lastSlashIndex + 1);
		}
		SecureRandom random = new SecureRandom();
		return random.nextLong() + ".jpg";
	}

	@Override
	public boolean saveDevice(DeviceExitFactoryInfoEntity entity) {
		Wrapper<DeviceExitFactoryInfoEntity> queryWrapper = Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, entity.getDeviceSerialNumber());
		Long count = baseMapper.selectCount(queryWrapper);
		if (count != null && count > 0) {
			throw new BusinessException("portable.device.exit.factory.deviceSerialNumber.is.exist");
		}
		// 默认未使用
		entity.setStatus(0);
		return super.save(entity);
	}

	@Override
	public boolean updateDeviceById(DeviceExitFactoryInfoEntity entity) {
		Wrapper<DeviceExitFactoryInfoEntity> queryWrapper = Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().
			eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, entity.getDeviceSerialNumber())
			.ne(DeviceExitFactoryInfoEntity::getId, entity.getId());
		Long count = baseMapper.selectCount(queryWrapper);
		if (count != null && count > 0) {
			throw new BusinessException("portable.device.exit.factory.deviceSerialNumber.is.exist");
		}
		return super.updateById(entity);
	}

	@Override
	public DeviceExitFactoryInfoVO queryDeviceById(Long id) {
		DeviceExitFactoryInfoEntity detail = super.getById(id);
		if (detail == null) {
			return new DeviceExitFactoryInfoVO();
		}
		DeviceExitFactoryInfoVO deviceExitFactoryInfoVO = DeviceExitFactoryInfoWrapper.build().entityVO(detail);
		R<Map<String, List<DictBiz>>> mapR = dictBizClient.batchGetList(Lists.newArrayList(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode(),
			DictBizCodeEnum.PORTABLE_COMPANY.getCode(), DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode()));
		if (deviceExitFactoryInfoVO.getBuyerUserId() != null) {
			R<List<User>> userResult = userSearchClient.listByUserIds(Lists.newArrayList(deviceExitFactoryInfoVO.getBuyerUserId()));
			this.setUserInfo(userResult, deviceExitFactoryInfoVO.getBuyerUserId(), deviceExitFactoryInfoVO::setBuyerUserName, deviceExitFactoryInfoVO::setBuyerUserPhone);
		}
		List<DictBiz> deviceTypeList = mapR.getData().get(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
		List<DictBiz> companyList = mapR.getData().get(DictBizCodeEnum.PORTABLE_COMPANY.getCode());
		List<DictBiz> buyChannelList = mapR.getData().get(DictBizCodeEnum.PORTABLE_BUY_CHANNEL.getCode());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		this.setDictValue(deviceTypeList, currentLanguage, deviceExitFactoryInfoVO.getDeviceType(), deviceExitFactoryInfoVO::setDeviceTypeName);
		this.setDictValue(companyList, currentLanguage, deviceExitFactoryInfoVO.getCompany(), deviceExitFactoryInfoVO::setCompanyName);
		this.setDictValue(buyChannelList, currentLanguage, deviceExitFactoryInfoVO.getBuyChannel(), deviceExitFactoryInfoVO::setBuyChannelName);
		if (deviceExitFactoryInfoVO.getBuyDate() != null) {
			deviceExitFactoryInfoVO.setQualityGuaranteeEndDate(deviceExitFactoryInfoVO.getBuyDate().plusYears(deviceExitFactoryInfoVO.getQualityGuaranteeYear()));
		}
		if (deviceExitFactoryInfoVO.getBuyCertificateImgBizKey() != null) {
			R<Map<Long, List<AttachmentInfoEntity>>> result = attachmentInfoClient.findByBusinessIds(Lists.newArrayList(deviceExitFactoryInfoVO.getBuyCertificateImgBizKey()));
			Map<Long, List<AttachmentInfoEntity>> data = result.getData();
			deviceExitFactoryInfoVO.setAttachmentMap(data);
		}
		return deviceExitFactoryInfoVO;
	}

	@Override
	public boolean updateDeviceByDeviceSerialNumber(DeviceExitFactoryInfoEntity entity) {
		Wrapper<DeviceExitFactoryInfoEntity> queryWrapperSn = Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery()
				.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, entity.getDeviceSerialNumber());
		Long countSn = baseMapper.selectCount(queryWrapperSn);
		if(countSn == null || countSn == 0) {
			throw new BusinessException("portable.device.exit.factory.not.exists");
		}
		Wrapper<DeviceExitFactoryInfoEntity> queryWrapper = Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery()
			.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, entity.getDeviceSerialNumber())
			.isNotNull(true, DeviceExitFactoryInfoEntity::getBuyDate);
		Long count = baseMapper.selectCount(queryWrapper);
		if (count != null && count > 0) {
			throw new BusinessException("portable.device.exit.factory.had.save.quality.guarantee");
		}
		BladeUser user = AuthUtil.getUser();
		Wrapper<DeviceExitFactoryInfoEntity> updateWrapper = Wrappers.<DeviceExitFactoryInfoEntity>lambdaUpdate()
			.set(DeviceExitFactoryInfoEntity::getQualityGuaranteeBeginDate, entity.getBuyDate())
			.set(DeviceExitFactoryInfoEntity::getBuyChannel, entity.getBuyChannel())
			.set(DeviceExitFactoryInfoEntity::getBuyDate, entity.getBuyDate())
			.set(DeviceExitFactoryInfoEntity::getBuyOrderNumber, entity.getBuyOrderNumber())
			.set(DeviceExitFactoryInfoEntity::getBuyCertificateImgBizKey, entity.getBuyCertificateImgBizKey())
			.set(DeviceExitFactoryInfoEntity::getBuyerUserId, user.getUserId())
			.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, entity.getDeviceSerialNumber());
		super.update(updateWrapper);
		attachmentInfoClient.saveAndUpdate(entity.getBatchVO());
		return true;
	}

	@Override
	public List<JSONObject> queryDeviceActivationsInfo(JSONObject jsonObject) {
		return baseMapper.queryDeviceActivationsInfo(jsonObject);
	}

	@Override
	public Long queryAccumulatedActivations(JSONObject jsonObject) {
		return baseMapper.queryAccumulatedActivations(jsonObject);
	}

	@Override
	public String importAddExcel(List<ExitFactoryImportExcel> data, boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(ExitFactoryImportExcel::getDeviceSerialNumber).collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntities = baseMapper.queryByDeviceSerialNumbers(deviceSerialNumbers);
		if (CollectionUtil.isNotEmpty(deviceExitFactoryInfoEntities)) {
			List<String> collect = deviceExitFactoryInfoEntities.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			String join = Joiner.on(",").skipNulls().join(collect);
			throw new BusinessException("portable.device.exit.factory.import.deviceSerialNumber.is.exist",join);
		}
		List<DeviceExitFactoryInfoEntity> insert = new ArrayList<>();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			data.forEach(userExcel -> {
				DeviceExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, DeviceExitFactoryInfoEntity.class));
				entity.setCreateUserAccount(user.getAccount());
				entity.setCreateUser(user.getUserId());
				try {
				Date date = formatter.parse(userExcel.getExitFactoryDate());
				entity.setExitFactoryDate(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				}catch (Exception e) {
					log.error("importAddExcel ExitFactoryDate error");
				}
				entity.setStatus(CommonConstant.NOT_SEALED_ID);
				insert.add(entity);
			});

		this.saveOrUpdateBatch(insert);
		return "";
	}

	@Override
	public String importModifyExcel(List<ExitFactoryImportExcel> data, Boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(ExitFactoryImportExcel::getDeviceSerialNumber).collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> dbData = baseMapper.queryByDeviceSerialNumbers(deviceSerialNumbers);
		// 如果入参中个数 和 DB中个数不相等，则表示有新增数据，提示使用新增导入
		if(deviceSerialNumbers.size() != dbData.size()) {
			List<String> dbSn = dbData.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			StringBuilder join = new StringBuilder();
			deviceSerialNumbers.forEach(p -> {
				if(!dbSn.contains(p)) {
					join.append(p).append(",");
				}
			});
			throw  new BusinessException("portable.device.exit.factory.modify.import.deviceSerialNumber.is.not.exist",join);
		}
		List<DeviceExitFactoryInfoEntity> update = new ArrayList<>();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		data.forEach(userExcel -> {
			DeviceExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, DeviceExitFactoryInfoEntity.class));
			entity.setUpdateUserAccount(user.getAccount());
			entity.setUpdateUser(user.getUserId());
			entity.setStatus(CommonConstant.NOT_SEALED_ID);
			try {
				Date date = formatter.parse(userExcel.getExitFactoryDate());
				entity.setExitFactoryDate(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
			}catch (Exception e) {
				log.error("importAddExcel ExitFactoryDate error");
			}
			update.add(entity);
		});
		baseMapper.updateBatchBySn(update);
		return "";
	}

	@Override
	public void exportImportantEventTemplate(HttpServletResponse response) {
		List<ExitFactoryImportExcel> list = new ArrayList<>();
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			String fileName = URLEncoder.encode("template", StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
			String currentLanguage = CommonUtil.getCurrentLanguage();
			List<List<String>> baseHeaderList = this.getColumn(currentLanguage,ExitFactoryBaseColumnEnum::getColumnAndSheetEnum,ExitFactorySheetEnum.activation);
			EasyExcel.write(response.getOutputStream()).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
					.head(baseHeaderList).sheet("exitFactoryInfo").doWrite(list);
		} catch (Throwable var6) {
			log.error("exportImportantEventTemplate : ",var6);
		}
	}

	@Override
	public List<DeviceActivationAndAlarmExcel> queryDeviceActivationsExcelInfo(JSONObject jsonObject) {
		return baseMapper.queryDeviceActivationsExcelInfo(jsonObject);
	}
	@Override
	public DeviceExitFactoryInfoEntity appQueryQualityGuarantee(String sn) {
		LambdaQueryWrapper<DeviceExitFactoryInfoEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, sn);
		DeviceExitFactoryInfoEntity one = this.getOne(wrapper);
		if(one.getBuyCertificateImgBizKey() != null) {
			R<Map<Long, List<AttachmentInfoEntity>>> byBusinessIds = attachmentInfoClient.findByBusinessIds(Lists.newArrayList(one.getBuyCertificateImgBizKey()));
			one.setAttachmentMap(byBusinessIds.getData());
		}
		return one;
	}
	@Override
	public List<DeviceExitFactoryInfoVO> appQueryQualityGuaranteeList(DeviceExitFactoryInfoVO vo) {
		BladeUser user = AuthUtil.getUser();
		vo.setCreateUser(user.getUserId());
		List<DeviceExitFactoryInfoVO> deviceExitFactoryInfoVOS = baseMapper.queryQualityGuaranteeListBySelf(vo);
		if (CollectionUtil.isEmpty(deviceExitFactoryInfoVOS)) {
			return new ArrayList<>();
		}
		R<List<DictBiz>> deviceTypeDict = dictBizClient.getListAllLang(DictBizCodeEnum.PORTABLE_DEVICE_TYPE.getCode());
		List<DictBiz> deviceType = deviceTypeDict.getData();
		if (CollectionUtil.isNotEmpty(deviceType)) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			for (DeviceExitFactoryInfoVO dbVo : deviceExitFactoryInfoVOS) {
				this.setDictValue(deviceType, currentLanguage, dbVo.getDeviceType(), dbVo::setDeviceTypeName);
			}
		}
		return deviceExitFactoryInfoVOS;
	}
}

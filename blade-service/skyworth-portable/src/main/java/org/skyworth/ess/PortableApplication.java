package org.skyworth.ess;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.launch.BladeApplication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * 系统模块启动器
 *
 * <AUTHOR>
 */

@EnableMethodCache(basePackages = "org.skyworth.ess")
@EnableCreateCacheAnnotation
@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients({"org.springblade","org.skyworth"})
public class PortableApplication implements CommandLineRunner {
	@Autowired
	private ConfigurableEnvironment springEnv;

	public static void main(String[] args) {
		BladeApplication.run(CommonConstant.APPLICATION_PORTABLE_NAME, PortableApplication.class, args);
	}

	/**
	 * 打印配置文件
	 *
	 * @Description
	 * @Param args
	 * @Return void
	 * <AUTHOR>
	 * @Date 2023/8/22 13:43
	 **/
	@Override
	public void run(String... args) throws Exception {
		CommonUtil.printConfigurationParameters(springEnv);
	}
}


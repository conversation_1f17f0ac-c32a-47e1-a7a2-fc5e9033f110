/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactoryImportExcel;
import org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备出厂信息 服务类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface IDeviceExitFactoryInfoService extends BaseService<DeviceExitFactoryInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page                  分页
	 * @param deviceExitFactoryInfo 实体
	 * @return 分页对象
	 */
	IPage<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage<DeviceExitFactoryInfoVO> page, DeviceExitFactoryInfoVO deviceExitFactoryInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper 查询
	 * @return 集合
	 */
	void exportDeviceExitFactoryInfo(Wrapper<DeviceExitFactoryInfoEntity> queryWrapper, HttpServletResponse response);

	boolean saveDevice(DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity);

	boolean updateDeviceById(DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity);

	DeviceExitFactoryInfoVO queryDeviceById(Long id);

	boolean updateDeviceByDeviceSerialNumber(DeviceExitFactoryInfoEntity entity);

	/**
	 * 查询设备激活情况
	 *
	 * @param jsonObject 入参
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @since 2024/1/26 13:39
	 **/
	List<JSONObject> queryDeviceActivationsInfo(JSONObject jsonObject);
	/**
	 * 查询某个时间段之前的累计激活数
	 * @param jsonObject 入参
	 * @return long
	 * <AUTHOR>
	 * @since 2024/5/24 14:46
	 **/
	Long queryAccumulatedActivations(JSONObject jsonObject);
	String importAddExcel(List<ExitFactoryImportExcel> data, boolean isCovered);
	/**
	 * 查询报表详情
	 *
	 * @param jsonObject 入参
	 * @return List<DeviceActivationAndAlarmExcel>
	 * <AUTHOR>
	 * @since 2024/2/1 10:57
	 **/
	List<DeviceActivationAndAlarmExcel> queryDeviceActivationsExcelInfo(JSONObject jsonObject);


	String importModifyExcel(List<ExitFactoryImportExcel> data, Boolean isCovered);
	void exportImportantEventTemplate(HttpServletResponse response);

	DeviceExitFactoryInfoEntity appQueryQualityGuarantee(String sn);

	List<DeviceExitFactoryInfoVO> appQueryQualityGuaranteeList(DeviceExitFactoryInfoVO vo);
}

#服务器端口
server:
  port: 8222

#数据源配置
spring:
  datasource:
    url: ${blade.datasource.portable.dev.url}
    username: ${blade.datasource.portable.dev.username}
    password: ${blade.datasource.portable.dev.password}


mybatis-plus:
  # 包扫描路径(当前项目的实体类所在位置。别名包扫描路径，通过该属性可以给包中的类注册别名，多个路径用逗号分割)
  type-aliases-package: org.skyworth.ess.**.entity
  # xml扫描，多个目录用逗号或者分号分隔(告诉 Mapper 所对应的 XML 文件位置)
  mapper-locations: classpath:org/skyworth/ess/**/mapper/*Mapper.xml
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    # 允许在resultType="map"时映射null值
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用(包括执行结果)
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: false
  global-config:
    db-config:
      # 主键类型 AUTO:"数据库ID自增"
      # NONE："无主键生成策略，需要手动设置主键值"
      # INPUT:"用户输入ID",
      # ASSIGN_ID:"雪花算法生成全局唯一ID(数字类型唯一ID)",
      # ASSIGN_UUID:"全局唯一ID，String类型主键，使用UUID生成";
      id-type: ASSIGN_ID
      # 字段策略 IGNORED:"忽略判断"  NOT_NULL:"非 NULL 判断")  NOT_EMPTY:"非空判断"
      field-strategy: NOT_EMPTY
      # 数据库类型
      db-type: MYSQL
      # 逻辑删除配置
      # 删除前
      logic-not-delete-value: 0
      # 删除后
      logic-delete-value: 1

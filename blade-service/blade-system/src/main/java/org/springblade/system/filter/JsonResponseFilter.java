package org.springblade.system.filter;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Component
public class JsonResponseFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse originalResponse = (HttpServletResponse)servletResponse;
        JsonHttpServletResponseWrapper wrapper = new JsonHttpServletResponseWrapper(originalResponse);
        filterChain.doFilter(servletRequest, wrapper);

        byte[] responseData = wrapper.getResponseData();
        String contentType = wrapper.getContentType();

        if (contentType != null && contentType.contains("application/json")) {
            // 替换 &amp; 为 &
            String jsonString = new String(responseData, StandardCharsets.UTF_8);
            jsonString = StringEscapeUtils.unescapeHtml4(jsonString);
            byte[] newResponseData = jsonString.getBytes(StandardCharsets.UTF_8);
            // 写入修改后的响应数据
            ServletOutputStream outputStream = servletResponse.getOutputStream();
            outputStream.write(newResponseData);
            outputStream.flush();
            outputStream.close();
        } else {
            // 对于其他类型，直接写入原始数据
            ServletOutputStream outputStream = servletResponse.getOutputStream();
            outputStream.write(responseData);
            outputStream.flush();
            outputStream.close();
        }
    }


    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.feign;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserApp;
import org.springblade.system.service.IRoleService;
import org.springblade.system.service.IUserAppService;
import org.springblade.system.service.IUserSearchService;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户查询服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class UserSearchClient implements IUserSearchClient {

	private final IUserSearchService service;

	private final IRoleService roleService;

	private final IUserAppService userAppService;

	@Override
	@GetMapping(LIST_BY_USER)
	public R<List<User>> listByUser(String userId) {
		return R.data(service.listByUser(Func.toLongList(userId)));
	}

	@Override
	@GetMapping(LIST_BY_DEPT)
	public R<List<User>> listByDept(String deptId) {
		return R.data(service.listByDept(Func.toLongList(deptId)));
	}

	@Override
	@GetMapping(LIST_TENANT_USER_BY_DEPT)
	@TenantIgnore
	public R<List<User>> listTenantUserByDept(Long deptId, Long plantId, String tenantId) {
		return R.data(service.listTenantUserByDept(deptId, plantId, tenantId));
	}

	@Override
	@GetMapping(LIST_BY_POST)
	public R<List<User>> listByPost(String postId) {
		return R.data(service.listByPost(Func.toLongList(postId)));
	}

	@Override
	@GetMapping(LIST_BY_ROLE)
	public R<List<User>> listByRole(String roleId) {
		return R.data(service.listByRole(Func.toLongList(roleId)));
	}

	@Override
	@GetMapping(LIST_BY_REALNAME)
	public R<List<User>> listByRealName(String realName) {
		LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
		if (realName != null && !realName.isEmpty()) {
			queryWrapper.likeRight(User::getRealName, realName);
		}
		return R.data(service.list(queryWrapper));
	}


	@Override
	@GetMapping(ROLEID_BY_ROLENAME)
	public R<Long> roleIdByRoleName(String roleName) {
		LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(roleName != null, Role::getRoleName, roleName)
			.eq(Role::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		Role roleOne = roleService.getOne(queryWrapper);
		Long id = roleOne.getId();
		return R.data(id);
	}

	@TenantIgnore
	@Override
	@PostMapping(LIST_BY_USERID_LIST)
	public R<List<User>> listByUserIds(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.in(User::getId, userIds);
		return R.data(service.list(queryWrapper));
	}

	@Override
	@PostMapping(LIST_ALL_BY_USERID_LIST)
	public R<List<User>> listAllByUserIds(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		return R.data(service.listAllByUser(userIds));
	}

	@Override
	public R<List<UserStatisticsVO>> singleDayRegisterInfo(Map<String, Object> map) {
		return R.data(service.singleDayRegisterInfo(map));
	}

	@Override
	public R<Long> accumulatedRegistrationData(Map<String, Object> map) {
		return R.data(service.accumulatedRegistrationData(map));
	}

	@Override
	public R<List<UserRegistrationVO>> singleDayRegisterExcelInfo(Map<String, Object> map) {
		return R.data(service.singleDayRegisterExcelInfo(map));
	}

	@Override
	public R<List<User>> listByAccount(List<String> accounts) {
		if (CollectionUtils.isNullOrEmpty(accounts)) {
			return R.data(null);
		}
		LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.in(User::getAccount, accounts).eq(User::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		return R.data(service.list(queryWrapper));
	}

	@TenantIgnore
	@Override
	public R<List<User>> listByMappingUser(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		return R.data(service.selectMappingUser(userIds));
	}

	@TenantIgnore
	@Override
	public R<List<User>> listByMappingSourceUser(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		return R.data(service.selectMappingSourceUser(userIds));
	}

	@TenantIgnore
	@Override
	public R<List<User>> listByPhone(String phone) {
		return R.data(service.selectUserByPhone(phone));
	}

	@Override
	@PostMapping(API_PORTABLE_USER_AND_REGIN_INFO)
	public R<Map<Long, JSONObject>> userAndReginInfo(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		return R.data(service.userAndReginInfo(userIds));
	}

	@Override
	@PostMapping(API_PORTABLE_USER_REGION_INFO)
	public R<Map<Long, UserApp>> userAppInfoList(List<Long> userIds) {
		if (CollectionUtils.isNullOrEmpty(userIds)) {
			return R.data(null);
		}
		List<UserApp> userApps = userAppService.queryUserAppInfoByUserIds(userIds);
		Map<Long, UserApp> userAppMap = Optional.ofNullable(userApps).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(UserApp::getUserId, Function.identity(), (a, b) -> a));
		return R.data(userAppMap);
	}

	@Override
	@GetMapping(LIST_BY_ROLE_NAME)
	public R<List<User>> listByRoleName(String roleName) {
		return R.data(service.listByRoleName(roleName));
	}

	@Override
	@GetMapping(LIST_BY_PHONE_MATCH)
	public R<List<User>> listByPhoneMatch(String phone) {
		LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
		if (StringUtil.isNotBlank(phone)) {
			queryWrapper.likeRight(User::getPhone, phone);
		}
		return R.data(service.list(queryWrapper));
	}
}

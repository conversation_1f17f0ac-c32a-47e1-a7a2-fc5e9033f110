/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.system.entity.UserMappingEntity;
import org.springblade.system.vo.UserInfoMappingVO;

import java.util.List;

/**
 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public interface UserMappingMapper extends BaseMapper<UserMappingEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param userMapping
	 * @return
	 */
	List<UserInfoMappingVO> selectUserMappingPage(IPage page, UserInfoMappingVO userMapping);

	@TenantIgnore
	UserMappingEntity getUserMappingIgnoreTenant(@Param("params") UserMappingEntity entity);

	@TenantIgnore
	List<UserMappingEntity> batchGetUserMappingIgnoreTenant(List<UserMappingEntity> list);

	@TenantIgnore
	int batchDeleteUserMappingIgnoreTenant(@Param("list") List<Long> list, @Param("tenantId") String tenantId);
}

package org.springblade.system.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.system.entity.UserApp;
import org.springblade.system.entity.UserAppExt;
import org.springblade.system.mapper.UserAppMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserAppServiceImpl implements IUserAppService{
    @Autowired
    private UserAppMapper userAppMapper;
    @Override
    public boolean updateUserAppByUserId(UserAppExt userAppExt) {
        return userAppMapper.updateUserAppByUserId(userAppExt);
    }
    @Override
    public boolean saveUserApp(UserApp userApp) {
        userAppMapper.insert(userApp);
        return true;
    }

	@Override
	public List<UserApp> queryUserAppInfoByUserIds(List<Long> userIds) {
		return userAppMapper.selectList(Wrappers.<UserApp>lambdaQuery().in(UserApp::getUserId,userIds));
	}
}

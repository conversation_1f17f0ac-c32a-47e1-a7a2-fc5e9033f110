<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2023. Skyworth All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.AttachmentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="attachmentInfoResultMap" type="org.springblade.system.vo.AttachmentInfoVO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_url" property="fileUrl"/>
        <result column="azure_key" property="azureKey"/>
        <result column="thumbnail_url" property="thumbnailUrl"/>
        <result column="thumbnail_azure_key" property="thumbnailAzureKey"/>
        <result column="cdn_url" property="cdnUrl"/>
        <result column="business_id" property="businessId"/>
        <result column="attach_size" property="attachSize"/>
        <result column="container" property="container"/>
        <result column="sort" property="sort"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectAttachmentInfoPage" resultMap="attachmentInfoResultMap">
        select *
        from attachment_info
        where is_deleted = 0
    </select>


    <select id="findByBusinessIds" resultMap="attachmentInfoResultMap">
        select * from attachment_info where is_deleted = 0
        <if test="ids != null and ids.size() > 0">
            and business_id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateBatch">
        <foreach collection="list" item="item" separator=";">
            update
            attachment_info
            set
            <if test="item.fileName!=null and item.fileName!=''">
                file_name = #{item.fileName},
            </if>
            <if test="item.fileUrl!=null and item.fileUrl!=''">
                file_url = #{item.fileUrl},
            </if>
            <if test="item.azureKey!=null and item.azureKey!=''">
                azure_key = #{item.azureKey},
            </if>
            <if test="item.businessId!=null">
                business_id = #{item.businessId},
            </if>
            <if test="item.attachSize!=null">
                attach_size = #{item.attachSize},
            </if>
            <if test="item.container!=null and item.container!=''">
                container = #{item.container},
            </if>
            <if test="item.thumbnailAzureKey!=null and item.thumbnailAzureKey!=''">
                thumbnail_azure_key = #{item.thumbnailAzureKey},
            </if>
            <if test="item.thumbnailUrl!=null and item.thumbnailUrl!=''">
                thumbnail_url = #{item.thumbnailUrl},
            </if>
            <if test="item.sort!=null">
                sort = #{item.sort},
            </if>
            <if test="item.attachSize!=null">
                attach_size = #{item.attachSize},
            </if>
            update_user = #{userId},
            update_time = now()
            where
            id = #{item.id}
        </foreach>
    </update>

</mapper>

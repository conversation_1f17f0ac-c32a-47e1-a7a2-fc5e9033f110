package org.springblade.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.core.mp.base.BaseService;
import org.springblade.system.entity.RedisSeqEnums;
import org.springblade.system.entity.RedisSequence;

/**
 * redis业务唯一id
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-13 16:53
 **/
public interface IRedisSeqService extends IService<RedisSequence> {
	/**
	 * 查询业务唯一id
	 *
	 * @param redisSeqEnums 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/11/13 16:58
	 **/
	String getRedisUniqueId(RedisSeqEnums redisSeqEnums);

	/**
	 * 清除redis唯一id
	 *
	 * <AUTHOR>
	 * @since 2023/11/13 17:09
	 **/
	void clearRedisSequence();
}

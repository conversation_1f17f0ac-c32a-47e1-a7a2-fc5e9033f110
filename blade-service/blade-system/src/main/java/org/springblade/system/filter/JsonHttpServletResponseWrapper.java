package org.springblade.system.filter;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
public class JsonHttpServletResponseWrapper extends HttpServletResponseWrapper  {

    private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();
    private PrintWriter writer;
    private ServletOutputStream outputStream;

    public JsonHttpServletResponseWrapper(HttpServletResponse response) {
        super(response);
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if (writer != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if (outputStream == null) {
            outputStream = new ServletOutputStream() {
                @Override
                public boolean isReady() {
                    return false;
                }
                @Override
                public void setWriteListener(WriteListener writeListener) {
                }
                @Override
                public void write(int b) throws IOException {
                    buffer.write(b);
                }
            };
        }

        return outputStream;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if (outputStream != null) {
            throw new IllegalStateException("getOutputStream() has already been called on this response.");
        }

        if (writer == null) {
            writer = new PrintWriter(new OutputStreamWriter(buffer, getCharacterEncoding()));
        }
        return writer;
    }

    // 提供一个方法来获取捕获的响应内容
    public byte[] getResponseData() throws IOException {
        if (writer != null) {
            writer.close();
        } else if (outputStream != null) {
            outputStream.close();
        }
        return buffer.toByteArray();
    }
}

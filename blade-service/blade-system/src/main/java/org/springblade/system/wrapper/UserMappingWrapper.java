/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.system.entity.UserMappingEntity;
import org.springblade.system.vo.UserInfoMappingVO;

import java.util.Objects;

/**
 * 用户关系映射表：目前用于代理商角色用户同步到设备端用户，供设备端使用 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public class UserMappingWrapper extends BaseEntityWrapper<UserMappingEntity, UserInfoMappingVO> {

	public static UserMappingWrapper build() {
		return new UserMappingWrapper();
	}

	@Override
	public UserInfoMappingVO entityVO(UserMappingEntity userMapping) {
		UserInfoMappingVO userMappingVO = Objects.requireNonNull(BeanUtil.copy(userMapping, UserInfoMappingVO.class));

		//User createUser = UserCache.getUser(userMapping.getCreateUser());
		//User updateUser = UserCache.getUser(userMapping.getUpdateUser());
		//userMappingVO.setCreateUserName(createUser.getName());
		//userMappingVO.setUpdateUserName(updateUser.getName());

		return userMappingVO;
	}


}

package org.springblade.system.filter;

import com.alibaba.csp.sentinel.SphU;
import feign.Feign;
import org.springblade.system.controller.RegionController;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnClass({ RegionController.class})
public class TestCondition {

    public TestCondition() {
        System.out.println("++++++++++++++++++++++");
    }
}

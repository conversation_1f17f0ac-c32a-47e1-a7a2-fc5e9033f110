package org.springblade.system.aspect;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;


/**
 * @description: 防止用户重复提交
 * @author: SDT50545
 * @since: 2024-02-18 16:58
 **/
@Aspect
@Component
@Slf4j
public class NoRepeatedSubmissionImpl {
	@Resource
	BladeRedis bladeRedis;

	/**
	 * 配置切入点
	 */
	@Pointcut("@annotation(org.springblade.system.aspect.NoRepeatedSubmission)")
	public void noRepeatDataCut() {

	}

	@Around("@annotation(noRepeatData)")
	public Object around(ProceedingJoinPoint point, NoRepeatedSubmission noRepeatData) throws Throwable {
		//加锁过期时间
		int lockExpire = noRepeatData.lockTime();
		//开始时间
		long start = System.currentTimeMillis();
		String args = JSON.toJSONString(point.getArgs());
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		assert requestAttributes != null;
		HttpServletRequest request = requestAttributes.getRequest();
		String url = request.getRequestURL().toString();
		Long userId = AuthUtil.getUser().getUserId();
		MethodSignature signature = (MethodSignature) point.getSignature();
		//请求的方法名
		String className = point.getTarget().getClass().getName();
		String methodName = signature.getName();
		String paramSign = DigestUtils.md5Hex(url + userId + args);
		log.warn("======request url=>{},param=>{},uid=>{},md5-=>{}", url, args, userId, paramSign);
		if (bladeRedis.exists(paramSign)) {
			throw new ServiceException("Please do not resubmit");
		}
		try {
			log.info("【接口加锁参数：{}】", paramSign);
			//过期时间由注解接收
			bladeRedis.setEx(paramSign, 1, Duration.ofSeconds(lockExpire));
			Object result = null;
			try {
				result = point.proceed();
				long end = System.currentTimeMillis();
				log.info("【接口正常执行完成】接口名：{}.{},执行时间:{}毫秒", className, methodName, (end - start));
				return result;
			} catch (Exception e) {
				log.error("业务代码执行异常", e);
				throw new ServiceException("Business code execution exception");
			} finally {
				bladeRedis.del(paramSign);
			}
		} catch (Exception e) {
			long end = System.currentTimeMillis();
			log.info("【重复提交已拦截】接口名：{}.{},执行时间:{}毫秒", className, methodName, (end - start));
			throw new ServiceException("Please do not resubmit");
		}
	}
}

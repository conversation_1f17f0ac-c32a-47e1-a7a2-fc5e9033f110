<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.UserAppMapper">

    <update id="updateUserAppByUserId">
        update blade_user_app set
        <if test="userApp.countryCode!=null and userApp.countryCode!=''">
            country_code = #{userApp.countryCode},
        </if>
        <if test="userApp.provinceCode!=null and userApp.provinceCode!=''">
            province_code = #{userApp.provinceCode},
        </if>
        <if test="userApp.cityCode!=null and userApp.cityCode!=''">
            city_code = #{userApp.cityCode},
        </if>
        <if test="userApp.countyCode!=null and userApp.countyCode!=''">
            county_code = #{userApp.countyCode},
        </if>
        update_time = now()
        where user_id = #{userApp.userId}
    </update>
</mapper>

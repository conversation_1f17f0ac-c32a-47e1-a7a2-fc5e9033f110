/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.feign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.ota.feign.IDistributeUpgradeClient;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.resource.feign.ISmsClient;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.service.IAttachmentInfoService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 附件表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@NonDS
@ApiIgnore
@RestController
public class AttachmentInfoClient implements IAttachmentInfoClient {
	@Resource
	private IAttachmentInfoService attachmentInfoService;
	@Resource
	private IDistributeUpgradeClient distributeUpgradeClient;

	@Value("${oss.domestic:false}")
	private boolean ossDomestic;

	@Resource
	private ISmsClient smsClient;

	@Override
	@PostMapping(FIND_BY_BUSINESS_IDS)
	public R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIds(@RequestBody List<Long> businessIds) {
		if (CollectionUtils.isNullOrEmpty(businessIds)) {
			return R.data(new HashMap<>(BizConstant.NUMBER_ZERO));
		}
		businessIds = businessIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
		if (CollectionUtils.isNullOrEmpty(businessIds)) {
			return R.data(new HashMap<>(BizConstant.NUMBER_ZERO));
		}
		List<AttachmentInfoEntity> attachmentInfoEntityList = attachmentInfoService.findByBusinessIds(businessIds);
		if (CollectionUtils.isNullOrEmpty(attachmentInfoEntityList)) {
			return R.data(new HashMap<>(BizConstant.NUMBER_ZERO));
		}
		return R.data(querySasTokenAndGroupSort(attachmentInfoEntityList));
	}

	@Override
	@TenantIgnore
	public R<Map<Long, List<AttachmentInfoEntity>>> findByBusinessIdsNoTent(List<Long> businessIds) {
		return findByBusinessIds(businessIds);
	}

	@Override
	@PostMapping(SAVE_AND_UPDATE_BATCH)
	public R saveAndUpdate(@RequestBody BatchVO<AttachmentInfoEntity> batchVO) {
		if (batchVO == null) {
			throw new ServiceException("Attachment information is empty.");
		}
		// addList和updateList过滤掉deleteList的数据
		filterTheDeleteRecords(batchVO);
		List<AttachmentInfoEntity> addList = batchVO.getAddList();
		List<AttachmentInfoEntity> deleteList = batchVO.getDeleteList();
		List<AttachmentInfoEntity> updateList = batchVO.getUpdateList();
		if (!CollectionUtils.isNullOrEmpty(addList)) {
			attachmentInfoService.saveBatch(addList);
		}
		if (!CollectionUtils.isNullOrEmpty(deleteList)) {
			List<Long> deleteTableDataIdList = deleteList.stream().map(AttachmentInfoEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
			if (!deleteTableDataIdList.isEmpty()) {
				attachmentInfoService.deleteLogic(deleteList.stream().map(AttachmentInfoEntity::getId).collect(Collectors.toList()));
			}
			// 删除微软云上附件原件
			this.deleteAzureAttachment(deleteList);
		}
		if (!CollectionUtils.isNullOrEmpty(updateList)) {
			attachmentInfoService.updateBatch(updateList, AuthUtil.getUser().getUserId());
		}
		return R.success("success");
	}

	/**
	 * addList和updateList过滤掉deleteList的数据
	 *
	 * @param batchVO 入参
	 * <AUTHOR>
	 * @since 2023/12/28 17:11
	 **/
	private void filterTheDeleteRecords(BatchVO<AttachmentInfoEntity> batchVO) {
		List<AttachmentInfoEntity> addList = batchVO.getAddList();
		List<AttachmentInfoEntity> deleteList = batchVO.getDeleteList();
		List<AttachmentInfoEntity> updateList = batchVO.getUpdateList();
		if (CollectionUtils.isNullOrEmpty(deleteList)) {
			return;
		}
		Set<String> set = deleteList.stream().map(a -> a.getAzureKey() + a.getBusinessId()).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
		// 新增过滤掉删除的数据
		if (!CollectionUtils.isNullOrEmpty(addList)) {
			batchVO.setAddList(addList.stream().filter(a -> !set.contains(a.getAzureKey() + a.getBusinessId())).collect(Collectors.toList()));
		}
		// 修改过滤掉删除的数据
		if (!CollectionUtils.isNullOrEmpty(updateList)) {
			batchVO.setUpdateList(updateList.stream().filter(a -> !set.contains(a.getAzureKey() + a.getBusinessId())).collect(Collectors.toList()));
		}
	}

	/**
	 * 调用接口删除微软云容器附件
	 *
	 * @param attachmentInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/10/24 15:37
	 **/
	private void deleteAzureAttachment(List<AttachmentInfoEntity> attachmentInfoEntityList) {
		if (attachmentInfoEntityList.isEmpty()) {
			return;
		}
		// 国内华为云
		if (ossDomestic) {
			Map<String, String> map = new HashMap<>();
			attachmentInfoEntityList.forEach(a -> {
				map.put(a.getAzureKey(), a.getContainer());
				if (StringUtil.isNotBlank(a.getThumbnailAzureKey())) {
					map.put(a.getThumbnailAzureKey(), a.getContainer());
				}
			});
			smsClient.deleteAzureAttachment(map);
		} else {
			Map<String, JSONObject> map = attachmentInfoEntityList.stream().collect(Collectors.toMap(AttachmentInfoEntity::getAzureKey, a -> (JSONObject) JSON.toJSON(a), (n1, n2) -> n1));
			distributeUpgradeClient.deleteAzureAttachment(map);
		}
	}

	/**
	 * 查询文件和缩略图的sasToken，并分组排序返回数据
	 *
	 * @param attachmentInfoEntityList 入参
	 * @return Map<Long, List < AttachmentInfoEntity>>
	 * <AUTHOR>
	 * @since 2023/10/27 14:10
	 **/
	private Map<Long, List<AttachmentInfoEntity>> querySasTokenAndGroupSort(List<AttachmentInfoEntity> attachmentInfoEntityList) {
		// 设置查询sasToken的查询条件
		Map<String, String> sasAzureTokenMap = attachmentInfoEntityList.stream().filter(a -> StringUtil.isNoneBlank(a.getAzureKey(), a.getContainer())).collect(Collectors.toMap(AttachmentInfoEntity::getAzureKey, AttachmentInfoEntity::getContainer, (n1, n2) -> n1));
		// 设置查询缩略图sasToken查询条件
		Map<String, String> sasAzurethumbnailTokenMap = attachmentInfoEntityList.stream().filter(a -> StringUtil.isNoneBlank(a.getThumbnailAzureKey(), a.getContainer())).collect(Collectors.toMap(AttachmentInfoEntity::getThumbnailAzureKey, AttachmentInfoEntity::getContainer, (n1, n2) -> n1));
		if (!sasAzurethumbnailTokenMap.isEmpty()) {
			sasAzureTokenMap.putAll(sasAzurethumbnailTokenMap);
		}
		// 调用远程接口获取sasToken
		Map<String, String> sasTokenResultMap;
		if (ossDomestic) {
			sasTokenResultMap = smsClient.getSasToken(sasAzureTokenMap).getData();
		} else {
			sasTokenResultMap = distributeUpgradeClient.getSasToken(sasAzureTokenMap).getData();
		}
		// 设置sasToken
		if (sasTokenResultMap != null && !sasTokenResultMap.isEmpty()) {
			for (AttachmentInfoEntity a : attachmentInfoEntityList) {
				String azureKey = a.getAzureKey();
				String thumbnailAzureKey = a.getThumbnailAzureKey();
				if (StringUtils.isNotBlank(azureKey) && sasTokenResultMap.containsKey(azureKey)) {
					a.setSasToken(sasTokenResultMap.get(azureKey));
				}
				if (StringUtils.isNotBlank(thumbnailAzureKey) && sasTokenResultMap.containsKey(thumbnailAzureKey)) {
					a.setThumbnailSasToken(sasTokenResultMap.get(thumbnailAzureKey));
				}
			}
		}
		// 文件分组排序
		return attachmentInfoEntityList.stream().collect(
			Collectors.groupingBy(AttachmentInfoEntity::getBusinessId, HashMap::new,
				Collectors.collectingAndThen(Collectors.toList(),
					//正序
					list -> list.stream().sorted(Comparator.comparing(AttachmentInfoEntity::getSort))
						.collect(Collectors.toList())
				)));
	}

}

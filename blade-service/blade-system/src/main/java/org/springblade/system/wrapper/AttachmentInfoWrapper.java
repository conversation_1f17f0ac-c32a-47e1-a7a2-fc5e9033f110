/*
 * Copyright (c) 2018-2023. Skyworth All rights reserved
 */
package org.springblade.system.wrapper;


import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.vo.AttachmentInfoVO;

import java.util.Objects;

/**
 * 附件表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
public class AttachmentInfoWrapper extends BaseEntityWrapper<AttachmentInfoEntity, AttachmentInfoVO> {

	public static AttachmentInfoWrapper build() {
		return new AttachmentInfoWrapper();
	}

	@Override
	public AttachmentInfoVO entityVO(AttachmentInfoEntity attachmentInfo) {
		if (attachmentInfo == null) {
			return new AttachmentInfoVO();
		}
		AttachmentInfoVO attachmentInfoVO = Objects.requireNonNull(BeanUtil.copy(attachmentInfo, AttachmentInfoVO.class));

		//User createUser = UserCache.getUser(attachmentInfo.getCreateUser());
		//User updateUser = UserCache.getUser(attachmentInfo.getUpdateUser());
		//attachmentInfoVO.setCreateUserName(createUser.getName());
		//attachmentInfoVO.setUpdateUserName(updateUser.getName());

		return attachmentInfoVO;
	}


}

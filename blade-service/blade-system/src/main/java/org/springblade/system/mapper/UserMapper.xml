<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userResultMap" type="org.springblade.system.entity.User">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="code" property="code"/>
        <result column="user_type" property="userType"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="real_name" property="realName"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="birthday" property="birthday"/>
        <result column="sex" property="sex"/>
        <result column="role_id" property="roleId"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
        <result column="first_name" property="firstName"/>
        <result column="last_name" property="lastName"/>
        <result column="phone_dialling_code" property="phoneDiallingCode"/>
        <result column="user_from" property="userFrom"/>
    </resultMap>

    <select id="selectUserPage" resultMap="userResultMap">
        select * from blade_user where is_deleted = 0
        <if test="tenantId!=null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
        <if test="user.tenantId!=null and user.tenantId != ''">
            and tenant_id = #{user.tenantId}
        </if>
        <if test="user.account!=null and user.account != ''">
            and account = #{user.account}
        </if>
        <if test="user.realName!=null and user.realName != ''">
            and real_name = #{user.realName}
        </if>
        <if test="user.name!=null and user.name != ''">
            and name = #{user.name}
        </if>
        <if test="user.userType!=null and user.userType != ''">
            and user_type = #{user.userType}
        </if>
        <if test="deptIdList!=null and deptIdList.size>0">
            and id in (
            SELECT
            user_id
            FROM
            blade_user_dept
            WHERE
            dept_id IN
            <foreach collection="deptIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ORDER BY id
    </select>

    <select id="getUser" resultMap="userResultMap">
        SELECT *
        FROM blade_user
        WHERE tenant_id = #{param1}
          and (account = #{param2} OR phone = #{param2} OR email = #{param2})
          and is_deleted = 0
    </select>

    <select id="exportUser" resultType="org.springblade.system.excel.UserExcel">
        SELECT id,
               tenant_id,
               user_type,
               account,
               name,
               real_name,
               email,
               phone,
               birthday,
               role_id,
               dept_id,
               post_id
        FROM blade_user ${ew.customSqlSegment}
    </select>

    <update id="updateUser">
        update blade_user
        set password=#{userInfo.newPassword}
        where is_deleted = 0
          and ( email = #{userInfo.userAccount} or account = #{userInfo.userAccount} or phone = #{userInfo.userAccount})
    </update>

    <select id="userInfoByCondition" resultMap="userResultMap">
        SELECT * FROM blade_user where is_deleted = 0
        <if test="params.tenantId!=null and params.tenantId!=''">
            and tenant_id = #{params.tenantId}
        </if>
        <if test="params.email!=null and params.email!=''">
            and ( email = #{params.email} or account = #{params.email} )
        </if>
        <if test="params.phone!=null and params.phone!=''">
            and phone = #{params.phone}
        </if>
        <if test="params.telephoneOrUserName!=null and params.telephoneOrUserName!=''">
            and ( phone like CONCAT(#{params.telephoneOrUserName}, '%') or real_name like
            CONCAT(#{params.telephoneOrUserName}, '%') )
        </if>
        <if test="params.phoneDiallingCode!=null and params.phoneDiallingCode!=''">
            and phone_dialling_code = #{params.phoneDiallingCode}
        </if>
    </select>

    <update id="appendRole">
        update blade_user set role_id = CONCAT(role_id , ',' , #{roleIds}),update_user = #{userId},
        <if test="deptId !=null">
            dept_id = (case dept_id when nullif(dept_id,'') then CONCAT(dept_id , ',' , #{deptId}) else #{deptId} end),
        </if>
        update_time = now()
        where id = #{userId}
    </update>

    <select id="roleUser" resultMap="userResultMap">
        select id,
               tenant_id,
               user_type,
               account,
               name,
               real_name,
               email,
               phone,
               first_name,
               last_name,
               phone_dialling_code
        from blade_user u
        where u.is_deleted = 0
          and find_in_set(#{roleId}, u.role_id)
          and tenant_id = #{tenantId}
    </select>

    <select id="listAllByUser" resultType="org.springblade.system.entity.User">
        select id,real_name,phone,phone_dialling_code,account from blade_user u where u.id in
        <foreach collection="userId" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="singleDayRegisterInfo" resultType="org.springblade.system.vo.UserStatisticsVO">
        select DATE_FORMAT(bu.create_time, '%Y-%m-%d') as registrationDate,
        count(1) as quantity
        from blade_user bu
        left join blade_user_app bua on bu.id = bua.user_id
        where bu.is_deleted = 0
        and bu.user_from = 'register'
        and bu.tenant_id = #{ew.tenantId}
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and bua.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and bua.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and bua.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and bu.create_time <![CDATA[ >= ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and bu.create_time <![CDATA[ <= ]]> DATE_FORMAT(#{ew.endDate}, '%Y-%m-%d 23:59:59')
        </if>
        group by DATE_FORMAT(bu.create_time, '%Y-%m-%d')
    </select>

    <select id="singleDayRegisterExcelInfo" resultType="org.springblade.system.vo.UserRegistrationVO">
        select
        concat(ifnull(bua.country_code, ''), '@', ifnull(bua.province_code, ''), '@', ifnull(bua.city_code, ''), '@',
        DATE_FORMAT(bu.create_time, '%Y-%m-%d')) as groupUniqueKey,
        count(1) as numberOfAppUserRegistrations
        from blade_user bu
        left join blade_user_app bua on bu.id = bua.user_id
        where bu.is_deleted = 0
        and bu.user_from = 'register'
        and bu.tenant_id = #{ew.tenantId}
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and bua.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and bua.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and bua.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and bu.create_time <![CDATA[ >= ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and bu.create_time <![CDATA[ <= ]]> DATE_FORMAT(#{ew.endDate}, '%Y-%m-%d 23:59:59')
        </if>
        group by groupUniqueKey
    </select>

    <select id="accumulatedRegistrationData" resultType="java.lang.Long">
        select  count(1) as quantity
        from blade_user bu
        left join blade_user_app bua on bu.id = bua.user_id
        where bu.is_deleted = 0
        and bu.user_from = 'register'
        and bu.tenant_id = #{ew.tenantId}
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and bua.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and bua.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and bua.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and bu.create_time <![CDATA[ < ]]> DATE_FORMAT(#{ew.beginDate}, '%Y-%m-%d 00:00:00')
        </if>
    </select>

    <select id="selectMappingUser" resultMap="userResultMap">
        select * from blade_user bu where id in (
        select distinct mapping_user_id from blade_user_mapping where
        source_user_id in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted =0
        )

    </select>
    <select id="selectMappingSourceUser" resultMap="userResultMap">
        select * from blade_user bu where id in (
        select distinct source_user_id from blade_user_mapping where
        mapping_user_id in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <select id="getUserIgnoreTenant" resultMap="userResultMap">
        select * from blade_user bu where bu.is_deleted =0
        <if test="user.tenantId!=null and user.tenantId!=''">
            and bu.tenant_id = #{user.tenantId}
        </if>
        <if test="user.phone!=null and user.phone!=''">
            and bu.phone = #{user.phone}
        </if>
        <if test="user.phoneDiallingCode!=null and user.phoneDiallingCode!=''">
            and bu.phone_dialling_code = #{user.phoneDiallingCode}
        </if>
        <if test="user.id!=null">
            and bu.id = #{user.id}
        </if>
    </select>
    <update id="updateUserIgnoreTenant">
        update blade_user set password = #{user.password},email= #{user.email},sex = #{user.sex},
                              real_name = #{user.realName},first_name = #{user.firstName},account= #{user.account},
                              last_name = #{user.lastName},phone= #{user.phone},name= #{user.name},
                              phone_dialling_code=#{user.phoneDiallingCode},
                              role_id = #{user.roleId},update_user = #{user.id},
                              dept_id = #{user.deptId},
                              update_time = #{user.updateTime}
        where id = #{user.id}
    </update>

    <select id="batchGetUserIgnoreTenant" resultMap="userResultMap">
        select * from blade_user bu where bu.is_deleted =0 and bu.id in
        <foreach collection="userIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="batchGetClientUserByEpcUserIgnoreTenant" resultMap="userInfoMap">
        select bu.*,um.source_user_id ,um.mapping_user_id from blade_user bu inner join
        blade_user_mapping um on bu.id
        =um.mapping_user_id and um.is_deleted =0
        where bu.is_deleted =0 and um.source_user_id in
        <foreach collection="userIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateUserIgnoreTenant">
        <foreach collection="listUser" item="user" separator=";">
            update blade_user set password = #{user.password},email= #{user.email},sex = #{user.sex},
            real_name = #{user.realName},first_name = #{user.firstName},account= #{user.account},
            last_name = #{user.lastName},phone= #{user.phone},name= #{user.name},
            phone_dialling_code=#{user.phoneDiallingCode},
            role_id = #{user.roleId},update_user = #{user.id},
            dept_id = #{user.deptId},
            update_time = #{user.updateTime}
            where id = #{user.id}
        </foreach>
    </update>

    <resultMap id="userInfoMap" type="org.springblade.system.vo.UserInfoMappingVO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="name" property="name"/>
        <result column="real_name" property="realName"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="sex" property="sex"/>
        <result column="role_id" property="roleId"/>
        <result column="dept_id" property="deptId"/>
        <result column="post_id" property="postId"/>
        <result column="first_name" property="firstName"/>
        <result column="last_name" property="lastName"/>
        <result column="phone_dialling_code" property="phoneDiallingCode"/>
        <result column="user_from" property="userFrom"/>
        <result column="source_user_id" property="sourceUserId"/>
        <result column="mapping_user_id" property="mappingUserId"/>
    </resultMap>

    <update id="updateUserListInfo">
        <foreach collection="list" index="index" item="item" separator=";" >
            update
            blade_user a
            left join blade_user_mapping b on
            a.id = b.mapping_user_id
            left join blade_user c on
            c.id = b.source_user_id
            set
            a.dept_id = if(find_in_set(#{item.deptId},ifnull(a.dept_id,''))=0 ,concat(ifnull(a.dept_id,''),if(a.dept_id is null or a.dept_id ='','',','),#{item.deptId}),ifnull(a.dept_id,'')),
            a.update_user = #{updateUserId},
            a.update_time = now()
            where
            c.id = #{item.id}
            and a.is_deleted = 0
            and b.is_deleted = 0
            and c.is_deleted = 0
        </foreach>
    </update>

    <update id="deleteUserListInfo">
        <foreach collection="list" index="index" item="item" separator=";">
            update
            blade_user a
            left join blade_user_mapping b on
            a.id = b.mapping_user_id
            left join blade_user c on
            c.id = b.source_user_id
            set
            a.dept_id = replace(TRIM(both ',' from replace(concat(',', replace(ifnull(a.dept_id,''),',',',,'), ','), concat(',', #{item.deptId}, ','), ',')),',,',','),
            a.update_user = #{updateUserId},
            a.update_time = now()
            where
            c.id = #{item.id}
            and a.is_deleted = 0
            and b.is_deleted = 0
            and c.is_deleted = 0
        </foreach>
    </update>

    <update id="deleteAgentRollOutManager">
        <foreach collection="list" index="index" item="item" separator=";">
            update
            blade_user a
            set
            a.dept_id = replace(TRIM(both ',' from replace(concat(',', replace(ifnull(a.dept_id,''),',',',,'), ','), concat(',',
            #{item.deptId}, ','), ',')),',,',','),
            a.update_user = #{updateUserId},
            a.update_time = now()
            where
            a.id = #{item.id}
            and a.is_deleted = 0
        </foreach>
    </update>

    <select id="getUserWithPhoneDiallingCode" resultMap="userResultMap">
        SELECT *
        FROM blade_user
        <where>
            tenant_id = #{param1}
            and (account = #{param2} OR phone = #{param2} OR email = #{param2})
            and is_deleted = 0
            <if test="phoneDiallingCode!=null and phoneDiallingCode!=''">
                and phone_dialling_code = #{phoneDiallingCode}
            </if>
        </where>
    </select>
</mapper>

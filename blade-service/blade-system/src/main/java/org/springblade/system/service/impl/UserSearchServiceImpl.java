/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.client.IPlantClient;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.Role;
import org.springblade.system.entity.User;
import org.springblade.system.entity.UserApp;
import org.springblade.system.mapper.UserMapper;
import org.springblade.system.service.IRegionService;
import org.springblade.system.service.IRoleService;
import org.springblade.system.service.IUserAppService;
import org.springblade.system.service.IUserSearchService;
import org.springblade.system.vo.UserRegistrationVO;
import org.springblade.system.vo.UserStatisticsVO;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户查询服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserSearchServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserSearchService {
	private final IPlantClient plantClient;
	private final IUserAppService userAppService;
	private final IRegionService regionService;
	private final IRoleService roleService;

	@Override
	public List<User> listByUser(List<Long> userId) {
		return this.list(Wrappers.<User>lambdaQuery().in(User::getId, userId));
	}

	@Override
	public List<User> listByDept(List<Long> deptId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		deptId.forEach(id -> queryWrapper.like(User::getDeptId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByPost(List<Long> postId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		postId.forEach(id -> queryWrapper.like(User::getPostId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listByRole(List<Long> roleId) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		roleId.forEach(id -> queryWrapper.like(User::getRoleId, id).or());
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listAllByUser(List<Long> userId) {
		return baseMapper.listAllByUser(userId);
	}

	@Override
	public List<UserStatisticsVO> singleDayRegisterInfo(Map<String, Object> map) {
		return baseMapper.singleDayRegisterInfo(map);
	}

	@Override
	public List<UserRegistrationVO> singleDayRegisterExcelInfo(Map<String, Object> map) {
		return baseMapper.singleDayRegisterExcelInfo(map);
	}

	@Override
	public Long accumulatedRegistrationData(Map<String, Object> map) {
		return baseMapper.accumulatedRegistrationData(map);
	}

	@Override
	public List<User> selectMappingUser(List<Long> userIds) {
		return baseMapper.selectMappingUser(userIds);
	}

	@Override
	public List<User> selectMappingSourceUser(List<Long> userIds) {
		return baseMapper.selectMappingSourceUser(userIds);
	}

	@Override
	public List<User> selectUserByPhone(String phone) {
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.likeRight(User::getPhone, phone);
		return this.list(queryWrapper);
	}

	@Override
	public List<User> listTenantUserByDept(Long deptId, Long plantId, String tenantId) {
		List<Long> userId = plantClient.queryUnauthorizedUser(plantId).getData();
		LambdaQueryWrapper<User> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(User::getTenantId, tenantId);
		queryWrapper.like(User::getDeptId, deptId);
		if (CollectionUtils.isNotEmpty(userId)) {
			queryWrapper.notIn(User::getId, userId);
		}
		queryWrapper.orderByDesc(User::getId);
		return this.list(queryWrapper);
	}

	@Override
	public Map<Long, JSONObject> userAndReginInfo(List<Long> userIds) {
		Map<Long,JSONObject> jsonObjectMap = new HashMap<>(userIds.size());
		List<User> userList = baseMapper.listAllByUser(userIds);
		if(CollectionUtils.isEmpty(userList)){
			return jsonObjectMap;
		}
		List<UserApp> userAppList = userAppService.queryUserAppInfoByUserIds(userIds);
		Map<Long,UserApp> userAppMap = Optional.ofNullable(userAppList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(UserApp::getUserId, Function.identity(),(a,b)->a));
		List<String> regionCodes = new ArrayList<>();
		Optional.ofNullable(userAppList).orElse(new ArrayList<>()).forEach(a -> {
			if (StringUtils.isNotBlank(a.getCountryCode())) {
				regionCodes.add(a.getCountryCode());
			}
			if (StringUtils.isNotBlank(a.getCityCode())) {
				regionCodes.add(a.getCityCode());
			}
		});
		List<Region> regionList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(regionCodes)) {
			regionList = regionService.getRegionList(regionCodes);
		}
		Map<String,String> regionMap = Optional.ofNullable(regionList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(Region::getCode, Region::getName,(a,b)->a));
		userList.forEach(a->{
			JSONObject jsonObject = new JSONObject();
			Long userId = a.getId();
			jsonObject.put("userName", a.getRealName());
			jsonObject.put("userPhone", a.getPhone());
			UserApp userApp = userAppMap.get(userId);
			if(userApp != null){
				jsonObject.put("countryName",regionMap.get(userApp.getCountryCode()));
				jsonObject.put("cityName",regionMap.get(userApp.getCityCode()));
			}
			jsonObjectMap.put(userId,jsonObject);
		});
		return jsonObjectMap;
	}

	@Override
	public List<User> listByRoleName(String roleName) {
		if (StringUtils.isBlank(roleName)) {
			return new ArrayList<>();
		}
		Role role =
			roleService.getOne(Wrappers.<Role>lambdaQuery().eq(Role::getRoleName, roleName).eq(Role::getIsDeleted,
			BladeConstant.DB_NOT_DELETED));
		if(role == null){
			return new ArrayList<>();
		}
		return this.list(Wrappers.<User>lambdaQuery().like(User::getRoleId, role.getId()).eq(User::getIsDeleted,
			BladeConstant.DB_NOT_DELETED));
	}
}

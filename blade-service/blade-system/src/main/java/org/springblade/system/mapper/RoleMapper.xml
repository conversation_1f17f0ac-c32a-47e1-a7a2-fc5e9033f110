<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleResultMap" type="org.springblade.system.entity.Role">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="role_name" property="roleName"/>
        <result column="sort" property="sort"/>
        <result column="role_alias" property="roleAlias"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="role_type" property="roleType"/>
        <result column="role_code" property="roleCode"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="code" property="code"/>
    </resultMap>

    <select id="selectRolePage" resultMap="roleResultMap">
        select * from blade_role where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, role_name as title, id as "value", id as "key" , role_code as code from blade_role where
        is_deleted = 0
        <if test="param1!=null">
            and tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and role_alias &lt;&gt; #{param2}
        </if>
    </select>

    <select id="getRoleNames" resultType="java.lang.String">
        SELECT
        role_name
        FROM
        blade_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getRoleAliases" resultType="java.lang.String">
        SELECT
        role_alias
        FROM
        blade_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getRoleList" resultMap="roleResultMap">
        SELECT
        id, role_name,role_alias, role_type,role_code
        FROM
        blade_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getDuplicateRole" resultMap="roleResultMap">
        SELECT id, role_name,role_alias, role_type,role_code
        FROM blade_role
        WHERE is_deleted = 0
        and ( role_name = #{role.roleName} or role_alias = #{role.roleName} or role_code = #{role.roleCode})
        and role_type = #{role.roleType}
    </select>

    <select id="getRoleIgnoreTenant" resultMap="roleResultMap">
        SELECT id, role_name,role_alias, role_type,role_code
        FROM blade_role
        WHERE is_deleted = 0
        and role_code in
        <foreach collection="roleCodeList" item="roleCode" index="index" open="(" close=")" separator=",">
            #{roleCode}
        </foreach>
        and tenant_id = #{tenantId}
    </select>
</mapper>

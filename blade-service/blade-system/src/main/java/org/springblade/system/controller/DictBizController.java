/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.apache.commons.text.StringEscapeUtils;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.service.IDictBizService;
import org.springblade.system.vo.DictBizVO;
import org.springblade.system.wrapper.DictBizWrapper;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springblade.core.cache.constant.CacheConstant.DICT_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/dict-biz")
@Api(value = "业务字典", tags = "业务字典")
public class DictBizController extends BladeController {

	private final IDictBizService dictService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入dict")
	public R<DictBizVO> detail(DictBiz dict) {
		DictBiz detail = dictService.getOne(Condition.getQueryWrapper(dict));
		return R.data(DictBizWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "字典编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "dictValue", value = "字典名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入dict")
	public R<List<DictBizVO>> list(@ApiIgnore @RequestParam Map<String, Object> dict) {
		List<DictBiz> list = dictService.list(Condition.getQueryWrapper(dict, DictBiz.class).lambda().orderByAsc(DictBiz::getSort));
		return R.data(DictBizWrapper.build().listNodeVO(list));
	}

	/**
	 * 顶级列表
	 */
	@GetMapping("/parent-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "字典编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "dictValue", value = "字典名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入dict")
	public R<IPage<DictBizVO>> parentList(@ApiIgnore @RequestParam Map<String, Object> dict, Query query) {
		return R.data(dictService.parentList(dict, query));
	}

	/**
	 * 子列表
	 */
	@GetMapping("/child-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "字典编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "dictValue", value = "字典名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "parentId", value = "字典名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "列表", notes = "传入dict")
	public R<List<DictBizVO>> childList(@ApiIgnore @RequestParam Map<String, Object> dict, @RequestParam(required = false, defaultValue = "-1") Long parentId) {
		return R.data(dictService.childList(dict, parentId));
	}

	/**
	 * 获取字典树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<DictBizVO>> tree() {
		List<DictBizVO> tree = dictService.tree();
		return R.data(tree);
	}

	/**
	 * 获取字典树形结构
	 */
	@GetMapping("/parent-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<DictBizVO>> parentTree() {
		List<DictBizVO> tree = dictService.parentTree();
		return R.data(tree);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入dict")
	public R submit(@Valid @RequestBody DictBiz dict) {
		CacheUtil.clear(DICT_CACHE);
		processSpecial(dict);
        return R.status(dictService.submit(dict));
	}

	private void processSpecial(DictBiz entity){
		Field[] fields = entity.getClass().getDeclaredFields();
		// 遍历每个字段进行特殊处理
		for (Field field : fields) {
			try {
				if (!Modifier.isFinal(field.getModifiers())) {
					// 设置字段可访问
					field.setAccessible(true);
					// 获取字段的值
					Object value = field.get(entity);
					Object newValue=value;
					if(value instanceof String){
						newValue= StringEscapeUtils.unescapeHtml4((String) value);
					}
					// 更新字段的值
					field.set(entity, newValue);
				}
			} catch (Exception e) {
                throw new BusinessException(e.getMessage());
			}
		}
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(DICT_CACHE);
		return R.status(dictService.removeDict(ids));
	}

	/**
	 * 获取字典
	 */
	@GetMapping("/dictionary")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取字典", notes = "获取字典")
	public R<List<DictBiz>> dictionary(String code) {
		List<DictBiz> tree = dictService.getList(code);
		return R.data(tree);
	}


	/**
	 * 获取字典
	 */
	@GetMapping("/dictionaryByLang")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取字典", notes = "获取字典")
	public R<List<DictBiz>> dictionaryByLang(String code) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<DictBiz> tree = dictService.getListByLang(code,currentLanguage);
		return R.data(tree);
	}

	/**
	 * 获取字典(所有语言)
	 */
	@GetMapping("/dictionaryAllLang")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取字典", notes = "获取字典(所有语言)")
	public R<List<DictBiz>> dictionaryAllLang(String code) {
		List<DictBiz> tree = dictService.getListAllLang(code);
		return R.data(tree);
	}

	/**
	 * 获取字典树
	 */
	@GetMapping("/dictionary-tree")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "获取字典树", notes = "获取字典树")
	public R<List<DictBizVO>> dictionaryTree(String code) {
		List<DictBiz> tree = dictService.getList(code);
		return R.data(DictBizWrapper.build().listNodeVO(tree));
	}

	/**
	 * 字典键值列表
	 */
	@GetMapping("/select")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "字典键值列表", notes = "字典键值列表")
	public R<List<DictBiz>> select() {
		List<DictBiz> list = dictService.list(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getParentId, CommonConstant.TOP_PARENT_ID));
		list.forEach(dict -> dict.setDictValue(dict.getCode() + StringPool.COLON + StringPool.SPACE + dict.getDictValue()));
		return R.data(list);
	}

	/**
	 * 字典全列表
	 */
	@GetMapping("/select-all")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "字典全列表", notes = "字典全列表")
	public R<List<DictBiz>> selectAll() {
		List<DictBiz> list = dictService.list(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getIsDeleted, BladeConstant.DB_NOT_DELETED).orderByAsc(DictBiz::getCode).orderByAsc(DictBiz::getSort));
		return R.data(list);
	}

	/**
	 * 根据字典code查询第2层数据
	 *
	 * @param code 入参
	 * @return R<List < DictBiz>>
	 * <AUTHOR>
	 * @since 2023/10/10 20:01
	 **/
	@GetMapping("/selectLevel2ByCode")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "根据字典code查询第2层数据", notes = "根据字典code查询第2层数据")
	public R<List<DictBiz>> selectLevel2ByCode(String code) {
		String language = CommonUtil.getCurrentLanguage();
		DictBiz dictBiz = dictService.getOne(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getCode, code).eq(DictBiz::getParentId, CommonConstant.TOP_PARENT_ID));
		if (ObjectUtil.isNotEmpty(dictBiz)){
			return R.data(dictService.list(Wrappers.<DictBiz>query().orderByAsc("sort").lambda().eq(DictBiz::getParentId, dictBiz.getId()).eq(DictBiz::getLanguage, language)));
		} else {
			return R.data(Collections.emptyList()); // 返回一个空的结果
		}
	}

	/**
	 * 根据父id查询子节点数据
	 *
	 * @param dictKey 入参
	 * @param code    类型
	 * @return R<List < DictBiz>>
	 * <AUTHOR>
	 * @since 2023/10/10 20:01
	 **/
	@GetMapping("/selectChildByDictKeyAndCode")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "根据父code和dictKey查询子节点数据", notes = "根据父code和dictKey查询子节点数据")
	public R<List<DictBiz>> selectChildByDictKeyAndCode(String dictKey, String code) {
		String language = CommonUtil.getCurrentLanguage();
		DictBiz dictBiz = dictService.getOne(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getCode, code).eq(DictBiz::getDictKey, dictKey).eq(DictBiz::getLanguage, language));
		return R.data(dictService.list(Wrappers.<DictBiz>query().orderByAsc("sort").lambda().eq(DictBiz::getParentId, dictBiz.getId()).eq(DictBiz::getLanguage, language)));
	}

	@GetMapping("/findAllDictMap")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "字典全列表", notes = "字典全列表")
	public R<Map<String, List<DictBiz>>> findAllDictMap() {
		List<DictBiz> list = dictService.list(Wrappers.<DictBiz>query().lambda().eq(DictBiz::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		Map<String, List<DictBiz>> map = list.stream().filter(a -> !CommonConstant.TOP_PARENT_ID.equals(a.getParentId())).collect(Collectors.groupingBy(DictBiz::getCode,
			Collectors.mapping(Function.identity(),
				Collectors.collectingAndThen(Collectors.toList(),
					a -> {
						a.sort(Comparator.comparing(DictBiz::getSort));
						return a;
					})))
		);
		return R.data(map);
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.system.mapper.DictBizMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="dictResultMap" type="org.springblade.system.entity.DictBiz">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="dict_key" property="dictKey"/>
        <result column="dict_value" property="dictValue"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="language" property="language"/>
        <result column="attribute1" property="attribute1"/>
        <result column="attribute2" property="attribute2"/>
        <result column="attribute3" property="attribute3"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <select id="getValue" resultType="java.lang.String">
        select
            dict_value
        from blade_dict_biz where code = #{param1} and dict_key = #{param2} and is_deleted = 0
    </select>

    <!-- oracle 版本 -->
    <!--<select id="getValue" resultType="java.lang.String">
        select
            dict_value
        from blade_dict_biz where code = #{param1, jdbcType=VARCHAR} and dict_key = #{param2} and dict_key >= 0  rownum 1
    </select>-->

    <select id="getList" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from
        blade_dict_biz where code = #{param1} and parent_id > 0 and is_sealed = 0 and is_deleted = 0
        order by sort asc
    </select>


    <select id="getListByLang" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from
            blade_dict_biz where code = #{param1} and parent_id > 0 and is_sealed = 0 and is_deleted = 0
         and language= #{param2}
        order by sort asc
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dict_value as title, id as "value", id as "key" from blade_dict_biz where is_deleted = 0
    </select>

    <select id="parentTree" resultMap="treeNodeResultMap">
        select id, parent_id, dict_value as title, id as "value", id as "key" from blade_dict_biz where is_deleted = 0 and parent_id = 0
    </select>

    <select id="getListAllLang" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from
            blade_dict_biz where code = #{param1} and parent_id > 0 and is_sealed = 0 and is_deleted = 0 order by sort asc
    </select>

    <select id="getRootListAllLang" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from blade_dict_biz
        where code = #{code} and parent_id = #{parentId} and is_deleted = 0 and is_sealed = 0
        order by sort asc
    </select>

    <select id="getListByParentCode" resultMap="dictResultMap">
        select
            a.id,
            a.tenant_id,
            a.parent_id,
            a.code,
            a.dict_key,
            a.dict_value,
            a.sort,
            a.remark,
            a.attribute1,
            a.attribute2,
            a.attribute3,
            a.`language`
        from
            blade_dict_biz a
        where
            a.code = #{code}
          and a.parent_id > 0
          and a.is_sealed = 0
          and a.is_deleted = 0
          and a.`language` = #{language}
          and exists (
            select
                1
            from
                blade_dict_biz b
            where
                a.parent_id = b.id
              and b.dict_key = #{parentCode}
              and b.`language` = #{language}
              and a.code = b.code
        )
    </select>

    <select id="getValueByLang" resultType="java.lang.String">
        select
            dict_value
        from blade_dict_biz where code = #{code} and dict_key = #{dictKey}  and language = #{language} and is_deleted = 0
    </select>
    <select id="batchGetList" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from
        blade_dict_biz where parent_id > 0 and is_sealed = 0 and is_deleted = 0 and code in
        <foreach collection="codeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryChildByDictKey" resultMap="dictResultMap">
        select * from blade_dict_biz b where b.is_deleted = 0 and ( b.parent_id =(
        select id
        from blade_dict_biz where  parent_id > 0 and is_deleted = 0 and language = #{language}
        and code = #{code} and dict_key = #{dictKey}
        ) ) or ( b.code = #{code} and b.dict_key = #{dictKey} and language = #{language} )
    </select>
    <select id="queryChild" resultMap="dictResultMap">
        select * from blade_dict_biz b where b.parent_id =(
        select id
        from blade_dict_biz where  parent_id > 0 and is_sealed = 0 and language = #{language}
        and code = #{code} and dict_key = #{dictKey}
        )
    </select>
    <select id="getDataByCodeAndKey" resultMap="dictResultMap">
        select id, tenant_id, parent_id, code, dict_key, dict_value, sort, remark,attribute1,attribute2,attribute3,language from
        blade_dict_biz where code = #{code} and parent_id > 0 and is_sealed = 0 and is_deleted = 0
        and language= #{lang} and dict_key=#{key}
        order by sort asc
    </select>
</mapper>

package org.springblade.system.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springblade.system.vo.KafkaMsgVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/kafkaMsg")
public class KafkaMsgController {

    @PostMapping("/detail")
    public String kafkaMsgDetail(@RequestBody KafkaMsgVO kafkaMsgVO) {

//            String bootstrapServers = "172.28.17.212:9092";
//            String topic = "lazzen_gateway_common_topic";
//            long startTime = 1733382028000L; // 替换为你的开始时间戳（毫秒）
//            long endTime = 1733383648000L;   // 替换为你的结束时间戳（毫秒）
            String startTimeStr = kafkaMsgVO.getStartTimeStr();
        String endTimeStr = kafkaMsgVO.getEndTimeStr();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析字符串为 LocalDateTime 对象
        LocalDateTime startLocalDateTime = LocalDateTime.parse(startTimeStr, formatter);
        LocalDateTime endLocalDateTime = LocalDateTime.parse(endTimeStr, formatter);
            StringBuilder sb = new StringBuilder();
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaMsgVO.getBootstrapServers());
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "time-range-consumer-group");
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

            try (
                KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props)) {
                TopicPartition partition = new TopicPartition(kafkaMsgVO.getTopic(), 0); // 假设只有一个分区
                consumer.assign(Collections.singletonList(partition));

                Map<TopicPartition, Long> timestampsToSearch = new HashMap<>();
                timestampsToSearch.put(partition, startLocalDateTime.toInstant(ZoneOffset.of("+" + kafkaMsgVO.getUtcStr())).toEpochMilli());

                Map<TopicPartition, OffsetAndTimestamp> offsetsForTimes = consumer.offsetsForTimes(timestampsToSearch);
                if (offsetsForTimes != null && offsetsForTimes.get(partition) != null) {
                    long startOffset = offsetsForTimes.get(partition).offset();
                    consumer.seek(partition, startOffset);

                    boolean running = true;
                    while (running) {
                        ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
                        for (ConsumerRecord<String, String> record : records) {
                            if (record.timestamp() > endLocalDateTime.toInstant(ZoneOffset.of("+" + kafkaMsgVO.getUtcStr())).toEpochMilli()) {
                                running = false;
                                break;
                            }
                            sb.append("Consumed message:").append("value=").append(record.value()).append("timestamp=").append(record.timestamp())
                                    .append("\n");
                        }
                    }
                } else {
                    sb.append("No messages found in the specified time range.");
                }
            }

        return sb.toString();
    }
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * RegionExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(32)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class RegionExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("*Division Code")
	private String code;

	@ExcelProperty("Parent Division Code")
	private String parentCode;

	@ExcelProperty("Ancestors Division Code")
	private String ancestors;

	@ExcelProperty("*Division Name")
	private String name;

	@ExcelProperty("Province Division Code")
	private String provinceCode;

	@ExcelProperty("Province Division Name")
	private String provinceName;

	@ExcelProperty("City Division Code")
	private String cityCode;

	@ExcelProperty("City Division Name")
	private String cityName;

	@ExcelProperty("District Division Code")
	private String districtCode;

	@ExcelProperty("District Division Name")
	private String districtName;

	@ExcelProperty("Town Division Code")
	private String townCode;

	@ExcelProperty("Town Division Name")
	private String townName;

	@ExcelProperty("Village Division Code")
	private String villageCode;

	@ExcelProperty("Village Division Name")
	private String villageName;

	@ExcelProperty("*Division Level")
	private Integer regionLevel;

	@ExcelProperty("*Division Sort")
	private Integer sort;

	@ExcelProperty("Division Remark")
	private String remark;

//	/**
//	 * 区划名称英文（新增）
//	 */
//
//	@ExcelProperty("Division Name(EN)")
//	private String nameEn;
	/**
	 * 国家区划编码（新增）
	 */

	@ExcelProperty("Country Division Code")
	private String countryCode;
//	/**
//	 * 国家名称英文（新增）
//	 */
//
//	@ExcelProperty("Country Division Name(EN)")
//	private String countryNameEn;
	/**
	 * 国家名称（新增）
	 */

	@ExcelProperty("Country Division Name")
	private String countryName;
	/**
	 * 省级名称英文（新增）
	 */

//	@ExcelProperty("Province Division Name(EN)")
//	private String provinceNameEn;
//	/**
//	 * 市级名称英文（新增）
//	 */
//
//	@ExcelProperty("City Division Name(EN)")
//	private String cityNameEn;
//	/**
//	 * 区级名称英文（新增）
//	 */
//
//	@ExcelProperty("District Division Name(EN)")
//	private String districtNameEn;
//	/**
//	 * 镇级名称英文（新增）
//	 */
//
//	@ExcelProperty("Town Division Name(EN)")
//	private String townNameEn;
//	/**
//	 * 村级名称英文（新增）
//	 */
//
//	@ExcelProperty("Village Division Name(EN)")
//	private String villageNameEn;

}

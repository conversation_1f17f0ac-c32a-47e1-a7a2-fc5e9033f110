system.agent.agentNumber.can.not.empty=Rollout manager ID cannot be empty
system.agent.agentNumber.is.not.exits=Distributor ID doesn't exist
system.error=System Error
system.user.account.is.exits=The user account is occupied
system.user.phone.is.exits=The phone number is occupied
system.user.phone.is.exits.to.another.tenant=This number is bound to another account,Please contact the system manager.
system.user.the.verification.code.is.error=the verification code is error
system.user.the.is.not.exist=the user is not exist.
system.user.email.is.exits=The email address has been registered
system.exception.delete.child.nodes=Please delete child nodes first!
system.exception.delete.child.nodes.interface=Please delete the interface permissions under the current menu!
system.dict.key.value.exist=The current dictionary key value already exists!
system.exception.not.resubmit=Please do not resubmit
system.attach.info.empty=Attachment information is empty.
system.exception.code.execution=Business code execution exception
system.exception.cannot.select.parent.itself=The parent node cannot select itself!
system.exception.cannot.config.superadmin.role=No permission to configure super management role!
system.exception.cannot.config.admin.role=No permission to configure administrator role!
system.exception.cannot.create.superadmin.role=No permission to create super management role
system.exception.cannot.delete.super.tenant=super admin tenant cannot be deleted!
system.exception.menu.name.number.exist=Menu name or number already exists!
system.exception.menu.parent.only.menu.type=The parent node can only select the menu type!
system.user.password.cannot.empty=the password can not empty
system.user.tenant.quota.max=The current tenant has reached the maximum account quota
system.user.enter.confirm.password=Please enter the correct confirmation password!
system.user.original.password.incorrect=The original password is incorrect!
system.user.tenant.info.error=Tenant information error!
system.user.third.login.error=Third-party login information error!
system.user.account.connot.delete=This account cannot be deleted!
system.user.account.abnormal=Current user [{%s}] account abnormal!
system.user.account.not.exist=The current user [{%s}] does not exist!
system.user.account.is.exist=Current user [{%s}] already exists!

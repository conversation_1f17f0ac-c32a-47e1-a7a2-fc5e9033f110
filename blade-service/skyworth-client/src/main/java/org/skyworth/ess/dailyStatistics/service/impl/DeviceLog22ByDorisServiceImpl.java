package org.skyworth.ess.dailyStatistics.service.impl;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.mapper.DeviceLog22ByDorisMapper;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.BatteryQueryPowerTypeEnum;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.device.vo.InvertStatusReport;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
@DS("slave")
public class DeviceLog22ByDorisServiceImpl extends BaseServiceImpl<DeviceLog22ByDorisMapper, DeviceLog22> implements DeviceLog22ByDorisService {
	private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BizConstant.PATTERN_HOUR_MINUS);

	@Override
	public List<DeviceLog22> selectDataByLatestTime() {
		return baseMapper.selectDataByLatestTime();
	}

	@Override
	public List<DeviceLog22> selectDailyData(String beginTime, String endTime) {
		return baseMapper.selectDailyData(beginTime, endTime);
	}

	@Override
	public List<JSONObject> stateCurve(QueryDeviceLog22Condition queryCondition) {
		if (queryCondition.getDate() == null
			|| queryCondition.getPlantId() == null || StringUtils.isBlank(queryCondition.getDeviceSerialNumber())
		) {
			throw new BusinessException("client.battery.query.connot.empty");
		}
		return dailyEstimateAbscissa(queryCondition);
	}

	private List<JSONObject> dailyEstimateAbscissa(QueryDeviceLog22Condition queryCondition) {
		List<JSONObject> newlist = new ArrayList<>();
		String date = queryCondition.getDate();
		if (ValidationUtil.isNotEmpty(date)) {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			try {
				LocalDate localDate = LocalDate.parse(date, formatter);
				ZoneId zoneId = ZoneId.systemDefault();
				ZonedDateTime startOfDay = localDate.atStartOfDay(zoneId);
				ZonedDateTime endOfDay = localDate.atTime(LocalTime.MAX).atZone(zoneId);
				Date startDate = Date.from(startOfDay.toInstant());
				Date endDate = Date.from(endOfDay.toInstant());
				queryCondition.setStartDateTime(startDate);
				queryCondition.setEndDateTime(endDate);
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		}
		List<JSONObject> statusList = baseMapper.stateCurve(queryCondition);
		fillEmptyObjects(queryCondition, statusList, newlist);
		newlist = getJsonObjects(queryCondition, newlist);
		return newlist;
	}

	/**
	 * 数据导出
	 */
	private List<JSONObject> dailyEstimateAbscissaExport(QueryDeviceLog22Condition queryCondition) {
		List<JSONObject> newlist = new ArrayList<>();
		// 全列导出
		queryCondition.setQueryPowerType("batteryPower,overallVoltage,batterySOC,cellVoltage,cellTemperature,parallel_batteryPower");
		List<JSONObject> statusList = baseMapper.stateCurve(queryCondition);
		fillEmptyObjects(queryCondition, statusList, newlist);
		newlist = getJsonObjects(queryCondition, newlist);
		return newlist;
	}



	/**
	 * 对没有数据的进行补零
	 */
	private static List<JSONObject> getJsonObjects(QueryDeviceLog22Condition queryCondition, List<JSONObject> newlist) {
		Date startDate = queryCondition.getStartDateTime();
		Date endDate = queryCondition.getEndDateTime();
		String queryPowerType = queryCondition.getQueryPowerType();
		List<Date> dateList = getSupplementDate(startDate, endDate);
		SimpleDateFormat formatHour = new SimpleDateFormat("HH:mm");
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat formatYYYYMMdd = new SimpleDateFormat("yyyy-MM-dd");
		List<JSONObject> res = new ArrayList<>();
		Map<String, JSONObject> dateJSONObjectMap = initDateMap(startDate, endDate,formatHour,format,queryPowerType,res);

		if (!CollectionUtils.isNullOrEmpty(newlist)) {
			for (JSONObject obj : newlist) {
				String deviceDateTime = obj.getString(EntityFieldConstant.DEVICE_DATETIME);
				Date parse = null;
				try {
					 parse = format.parse((obj.getString(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL)));
				} catch (ParseException e) {
					throw new RuntimeException(e);
				}
				String startDateKey = null;
				int count = 0;
				// 先取初始化map中的 第一个 startDateKey ，然后将 第一个 和 第二个作为一个区间， 如果数据不在此区间，将 第二个作为第一个，开始下一个区间
				// 如果相等，则赋值 到 value上，跳出循环
				// 数据库查询按照升序排列，实际值如果为 00:08, 00:09 两条, 则比较顺序为： 00:00  00:05 -> 00:05  00:10 ，最终会取 00:09的落在 00:10 区间
				for (Map.Entry<String, JSONObject> entry : dateJSONObjectMap.entrySet()) {
					// 取第一个作为 首个比较对象
					if (count == 0) {
						startDateKey = entry.getKey();
						count++;
						continue;
					}
					String endDateKey = entry.getKey();
					if (deviceDateTime.compareTo(startDateKey) >= 0 && deviceDateTime.compareTo(endDateKey) < 0) {
						conditionalEncapsulationJson(obj, formatHour,parse , format, queryPowerType, res,startDateKey,
							formatYYYYMMdd);
						break;
					} else {
						startDateKey = endDateKey;
					}
				}
			}
		}
//			int i = 0;
//			for (JSONObject obj : newlist) {
//				LocalDateTime deviceDateTime = LocalDateTime.parse(obj.getString(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//				try {
//					boolean flag = false;
//					while (!flag) {
//						Date tmpDate = dateList.get(i);
//						Instant instant = deviceDateTime.atZone(ZoneId.systemDefault()).toInstant();
//						long diffInMillis = Math.abs(instant.toEpochMilli() - tmpDate.getTime());
//						long diffInMinutes = diffInMillis / (60 * 1000);
//						if (diffInMinutes <= 5) {
//							conditionalEncapsulationJson(obj, formatHour, tmpDate, format, queryPowerType, res);
//							flag = true;
//						} else {
//							initJsonObject(formatHour, tmpDate, format, res, queryPowerType);
//						}
//						++i;
//					}
//				} catch (Exception e) {
//					log.error(e.getMessage());
//				}
//			}
//		} else {
//			dateList.forEach(v -> {
//				initJsonObject(formatHour, v, format, res, queryPowerType);
//			});
//		}

		newlist = res;
		newlist.remove(288);
		return newlist;
	}

	/**
	 * 按条件封装数据
	 *
	 * @param obj            数据对象
	 * @param formatHour     日期格式
	 * @param tmpDate        时间
	 * @param format         时间
	 * @param queryPowerType 查询条件
	 * @param res            入参
	 * <AUTHOR>
	 * @since 2024/6/23 15:11
	 **/
	private static void conditionalEncapsulationJson(JSONObject obj, SimpleDateFormat formatHour, Date tmpDate,
													 SimpleDateFormat format, String queryPowerType, List<JSONObject> res,String initDateStr
		,SimpleDateFormat formatYYYYMMdd) {

		res.forEach(objNew -> {
			String deviceDateTimeForCal1 = null;
			try {
				deviceDateTimeForCal1 = formatYYYYMMdd.format(DateUtil.parseStringToDate((String) objNew.get("deviceDateTimeForCal"), "yyyy-MM-dd"));
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}

			if ((initDateStr.equals(objNew.get("deviceDateTime") ) || "24:00".equals(objNew.get("deviceDateTime")))
				&& formatYYYYMMdd.format(tmpDate).equals(deviceDateTimeForCal1)) {
				objNew.put("deviceDateTime", initDateStr);
				objNew.put("deviceDateTimeForCal", format.format(tmpDate));
				if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_POWER.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_POWER, obj.getBigDecimal(EntityFieldConstant.BATTERY_POWER));
				}
				if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.OVERALL_VOLTAGE.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_CURRENT, obj.getBigDecimal(EntityFieldConstant.BATTERY_CURRENT));
					objNew.put(EntityFieldConstant.BATTERY_VOLTAGE, obj.getBigDecimal(EntityFieldConstant.BATTERY_VOLTAGE));
				}
				if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_VOLTAGE.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_MINIMUMCELL_VOLTAGE, obj.getBigDecimal(EntityFieldConstant.BATTERY_MINIMUMCELL_VOLTAGE));
					objNew.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_VOLTAGE, obj.getBigDecimal(EntityFieldConstant.BATTERY_MAXIMUMCELL_VOLTAGE));
				}
				if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_TEMPERATURE.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_MINIMUMCELL_TEMPERATURE, obj.getBigDecimal(EntityFieldConstant.BATTERY_MINIMUMCELL_TEMPERATURE));
					objNew.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_TEMPERATURE, obj.getBigDecimal(EntityFieldConstant.BATTERY_MAXIMUMCELL_TEMPERATURE));
				}
				if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_SOC.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_SOC, obj.getBigDecimal(EntityFieldConstant.BATTERY_SOC));
				}
				/*if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.PARALLEL_BATTERY_POWER.getPowerType())) {
					objNew.put(EntityFieldConstant.BATTERY_POWER_SUM, obj.getBigDecimal(EntityFieldConstant.BATTERY_POWER_SUM));
				}*/
			}

		});


	}

	private static void initJsonObject(SimpleDateFormat formatHour, Date tmpDate, SimpleDateFormat format, List<JSONObject> res, String queryPowerType) {
		JSONObject jsonObject = new JSONObject();
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_POWER.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_POWER, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.OVERALL_VOLTAGE.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_CURRENT, "0.00");
			jsonObject.put(EntityFieldConstant.BATTERY_VOLTAGE, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_VOLTAGE.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_MINIMUMCELL_VOLTAGE, "0.00");
			jsonObject.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_VOLTAGE, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_TEMPERATURE.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_MINIMUMCELL_TEMPERATURE, "0.00");
			jsonObject.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_TEMPERATURE, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_SOC.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_SOC, "0.00");
		}
		/*if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.PARALLEL_BATTERY_POWER.getPowerType())) {
			jsonObject.put(EntityFieldConstant.BATTERY_POWER_SUM, "0.00");
		}*/
		jsonObject.put("deviceDateTime", formatHour.format(tmpDate));
		jsonObject.put("deviceDateTimeForCal", format.format(tmpDate));
		res.add(jsonObject);
	}

	private static List<Date> getSupplementDate(Date startTime, Date endTime) {
		long startLongTime = startTime.getTime();
		long endLongTime = endTime.getTime();
		List<Date> set = new ArrayList<>();
		LinkedHashMap<Date, JSONObject> map = new LinkedHashMap<>();
		for (long time = startLongTime; time <= endLongTime; ) {
			Date date = new Date(time);
			set.add(date);
			map.put(date, new JSONObject());
			time += 5000L * 60;
		}
		return set;
	}

	public static Map<String, JSONObject> initDateMap(Date startTime, Date endTime,SimpleDateFormat formatHour,SimpleDateFormat format,
													String queryPowerType,	List<JSONObject> res) {
		long startLongTime = startTime.getTime();
		long endLongTime = endTime.getTime();
		LinkedHashMap<String, JSONObject> map = new LinkedHashMap<>();
		for (long time = startLongTime; time <= endLongTime; ) {
			Date date = new Date(time);
			map.put(formatHour.format(date), new JSONObject());
			initJsonObject(formatHour, date, format, res, queryPowerType);
			time += 5000L * 60;
		}
		map.put("24:00", new JSONObject());
		initJsonObjectFor24(formatHour, endTime, format, res);
		return map;
	}

	private static void initJsonObjectFor24(SimpleDateFormat formatHour, Date tmpDate, SimpleDateFormat format, List<JSONObject> res) {
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("batteryCurrent","");
		jsonObject.put("batteryMaximumCellTemperature","");
		jsonObject.put("batteryMaximumCellVoltage","");
		jsonObject.put("batteryMinimumCellTemperature","");
		jsonObject.put("batteryMinimumCellVoltage","");
		jsonObject.put("batteryVoltage","");
		jsonObject.put("batteryPower","");
		jsonObject.put("batterySoc","");
		jsonObject.put("deviceDateTime","24:00");
		jsonObject.put("deviceDateTimeForCal",format.format(tmpDate));
		res.add(jsonObject);
	}
	private void fillEmptyObjects(QueryDeviceLog22Condition queryCondition, List<JSONObject> statusList, List<JSONObject> newlist) {
		if (!statusList.isEmpty()) {
			// 第一条数据
			Map<String, Object> firstData = statusList.get(0);
			LocalDateTime firstDeviceDateTime = LocalDateTime.parse((String) firstData.get(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			// 最后一条数据
			Map<String, Object> lastData = statusList.get(statusList.size() - 1);
			LocalDateTime lastDeviceDateTime = LocalDateTime.parse((String) lastData.get(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			// 查询时间进行转换
			Date startDateTime = queryCondition.getStartDateTime();
			Date endDateTime = queryCondition.getEndDateTime();
			String queryPowerType = queryCondition.getQueryPowerType();
			LocalDateTime sldt = LocalDateTime.ofInstant(startDateTime.toInstant(), ZoneId.systemDefault());
			LocalDateTime eldt = LocalDateTime.ofInstant(endDateTime.toInstant(), ZoneId.systemDefault());
			// 如果查询开始时间在第一条数据的时间-5mins之前，则造出一条数据填充图表
			LocalDateTime firstDeviceDateTimeMinus = firstDeviceDateTime.minusMinutes(5);
			while (sldt.isBefore(firstDeviceDateTimeMinus)) {
				buildDataObject(newlist, firstDeviceDateTimeMinus, queryPowerType);
				firstDeviceDateTimeMinus = firstDeviceDateTimeMinus.minusMinutes(5);
			}
			// 按时间顺序翻转
			Collections.reverse(newlist);
			newlist.addAll(statusList);
			// 如果查询结束时间在最后一条条数据的时间+5mins之后，则造出一条数据填充图表
			LocalDateTime lastDeviceDateTimePlus = lastDeviceDateTime.plusMinutes(5);
			while (eldt.isAfter(lastDeviceDateTimePlus)) {
				buildDataObject(newlist, lastDeviceDateTimePlus, queryPowerType);
				lastDeviceDateTimePlus = lastDeviceDateTimePlus.plusMinutes(5);
			}
		}
	}

	private static boolean includeQueryPowerType(String queryPowerType, String powerType) {
		queryPowerType = CommonConstant.SYMBOL_COMMA + queryPowerType + CommonConstant.SYMBOL_COMMA;
		return queryPowerType.toLowerCase().contains(CommonConstant.SYMBOL_COMMA + powerType.toLowerCase() + CommonConstant.SYMBOL_COMMA);
	}

	/**
	 * 返回空对象
	 *
	 * @param newlist       对象
	 * @param localDateTime 时间格式
	 */
	private void buildDataObject(List<JSONObject> newlist, LocalDateTime localDateTime, String queryPowerType) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BizConstant.PATTERN_HOUR_MINUS);
		JSONObject objectHashMap = new JSONObject();
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_POWER.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_POWER, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.OVERALL_VOLTAGE.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_VOLTAGE, "0.00");
			objectHashMap.put(EntityFieldConstant.BATTERY_CURRENT, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_VOLTAGE.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_VOLTAGE, "0.00");
			objectHashMap.put(EntityFieldConstant.BATTERY_MINIMUMCELL_VOLTAGE, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.CELL_TEMPERATURE.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_MAXIMUMCELL_TEMPERATURE, "0.00");
			objectHashMap.put(EntityFieldConstant.BATTERY_MINIMUMCELL_TEMPERATURE, "0.00");
		}
		if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.BATTERY_SOC.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_SOC, "0.00");
		}
		/*if (includeQueryPowerType(queryPowerType, BatteryQueryPowerTypeEnum.PARALLEL_BATTERY_POWER.getPowerType())) {
			objectHashMap.put(EntityFieldConstant.BATTERY_POWER_SUM, "0.00");
		}*/
		objectHashMap.put(EntityFieldConstant.DEVICE_DATETIME, localDateTime.format(formatter));
		objectHashMap.put(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL, localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
		newlist.add(objectHashMap);
	}

	@Override
	public List<InvertStatusReport> selectStatusReportByTime(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.selectStatusReportByTime(queryCondition);
	}

	@Override
	public List<DeviceLog22VO> appReportEstimate(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appReportEstimate(queryCondition);
	}

	@Override
	public List<DeviceLog22VO> appReportEstimateV2(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appReportEstimateV2(queryCondition);
	}

	@Override
	public List<DeviceLog22VO> appParallelReportEstimateV2(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appParallelReportEstimateV2(queryCondition);
	}

	@Override
	public DeviceLog22VO appDailyFromPvAndGrid(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appDailyFromPvAndGrid(queryCondition);
	}

	@Override
	public List<JSONObject> inverterAndLoadSummaryStateCurve() {
		return getSummaryStateCurve();
	}

	@Override
	public void selectStatusReportExport(QueryDeviceLog22Condition queryCondition, HttpServletResponse response) {
		try {
			String fileName = URLEncoder.encode("储能每日功率", StandardCharsets.UTF_8.name());
			fileName = fileName.replace(" ", "%20");
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

			List<JSONObject> dailyEstimateAbscissaExport = dailyEstimateAbscissaExport(queryCondition);
			//分钟
			List<String> distinctMinTime = dailyEstimateAbscissaExport.stream().
				map(jsonObject -> jsonObject.getString("deviceDateTime")).distinct().collect(Collectors.toList());
			//天
			List<String> firstHeadList = dailyEstimateAbscissaExport.stream()
				.map(obj -> LocalDateTime.parse(obj.getString("deviceDateTimeForCal"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate())
				.sorted(Comparator.naturalOrder())
				.map(dateTime -> dateTime.format(timeFormatter)).distinct()
				.collect(Collectors.toList());


			List<String> secondHead4Power = Arrays.asList("储能功率(W)");
			List<String> secondHead4VoltageAndCur = Arrays.asList("电压(V)", "电流(A)");
			List<String> secondHead4Soc = Arrays.asList("储能SOC(%)");
			List<String> secondHead4Cell = Arrays.asList("储能最大单体电压(V)", "储能最小单体电压(V)");
			List<String> secondHead4Temp = Arrays.asList("储能最大单体温度(℃)", "储能最小单体温度(℃)");
			//List<String> secondHead4PowerSum = Arrays.asList("Battery power sum(W)");

			List<List<String>> powerDataList = new ArrayList<>();
			List<List<String>> volAndCurDataList = new ArrayList<>();
			List<List<String>> socDataList = new ArrayList<>();
			List<List<String>> cellDataList = new ArrayList<>();
			List<List<String>> tempDataList = new ArrayList<>();
			//List<List<String>> powerSumDataList = new ArrayList<>();

			// 根据时间分组、再根据日期分组
			Map<String, Map<String, JSONObject>> groupedMap = dailyEstimateAbscissaExport.stream()
				.collect(Collectors.groupingBy(jsonObject -> jsonObject.getString("deviceDateTime"),
					Collectors.toMap(jsonObject -> {
							try {
								return formatter.format(formatter.parse(jsonObject.getString("deviceDateTimeForCal")));
							} catch (ParseException e) {
								throw new ServiceException(e.getMessage());
							}
						},
						Function.identity(), (existing, duplicate) -> existing)));


			// 循环外层：按每5分钟的行数据
			for (String mintStr : distinctMinTime) {
				List<String> rowPower = Lists.newArrayList(mintStr);
				List<String> rowVolAndCur = Lists.newArrayList(mintStr);
				List<String> rowSoc = Lists.newArrayList(mintStr);
				List<String> rowCell = Lists.newArrayList(mintStr);
				List<String> rowTemp = Lists.newArrayList(mintStr);
				//List<String> rowPowerSum = Lists.newArrayList(mintStr);
				if (groupedMap.containsKey(mintStr)) {
					Map<String, JSONObject> stringListMap = groupedMap.get(mintStr);
					// 循环内层：按日的列数据
					// 限制流的大小为总元素数减1(去除最后一天冗余日期)
					for (int i = 0; i < firstHeadList.size(); i++) {
						String dayData = firstHeadList.get(i);
						if (stringListMap.containsKey(dayData)) {
							JSONObject obj = stringListMap.get(dayData);
							rowPower.add(obj.getString("batteryPower"));
							rowVolAndCur.add(obj.getString("batteryVoltage"));
							rowVolAndCur.add(obj.getString("batteryCurrent"));
							rowSoc.add(obj.getString("batterySoc"));
							rowCell.add(obj.getString("batteryMaximumCellVoltage"));
							rowCell.add(obj.getString("batteryMinimumCellVoltage"));
							rowTemp.add(obj.getString("batteryMaximumCellTemperature"));
							rowTemp.add(obj.getString("batteryMinimumCellTemperature"));
							//rowPowerSum.add(obj.getString("batteryPowerSum"));
						}
					}
				}
				powerDataList.add(rowPower);
				volAndCurDataList.add(rowVolAndCur);
				socDataList.add(rowSoc);
				cellDataList.add(rowCell);
				tempDataList.add(rowTemp);
				//powerSumDataList.add(rowPowerSum);
			}

			ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
			// 构建sheet对象&写数据
			WriteSheet writeSheet1 =
				EasyExcelFactory.writerSheet("储能功率").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).
				head(getHeadListReverse(firstHeadList, secondHead4Power)).build();
			excelWriter.write(powerDataList, writeSheet1);
			WriteSheet writeSheet2 =
				EasyExcelFactory.writerSheet("总体电压").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHead4VoltageAndCur)).build();
			excelWriter.write(volAndCurDataList, writeSheet2);
			WriteSheet writeSheet3 =
				EasyExcelFactory.writerSheet("储能SOC").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadListReverse(firstHeadList, secondHead4Soc)).build();
			excelWriter.write(socDataList, writeSheet3);
			WriteSheet writeSheet4 =
				EasyExcelFactory.writerSheet("单体电压").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHead4Cell)).build();
			excelWriter.write(cellDataList, writeSheet4);

			WriteSheet writeSheet5 =
				EasyExcelFactory.writerSheet("单体温度").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHead4Temp)).build();
			excelWriter.write(tempDataList, writeSheet5);

			/*WriteSheet writeSheet6 = EasyExcelFactory.writerSheet("Battery power sum").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadListReverse(firstHeadList, secondHead4PowerSum)).build();
			excelWriter.write(powerSumDataList, writeSheet6);*/
			excelWriter.finish();

		} catch (Exception e) {
			e.printStackTrace();
			log.error("export Status Report error: ", e.getMessage());
		}
	}

	@Override
	public DeviceLog22 appParallelDailyFromPvAndGrid(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.appParallelDailyFromPvAndGrid(queryCondition);
	}

	@Override
	public DeviceLog22 queryLatestData(QueryDeviceLog22Condition queryCondition) {
		return baseMapper.queryLatestData(queryCondition);
	}
	public static void main(String[] args) {
		LocalDate localDate = LocalDateTime.parse("2024-02-03 12:12:12", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate();
		System.out.println(localDate);
	}

	private static JSONObject mergeJsonObjects(JSONObject oldValue, Object newValue) {
		// 根据您的业务需求实现JSON对象的合并逻辑
		// 这里仅作示例，实际上无法直接将两个JSONObject相加
		// 请替换成合适的合并逻辑
		throw new UnsupportedOperationException("Please implement the merging logic for JSONObjects.");
	}

	private List<List<String>> getHeadList(List<String> firstHeadList, List<String> secondHeadList) {
		List<List<String>> headList = new ArrayList<>();
		headList.add(Lists.newArrayList("日期"));
		for (String time : firstHeadList) {
			for (String type : secondHeadList) {
				List<String> secondList = new ArrayList<>();
				secondList.add(time);
				secondList.add(type);
				headList.add(secondList);
			}
		}
		return headList;
	}

	private List<List<String>> getHeadListReverse(List<String> firstHeadList, List<String> secondHeadList) {
		List<List<String>> headList = new ArrayList<>();
		headList.add(Lists.newArrayList("Time"));
		for (String type : secondHeadList) {
			for (String time : firstHeadList) {
				List<String> secondList = new ArrayList<>();
				secondList.add(type);
				secondList.add(time);
				headList.add(secondList);
			}
		}
		return headList;
	}

	private List<JSONObject> getSummaryStateCurve() {
		List<JSONObject> jsonObjectList = new ArrayList<>();
		LocalDateTime endLdt = LocalDateTime.ofInstant(new Date().toInstant(), ZoneId.systemDefault());
		LocalDateTime startLdt = endLdt.minusHours(1);
		while (startLdt.isBefore(endLdt)) {
			// 查询最初5分钟的数据
			LocalDateTime conditionLdt = startLdt.plusMinutes(5);
			JSONObject jsonObject = baseMapper.getSummaryStateCurve(startLdt, conditionLdt);
			if (null == jsonObject) {
				buildSummaryDataObject(jsonObjectList, conditionLdt);
			} else {
				// 单位转换
				jsonObject.put(EntityFieldConstant.PHASE_A_POWER, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_A_POWER)));
				jsonObject.put(EntityFieldConstant.PHASE_B_POWER, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_B_POWER)));
				jsonObject.put(EntityFieldConstant.PHASE_C_POWER, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_C_POWER)));
				jsonObject.put(EntityFieldConstant.PHASE_R_WATT_OF_GRID, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_R_WATT_OF_GRID)));
				jsonObject.put(EntityFieldConstant.PHASE_S_WATT_OF_GRID, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_S_WATT_OF_GRID)));
				jsonObject.put(EntityFieldConstant.PHASE_T_WATT_OF_GRID, convertUnit(jsonObject.getBigDecimal(EntityFieldConstant.PHASE_T_WATT_OF_GRID)));
				jsonObject.put(EntityFieldConstant.DEVICE_DATETIME, conditionLdt.format(formatter));
				jsonObject.put(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL, conditionLdt);
				jsonObjectList.add(jsonObject);
			}
			startLdt = conditionLdt;
		}
		return jsonObjectList;
	}

	/**
	 * 单位转换
	 *
	 * @param decimal 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/12/13 16:51
	 **/
	private String convertUnit(BigDecimal decimal) {
		return DataUnitConversionUtil.getChangeEnergyResult(decimal, BizConstant.NUMBER_TWO);
	}

	private void buildSummaryDataObject(List<JSONObject> newlist, LocalDateTime localDateTime) {
		JSONObject objectHashMap = new JSONObject();
		objectHashMap.put(EntityFieldConstant.PHASE_A_POWER, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.PHASE_B_POWER, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.PHASE_C_POWER, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.PHASE_R_WATT_OF_GRID, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.PHASE_S_WATT_OF_GRID, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.PHASE_T_WATT_OF_GRID, BigDecimal.ZERO);
		objectHashMap.put(EntityFieldConstant.DEVICE_DATETIME, localDateTime.format(formatter));
		objectHashMap.put(EntityFieldConstant.DEVICE_DATETIME_FOR_CAL, localDateTime);
		newlist.add(objectHashMap);
	}
}

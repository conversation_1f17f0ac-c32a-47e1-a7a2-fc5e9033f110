/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.vo.GuardianAlarmThresholdVO;
import java.util.Objects;

/**
 * 安全卫士阈值&amp;闸位状态 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public class GuardianAlarmThresholdWrapper extends BaseEntityWrapper<GuardianAlarmThresholdEntity, GuardianAlarmThresholdVO>  {

	public static GuardianAlarmThresholdWrapper build() {
		return new GuardianAlarmThresholdWrapper();
 	}

	@Override
	public GuardianAlarmThresholdVO entityVO(GuardianAlarmThresholdEntity GuardianAlarmThreshold) {
		GuardianAlarmThresholdVO GuardianAlarmThresholdVO = Objects.requireNonNull(BeanUtil.copy(GuardianAlarmThreshold, GuardianAlarmThresholdVO.class));

		//User createUser = UserCache.getUser(GuardianAlarmThreshold.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianAlarmThreshold.getUpdateUser());
		//GuardianAlarmThresholdVO.setCreateUserName(createUser.getName());
		//GuardianAlarmThresholdVO.setUpdateUserName(updateUser.getName());

		return GuardianAlarmThresholdVO;
	}


}

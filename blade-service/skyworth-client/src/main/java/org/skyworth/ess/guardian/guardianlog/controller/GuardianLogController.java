package org.skyworth.ess.guardian.guardianlog.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.guardianlog.entity.GuardianLogEntity;
import org.skyworth.ess.guardian.guardianlog.vo.GuardianLogVO;
import org.skyworth.ess.guardian.guardianlog.excel.GuardianLogExcel;
import org.skyworth.ess.guardian.guardianlog.wrapper.GuardianLogWrapper;
import org.skyworth.ess.guardian.guardianlog.service.IGuardianLogService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士日志 控制器
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianLog")
@Api(value = "安全卫士日志", tags = "安全卫士日志接口")
public class GuardianLogController extends BladeController {

	private final IGuardianLogService GuardianLogService;

	/**
	 * 安全卫士日志 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianLog")
	public R<GuardianLogVO> detail(GuardianLogEntity GuardianLog) {
		GuardianLogEntity detail = GuardianLogService.getOne(Condition.getQueryWrapper(GuardianLog));
		return R.data(GuardianLogWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士日志 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianLog")
	public R<IPage<GuardianLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianLog, Query query) {
		IPage<GuardianLogEntity> pages = GuardianLogService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianLog, GuardianLogEntity.class));
		return R.data(GuardianLogWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士日志 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianLog")
	public R<IPage<GuardianLogVO>> page(GuardianLogVO GuardianLog, Query query) {
		IPage<GuardianLogVO> pages = GuardianLogService.selectGuardianLogPage(Condition.getPage(query), GuardianLog);
		return R.data(pages);
	}

	/**
	 * 安全卫士日志 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianLog")
	public R save(@Valid @RequestBody GuardianLogEntity GuardianLog) {
		return R.status(GuardianLogService.save(GuardianLog));
	}

	/**
	 * 安全卫士日志 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianLog")
	public R update(@Valid @RequestBody GuardianLogEntity GuardianLog) {
		return R.status(GuardianLogService.updateById(GuardianLog));
	}

	/**
	 * 安全卫士日志 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianLog")
	public R submit(@Valid @RequestBody GuardianLogEntity GuardianLog) {
		return R.status(GuardianLogService.saveOrUpdate(GuardianLog));
	}

	/**
	 * 安全卫士日志 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianLogService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianLog")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianLog")
	public void exportGuardianLog(@ApiIgnore @RequestParam Map<String, Object> GuardianLog, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianLogEntity> queryWrapper = Condition.getQueryWrapper(GuardianLog, GuardianLogEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianLog::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianLogEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianLogExcel> list = GuardianLogService.exportGuardianLog(queryWrapper);
		ExcelUtil.export(response, "安全卫士日志数据" + DateUtil.time(), "安全卫士日志数据表", list, GuardianLogExcel.class);
	}

}

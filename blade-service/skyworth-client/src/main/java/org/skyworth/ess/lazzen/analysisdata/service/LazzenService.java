package org.skyworth.ess.lazzen.analysisdata.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.vo.LazzenConsumerDataVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LazzenService {
    JSONObject analysisData(LazzenConsumerDataVO lazzenConsumerDataVO, Map<String, List<AddressMapDefinitionEntity>> dbAddressMap);

    void saveData(List<JSONObject> listMap);
}

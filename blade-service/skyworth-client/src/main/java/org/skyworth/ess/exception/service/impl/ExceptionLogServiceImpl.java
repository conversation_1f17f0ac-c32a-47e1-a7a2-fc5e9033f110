/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.service.impl;

import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.exception.excel.ExceptionLogExcel;
import org.skyworth.ess.exception.mapper.ExceptionLogMapper;
import org.skyworth.ess.exception.service.IExceptionLogService;
import org.springblade.common.constant.BizConstant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 异常日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class ExceptionLogServiceImpl extends BaseServiceImpl<ExceptionLogMapper, ExceptionLogEntity> implements IExceptionLogService {

	@Override
	public IPage<ExceptionLogVO> selectExceptionLogPage(IPage<ExceptionLogVO> page, ExceptionLogVO exceptionLog) {
		// 如果是并机，则查询这个站点下所有智能能量变换器的数据
		if(BizConstant.CHAR_ONE.equals(exceptionLog.getIsParallelMode())) {
			exceptionLog.setDeviceSerialNumber(null);
		}
		List<ExceptionLogVO> exceptionLogList = baseMapper.selectExceptionLogPage(page, exceptionLog);
		return page.setRecords(exceptionLogList);
	}


	@Override
	public List<ExceptionLogExcel> exportExceptionLog(Wrapper<ExceptionLogEntity> queryWrapper) {
		List<ExceptionLogExcel> exceptionLogList = baseMapper.exportExceptionLog(queryWrapper);
		//exceptionLogList.forEach(exceptionLog -> {
		//	exceptionLog.setTypeName(DictCache.getValue(DictEnum.YES_NO, ExceptionLog.getType()));
		//});
		return exceptionLogList;
	}

	@Override
	public IPage<ExceptionLogEntity> selectExceptionLogPageByCondition(IPage<ExceptionLogEntity> page, QueryCondition queryCondition) {
		List<ExceptionLogEntity> exceptionLogList=baseMapper.selectExceptionLogPageByBattery(page,queryCondition);
		return page.setRecords(exceptionLogList);
	}

}

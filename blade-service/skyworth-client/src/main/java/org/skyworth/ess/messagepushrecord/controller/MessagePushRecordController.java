/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity;
import org.skyworth.ess.messagepushrecord.vo.MessagePushRecordVO;
import org.skyworth.ess.messagepushrecord.excel.MessagePushRecordExcel;
import org.skyworth.ess.messagepushrecord.wrapper.MessagePushRecordWrapper;
import org.skyworth.ess.messagepushrecord.service.IMessagePushRecordService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 消息推送日志表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("messagePushRecord/messagePushRecord")
@Api(value = "消息推送日志表", tags = "消息推送日志表接口")
public class MessagePushRecordController extends BladeController {

	private final IMessagePushRecordService messagePushRecordService;

	/**
	 * 消息推送日志表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入messagePushRecord")
	public R<MessagePushRecordVO> detail(MessagePushRecordEntity messagePushRecord) {
		MessagePushRecordEntity detail = messagePushRecordService.getOne(Condition.getQueryWrapper(messagePushRecord));
		return R.data(MessagePushRecordWrapper.build().entityVO(detail));
	}
	/**
	 * 消息推送日志表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入messagePushRecord")
	public R<IPage<MessagePushRecordVO>> list(@ApiIgnore @RequestParam Map<String, Object> messagePushRecord, Query query) {
		IPage<MessagePushRecordEntity> pages = messagePushRecordService.page(Condition.getPage(query), Condition.getQueryWrapper(messagePushRecord, MessagePushRecordEntity.class));
		return R.data(MessagePushRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 消息推送日志表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入messagePushRecord")
	public R<IPage<MessagePushRecordVO>> page(MessagePushRecordVO messagePushRecord, Query query) {
		IPage<MessagePushRecordVO> pages = messagePushRecordService.selectMessagePushRecordPage(Condition.getPage(query), messagePushRecord);
		return R.data(pages);
	}

	/**
	 * 消息推送日志表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入messagePushRecord")
	public R save(@Valid @RequestBody MessagePushRecordEntity messagePushRecord) {
		return R.status(messagePushRecordService.save(messagePushRecord));
	}

	/**
	 * 消息推送日志表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入messagePushRecord")
	public R update(@Valid @RequestBody MessagePushRecordEntity messagePushRecord) {
		return R.status(messagePushRecordService.updateById(messagePushRecord));
	}

	/**
	 * 消息推送日志表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入messagePushRecord")
	public R submit(@Valid @RequestBody MessagePushRecordEntity messagePushRecord) {
		return R.status(messagePushRecordService.saveOrUpdate(messagePushRecord));
	}

	/**
	 * 消息推送日志表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(messagePushRecordService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-messagePushRecord")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入messagePushRecord")
	public void exportMessagePushRecord(@ApiIgnore @RequestParam Map<String, Object> messagePushRecord, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<MessagePushRecordEntity> queryWrapper = Condition.getQueryWrapper(messagePushRecord, MessagePushRecordEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(MessagePushRecord::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(MessagePushRecordEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<MessagePushRecordExcel> list = messagePushRecordService.exportMessagePushRecord(queryWrapper);
		ExcelUtil.export(response, "消息推送日志表数据" + DateUtil.time(), "消息推送日志表数据表", list, MessagePushRecordExcel.class);
	}

}

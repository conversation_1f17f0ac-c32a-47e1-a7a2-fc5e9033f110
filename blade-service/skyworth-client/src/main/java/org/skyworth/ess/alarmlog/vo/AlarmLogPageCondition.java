package org.skyworth.ess.alarmlog.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-08-10 15:14
 **/
@Data
public class AlarmLogPageCondition implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "告警类型，inverter：智能能量变换器；battery：储能包；guardian：安全卫士")
	private String exceptionType;

	@ApiModelProperty(value = "日期范围类型;y/m/w/d，y:近一年，m:近一月，w:近一周，d:近一日")
	private String dateRangeType;

	@ApiModelProperty(value = "时间类型;y/m/d")
	private String timeType;

	@ApiModelProperty(value = "告警开始时间")
	private String startDateTime;

	@ApiModelProperty(value = "告警结束时间")
	private String endDateTime;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;

	@ApiModelProperty(value = "设备SN")
	private String serialNumber;

	@ApiModelProperty(value = "告警状态，0：待处理，1：已处理，2：待跟进")
	private Integer status;

	@ApiModelProperty(value = "用户ID")
	private Long userId;

	@ApiModelProperty(value = "部门ID")
	private String departmentId;

	@ApiModelProperty(value = "告警信息处理人")
	private String alarmHandler;
}

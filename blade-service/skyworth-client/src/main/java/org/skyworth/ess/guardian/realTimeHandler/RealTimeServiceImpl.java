package org.skyworth.ess.guardian.realTimeHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.fegin.ErrorInfoBizClient;
import org.skyworth.ess.guardian.guardianlog.entity.GuardianLogEntity;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.util.HumpConvert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.DictBiz;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 探测器实时数据
 */
@Component(CommonConstant.TERMINAL_WARDS_KEY +"_"+ CommonConstant.BIZ_REALTIME_DATA)
@Slf4j
@AllArgsConstructor
public class RealTimeServiceImpl implements DataHandler{

	private IGuardianPlantService plantService;

	private DorisSinkService dorisSinkService;

	private IAlarmLogService alarmLogService;

	private BladeRedis redis;

	private static final SimpleDateFormat INPUT_FORMAT = new SimpleDateFormat("yyMMddHHmmss");
	private static final ThreadLocal<DateFormat> OUTPUT_FORMAT = ThreadLocal.withInitial(
		() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()));


	/**
	 * 故障码
	 * */
	private final List<Integer> faultCode= Arrays.asList(22,23,24,25,26,27,28);
	@Override
	public void handler(List<JSONObject> dataList, Map<String, Long> resultMap,Map<String,String> partitionMap) {

		List<JSONObject> guardLogObjList=new ArrayList<>();
		List<DictBiz> dictBizList = DictBizCache.getListByParentCode(DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(), "301F", CommonConstant.CURRENT_LANGUAGE_ZH);
		Map<String, DictBiz> dictBizMap=new HashMap<>();
		if(ValidationUtil.isNotEmpty(dictBizList)&&!dictBizList.isEmpty()){
			dictBizMap = dictBizList.stream().collect(Collectors.toMap(DictBiz::getDictKey, Function.identity(), (k1, k2) -> k1));
		}
		List<Long> plantIds = new ArrayList<>(resultMap.values());
		List<Map<String,Object>> plantInfoList=new ArrayList<>();
		if(ValidationUtil.isNotEmpty(plantIds)&&!plantIds.isEmpty()){
			plantInfoList=plantService.getPlantInfo(plantIds);
		}
		List<Map<String, Object>> finalPlantInfoList = plantInfoList;
		Map<String, DictBiz> finalDictBizMap = dictBizMap;
		dataList.parallelStream().forEach(data->{
			 try{
				 log.info("探测器实时数据:{}",data.toJSONString());
				 String deviceSn = data.getString("deviceSn");
				 Long plantId=resultMap.get(deviceSn);
				 if(ValidationUtil.isEmpty(plantId)){
					 return;
				 }

				 String content= data.getString("content");
				 String alarm=content.substring(0,8);
				 String current= content.substring(8,52);
				 //电流温度相关的数据
				 List<Double> res=getCurrentData(current,0.1f,Double.class);

				 //有功功率
				 int activePower=BinaryToHexUtils.hexToDecimal(content.substring(52,60));
				 //无功功率
				 int reactivePower=BinaryToHexUtils.hexToDecimal(content.substring(60,68));
				 //有功电量
				 Double activeEnergy=BinaryToHexUtils.hexToDecimal(content.substring(68,76))*0.1;
				 //无功电量
				 Double reactiveEnergy=BinaryToHexUtils.hexToDecimal(content.substring(76,84))*0.1;
				 //功率因数
				 Double powerFactor=BinaryToHexUtils.hexToDecimal(content.substring(84,92))*0.01;
				 //电网频率
				 Double frequency=BinaryToHexUtils.hexToDecimal(content.substring(92,100))*0.01;
				 //时间
				 String time=content.substring(100,112);

				 Date deviceDateTime = null;
				 try {
					 String formattedTime=TimeUtils.getCurrentTime();
					 if(!GuardianInstructConstants.TIME_IS_NULL.equals(time)){
						 Date date = INPUT_FORMAT.parse(time);
						 formattedTime =  OUTPUT_FORMAT.get().format(date);
					 }
					 deviceDateTime=TimeUtils.getCurrentTimeByDate(formattedTime);
				 } catch (Exception e) {
					 log.error("时间格式错误:{} " , e.getMessage());
				 }

				 //更新设备状态以及新增异常数据
				 updateStatus(alarm, deviceSn, deviceDateTime,plantId, finalDictBizMap, finalPlantInfoList);

				 //处理数据入库 doris表security_guard_log  security_guard_current_status(只保存一条)
				 realTimeData(deviceSn, deviceDateTime, plantId, res, activeEnergy, reactiveEnergy, powerFactor, frequency, activePower, reactivePower,guardLogObjList,partitionMap,alarm);

			 }catch (Exception e){
				 log.error("解析数据异常:{} " , e.getMessage());
			 }
		 });
		if(!guardLogObjList.isEmpty()){
			dorisSinkService.write(guardLogObjList,"security_guard_log");
			dorisSinkService.write(guardLogObjList,"security_guard_current_status");
		}

	}

	/**
	 * 处理实时数据入库
	 * */
	private void realTimeData(String deviceSn, Date deviceDateTime, Long plantId, List<Double> res,
							  Double activeEnergy, Double reactiveEnergy, Double powerFactor,
							  Double frequency,int activePower,int reactivePower,List<JSONObject> guardLogObjList,Map<String,String> partitionMap,String alarm) {
		GuardianLogEntity logEntity=new GuardianLogEntity();
		logEntity.setPlantId(plantId);
		logEntity.setDeviceDateTime(deviceDateTime);
		logEntity.setSecurityGuardSerialNumber(deviceSn);
		logEntity.setActivePower(BigDecimal.valueOf(activePower));
		logEntity.setReactivePower(BigDecimal.valueOf(reactivePower));
		logEntity.setLeakageCurrent(BigDecimal.valueOf(res.get(0)));
		logEntity.setAPhaseCurrent(BigDecimal.valueOf(res.get(1)));
		logEntity.setBPhaseCurrent(BigDecimal.valueOf(res.get(2)));
		logEntity.setCPhaseCurrent(BigDecimal.valueOf(res.get(3)));
		logEntity.setAPhaseVoltage(BigDecimal.valueOf(res.get(4)));
		logEntity.setBPhaseVoltage(BigDecimal.valueOf(res.get(5)));
		logEntity.setCPhaseVoltage(BigDecimal.valueOf(res.get(6)));
		logEntity.setAPhaseTemperature(BigDecimal.valueOf(res.get(7)));
		logEntity.setBPhaseTemperature(BigDecimal.valueOf(res.get(8)));
		logEntity.setCPhaseTemperature(BigDecimal.valueOf(res.get(9)));
		logEntity.setNNeutralLineTemperature(BigDecimal.valueOf(res.get(10)));
		logEntity.setActivePowerConsumption(BigDecimal.valueOf(activeEnergy));
		logEntity.setReactivePowerConsumption(BigDecimal.valueOf(reactiveEnergy));
		logEntity.setPowerFactor(BigDecimal.valueOf(powerFactor));
		logEntity.setGridFrequency(BigDecimal.valueOf(frequency));
		logEntity.setPartitionDate(partitionMap.get(deviceSn));
		logEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		logEntity.setAlarmStatus(alarm);
		JSONObject result = (JSONObject) JSON.toJSON(logEntity);
		//将驼峰转化为下划线
		JSONObject newObj = HumpConvert.convertKeysToUnderscore(result);
		IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
		Number number = identifierGenerator.nextId(new Object());
		newObj.put("id", number.longValue());
		String currentTime=TimeUtils.getCurrentTime();
		newObj.put("create_time", currentTime);
		newObj.put("update_time", currentTime);
		newObj.put("device_date_time", OUTPUT_FORMAT.get().format(deviceDateTime));
		guardLogObjList.add(newObj);
	}


	/**
	 * 更新设备状态以及新增异常数据
	 * */
	private void updateStatus(String alarm, String deviceSn, Date deviceDateTime,Long plantId,Map<String, DictBiz> dictBizMap,List<Map<String,Object>> plantInfoList) {
		String paddedRes=BinaryToHexUtils.binaryComplement(alarm,32);
		List<Integer> indexes=ErrorInfoBizClient.getIndex(paddedRes,'1');
		//设备状态
		String deviceStatus="1";
		//默认合闸值
		int gateStatus=1;
		if(ValidationUtil.isNotEmpty(indexes)&&!indexes.isEmpty()){
			boolean containsAny = faultCode.stream().anyMatch(indexes::contains);
			if(containsAny){
				//故障
				deviceStatus="3";
			}else {
				//告警
				deviceStatus="2";
			}
			//如果bit7为1则为分闸0则为合闸
			if(indexes.contains(7)){
				gateStatus=0;
			}
		}

		List<AlarmLogEntity> logEntities=new ArrayList<>();

		//更新设备状态
		LambdaQueryWrapper<GuardianPlantEntity> eq = Wrappers.<GuardianPlantEntity>query().lambda()
				.eq(GuardianPlantEntity::getPlantId,plantId).eq(GuardianPlantEntity::getIsDeleted,0);
		GuardianPlantEntity guardianPlantEntity = plantService.getOne(eq);
		if(ValidationUtil.isNotEmpty(guardianPlantEntity)){
			guardianPlantEntity.setSecurityGuardStatus(Integer.parseInt(deviceStatus));
			guardianPlantEntity.setGatePositionStatus(gateStatus);
			plantService.updateById(guardianPlantEntity);
		}


		// 使用Stream API和Lambda表达式获取键为"plantId"且值为2的唯一数据
		Optional<Map<String, Object>> result = plantInfoList.stream()
				.filter(map -> map.containsKey("plantId") && Objects.equals(map.get("plantId"), plantId))
				.findFirst();

		// 将结果放入一个新的Map中
		Map<String, Object> resultMap = result.orElse(new HashMap<>(16));

		//新增异常数据
		for (Integer code : indexes) {
			AlarmLogEntity logEntity = new AlarmLogEntity();
			logEntity.setPlantId(plantId);
			logEntity.setAddressCode(code+"");
			logEntity.setSerialNumber(deviceSn);
			logEntity.setDeviceDateTime(deviceDateTime);
			// 设置异常类型
			logEntity.setExceptionType(CommonConstant.TERMINAL_WARDS_NAME);
			logEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			logEntity.setAlarmNumber(code);
			logEntity.setTimeZone((String) resultMap.get("timeZone"));
			DictBiz dictBiz = dictBizMap.get(String.valueOf(code));
			if (dictBiz == null) {
				continue;
			}
			String msg=dictBiz.getDictValue();
			logEntity.setExceptionMessage(msg);
			logEntity.setAlarmLevel(dictBiz.getAttribute1());
			// 是否推送给用户
			if (CommonConstant.FLAG_Y.equalsIgnoreCase(dictBiz.getAttribute2())) {
				logEntity.setUserId((Long) resultMap.get("createUser"));
			}
			// 是否推送给安装商
			if (CommonConstant.FLAG_Y.equalsIgnoreCase(dictBiz.getAttribute3())) {
				logEntity.setDepartmentId((Long) resultMap.get("operationCompanyId"));
			}
			String msgKey=plantId+":"+deviceSn+":"+msg+":"+code;
			//如果缓存中不存该异常，则添加到列表中,缓存一天结束
			if(ValidationUtil.isEmpty(redis.get(msgKey))){
				redis.setEx(msgKey, LocalDate.now().toString(), Duration.ofDays(1));
				logEntities.add(logEntity);
			}
		}
		if(!logEntities.isEmpty()){
			alarmLogService.saveBatch(logEntities);
		}

	}

	/**
	 * 读取电流温度数据
	 */
	public static <T extends Number> List<T>  getCurrentData(String current, float decimal, Class<T> type){
		List<T> resultList = new ArrayList<>();

		for (int i = 0; i < current.length(); i += 4) {
			String hexSubstring = current.substring(i, Math.min(i + 4, current.length()));
			int decimalValue = Integer.parseInt(hexSubstring, 16);
			double result = decimalValue * decimal;
			BigDecimal bd = new BigDecimal(result).setScale(1, RoundingMode.HALF_UP);

			if (type == Integer.class) {
				resultList.add(type.cast(bd.intValue()));
			} else if (type == Double.class) {
				resultList.add(type.cast(bd.doubleValue()));
			}
		}

		return resultList;
	}



}

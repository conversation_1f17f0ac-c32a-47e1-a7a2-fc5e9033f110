/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianlog.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.guardian.guardianlog.entity.GuardianLogEntity;
import org.skyworth.ess.guardian.guardianlog.vo.GuardianLogVO;
import org.skyworth.ess.guardian.guardianlog.excel.GuardianLogExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.guardianplant.dto.GuardianLogQueryCondition;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianLogDataStatVO;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 安全卫士日志 服务类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface IGuardianLogService extends BaseService<GuardianLogEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianLog
	 * @return
	 */
	IPage<GuardianLogVO> selectGuardianLogPage(IPage<GuardianLogVO> page, GuardianLogVO GuardianLog);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianLogExcel> exportGuardianLog(Wrapper<GuardianLogEntity> queryWrapper);

	/**
	 *
	 */
	List<GuardianLogDataStatVO> selectGuardianLogDataStatByTime(GuardianLogQueryCondition queryCondition);

}

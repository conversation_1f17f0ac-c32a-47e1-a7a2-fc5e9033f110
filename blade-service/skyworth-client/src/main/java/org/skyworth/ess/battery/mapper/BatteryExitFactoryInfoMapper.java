/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.app.vo.AppBatteryExitFactoryInfoVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryExitFactoryInfoExcel;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 储能出厂信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface BatteryExitFactoryInfoMapper extends BaseMapper<BatteryExitFactoryInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param BatteryExitFactoryInfo
	 * @return
	 */
	List<BatteryExitFactoryInfoVO> selectBatteryExitFactoryInfoPage(IPage page, BatteryExitFactoryInfoVO BatteryExitFactoryInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<BatteryExitFactoryInfoExcel> exportBatteryExitFactoryInfo(@Param("ew") Wrapper<BatteryExitFactoryInfoEntity> queryWrapper);

	List<AppBatteryExitFactoryInfoVO> queryAppBatteryExitFactoryInfo(Long plantId);

	List<AppBatteryExitFactoryInfoVO> queryAppBatteryDeviceInfo(@Param("params")AppVO appVO);

	boolean deleteLogicBatteryExitFactory(@Param("updateUser") Long updateUser, @Param("updateUserAccount") String updateUserAccount
		, @Param("ids") List<Long> ids);

	List<BatteryExitFactoryInfoEntity> queryByBatterySerialNumbers(@Param("list") List<String> list);

	int updateBatchBySn(@Param("list") List<BatteryExitFactoryInfoEntity> list);

	BigDecimal queryTotalInstalledPower();

	/**
	 * 查询设备信息
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryExitFactoryInfoVO>
	 * <AUTHOR>
	 * @since 2024/1/11 10:37
	 **/
	List<BatteryExitFactoryInfoVO> deviceInformation(@Param("qc") QueryCondition queryCondition);

	List<BatteryExitFactoryInfoVO> deviceInformationIsDelete(@Param("qc") QueryCondition queryCondition);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.timedswitchgate.mapper.GuardianTimedSwitchGateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianTimedSwitchGateResultMap" type="org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="closing_time" property="closingTime"/>
        <result column="opening_time" property="openingTime"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianTimedSwitchGatePage" resultMap="GuardianTimedSwitchGateResultMap">
        select * from guardian_timed_switch_gate where is_deleted = 0
    </select>


    <select id="exportGuardianTimedSwitchGate" resultType="org.skyworth.ess.guardian.timedswitchgate.excel.GuardianTimedSwitchGateExcel">
        SELECT * FROM guardian_timed_switch_gate ${ew.customSqlSegment}
    </select>

</mapper>

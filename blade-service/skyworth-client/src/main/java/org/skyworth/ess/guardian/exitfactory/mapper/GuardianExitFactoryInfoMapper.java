/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.mapper;

import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.vo.GuardianExitFactoryInfoVO;
import org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 安全卫士储能出厂信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface GuardianExitFactoryInfoMapper extends BaseMapper<GuardianExitFactoryInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param guardianExitFactoryInfo
	 * @return
	 */
	List<GuardianExitFactoryInfoVO> selectGuardianExitFactoryInfoPage(IPage page, GuardianExitFactoryInfoVO guardianExitFactoryInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianExitFactoryInfoExcel> exportGuardianExitFactoryInfo(@Param("ew") Wrapper<GuardianExitFactoryInfoEntity> queryWrapper);

	boolean deleteLogicGuardianExitFactory(@Param("updateUser") Long updateUser, @Param("updateUserAccount") String updateUserAccount
		,@Param("ids")List<Long> ids);

	int updateBatchBySn(@Param("list")List<GuardianExitFactoryInfoEntity> update);

	List<GuardianExitFactoryInfoEntity> queryBySerialNumbers(@Param("list") List<String> serialNumbers);
}

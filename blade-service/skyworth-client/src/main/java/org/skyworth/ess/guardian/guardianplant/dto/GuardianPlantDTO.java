/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.dto;

import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 安全卫士对应站点 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GuardianPlantDTO extends GuardianPlantEntity {
	private static final long serialVersionUID = 1L;

}

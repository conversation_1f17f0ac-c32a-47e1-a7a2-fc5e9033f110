/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import java.util.Objects;

/**
 * 异常日志 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public class ExceptionLogWrapper extends BaseEntityWrapper<ExceptionLogEntity, ExceptionLogVO>  {

	public static ExceptionLogWrapper build() {
		return new ExceptionLogWrapper();
 	}

	@Override
	public ExceptionLogVO entityVO(ExceptionLogEntity exceptionLog) {
		ExceptionLogVO exceptionLogVO = Objects.requireNonNull(BeanUtil.copy(exceptionLog, ExceptionLogVO.class));

		//User createUser = UserCache.getUser(exceptionLog.getCreateUser());
		//User updateUser = UserCache.getUser(exceptionLog.getUpdateUser());
		//exceptionLogVO.setCreateUserName(createUser.getName());
		//exceptionLogVO.setUpdateUserName(updateUser.getName());

		return exceptionLogVO;
	}


}

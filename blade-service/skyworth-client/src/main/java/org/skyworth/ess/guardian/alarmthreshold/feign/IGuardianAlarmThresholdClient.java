/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.feign;

import org.springblade.core.mp.support.BladePage;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 安全卫士阈值&amp;闸位状态 Feign接口类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@FeignClient(
	value ="skyworth-client"
)
public interface IGuardianAlarmThresholdClient {

    String API_PREFIX = "/client";
    String TOP = API_PREFIX + "/guardianAlarmThreshold";

    /**
     * 获取安全卫士阈值&amp;闸位状态列表
     *
     * @param current   页号
     * @param size      页数
     * @return BladePage
     */
    @GetMapping(TOP)
    BladePage<GuardianAlarmThresholdEntity> top(@RequestParam("current") Integer current, @RequestParam("size") Integer size);

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.device.vo.InverterReportQueryVO;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianLogDataStatVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantOverviewVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantReportQueryVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO;
import org.skyworth.ess.guardian.guardianplant.excel.GuardianPlantExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.plant.vo.PlantVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 安全卫士对应站点 服务类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface IGuardianPlantService extends BaseService<GuardianPlantEntity> {

	/**
	 * 安全卫士对应站点总览
	 * @param guardianPlantVO
	 * @return IPage<GuardianPlantVO>
	 */
	GuardianPlantOverviewVO getGuardianPlantOverview(GuardianPlantVO guardianPlantVO);

	/**
	 * 自定义分页
	 *
	 * @param query
	 * @param guardianPlantVO
	 * @return IPage<GuardianPlantVO>
	 */
	IPage<GuardianPlantVO> page(Query query, GuardianPlantVO guardianPlantVO);

	/**
	 * 详情
	 *
	 * @param guardianPlantEntity
	 * @return GuardianPlantVO
	 */
	GuardianPlantVO detail(GuardianPlantEntity guardianPlantEntity);

	/**
	 * 当前状态
	 *
	 * @param guardianPlantEntity
	 * @return GuardianPlantVO
	 */
	GuardianCurrentStatusVO detailCurrentStatus(GuardianPlantEntity guardianPlantEntity);

	/**
	 * 解绑安全卫士
	 *
	 * @param guardianPlantEntity
	 * @return GuardianPlantVO
	 */
	Boolean unbind(GuardianPlantEntity guardianPlantEntity);

	/**
	 * 状态曲线
	 *
	 * @param queryCondition
	 * @return GuardianPlantVO
	 */
	Map<String, List<Object>> selectStatusReport(GuardianPlantReportQueryVO queryCondition);

	/**
	 * 状态曲线导出
	 *
	 * @param queryCondition
	 * @param response
	 */
	void selectStatusReportExport(GuardianPlantReportQueryVO queryCondition, HttpServletResponse response);

	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianPlantExcel> exportGuardianPlant(Wrapper<GuardianPlantEntity> queryWrapper);

	List<GuardianPlantEntity> queryByGuardianSerialNumberList(List<String> serialNumberList);

	List<GuardianPlantEntity> queryGuardianSerialNumberList(List<Long> longList);

	int batchDeleteLogicByPlantId(List<Long> longList, String account);

	List<GuardianPlantEntity> queryOwnerData(Long userId);

	int updateDataByCondition(GuardianPlantEntity updateGuardianPlantEntity);

	Map<String, Object> getAllGuardianPowerSetup(Long plantId, String serialNumber);

	Map<String, Object> getAllGuardianSwitchGateSetup(Long plantId, String serialNumber);

	GuardianCurrentStatusVO getGuardianStatusAndDeviceInfo(GuardianPlantEntity guardianPlantEntity);

	Map<String, Object> getAllGuardianGatePositionSetup(Long plantId, String serialNumber);

	List<Map<String,Object>> getPlantInfo(List<Long> plantIdList);
}

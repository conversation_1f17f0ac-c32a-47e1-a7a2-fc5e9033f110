package org.skyworth.ess.balconypv.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 能源系统 实体类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@TableName("energy_system")
@ApiModel(value = "EnergySystem对象", description = "能源系统")
@EqualsAndHashCode(callSuper = true)
public class EnergySystemEntity extends TenantEntity {

	/**
	 * 能源系统名称
	 */
	@ApiModelProperty(value = "能源系统名称")
	private String energySystemName;
	/**
	 * 国家
	 */
	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;
	/**
	 * 详细地址
	 */
	@ApiModelProperty(value = "详细地址")
	private String detailAddress;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	@ApiModelProperty(value = "是否需要录入区域信息")
	@TableField(exist = false)
	private boolean supplementRegionalInfo = false;

	/**
	 * 国家名称
	 */
	@ApiModelProperty(value = "国家名字")
	@TableField(exist = false)
	private String countryName;
	/**
	 * 一级行政区域名称
	 */
	@ApiModelProperty(value = "省份名字")
	@TableField(exist = false)
	private String provinceName;
	/**
	 * 二级行政区域名称
	 */
	@ApiModelProperty(value = "城市名字")
	@TableField(exist = false)
	private String cityName;
	/**
	 * 三级行政区域名称
	 */
	@ApiModelProperty(value = "区县名字")
	@TableField(exist = false)
	private String countyName;

}

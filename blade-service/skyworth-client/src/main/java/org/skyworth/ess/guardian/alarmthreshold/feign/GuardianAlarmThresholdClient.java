/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springblade.core.mp.support.BladePage;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 安全卫士阈值&amp;闸位状态 Feign实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@ApiIgnore()
@RestController
@AllArgsConstructor
public class GuardianAlarmThresholdClient implements IGuardianAlarmThresholdClient {

    private final IGuardianAlarmThresholdService GuardianAlarmThresholdService;

    @Override
    @GetMapping(TOP)
    public BladePage<GuardianAlarmThresholdEntity> top(Integer current, Integer size) {
        Query query = new Query();
        query.setCurrent(current);
        query.setSize(size);
        IPage<GuardianAlarmThresholdEntity> page = GuardianAlarmThresholdService.page(Condition.getPage(query));
        return BladePage.of(page);
    }

}

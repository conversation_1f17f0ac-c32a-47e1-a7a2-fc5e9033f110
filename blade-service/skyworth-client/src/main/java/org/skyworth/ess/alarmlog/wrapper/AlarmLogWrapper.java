/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.vo.AlarmLogVO;
import java.util.Objects;

/**
 * 异常日志表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public class AlarmLogWrapper extends BaseEntityWrapper<AlarmLogEntity, AlarmLogVO>  {

	public static AlarmLogWrapper build() {
		return new AlarmLogWrapper();
 	}

	@Override
	public AlarmLogVO entityVO(AlarmLogEntity alarmLog) {
		AlarmLogVO alarmLogVO = Objects.requireNonNull(BeanUtil.copy(alarmLog, AlarmLogVO.class));

		//User createUser = UserCache.getUser(alarmLog.getCreateUser());
		//User updateUser = UserCache.getUser(alarmLog.getUpdateUser());
		//alarmLogVO.setCreateUserName(createUser.getName());
		//alarmLogVO.setUpdateUserName(updateUser.getName());

		return alarmLogVO;
	}


}

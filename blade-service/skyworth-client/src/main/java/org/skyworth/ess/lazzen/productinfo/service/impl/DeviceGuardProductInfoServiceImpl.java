/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.productinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity;
import org.skyworth.ess.lazzen.productinfo.vo.DeviceGuardProductInfoVO;
import org.skyworth.ess.lazzen.productinfo.excel.DeviceGuardProductInfoExcel;
import org.skyworth.ess.lazzen.productinfo.mapper.DeviceGuardProductInfoMapper;
import org.skyworth.ess.lazzen.productinfo.service.IDeviceGuardProductInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 产品信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
@DS("slave")
@Slf4j
public class DeviceGuardProductInfoServiceImpl extends BaseServiceImpl<DeviceGuardProductInfoMapper, DeviceGuardProductInfoEntity> implements IDeviceGuardProductInfoService {

	@Override
	public IPage<DeviceGuardProductInfoVO> selectDeviceGuardProductInfoPage(IPage<DeviceGuardProductInfoVO> page, DeviceGuardProductInfoVO deviceGuardProductInfo) {
		return page.setRecords(baseMapper.selectDeviceGuardProductInfoPage(page, deviceGuardProductInfo));
	}


	@Override
	public List<DeviceGuardProductInfoExcel> exportDeviceGuardProductInfo(Wrapper<DeviceGuardProductInfoEntity> queryWrapper) {
		List<DeviceGuardProductInfoExcel> deviceGuardProductInfoList = baseMapper.exportDeviceGuardProductInfo(queryWrapper);
		//deviceGuardProductInfoList.forEach(deviceGuardProductInfo -> {
		//	deviceGuardProductInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceGuardProductInfo.getType()));
		//});
		return deviceGuardProductInfoList;
	}

}

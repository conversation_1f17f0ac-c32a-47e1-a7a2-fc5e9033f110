package org.skyworth.ess.guardian.guardianplant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName   GuardianLogDataStatVO
 * @Description 安全卫士日志数据统计VO
 * <AUTHOR>
 * @Date        2024/8/9 下午3:08
 * @version     V1.0
 */
@Data
public class GuardianLogDataStatVO {

	/**
	 * 数据上报时间
	 */
	private String deviceDateTime;

	/**
	 * 数据上报时间2
	 */
	private String deviceDateTimeForCal;

	/**
	 * 数据上报时间3
	 */
	private String deviceDateTimeForDay;

	/**
	 * A相电压，单位V
	 */
	private BigDecimal aPhaseVoltage;
	/**
	 * A相电流，单位A
	 */
	private BigDecimal aPhaseCurrent;
	/**
	 * A相温度，单位°C
	 */
	private BigDecimal aPhaseTemperature;
	/**
	 * B相电压，单位V
	 */
	private BigDecimal bPhaseVoltage;
	/**
	 * B相电流，单位A
	 */
	private BigDecimal bPhaseCurrent;
	/**
	 * B相温度，单位°C
	 */
	private BigDecimal bPhaseTemperature;
	/**
	 * C相电压，单位V
	 */
	private BigDecimal cPhaseVoltage;
	/**
	 * C相电流，单位A
	 */
	private BigDecimal cPhaseCurrent;
	/**
	 * C相温度，单位°C
	 */
	private BigDecimal cPhaseTemperature;
	/**
	 * N零线温度，单位°C
	 */
	private BigDecimal nNeutralLineTemperature;
	/**
	 * 漏电电流，单位ma
	 */
	private BigDecimal leakageCurrent;

	public static GuardianLogDataStatVO init() {
		GuardianLogDataStatVO guardianLogDataStatVO = new GuardianLogDataStatVO();
		// 初始化BigDecimal类型的属性为BigDecimal.ZERO
		guardianLogDataStatVO.aPhaseVoltage = BigDecimal.ZERO;
		guardianLogDataStatVO.aPhaseCurrent = BigDecimal.ZERO;
		guardianLogDataStatVO.aPhaseTemperature = BigDecimal.ZERO;
		guardianLogDataStatVO.bPhaseVoltage = BigDecimal.ZERO;
		guardianLogDataStatVO.bPhaseCurrent = BigDecimal.ZERO;
		guardianLogDataStatVO.bPhaseTemperature = BigDecimal.ZERO;
		guardianLogDataStatVO.cPhaseVoltage = BigDecimal.ZERO;
		guardianLogDataStatVO.cPhaseCurrent = BigDecimal.ZERO;
		guardianLogDataStatVO.cPhaseTemperature = BigDecimal.ZERO;
		guardianLogDataStatVO.nNeutralLineTemperature = BigDecimal.ZERO;
		guardianLogDataStatVO.leakageCurrent = BigDecimal.ZERO;
		return guardianLogDataStatVO;
	}
}

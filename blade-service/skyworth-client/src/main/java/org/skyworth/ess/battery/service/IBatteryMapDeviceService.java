package org.skyworth.ess.battery.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.battery.entity.BatteryDeviceInstallVO;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 储能映射设备表(BatteryMapDeviceT)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-13 17:22:57
 */
public interface IBatteryMapDeviceService extends BaseService<BatteryMapDeviceEntity> {

	/**
	 * 通过ID查询单条数据
	 *
	 * @param batteryMapDeviceId 主键
	 * @return 实例对象
	 */
	BatteryMapDeviceVO queryById(Integer batteryMapDeviceId);

	/**
	 * 分页查询储能信息
	 *
	 * @param queryPageCondition 查询条件
	 * @param page               入参
	 * @return IPage<BatteryMapDevice>
	 * <AUTHOR>
	 * @since 2023/9/15 9:23
	 **/
	IPage<BatteryPageResultVO> queryPage(QueryCondition queryPageCondition, IPage<BatteryPageResultVO> page);

	/**
	 * 查询储能储能列表查询汇总项
	 *
	 * @param queryPageCondition 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/15 14:30
	 **/
	Map<String, Object> listSummary(QueryCondition queryPageCondition);

	/**
	 * 安装信息
	 *
	 * @param queryPageCondition 入参
	 * @return BatteryDeviceInstallVO
	 * <AUTHOR>
	 * @since 2023/10/7 18:17
	 **/
	List<BatteryDeviceInstallVO> installation(QueryCondition queryPageCondition);

	int batchDeleteLogicByPlantId(List<Long> plantIdList, String updateUserAccount);

	List<BatteryMapDeviceEntity> queryOwnerData(Long createUser);

	int updateDataByCondition(BatteryMapDeviceEntity updateOwner);

	List<BatteryMapDeviceEntity> queryListByPlantId(List<Long> list);

	List<BatteryMapDeviceVO> queryBatteryDeviceInfo(BatteryMapDeviceEntity batteryMapDeviceEntity);

	/**
	 * 储能设备信息
	 *
	 * @param queryPageCondition 入参
	 * @return List<BatteryExitFactoryInfoVO>
	 * <AUTHOR>
	 * @since 2023/12/20 16:12
	 **/
	List<BatteryExitFactoryInfoVO> deviceInformation(QueryCondition queryPageCondition);

	/**
	 * 储能重要事件
	 *
	 * @param queryCondition 入参
	 * @param page           分页
	 * @return List<ImportantEventVO>
	 * <AUTHOR>
	 * @since 2023/12/20 17:58
	 **/
	IPage<ImportantEventEntity> importantEvents(QueryCondition queryCondition, IPage<ImportantEventEntity> page);

	/**
	 * 储能异常日志
	 *
	 * @param queryCondition 入参
	 * @param page           分页
	 * @return List<ExceptionLogVO>
	 * <AUTHOR>
	 * @since 2023/12/20 17:58
	 **/
	IPage<ExceptionLogEntity> exceptionLog(QueryCondition queryCondition, IPage<ExceptionLogEntity> page);


	List<BatteryMapDeviceEntity> queryListByPlantIdAndSn(Long plantId, String deviceSerialNumber);

	List<BatteryMapDeviceEntity> queryListByPlantIdAndSnAndBattery(Long plantId, String deviceSerialNumber,Integer batteryEnergyStorageNumber);

	void batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account);
}

package org.skyworth.ess.device.service.impl;

import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.skyworth.ess.device.mapper.TimeZoneDeviceMapper;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.skyworth.ess.ota.feign.ITimeZoneCachedClient;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class TimeZoneDeviceServiceImpl extends BaseServiceImpl<TimeZoneDeviceMapper, TimeZoneDevice> implements TimeZoneDeviceService {
	@Resource
	TimeZoneDeviceMapper timeZoneDeviceMapper;
	@Resource
	private ITimeZoneCachedClient timeZoneCachedClient;
    @Override
    public List<TimeZoneDevice> getListBySnList(List<String> deviceSnList) {
        return timeZoneDeviceMapper.getListBySnList(deviceSnList);
    }
	@Override
	public Map<String, String> getMapFromCacheByPlantIdList(List<Long> plantIdList) {
		Map<String, String> timeZoneMap = new ConcurrentHashMap<>();
		if (plantIdList == null || plantIdList.isEmpty()) {
			return timeZoneMap;
		}
		// 提取plantID并去重
		Set<String> plantIdSet = plantIdList.stream()
			.filter(Objects::nonNull)
			.map(String::valueOf)
			.collect(Collectors.toSet());
		// 批量查询缓存
		Map<String, String> cachedData = timeZoneCachedClient.getAllCache(plantIdSet);
		// 缓存查询的数据放入返回结果中
		timeZoneMap.putAll(cachedData);
		// 找出未命中的 ID
		List<String> missedIds = plantIdSet.stream()
			.filter(id -> !cachedData.containsKey(id))
			.collect(Collectors.toList());
		// 批量查询数据库并更新缓存
		if (!missedIds.isEmpty()) {
			// 批量查询数据库并处理结果
			List<Map<String, String>> timeZoneList = timeZoneDeviceMapper.getTimeZoneListByPlantIds(missedIds);
			Optional.ofNullable(timeZoneList)
				.orElse(Collections.emptyList())
				.stream()
				.filter(Objects::nonNull)
				.forEach(map -> processDatabaseRecord(map, timeZoneMap));
		}
		return timeZoneMap;
	}

	/**
	 * 单独封装数据库记录处理逻辑
	 * @param  record 数据
	 * @param  resultMap 返回map
	 * <AUTHOR>
	 * @date   2025/3/5 15:52
	 */
	private void processDatabaseRecord(Map<String, String> record, Map<String, String> resultMap) {
		String plantId = String.valueOf(record.get("plant_id"));
		String timeZone = record.get("time_zone");
		if (plantId != null && timeZone != null) {
			// 线程安全的put操作
			resultMap.putIfAbsent(plantId, timeZone);
			// 缓存填充（幂等操作）
			timeZoneCachedClient.putCache(plantId, timeZone);
		}
	}
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.vo.AppBatteryCurrentStatusInfo;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.mapper.BatteryCurrentStatusMapper;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.vo.BatteryCurrentStatusVO;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.wrapper.BatteryCurrentStatusWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.mp.support.Condition;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 储能当前状态 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
@AllArgsConstructor
public class BatteryCurrentStatusServiceImpl extends ServiceImpl<BatteryCurrentStatusMapper,
	BatteryCurrentStatusEntity> implements IBatteryCurrentStatusService {


	private BatteryMapDeviceServiceImpl batteryMapDeviceService;

	/**
	 * 查询当前储能状态
	 *
	 * @param queryCondition 入参
	 * @return BatteryCurrentStatusEntity
	 * <AUTHOR>
	 * @since 2023/9/16 11:01
	 **/
	@Override
	public BatteryCurrentStatusVO view(QueryCondition queryCondition) {
		// 获取当前状态
		BatteryCurrentStatusEntity batteryCurrentStatusEntity = baseMapper.view(queryCondition);
		if (ObjectUtil.isEmpty(batteryCurrentStatusEntity)) {
			batteryCurrentStatusEntity = baseMapper.viewIsDelete(queryCondition);
		}
		if (batteryCurrentStatusEntity == null) {
			return null;
		}
		// 定义常量
		final BigDecimal thousand = new BigDecimal(1000);
		// 提取公共方法处理 BigDecimal 字段
		batteryCurrentStatusEntity.setBatteryAccumulatedChargeEnergy(
			scaleBigDecimal(batteryCurrentStatusEntity.getBatteryAccumulatedChargeEnergy(), thousand)
		);
		batteryCurrentStatusEntity.setBatteryAccumulatedDischargeEnergy(
			scaleBigDecimal(batteryCurrentStatusEntity.getBatteryAccumulatedDischargeEnergy(), thousand)
		);
		batteryCurrentStatusEntity.setBatteryDailyChargeEnergy(
			scaleBigDecimal(batteryCurrentStatusEntity.getBatteryDailyChargeEnergy(), thousand)
		);
		batteryCurrentStatusEntity.setBatteryDailyDischargeEnergy(
			scaleBigDecimal(batteryCurrentStatusEntity.getBatteryDailyDischargeEnergy(), thousand)
		);
		return BatteryCurrentStatusWrapper.build().entityVO(batteryCurrentStatusEntity);
	}

	// 公共方法：处理 BigDecimal 字段的缩放逻辑
	private BigDecimal scaleBigDecimal(BigDecimal value, BigDecimal divisor) {
		// 防止除以零或空值
		if (value == null || divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
			return null;
		}
		try {
			return value.divide(divisor, 2, RoundingMode.HALF_UP);
		} catch (ArithmeticException e) {
			// 捕获异常并返回 null 或其他默认值
			return null;
		}
	}


	@Override
	public BatteryCurrentStatusEntity queryIsDelete(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryIsDelete(plantId, deviceSerialNumber);
	}

	@Override
	public List<AppBatteryCurrentStatusInfo> queryAppBatteryInfo(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryAppBatteryInfo(plantId, deviceSerialNumber);
	}

	@Override
	public List<AppBatteryCurrentStatusInfo> queryAppBatteryInfoV2(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryAppBatteryInfoV2(plantId, deviceSerialNumber);
	}

	@Override
	public List<BatteryCurrentStatusEntity> batchQueryAppBatteryCurrentStatus(List<QueryCondition> list) {
		return baseMapper.batchQueryAppBatteryCurrentStatus(list);
	}

	@Override
	public int batchDeleteLogicByPlantId(List<Long> longList, String account) {
		return baseMapper.batchDeleteLogicByPlantId(longList, account);
	}

	@Override
	public int batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account) {
		return baseMapper.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber, account);
	}
}

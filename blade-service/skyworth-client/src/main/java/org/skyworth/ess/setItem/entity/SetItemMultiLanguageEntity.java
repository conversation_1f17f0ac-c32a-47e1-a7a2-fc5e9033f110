/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * APP设置项配置名称多语言 实体类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@TableName("device_set_item_multi_language")
@ApiModel(value = "SetItemMultiLanguage对象", description = "APP设置项配置名称多语言")
@EqualsAndHashCode(callSuper = true)
public class SetItemMultiLanguageEntity extends TenantEntity {

	@TableField(exist = false)
	private Long id;

	@TableField(exist = false)
	private Long createUser;

	@TableField(exist = false)
	private Long createDept;

	@TableField(exist = false)
	private Date createTime;

	@TableField(exist = false)
	private Long updateUser;

	@TableField(exist = false)
	private Date updateTime;

	@TableField(exist = false)
	private Integer status;

	@TableField(exist = false)
	private Integer isDeleted;

	@TableField(exist = false)
	private String tenantId;
	// 描述，用于批量生成时识别哪一批
	private String description;
	/**
	 * 设置项ID
	 */
	@ApiModelProperty(value = "设置项ID")
	private Long itemId;
	/**
	 * 设置项语言类型
	 */
	@ApiModelProperty(value = "设置项语言类型")
	private String itemLanguageType;
	/**
	 * 设置项语言
	 */
	@ApiModelProperty(value = "设置项语言")
	private String itemLanguageName;

}

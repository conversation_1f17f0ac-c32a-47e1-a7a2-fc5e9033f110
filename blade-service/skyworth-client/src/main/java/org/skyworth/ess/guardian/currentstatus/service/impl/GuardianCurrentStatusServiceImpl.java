/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.currentstatus.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.currentstatus.excel.GuardianCurrentStatusExcel;
import org.skyworth.ess.guardian.currentstatus.mapper.GuardianCurrentStatusMapper;
import org.skyworth.ess.guardian.currentstatus.service.IGuardianCurrentStatusService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安全卫士当前状态 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
@Slf4j
@DS("slave")
public class GuardianCurrentStatusServiceImpl extends BaseServiceImpl<GuardianCurrentStatusMapper, GuardianCurrentStatusEntity> implements IGuardianCurrentStatusService {

	@Override
	public IPage<GuardianCurrentStatusVO> selectGuardianCurrentStatusPage(IPage<GuardianCurrentStatusVO> page, GuardianCurrentStatusVO GuardianCurrentStatus) {
		return page.setRecords(baseMapper.selectGuardianCurrentStatusPage(page, GuardianCurrentStatus));
	}


	@Override
	public List<GuardianCurrentStatusExcel> exportGuardianCurrentStatus(Wrapper<GuardianCurrentStatusEntity> queryWrapper) {
		List<GuardianCurrentStatusExcel> GuardianCurrentStatusList = baseMapper.exportGuardianCurrentStatus(queryWrapper);
		//GuardianCurrentStatusList.forEach(GuardianCurrentStatus -> {
		//	GuardianCurrentStatus.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianCurrentStatus.getType()));
		//});
		return GuardianCurrentStatusList;
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		return baseMapper.deleteLogicByPlantIdAndSn(plantId,securityGuardSerialNumber);
	}

}

package org.skyworth.ess.guardian.issue;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;

public interface IssueStrategy {

	//01.获取远程通道
	//02.拼接content内容
	//03.构造下发对象
	//04.调用远程接口进行下发
	//05.处理业务结果
	//06.关闭资源
    String getRemoteChannel(IssueStrategyEntity issueStrategyEntity);
    abstract String assembleContent(IssueStrategyEntity issueStrategyEntity);
	JSONObject constructIssueObject(String content,IssueStrategyEntity issueStrategyEntity);
	R<String> invokeRemoteInterface(JSONObject issueObject,String remoteChannelIp);
	abstract R handleBusinessResult(R<String> invokeResult,IssueStrategyEntity issueStrategyEntity);
	void closeResource();
}

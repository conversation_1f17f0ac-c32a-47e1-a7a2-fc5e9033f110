package org.skyworth.ess.lazzen.productinfo.vo;

import lombok.Getter;

@Getter
public enum LazzenSwitchOnOffCodeEnum {
	SWITCH_ON("1", "00FF"),
	SWITCH_OFF("0", "FF00");



	private String decimalCode;
	private String issueCode;

	LazzenSwitchOnOffCodeEnum(String decimalCode, String issueCode){
		this.decimalCode = decimalCode;
		this.issueCode = issueCode;
	}
	public static String getIssueCodeByDecimalCode(String decimalCode){
		for (LazzenSwitchOnOffCodeEnum switchOnOffCodeEnum : LazzenSwitchOnOffCodeEnum.values()) {
			if(switchOnOffCodeEnum.getDecimalCode().equals(decimalCode)) {
				return switchOnOffCodeEnum.getIssueCode();
			}
		}
		return "";
	}

}

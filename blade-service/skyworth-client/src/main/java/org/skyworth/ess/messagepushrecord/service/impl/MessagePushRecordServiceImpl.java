/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.service.impl;

import org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity;
import org.skyworth.ess.messagepushrecord.vo.MessagePushRecordVO;
import org.skyworth.ess.messagepushrecord.excel.MessagePushRecordExcel;
import org.skyworth.ess.messagepushrecord.mapper.MessagePushRecordMapper;
import org.skyworth.ess.messagepushrecord.service.IMessagePushRecordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 消息推送日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
public class MessagePushRecordServiceImpl extends BaseServiceImpl<MessagePushRecordMapper, MessagePushRecordEntity> implements IMessagePushRecordService {

	@Override
	public IPage<MessagePushRecordVO> selectMessagePushRecordPage(IPage<MessagePushRecordVO> page, MessagePushRecordVO messagePushRecord) {
		return page.setRecords(baseMapper.selectMessagePushRecordPage(page, messagePushRecord));
	}


	@Override
	public List<MessagePushRecordExcel> exportMessagePushRecord(Wrapper<MessagePushRecordEntity> queryWrapper) {
		List<MessagePushRecordExcel> messagePushRecordList = baseMapper.exportMessagePushRecord(queryWrapper);
		//messagePushRecordList.forEach(messagePushRecord -> {
		//	messagePushRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, MessagePushRecord.getType()));
		//});
		return messagePushRecordList;
	}

}

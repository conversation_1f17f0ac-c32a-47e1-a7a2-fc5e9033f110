package org.skyworth.ess.guardian.thresholdcurrentstatus.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.vo.GuardianThresholdCurrentStatusVO;
import org.skyworth.ess.guardian.thresholdcurrentstatus.excel.GuardianThresholdCurrentStatusExcel;
import org.skyworth.ess.guardian.thresholdcurrentstatus.wrapper.GuardianThresholdCurrentStatusWrapper;
import org.skyworth.ess.guardian.thresholdcurrentstatus.service.IGuardianThresholdCurrentStatusService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士-设备上报阈值实时数据 控制器
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianThresholdCurrentStatus")
@Api(value = "安全卫士-设备上报阈值实时数据", tags = "安全卫士-设备上报阈值实时数据接口")
public class GuardianThresholdCurrentStatusController extends BladeController {

	private final IGuardianThresholdCurrentStatusService GuardianThresholdCurrentStatusService;

	/**
	 * 安全卫士-设备上报阈值实时数据 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianThresholdCurrentStatus")
	public R<GuardianThresholdCurrentStatusVO> detail(GuardianThresholdCurrentStatusEntity GuardianThresholdCurrentStatus) {
		GuardianThresholdCurrentStatusEntity detail = GuardianThresholdCurrentStatusService.getOne(Condition.getQueryWrapper(GuardianThresholdCurrentStatus));
		return R.data(GuardianThresholdCurrentStatusWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士-设备上报阈值实时数据 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianThresholdCurrentStatus")
	public R<IPage<GuardianThresholdCurrentStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianThresholdCurrentStatus, Query query) {
		IPage<GuardianThresholdCurrentStatusEntity> pages = GuardianThresholdCurrentStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianThresholdCurrentStatus, GuardianThresholdCurrentStatusEntity.class));
		return R.data(GuardianThresholdCurrentStatusWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士-设备上报阈值实时数据 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianThresholdCurrentStatus")
	public R<IPage<GuardianThresholdCurrentStatusVO>> page(GuardianThresholdCurrentStatusVO GuardianThresholdCurrentStatus, Query query) {
		IPage<GuardianThresholdCurrentStatusVO> pages = GuardianThresholdCurrentStatusService.selectGuardianThresholdCurrentStatusPage(Condition.getPage(query), GuardianThresholdCurrentStatus);
		return R.data(pages);
	}

	/**
	 * 安全卫士-设备上报阈值实时数据 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianThresholdCurrentStatus")
	public R save(@Valid @RequestBody GuardianThresholdCurrentStatusEntity GuardianThresholdCurrentStatus) {
		return R.status(GuardianThresholdCurrentStatusService.save(GuardianThresholdCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报阈值实时数据 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianThresholdCurrentStatus")
	public R update(@Valid @RequestBody GuardianThresholdCurrentStatusEntity GuardianThresholdCurrentStatus) {
		return R.status(GuardianThresholdCurrentStatusService.updateById(GuardianThresholdCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报阈值实时数据 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianThresholdCurrentStatus")
	public R submit(@Valid @RequestBody GuardianThresholdCurrentStatusEntity GuardianThresholdCurrentStatus) {
		return R.status(GuardianThresholdCurrentStatusService.saveOrUpdate(GuardianThresholdCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报阈值实时数据 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianThresholdCurrentStatusService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianThresholdCurrentStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianThresholdCurrentStatus")
	public void exportGuardianThresholdCurrentStatus(@ApiIgnore @RequestParam Map<String, Object> GuardianThresholdCurrentStatus, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianThresholdCurrentStatusEntity> queryWrapper = Condition.getQueryWrapper(GuardianThresholdCurrentStatus, GuardianThresholdCurrentStatusEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianThresholdCurrentStatus::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianThresholdCurrentStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianThresholdCurrentStatusExcel> list = GuardianThresholdCurrentStatusService.exportGuardianThresholdCurrentStatus(queryWrapper);
		ExcelUtil.export(response, "安全卫士-设备上报阈值实时数据数据" + DateUtil.time(), "安全卫士-设备上报阈值实时数据数据表", list, GuardianThresholdCurrentStatusExcel.class);
	}

}

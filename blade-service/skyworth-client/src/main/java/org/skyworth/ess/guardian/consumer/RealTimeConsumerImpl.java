package org.skyworth.ess.guardian.consumer;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.realTimeHandler.DataHandler;
import org.skyworth.ess.kafka.common.KafkaTopicContent;
import org.skyworth.ess.kafka.common.TopicProperty;
import org.skyworth.ess.kafka.consumer.KafkaConsumerService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.IotContextUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 实时数据处理类
 */
//@Service
@Slf4j
public class RealTimeConsumerImpl implements KafkaConsumerService<JSONObject> {

	@Autowired
	private IGuardianPlantService plantService;
	private static final List<String> TYPE_LIST = Arrays.asList(CommonConstant.BIZ_REALTIME_DATA,CommonConstant.SIM_NUMBER,CommonConstant.BIZ_ALARM_THRESHOLD,CommonConstant.BIZ_POWER_SETTING);
//	@TopicProperty(topicName = CommonConstant.TERMINAL_WARDS_TOPIC, key = CommonConstant.TERMINAL_WARDS_KEY, groupId = CommonConstant.TERMINAL_WARDS_KEY)
	@Override
	public boolean consumer(List<KafkaTopicContent<JSONObject>> topiContent) {
		Map<String,List<JSONObject>> dataList=new ConcurrentHashMap<>(16);

		topiContent.parallelStream().forEach(content -> {
			try{
				JSONObject data=content.getTopicData();
				if(ValidationUtil.isNotEmpty(data)){
					String bizType=data.getString(CommonConstant.BIZ_TYPE);
					if (dataList.containsKey(bizType)) {
						List<JSONObject> tmpList = dataList.get(bizType);
						tmpList.add(data);
					} else {
						List<JSONObject> tmpList = new ArrayList<>();
						tmpList.add(data);
						dataList.put(bizType, tmpList);
					}
				}

			}catch (Exception e){
				log.error("实时数据处理异常{}",e.getMessage());
			}
		});

		for(Map.Entry<String,List<JSONObject>> entry:dataList.entrySet()){
			String type=entry.getKey();
			if(ValidationUtil.isNotEmpty(type) && TYPE_LIST.contains(type)){
				DataHandler dataHandler= (DataHandler) IotContextUtils.getBean(CommonConstant.TERMINAL_WARDS_KEY+"_"+type);
				//处理业务逻辑
				List<JSONObject> objectList=entry.getValue();
				List<String> deviceSnList = objectList.stream()
					.filter(jsonObject -> jsonObject.containsKey("deviceSn"))
					.map(jsonObject -> jsonObject.getString("deviceSn"))
					.collect(Collectors.toList());
				List<GuardianPlantEntity> guardianPlantEntities = plantService.queryByGuardianSerialNumberList(deviceSnList);
				Map<String, Long> resultMap;
				Map<String,String> partitionMap;
				if(ValidationUtil.isNotEmpty(guardianPlantEntities)&&!guardianPlantEntities.isEmpty()){
					resultMap = guardianPlantEntities.stream()
						.collect(Collectors.toMap(
							GuardianPlantEntity::getSecurityGuardSerialNumber,
							GuardianPlantEntity::getPlantId
						));
					partitionMap = guardianPlantEntities.stream()
						.collect(Collectors.toMap(
							GuardianPlantEntity::getSecurityGuardSerialNumber,
							GuardianPlantEntity::getPartitionDate
						));
				}else {
					resultMap=new HashMap<>(16);
					partitionMap=new HashMap<>(16);
				}
				dataHandler.handler(entry.getValue(),resultMap,partitionMap);
			}
		}

		return true;
	}


}

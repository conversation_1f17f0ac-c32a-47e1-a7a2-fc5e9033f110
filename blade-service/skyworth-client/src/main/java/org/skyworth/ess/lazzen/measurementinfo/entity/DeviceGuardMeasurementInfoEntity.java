/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备卫士测量信息 实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@TableName("lazzen_device_guard_measurement_info")
@ApiModel(value = "DeviceGuardMeasurementInfo对象", description = "设备卫士测量信息")
@EqualsAndHashCode(callSuper = true)
public class DeviceGuardMeasurementInfoEntity extends TenantEntity {

	/**
	 * 设备网关唯一编号
	 */
	@ApiModelProperty(value = "设备网关唯一编号")
	private String gatewayUniqueNumber;
	/**
	 * 设备断路器地址64为第一位置
	 */
	@ApiModelProperty(value = "设备断路器地址64为第一位置")
	private String circuitBreakerAddress;
	/**
	 * A相电压，单位V 0x0276
	 */
	@ApiModelProperty(value = "A相电压，单位V 0x0276")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseAVoltage;
	/**
	 * A相电流，单位A 0x0260 ~0x0261
	 */
	@ApiModelProperty(value = "A相电流，单位A 0x0260 ~0x0261")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseACurrent;
	/**
	 * A相有功功率 0x0282
	 */
	@ApiModelProperty(value = "A相有功功率 0x0282")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseAPower;
	/**
	 * B相电压，单位V 0x0277
	 */
	@ApiModelProperty(value = "B相电压，单位V 0x0277")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseBVoltage;
	/**
	 * B相电流，单位A 0x0262 ~0x0263
	 */
	@ApiModelProperty(value = "B相电流，单位A 0x0262 ~0x0263")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseBCurrent;
	/**
	 * B相有功功率 0x0282
	 */
	@ApiModelProperty(value = "B相有功功率 0x0282")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseBPower;
	/**
	 * C相电压，单位V 0x0278
	 */
	@ApiModelProperty(value = "C相电压，单位V 0x0278")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseCVoltage;
	/**
	 * C相电流，单位A 0x0264 ~0x0265
	 */
	@ApiModelProperty(value = "C相电流，单位A 0x0264 ~0x0265")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseCCurrent;
	/**
	 * C相有功功率 0x0284
	 */
	@ApiModelProperty(value = "C相有功功率 0x0284")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal phaseCPower;
	/**
	 * 漏电电流 0x02AF
	 */
	@ApiModelProperty(value = "漏电电流 0x02AF")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal leakageCurrent;
	/**
	 * 实时温度 0x02AE
	 */
	@ApiModelProperty(value = "实时温度 0x02AE")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal temperature;
	/**
	 * (总)有功电能 0x029C ~0x029D
	 */
	@ApiModelProperty(value = "(总)有功电能 0x029C ~0x029D")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal totalActiveElectricalEnergy;
	/**
	 * (总)有功功率 0x0285
	 */
	@ApiModelProperty(value = "(总)有功功率 0x0285")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal totalActivePower;
	/**
	 * (总)功率因数 0x0295
	 */
	@ApiModelProperty(value = "(总)功率因数 0x0295")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal totalPowerFactor;
	/**
	 * (总)无功功率 0x0289
	 */
	@ApiModelProperty(value = "(总)无功功率 0x0289")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal reactivePower;
	/**
	 * (总)无功电能 0x02A4 ~0x02A5
	 */
	@ApiModelProperty(value = "(总)无功电能 0x02A4 ~0x02A5")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal reactiveEnergy;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "设备时间")
	private LocalDateTime deviceDateTime;

}

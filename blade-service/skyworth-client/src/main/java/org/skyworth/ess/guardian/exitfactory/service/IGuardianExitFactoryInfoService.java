/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.vo.GuardianExitFactoryInfoVO;
import org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * 安全卫士储能出厂信息 服务类
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public interface IGuardianExitFactoryInfoService extends BaseService<GuardianExitFactoryInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param guardianExitFactoryInfo
	 * @return
	 */
	IPage<GuardianExitFactoryInfoVO> selectGuardianExitFactoryInfoPage(IPage<GuardianExitFactoryInfoVO> page, GuardianExitFactoryInfoVO guardianExitFactoryInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianExitFactoryInfoExcel> exportGuardianExitFactoryInfo(Wrapper<GuardianExitFactoryInfoEntity> queryWrapper);

	R deleteLogicGuardianExitFactory(List<Long> longList);

	String importAddExcel(List<GuardianExitFactoryInfoExcel> guardianExitFactoryInfoExcels, Boolean aBoolean);

	String importModifyExcel(List<GuardianExitFactoryInfoExcel> guardianExitFactoryInfoExcels, Boolean aBoolean);
	int batchUpdate(List<String> serialNumberList);
	boolean updateDeadlineInfo(GuardianExitFactoryInfoEntity entity);

	GuardianExitFactoryInfoEntity getModelBySn(GuardianExitFactoryInfoEntity guardianExitFactoryInfoEntity);
}

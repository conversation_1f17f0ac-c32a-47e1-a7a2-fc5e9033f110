/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity;
import org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoVO;
import org.skyworth.ess.lazzen.measurementinfo.excel.DeviceGuardMeasurementInfoExcel;
import org.skyworth.ess.lazzen.measurementinfo.wrapper.DeviceGuardMeasurementInfoWrapper;
import org.skyworth.ess.lazzen.measurementinfo.service.IDeviceGuardMeasurementInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备卫士测量信息 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("deviceGuardMeasurementInfo/deviceGuardMeasurementInfo")
@Api(value = "设备卫士测量信息", tags = "设备卫士测量信息接口")
public class DeviceGuardMeasurementInfoController extends BladeController {

	private final IDeviceGuardMeasurementInfoService deviceGuardMeasurementInfoService;

	/**
	 * 设备卫士测量信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceGuardMeasurementInfo")
	public R<DeviceGuardMeasurementInfoVO> detail(DeviceGuardMeasurementInfoEntity deviceGuardMeasurementInfo) {
		DeviceGuardMeasurementInfoEntity detail = deviceGuardMeasurementInfoService.getOne(Condition.getQueryWrapper(deviceGuardMeasurementInfo));
		return R.data(DeviceGuardMeasurementInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 设备卫士测量信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceGuardMeasurementInfo")
	public R<IPage<DeviceGuardMeasurementInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceGuardMeasurementInfo, Query query) {
		IPage<DeviceGuardMeasurementInfoEntity> pages = deviceGuardMeasurementInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceGuardMeasurementInfo, DeviceGuardMeasurementInfoEntity.class));
		return R.data(DeviceGuardMeasurementInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 设备卫士测量信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceGuardMeasurementInfo")
	public R<IPage<DeviceGuardMeasurementInfoVO>> page(DeviceGuardMeasurementInfoVO deviceGuardMeasurementInfo, Query query) {
		IPage<DeviceGuardMeasurementInfoVO> pages = deviceGuardMeasurementInfoService.selectDeviceGuardMeasurementInfoPage(Condition.getPage(query), deviceGuardMeasurementInfo);
		return R.data(pages);
	}

	/**
	 * 设备卫士测量信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceGuardMeasurementInfo")
	public R save(@Valid @RequestBody DeviceGuardMeasurementInfoEntity deviceGuardMeasurementInfo) {
		return R.status(deviceGuardMeasurementInfoService.save(deviceGuardMeasurementInfo));
	}

	/**
	 * 设备卫士测量信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceGuardMeasurementInfo")
	public R update(@Valid @RequestBody DeviceGuardMeasurementInfoEntity deviceGuardMeasurementInfo) {
		return R.status(deviceGuardMeasurementInfoService.updateById(deviceGuardMeasurementInfo));
	}

	/**
	 * 设备卫士测量信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceGuardMeasurementInfo")
	public R submit(@Valid @RequestBody DeviceGuardMeasurementInfoEntity deviceGuardMeasurementInfo) {
		return R.status(deviceGuardMeasurementInfoService.saveOrUpdate(deviceGuardMeasurementInfo));
	}

	/**
	 * 设备卫士测量信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceGuardMeasurementInfoService.deleteLogic(Func.toLongList(ids)));
	}
}

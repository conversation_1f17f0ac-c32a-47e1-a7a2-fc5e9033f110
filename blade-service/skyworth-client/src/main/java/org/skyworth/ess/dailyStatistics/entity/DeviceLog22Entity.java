/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.dailyStatistics.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/智能能量变换器日志表，记录22数据 实体类
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@TableName("device_log22")
@ApiModel(value = "DeviceLog22对象", description = "设备/智能能量变换器日志表，记录22数据")
@EqualsAndHashCode(callSuper = true)
public class DeviceLog22Entity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 同步状态（N未同步；Y已同步）
	 */
	@ApiModelProperty(value = "同步状态（N未同步；Y已同步）")
	private String synchStatus;
	/**
	 * 设备/智能能量变换器SN
	 */
	@ApiModelProperty(value = "设备/智能能量变换器SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ApiModelProperty(value = "modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 电压 phase a
	 */
	@ApiModelProperty(value = "电压 phase a")
	private BigDecimal phaseAVoltage;
	/**
	 * 电流 phase a
	 */
	@ApiModelProperty(value = "电流 phase a")
	private BigDecimal phaseACurrent;
	/**
	 * 功率 phase a
	 */
	@ApiModelProperty(value = "功率 phase a")
	private BigDecimal phaseAPower;
	/**
	 * 频率 phase  a
	 */
	@ApiModelProperty(value = "频率 phase  a")
	private BigDecimal phaseAFrequency;
	/**
	 * 电压 phase b
	 */
	@ApiModelProperty(value = "电压 phase b")
	private BigDecimal phaseBVoltage;
	/**
	 * 电流 phase b
	 */
	@ApiModelProperty(value = "电流 phase b")
	private BigDecimal phaseBCurrent;
	/**
	 * 功率 phase b
	 */
	@ApiModelProperty(value = "功率 phase b")
	private BigDecimal phaseBPower;
	/**
	 * 频率 phase  b
	 */
	@ApiModelProperty(value = "频率 phase  b")
	private BigDecimal phaseBFrequency;
	/**
	 * 电压 phase c
	 */
	@ApiModelProperty(value = "电压 phase c")
	private BigDecimal phaseCVoltage;
	/**
	 * 电流 phase c
	 */
	@ApiModelProperty(value = "电流 phase c")
	private BigDecimal phaseCCurrent;
	/**
	 * 功率 phase  c
	 */
	@ApiModelProperty(value = "功率 phase  c")
	private BigDecimal phaseCPower;
	/**
	 * 频率 phase  c
	 */
	@ApiModelProperty(value = "频率 phase  c")
	private BigDecimal phaseCFrequency;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv1Voltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv1Current;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal mppt1Power;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv2Voltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv2Current;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal mppt2Power;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv3Voltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv3Current;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal mppt3Power;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal innerTemperature;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal inverterMode;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal errorCode;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal totalEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal totalGenerationTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal todayEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal activePower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reactivePower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal todayPeakPower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal powerFactor;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv4Voltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal pv4Current;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal mppt4Power;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseSWattOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseTWattOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal accumulatedEnergyOfPositive;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal accumulatedEnergyOfNegative;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseSWattOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseTWattOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal accumulatedEnergyOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l1NPhaseVoltageOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l2NPhaseVoltageOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l3NPhaseVoltageOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l1CurrentOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l2CurrentOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l3CurrentOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l1NPhaseVoltageOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l2NPhaseVoltageOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l3NPhaseVoltageOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l1CurrentOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l2CurrentOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal l3CurrentOfLoad;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal todayImportEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal todayExportEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal todayLoadEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal frequencyOfGrid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseRVoltageOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseRCurrentOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal frequencyOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseSVoltageOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseSCurrentOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseSWattOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseTVoltageOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseTCurrentOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal phaseTWattOfEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal dailyEnergyToEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal accumulatedEnergyToEps;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batterySoc;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryTemperature;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryVoltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryCurrent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryPower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 最高单体储能电压
	 */
	@ApiModelProperty(value = "最高单体储能电压")
	private BigDecimal batteryMaximumCellVoltage;
	/**
	 * 最低单体储能电压
	 */
	@ApiModelProperty(value = "最低单体储能电压")
	private BigDecimal batteryMinimumCellVoltage;
	/**
	 * 最高单体储能温度
	 */
	@ApiModelProperty(value = "最高单体储能温度")
	private BigDecimal batteryMaximumCellTemperature;
	/**
	 * 最低单体储能温度
	 */
	@ApiModelProperty(value = "最低单体储能温度")
	private BigDecimal batteryMinimumCellTemperature;
	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	private String errorMessage4;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseCurrentOfGenerator ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorFrequency;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTodayEnergy;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTotalEnergy;

	@ApiModelProperty(value = "储能循环次数")
	private BigDecimal batteryMaximumCyclesTimes;
	// 104A 中的 4-5位
	private String batteryStatus;
}

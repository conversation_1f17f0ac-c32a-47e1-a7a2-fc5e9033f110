/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 设备卫士测量信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceGuardMeasurementInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备网关唯一编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备网关唯一编号")
	private String gatewayUniqueNumber;
	/**
	 * 设备断路器地址64为第一位置
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备断路器地址64为第一位置")
	private String circuitBreakerAddress;
	/**
	 * A相电压，单位V 0x0276
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相电压，单位V 0x0276")
	private BigDecimal phaseAVoltage;
	/**
	 * A相电流，单位A 0x0260 ~0x0261
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相电流，单位A 0x0260 ~0x0261")
	private BigDecimal phaseACurrent;
	/**
	 * A相有功功率 0x0282
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相有功功率 0x0282")
	private BigDecimal phaseAPower;
	/**
	 * B相电压，单位V 0x0277
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相电压，单位V 0x0277")
	private BigDecimal phaseBVoltage;
	/**
	 * B相电流，单位A 0x0262 ~0x0263
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相电流，单位A 0x0262 ~0x0263")
	private BigDecimal phaseBCurrent;
	/**
	 * B相有功功率 0x0282
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相有功功率 0x0282")
	private BigDecimal phaseBPower;
	/**
	 * C相电压，单位V 0x0278
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相电压，单位V 0x0278")
	private BigDecimal phaseCVoltage;
	/**
	 * C相电流，单位A 0x0264 ~0x0265
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相电流，单位A 0x0264 ~0x0265")
	private BigDecimal phaseCCurrent;
	/**
	 * C相有功功率 0x0284
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相有功功率 0x0284")
	private BigDecimal phaseCPower;
	/**
	 * 漏电电流 0x02AF
	 */
	@ColumnWidth(20)
	@ExcelProperty("漏电电流 0x02AF")
	private BigDecimal leakageCurrent;
	/**
	 * 实时温度 0x02AE
	 */
	@ColumnWidth(20)
	@ExcelProperty("实时温度 0x02AE")
	private BigDecimal temperature;
	/**
	 * (总)有功电能 0x029C ~0x029D
	 */
	@ColumnWidth(20)
	@ExcelProperty("(总)有功电能 0x029C ~0x029D")
	private BigDecimal totalActiveElectricalEnergy;
	/**
	 * (总)有功功率 0x0285
	 */
	@ColumnWidth(20)
	@ExcelProperty("(总)有功功率 0x0285")
	private BigDecimal totalActivePower;
	/**
	 * (总)功率因数 0x0295
	 */
	@ColumnWidth(20)
	@ExcelProperty("(总)功率因数 0x0295")
	private BigDecimal totalPowerFactor;
	/**
	 * (总)无功功率 0x0289
	 */
	@ColumnWidth(20)
	@ExcelProperty("(总)无功功率 0x0289")
	private BigDecimal reactivePower;
	/**
	 * (总)无功电能 0x02A4 ~0x02A5
	 */
	@ColumnWidth(20)
	@ExcelProperty("(总)无功电能 0x02A4 ~0x02A5")
	private BigDecimal reactiveEnergy;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

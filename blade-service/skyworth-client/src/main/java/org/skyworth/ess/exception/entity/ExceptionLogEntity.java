/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 异常日志 实体类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Data
@TableName("exception_log")
@ApiModel(value = "ExceptionLog对象", description = "异常日志")
@EqualsAndHashCode(callSuper = true)
public class ExceptionLogEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 异常类型：device智能能量变换器；battery储能
	 */
	@ApiModelProperty(value = "异常类型：device智能能量变换器；battery储能")
	private String exceptionType;
	/**
	 * SN号
	 */
	@ApiModelProperty(value = "SN号")
	@TableField(value = "serial_number")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 异常消息
	 */
	@ApiModelProperty(value = "异常消息")
	private String exceptionMessage;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "地址码")
	private String addressCode;

	@ApiModelProperty(value = "设备上报时间时区")
	private String timeZone;

}

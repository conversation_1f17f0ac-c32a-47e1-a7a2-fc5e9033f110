/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.mapper;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity;
import org.skyworth.ess.setItem.vo.SetItemMultiLanguageVO;
import org.skyworth.ess.setItem.excel.SetItemMultiLanguageExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * APP设置项配置名称多语言 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
public interface SetItemMultiLanguageMapper extends BaseMapper<SetItemMultiLanguageEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param setItemMultiLanguage
	 * @return
	 */
	List<SetItemMultiLanguageVO> selectSetItemMultiLanguagePage(IPage page, SetItemMultiLanguageVO setItemMultiLanguage);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SetItemMultiLanguageExcel> exportSetItemMultiLanguage(@Param("ew") Wrapper<SetItemMultiLanguageEntity> queryWrapper);


	List<JSONObject> selectListByItemId(List<Long> longList);

    int deleteLogicByItemId(@Param("longList") List<Long> longList);
}

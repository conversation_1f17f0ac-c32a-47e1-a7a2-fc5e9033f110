package org.skyworth.ess.lazzen.analysisdata.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.analysisdata.service.constant.ObjAttributeName;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.redis.cache.BladeRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
/**
 * <AUTHOR>
 * 网关应答
 */
@Service("lazzenGatewayAckServiceImpl" )
@Slf4j
public class LazzenGatewayAckServiceImpl extends LazzenAbstractService{
    @Autowired
    private BladeRedis bladeRedis;
    @Override
    public void saveData(List<JSONObject> listMap) {
        log.info("LazzenGatewayAckServiceImpl saveData params : {}", listMap);
        for(JSONObject jsonObject : listMap) {
            String requestId = (String) jsonObject.get(ObjAttributeName.fcCmdId.getAttributeName());
            String requestAck = (String) jsonObject.get(ObjAttributeName.fcCmdAck.getAttributeName());
            bladeRedis.setEx(CommonConstant.LAZZEN_REQUEST_ID_PREFIX + requestId, requestAck, Duration.ofSeconds(30));
        }
    }
}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.vo;

import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 储能每日统计 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatteryEverydayTotalVO extends BatteryEverydayTotalEntity {
	private static final long serialVersionUID = 1L;

	private String appTotalDate;
	/**
	 * 光伏日发电量 200B
	 */
	private BigDecimal appBatteryDailyChargeEnergy;
	// 地址码 200F
	private BigDecimal appBatteryDailyDischargeEnergy;
	/**
	 * 储能总储电量
	 */
	private BigDecimal appBatteryAccumulatedChargeEnergy;
	// 地址码 1027
	private BigDecimal appTodayEnergy;
	// 地址码 1336
	private BigDecimal appTodayLoadEnergy;
	// 地址码 1360
	private BigDecimal appDailyEnergyToEps;
	// 地址码 1336 + 1360
	// 如果只有一个电池： 1027 + 200F ，如果有2个电池则 再加 220F
	private BigDecimal appLoadAddEps;
	// 1332
	private BigDecimal appTodayImportEnergy;
	// 1334
	private BigDecimal appTodayExportEnergy;
	// 并机
	// 13D8
	private BigDecimal appPvlDailyGeneratingEnergySum;
	// 13E4
	private BigDecimal appBatteryDailyDischargeEnergyParallel;
	// 13AC + 13C0
	private BigDecimal appLoadAddBackup;
	// 13E0
	private BigDecimal appBatteryDailyChargeEnergyParallel;
	// begin 2025.4
	// 220B 电池2 充电量
	private BigDecimal appBatteryTodayChargeEnergy2;
	// 220F 电池2 放电量
	private BigDecimal appBatteryTodayDischargeEnergy2;
	// end 2025.4
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedcurrentstatus.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity;
import org.skyworth.ess.guardian.timedcurrentstatus.vo.GuardianTimedCurrentStatusVO;
import org.skyworth.ess.guardian.timedcurrentstatus.excel.GuardianTimedCurrentStatusExcel;
import org.skyworth.ess.guardian.timedcurrentstatus.mapper.GuardianTimedCurrentStatusMapper;
import org.skyworth.ess.guardian.timedcurrentstatus.service.IGuardianTimedCurrentStatusService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安全卫士-设备上报定时设置当前状态信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
@Slf4j
@DS("slave")
public class GuardianTimedCurrentStatusServiceImpl extends BaseServiceImpl<GuardianTimedCurrentStatusMapper, GuardianTimedCurrentStatusEntity> implements IGuardianTimedCurrentStatusService {

	@Override
	public IPage<GuardianTimedCurrentStatusVO> selectGuardianTimedCurrentStatusPage(IPage<GuardianTimedCurrentStatusVO> page, GuardianTimedCurrentStatusVO GuardianTimedCurrentStatus) {
		return page.setRecords(baseMapper.selectGuardianTimedCurrentStatusPage(page, GuardianTimedCurrentStatus));
	}


	@Override
	public List<GuardianTimedCurrentStatusExcel> exportGuardianTimedCurrentStatus(Wrapper<GuardianTimedCurrentStatusEntity> queryWrapper) {
		List<GuardianTimedCurrentStatusExcel> GuardianTimedCurrentStatusList = baseMapper.exportGuardianTimedCurrentStatus(queryWrapper);
		//GuardianTimedCurrentStatusList.forEach(GuardianTimedCurrentStatus -> {
		//	GuardianTimedCurrentStatus.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianTimedCurrentStatus.getType()));
		//});
		return GuardianTimedCurrentStatusList;
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		return baseMapper.deleteLogicByPlantIdAndSn(plantId,securityGuardSerialNumber);
	}

	@Override
	public GuardianTimedCurrentStatusEntity getTimeByLast(String deviceSn) {
		return baseMapper.getTimeByLast(deviceSn);
	}

}

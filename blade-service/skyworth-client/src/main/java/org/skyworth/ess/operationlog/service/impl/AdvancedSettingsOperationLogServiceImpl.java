/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.operationlog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.mapper.AdvancedSettingsOperationLogMapper;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 高级设置操作日志 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Service
public class AdvancedSettingsOperationLogServiceImpl extends BaseServiceImpl<AdvancedSettingsOperationLogMapper, AdvancedSettingsOperationLogEntity> implements IAdvancedSettingsOperationLogService {


    @Override
    public IPage<AdvancedSettingsOperationLogVO> getLogList(IPage<AdvancedSettingsOperationLogVO> page, Map<String, Object> map) {
		// 如果是并机，则查询这个站点下所有智能能量变换器的数据
		if(map.containsKey("isParallelMode") && BizConstant.CHAR_ONE.equals(map.get("isParallelMode"))) {
			map.remove("deviceSerialNumber");
		}
		// 校验时间格式
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dateFormat.setLenient(false);
		Date startTime = null;
		Date endTime = null;

		if (map.containsKey("startTime") && map.get("startTime") != null) {
			try {
				startTime = dateFormat.parse((String) map.get("startTime"));
			} catch (ParseException e) {
				throw new BusinessException("client.params.time.format.error");
			}
		}

		if (map.containsKey("endTime") && map.get("endTime") != null) {
			try {
				endTime = dateFormat.parse((String) map.get("endTime"));
			} catch (ParseException e) {
				throw new BusinessException("client.params.time.format.error");
			}
		}

		// 校验开始时间是否早于结束时间
		if (startTime != null && endTime != null && startTime.after(endTime)) {
			throw new BusinessException("client.params.time.size.error");
		}

		List<AdvancedSettingsOperationLogVO> logList = baseMapper.getLogList(page,map);
		return page.setRecords(logList);
    }
}

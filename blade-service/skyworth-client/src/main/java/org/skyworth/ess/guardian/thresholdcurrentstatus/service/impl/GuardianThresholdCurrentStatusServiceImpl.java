/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.thresholdcurrentstatus.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.vo.GuardianThresholdCurrentStatusVO;
import org.skyworth.ess.guardian.thresholdcurrentstatus.excel.GuardianThresholdCurrentStatusExcel;
import org.skyworth.ess.guardian.thresholdcurrentstatus.mapper.GuardianThresholdCurrentStatusMapper;
import org.skyworth.ess.guardian.thresholdcurrentstatus.service.IGuardianThresholdCurrentStatusService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安全卫士-设备上报阈值实时数据 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
@Slf4j
@DS("slave")
public class GuardianThresholdCurrentStatusServiceImpl extends BaseServiceImpl<GuardianThresholdCurrentStatusMapper, GuardianThresholdCurrentStatusEntity> implements IGuardianThresholdCurrentStatusService {

	@Override
	public IPage<GuardianThresholdCurrentStatusVO> selectGuardianThresholdCurrentStatusPage(IPage<GuardianThresholdCurrentStatusVO> page, GuardianThresholdCurrentStatusVO GuardianThresholdCurrentStatus) {
		return page.setRecords(baseMapper.selectGuardianThresholdCurrentStatusPage(page, GuardianThresholdCurrentStatus));
	}


	@Override
	public List<GuardianThresholdCurrentStatusExcel> exportGuardianThresholdCurrentStatus(Wrapper<GuardianThresholdCurrentStatusEntity> queryWrapper) {
		List<GuardianThresholdCurrentStatusExcel> GuardianThresholdCurrentStatusList = baseMapper.exportGuardianThresholdCurrentStatus(queryWrapper);
		//GuardianThresholdCurrentStatusList.forEach(GuardianThresholdCurrentStatus -> {
		//	GuardianThresholdCurrentStatus.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianThresholdCurrentStatus.getType()));
		//});
		return GuardianThresholdCurrentStatusList;
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		return baseMapper.deleteLogicByPlantIdAndSn(plantId,securityGuardSerialNumber);
	}

	@Override
	public GuardianThresholdCurrentStatusEntity getThresholdByLast(String deviceSn) {
		return baseMapper.getThresholdByLast(deviceSn);
	}

}

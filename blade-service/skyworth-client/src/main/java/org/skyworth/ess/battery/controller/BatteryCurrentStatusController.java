/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.vo.BatteryCurrentStatusVO;
import org.skyworth.ess.battery.wrapper.BatteryCurrentStatusWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 储能当前状态 控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("batteryCurrentStatus")
@Api(value = "储能当前状态", tags = "储能当前状态接口")
public class BatteryCurrentStatusController extends BladeController {

	private final IBatteryCurrentStatusService batteryCurrentStatusService;



	/**
	 * 储能当前状态+运行天数
	 */
	@GetMapping("/view")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情+运行天数", notes = "传入queryCondition")
	public R<BatteryCurrentStatusVO> view(QueryCondition queryCondition) {
		return R.data(batteryCurrentStatusService.view(queryCondition));
	}

	/**
	 * 储能当前状态 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入batteryCurrentStatus")
	public R<BatteryCurrentStatusVO> detail(BatteryCurrentStatusEntity batteryCurrentStatus) {
		BatteryCurrentStatusEntity detail = batteryCurrentStatusService.getOne(Condition.getQueryWrapper(batteryCurrentStatus));
		return R.data(BatteryCurrentStatusWrapper.build().entityVO(detail));
	}

	/**
	 * 储能当前状态 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入batteryCurrentStatus")
	public R<IPage<BatteryCurrentStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> batteryCurrentStatus, Query query) {
		IPage<BatteryCurrentStatusEntity> pages = batteryCurrentStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(batteryCurrentStatus, BatteryCurrentStatusEntity.class));
		return R.data(BatteryCurrentStatusWrapper.build().pageVO(pages));
	}


	/**
	 * 储能当前状态 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入batteryCurrentStatus")
	public R save(@Valid @RequestBody BatteryCurrentStatusEntity batteryCurrentStatus) {
		return R.status(batteryCurrentStatusService.save(batteryCurrentStatus));
	}

	/**
	 * 储能当前状态 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入batteryCurrentStatus")
	public R update(@Valid @RequestBody BatteryCurrentStatusEntity batteryCurrentStatus) {
		return R.status(batteryCurrentStatusService.updateById(batteryCurrentStatus));
	}

	/**
	 * 储能当前状态 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入batteryCurrentStatus")
	public R submit(@Valid @RequestBody BatteryCurrentStatusEntity batteryCurrentStatus) {
		return R.status(batteryCurrentStatusService.saveOrUpdate(batteryCurrentStatus));
	}


}

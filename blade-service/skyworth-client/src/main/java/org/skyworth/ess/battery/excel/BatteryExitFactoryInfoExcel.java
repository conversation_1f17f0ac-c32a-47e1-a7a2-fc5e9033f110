/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.springblade.common.excel.ExcelBusinessUniqueValidate;
import org.springblade.common.excel.ExcelNotNullValidate;

import java.io.Serializable;
import java.util.Date;


/**
 * 储能出厂信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class BatteryExitFactoryInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 储能SN
	 */
	@ColumnWidth(50)
	@ExcelProperty("储能SN")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
	@ExcelNotNullValidate(message = "储能SN")
    private String batterySerialNumber;
	/**
	 * 储能型号
	 */
	@ColumnWidth(30)
	@ExcelProperty("设备型号")
	@ExcelNotNullValidate(message = "储能型号")
	private String batteryType;
	/**
	 * 额定储能电压
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定储能电压")
	@ExcelNotNullValidate(message = "额定储能电压")
	private String ratedBatteryVoltage;
	/**
	 * 额定储能容量
	 */
	@ColumnWidth(30)
	@ExcelProperty("额定储能容量")
	@ExcelNotNullValidate(message = "额定储能容量")
	private String ratedBatteryCapacity;
	/**
	 * 额定储能能量
	 */
	@ColumnWidth(30)
	@ExcelProperty("额定储能能量")
	@ExcelNotNullValidate(message = "额定储能能量")
	private String ratedBatteryEnergy;
	/**
	 * 内含单体电芯容量
	 */
	@ColumnWidth(30)
	@ExcelProperty("单体容量")
	@ExcelNotNullValidate(message = "单体容量")
	private String singleCapacity;
	/**
	 * 内含单体电芯串并联数
	 */
	@ColumnWidth(80)
	@ExcelProperty("内含单体串并联数")
	@ExcelNotNullValidate(message = "内含单体串并联数")
	private String singleSeriesParallelingNumber;
	/**
	 * 厂家
	 */
	@ColumnWidth(20)
	@ExcelProperty("公司")
	@ExcelNotNullValidate(message = "公司")
	private String company;
	/**
	 * 出厂日期
	 */
	@ColumnWidth(50)
	@ExcelProperty("出厂日期")
	@ExcelNotNullValidate(message = "出厂日期")
	private Date exitFactoryDate;
	/**
	 * 质保年限
	 */
	@ColumnWidth(50)
	@ExcelProperty("质保年限")
	@ExcelNotNullValidate(message = "质保年限")
	private String qualityGuaranteeYear;
//	/**
//	 * 内含单体串联数
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("Single Series Number")
//	private String singleSeriesNumber;
//	/**
//	 * 创建人账号
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("创建人账号")
//	private String createUserAccount;
//	/**
//	 * 更新人账号
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("更新人账号")
//	private String updateUserAccount;
//	/**
//	 * 租户ID
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("租户ID")
//	private String tenantId;
//	/**
//	 * 逻辑删除
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("逻辑删除")
//	private Integer isDeleted;

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.mapper;

import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantVO;
import org.skyworth.ess.lazzen.gatewayplant.excel.GatewayPlantExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 良信网关站点关系表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface GatewayPlantMapper extends BaseMapper<GatewayPlantEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gatewayPlant
	 * @return
	 */
	List<GatewayPlantVO> selectGatewayPlantPage(@Param("page") IPage page, @Param("params") GatewayPlantVO gatewayPlant);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GatewayPlantExcel> exportGatewayPlant(@Param("ew") Wrapper<GatewayPlantEntity> queryWrapper);

	boolean batchUpdateHeartTimeByGatewayUniqueNumber(@Param("updateList")List<GatewayPlantEntity> updateList);
	boolean batchUpdateStatusByGatewayUniqueNumber(@Param("updateList")List<GatewayPlantEntity> updateList);
	Map<String, Object> getAllGuardianGatePositionSetup(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);

	GatewayPlantEntity detail(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);

    void batchDeleteLogicByPlantId(@Param("list")List<Long> longList,@Param("account") String account);

	int updateDataByCondition(@Param("params") GatewayPlantEntity updateGuardianPlantEntity);
}

/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedpower.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.vo.GuardianTimedPowerVO;
import java.util.Objects;

/**
 * 安全卫士定时设置-功率设置 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public class GuardianTimedPowerWrapper extends BaseEntityWrapper<GuardianTimedPowerEntity, GuardianTimedPowerVO>  {

	public static GuardianTimedPowerWrapper build() {
		return new GuardianTimedPowerWrapper();
 	}

	@Override
	public GuardianTimedPowerVO entityVO(GuardianTimedPowerEntity GuardianTimedPower) {
		GuardianTimedPowerVO GuardianTimedPowerVO = Objects.requireNonNull(BeanUtil.copy(GuardianTimedPower, GuardianTimedPowerVO.class));

		//User createUser = UserCache.getUser(GuardianTimedPower.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianTimedPower.getUpdateUser());
		//GuardianTimedPowerVO.setCreateUserName(createUser.getName());
		//GuardianTimedPowerVO.setUpdateUserName(updateUser.getName());

		return GuardianTimedPowerVO;
	}


}

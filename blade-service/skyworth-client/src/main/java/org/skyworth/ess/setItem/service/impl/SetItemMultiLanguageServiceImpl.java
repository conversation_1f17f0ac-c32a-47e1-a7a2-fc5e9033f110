/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity;
import org.skyworth.ess.setItem.vo.SetItemMultiLanguageVO;
import org.skyworth.ess.setItem.excel.SetItemMultiLanguageExcel;
import org.skyworth.ess.setItem.mapper.SetItemMultiLanguageMapper;
import org.skyworth.ess.setItem.service.ISetItemMultiLanguageService;
import org.skyworth.ess.setItem.wrapper.SetItemMultiLanguageWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.util.List;

/**
 * APP设置项配置名称多语言 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class SetItemMultiLanguageServiceImpl extends BaseServiceImpl<SetItemMultiLanguageMapper, SetItemMultiLanguageEntity> implements ISetItemMultiLanguageService {

	@Resource
	private SetItemMultiLanguageMapper setItemMultiLanguageMapper;

	@Override
	public IPage<SetItemMultiLanguageVO> selectSetItemMultiLanguagePage(IPage<SetItemMultiLanguageVO> page, SetItemMultiLanguageVO setItemMultiLanguage) {
		return page.setRecords(baseMapper.selectSetItemMultiLanguagePage(page, setItemMultiLanguage));
	}


	@Override
	public List<SetItemMultiLanguageExcel> exportSetItemMultiLanguage(Wrapper<SetItemMultiLanguageEntity> queryWrapper) {
		List<SetItemMultiLanguageExcel> setItemMultiLanguageList = baseMapper.exportSetItemMultiLanguage(queryWrapper);
		//setItemMultiLanguageList.forEach(setItemMultiLanguage -> {
		//	setItemMultiLanguage.setTypeName(DictCache.getValue(DictEnum.YES_NO, SetItemMultiLanguage.getType()));
		//});
		return setItemMultiLanguageList;
	}

	@Override
	public List<JSONObject> selectListByItemId(List<Long> longList) {
		return setItemMultiLanguageMapper.selectListByItemId(longList);
	}

    @Override
    public int deleteLogicByItemId(List<Long> longList) {
        return setItemMultiLanguageMapper.deleteLogicByItemId(longList);
    }

}

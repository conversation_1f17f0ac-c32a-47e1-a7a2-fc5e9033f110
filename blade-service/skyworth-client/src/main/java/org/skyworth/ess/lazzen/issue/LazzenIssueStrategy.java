package org.skyworth.ess.lazzen.issue;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.guardian.issue.IssueStrategyEntity;
import org.springblade.core.tool.api.R;

import java.util.Map;

public interface LazzenIssueStrategy {

	//01.校验设备在线状态
	//02.拼接content内容
	//03.构造下发对象
	//04.调用远程接口进行下发
	//05.处理业务结果
	boolean checkDeviceOnlineStatus(IssueStrategyEntity issueStrategyEntity);

	String assembleContent(IssueStrategyEntity issueStrategyEntity);

	JSONObject constructIssueObject(String content, IssueStrategyEntity issueStrategyEntity);

	Map<String, String> invokeRemoteInterface(JSONObject issueObject);

	R handleBusinessResult(Map<String, String> invokeResult, IssueStrategyEntity issueStrategyEntity);
}

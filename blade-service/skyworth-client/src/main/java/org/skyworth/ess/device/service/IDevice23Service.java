/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.excel.Device23Excel;
import org.skyworth.ess.device.vo.Device23VO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Map;

/**
 * 设备/智能能量变换器表，记录2.1数据 服务类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface IDevice23Service extends BaseService<Device23Entity> {

	List<Device23Entity> getEntity(Wrapper<Device23Entity> queryWrapper);

	boolean save(Device23Entity device21);

	R<String> workModeIssueToDevice(DeviceCustomModeEntity deviceCustomModeEntity);

	IPage<Device23VO> selectDevice23Page(IPage<Object> page, Device23VO device23);

	List<Device23Excel> exportDevice23(QueryWrapper<Device23Entity> queryWrapper);

    int updateSetup(Map<String, Object> device23, Long plantId, String deviceSerialNumber);

	int updateByPlantId(DeviceCustomModeEntity deviceCustomModeEntity);

	int deleteByPlantId(Long plantId, String deviceSerialNumber);

	int updatePlantMode(Long plantId,String deviceSerialNumber);
}

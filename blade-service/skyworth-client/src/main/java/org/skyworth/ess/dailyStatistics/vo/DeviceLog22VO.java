/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.dailyStatistics.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.dailyStatistics.entity.DeviceLog22Entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/智能能量变换器日志表，记录22数据 视图实体类
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceLog22VO extends DeviceLog22Entity {
	private static final long serialVersionUID = 1L;

	private String appTotalDate;
	private Date appDeviceDateTime;
	private BigDecimal appBatteryDailyChargeEnergy;

	private BigDecimal appBatteryDailyDischargeEnergy;
	// 光伏日发电量，上层对象中包含
	private BigDecimal pvTodayEnergy;
	//储能包 储电量
	private BigDecimal appBatteryAccumulatedChargeEnergy;

	// 地址码 1048
	private BigDecimal appPvTotalInputPower;
	// 地址码 2009 正数和0记录为0，负数则取绝对值，取 Battery power 的负数值
	private BigDecimal appBatteryPower;
	// 地址码 130A
	private BigDecimal appPhaserWattOfLoad;
	// 130C
	private BigDecimal appPhasesWattOfLoad;
	// 130E
	private BigDecimal appPhasetWattOfLoad;
	// 地址码 1353
	private BigDecimal appPhaserWattOfEps;
	// 1359
	private BigDecimal appPhasesWattOfEps;
	// 135E
	private BigDecimal appPhasetWattOfEps;

	// 绿电消耗功率 ： 天报表 0x1048+0x2009(电池充电时，0x2009为负数，取0）+0x2209(电池充电时，0x2209为负数，取0)
	// 地址码 130A + 130C + 130E + 地址码 1353 + 1359 + 135E
	private BigDecimal appLoadAddEps;
	// 1300
	private BigDecimal appPhaserWattOfGrid;
	// 1302
	private BigDecimal appPhasesWattOfGrid;
	// 1304
	private BigDecimal appPhasetWattOfGrid;
	// 2000
	private BigDecimal appBatterySoc;
	// 2009  少于0的时候记录为0，取 Battery power 的正数值 , 正数为放电功率 ，负数为充电功率
	private BigDecimal appBatteryInput;
	// 1300 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhaser;
	// 1302 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhases;
	// 1304 取负数，正数和0记录为0，负数则取绝对值相加
	private BigDecimal appFeedInGridPhaset;

	private BigDecimal fromPv = new BigDecimal("0");
	private BigDecimal fromGrid = new BigDecimal("0");
	// 2024.3月版本 begin
	/**
	 * 原始当日光伏发电量，未转换，不带单位  1027  battery_current_status
	 */
	private BigDecimal originalTodayEnergy;
	/**
	 * 原始当日电网输出，未转换，不带单位  1334  馈电量  device_current_status
	 */
	private BigDecimal originalTodayExportEnergy;
	// 1336 原始值  device_current_status
	private BigDecimal originalTodayLoadEnergy;
	// 1360 原始值 device_current_status
	private BigDecimal originalDailyEnergyToEps;
	// 200B 原始值  device_current_status
	private BigDecimal originalBatteryDailyChargeEnergy;
	/**
	 * 电网今日输入 1332 原始数据  device_current_status
	 */
	private BigDecimal originalTodayImportEnergy;
	// end

	// 并机
	// 13DC
	private BigDecimal appTotallyInputDcWattSum;
	// 13DE
	private BigDecimal appBatteryPowerSum;
	// 13A6
	private BigDecimal appPhaseL1WattOfLoadSum;
	// 13A8
	private BigDecimal appPhaseL2WattOfLoadSum;
	// 13AA
	private BigDecimal appPhaseL3WattOfLoadSum;
	// 13B4
	private BigDecimal appPhaseL1WattSumOfBackup;
	// 13B6
	private BigDecimal appPhaseL2WattSumOfBackup;
	// 13B8
	private BigDecimal appPhaseL3WattSumOfBackup;
	// 13A6 + 13A8 + 13AA + 13B4 + 13B6 + 13B8
	private BigDecimal appLoadAddBackup;
	// 13A0
	private BigDecimal appPhaseL1WattOfGridSum;
	// 13A2
	private BigDecimal appPhaseL2WattOfGridSum;
	// 13A4
	private BigDecimal appPhaseL3WattOfGridSum;
	// 并机 end

	// begin 2025.4
	// 2200 - SOC值2
	private BigDecimal batterySoc2;

	// 2209 - 电池功率2
	private BigDecimal batteryPower2;

	// 220B - 今日充电能量2
	private BigDecimal batteryTodayChargeEnergy2;

	// 220F - 今日放电能量2
	private BigDecimal batteryTodayDischargeEnergy2;

	// 220D - 累计充电能量2
	private BigDecimal batteryAccumulatedChargeEnergy2;

	// 2211 - 累计放电能量2
	private BigDecimal batteryAccumulatedDischargeEnergy2;

	// 2216 - 所有模块平均SOH2
	private BigDecimal batteryAverageOfAllModulesSoh2;

	// 2214 - 最大循环次数2
	private BigDecimal batteryMaximumCyclesTimes2;

	// 2206 - 电池电压2
	private BigDecimal batteryVoltage2;

	// 2207 - 电池电流2
	private BigDecimal batteryCurrent2;

	// 221C - 最大单体温度2
	private BigDecimal batteryMaximumCellTemperature2;

	// 221E - 最小单体温度2
	private BigDecimal batteryMinimumCellTemperature2;

	// 221B - 平均单体温度2
	private BigDecimal batteryAverageCellTemperature2;

	// 2217 - 最大单体电压2
	private BigDecimal batteryMaximumCellVoltage2;

	// 2219 - 最小单体电压2
	private BigDecimal batteryMinimumCellVoltage2;
	// end   2025.4
}

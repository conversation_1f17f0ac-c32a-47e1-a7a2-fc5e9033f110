/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppDeviceInfo;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.excel.Device21Excel;
import org.skyworth.ess.device.mapper.Device21Mapper;
import org.skyworth.ess.device.service.IDevice21Service;
import org.skyworth.ess.device.vo.Device21VO;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备/智能能量变换器表，记录2.1数据 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Service
@Slf4j
public class Device21ServiceImpl extends BaseServiceImpl<Device21Mapper, Device21Entity> implements IDevice21Service {

	@Resource
	Device21Mapper device21Mapper;

	@Override
	public IPage<Device21VO> selectDevice21Page(IPage<Device21VO> page, Device21VO Device21) {
		return page.setRecords(baseMapper.selectDevice21Page(page, Device21));
	}


	@Override
	public List<Device21Excel> exportDevice21(Wrapper<Device21Entity> queryWrapper) {
		List<Device21Excel> Device21List = baseMapper.exportDevice21(queryWrapper);
		//Device21List.forEach(Device21 -> {
		//	Device21.setTypeName(DictCache.getValue(DictEnum.YES_NO, Device21.getType()));
		//});
		return Device21List;
	}

	@Override
	public List<Device21Entity> getEntity(Wrapper<Device21Entity> queryWrapper) {
		return baseMapper.getEntity(queryWrapper);
	}

	@Override
	public List<Device21Entity> getEntityIsDelete(Long plantId, String deviceSerialNumber) {
		return baseMapper.getEntityIsDelete(plantId, deviceSerialNumber);
	}


	@Override
	public List<AppDeviceInfo> queryAppInverterInfo(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryAppInverterInfo(plantId, deviceSerialNumber);
	}

	@Override
	public List<AppDeviceInfo> queryAppInverterInfoV2(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryAppInverterInfoV2(plantId, deviceSerialNumber);
	}

	@Override
	public int updateByDeviceId(String deviceSn, String status) {
		return baseMapper.updateByDeviceId(deviceSn, status);
	}

	@Override
	public Long getRunDays(String deviceSerialNumber, Long plantId) {
		Device21Entity device21Entity = new Device21Entity();
		device21Entity.setDeviceSerialNumber(deviceSerialNumber);
		device21Entity.setPlantId(plantId);
		Device21Entity device21 = baseMapper.selectOne(Condition.getQueryWrapper(device21Entity));
		if (ObjUtil.isNotEmpty(device21)) {
			Date createTime = device21.getCreateTime();
			try {
				return DateUtil.between(createTime, TimeUtils.getCurrentTimeForDate(), DateUnit.DAY) + 1L;
			} catch (ParseException e) {
				log.error("运行时间日期格式化错误，{}", e.getMessage());
			}
		}
		return 0L;
	}

	@Override
	public int updateSetup(Map<String, Object> device21, Long plantId, String deviceSerialNumber) {

		return device21Mapper.updateSetup(device21, plantId, deviceSerialNumber);
	}

	@Override
	public int deleteByPlantId(Long plantId, String deviceSerialNumber) {
		LambdaUpdateWrapper<Device21Entity> update = Wrappers.<Device21Entity>update().lambda()
			.set(Device21Entity::getIsDeleted, 1)
			.eq(Device21Entity::getPlantId, plantId)
			.eq(Device21Entity::getDeviceSerialNumber, deviceSerialNumber);
		return device21Mapper.delete(update);
	}

	@Override
	public List<Device21Entity> queryDevice21InfoBySerialNumber(List<String> serialNumbers) {
		return device21Mapper.queryDevice21InfoBySerialNumber(serialNumbers);
	}

}

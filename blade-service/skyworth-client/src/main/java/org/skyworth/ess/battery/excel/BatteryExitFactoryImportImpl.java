package org.skyworth.ess.battery.excel;

import lombok.AllArgsConstructor;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class BatteryExitFactoryImportImpl extends ExcelImportServiceAbstract<BatteryExitFactoryInfoExcel> {
    private final IDictBizClient dictBizClient;
    @Override
    public String validateDataEffective(List<BatteryExitFactoryInfoExcel> dataList) {
        R<List<DictBiz>> companyMatch = dictBizClient.getListAllLang("device_company");
        R<List<DictBiz>> modeTypeDict = dictBizClient.getListAllLang("battery_mode_type");
        List<DictBiz> dictData = companyMatch.getData();
        List<DictBiz> modeType = modeTypeDict.getData();
        if (CollectionUtil.isEmpty(dictData) || CollectionUtil.isEmpty(modeType)) {
            return "the company or device mode type or  not setting in system,please setting in bussiness menu,the code is : device_company, battery_mode_type ";
        }
        StringBuilder stringBuilder = this.validateCompany(dataList, dictData);
        stringBuilder.append(this.validateDeviceModeType(dataList, modeType));
        return stringBuilder.toString();
    }

    private  StringBuilder validateCompany(List<BatteryExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
        StringBuilder sb = new StringBuilder();
        for(BatteryExitFactoryInfoExcel vo : dataList) {
            String company = vo.getCompany();
            boolean flag = false;
            for(DictBiz biz : dictData) {
                if(biz.getDictValue().equalsIgnoreCase(company)) {
                    vo.setCompany(biz.getDictKey());
                    flag = true;
                }
            }
            if(!flag) {
                sb.append(company).append(",");
            }
        }
        if(StringUtils.isNotEmpty(sb)) {
            sb.append( " these company is not setting in bussiness menu,the code is : device_company");
        }
        return sb;
    }

    private  StringBuilder validateDeviceModeType(List<BatteryExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
        StringBuilder sb = new StringBuilder();
        for(BatteryExitFactoryInfoExcel vo : dataList) {
            String type = vo.getBatteryType();
            boolean flag = false;
            for(DictBiz biz : dictData) {
                if(biz.getDictValue().equalsIgnoreCase(type)) {
                    vo.setBatteryType(biz.getDictKey());
                    flag = true;
                }
            }
            if(!flag) {
                sb.append(type).append(",");
            }
        }
        if(StringUtils.isNotEmpty(sb)) {
            sb.append( " these device type is not setting in bussiness menu,the code is : battery_mode_type");
        }
        return sb;
    }
}

package org.skyworth.ess.guardian.realTimeHandler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * sim卡号数据
 *
 * <AUTHOR>
@Component(CommonConstant.TERMINAL_WARDS_KEY +"_"+ CommonConstant.SIM_NUMBER)
@Slf4j
@AllArgsConstructor
public class SimNumberServiceImpl implements DataHandler {

	private IGuardianExitFactoryInfoService exitFactoryInfoService;

	@Override
	public void handler(List<JSONObject> dataList, Map<String, Long> resultMap, Map<String,String> partitionMap) {
       dataList.parallelStream().forEach(data->{
		   try {
			   log.info("sim卡号数据:{}",data.toJSONString());
			   String content=data.getString("content");
			   String deviceSn = data.getString("deviceSn");
			   String deviceType= String.valueOf(BinaryToHexUtils.hexToDecimal(content.substring(0,2)));
			   String simNumber=content.substring(2);
			   //更新出厂信息 如果上报的sim卡号和出厂信息中的sim卡号一致则不更新
			   LambdaQueryWrapper<GuardianExitFactoryInfoEntity> eq = Wrappers.<GuardianExitFactoryInfoEntity>query().lambda()
					   .eq(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber,deviceSn).eq(GuardianExitFactoryInfoEntity::getIsDeleted,0);
			   GuardianExitFactoryInfoEntity exitFactoryInfo = exitFactoryInfoService.getOne(eq);
			   if(ValidationUtil.isNotEmpty(exitFactoryInfo)){
				   String oldSimNumber = exitFactoryInfo.getSimCardNumber();
				   String oldDeviceType = exitFactoryInfo.getDeviceType();
				   if(oldDeviceType.equals(deviceType)&&oldSimNumber.equals(simNumber)){
					   return;
				   }
				   exitFactoryInfo.setSimCardNumber(simNumber);
				   exitFactoryInfo.setDeviceType(deviceType);
				   exitFactoryInfoService.updateById(exitFactoryInfo);
			   }
		   }catch (Exception e){
			   log.error("sim卡号数据异常:{}",e.getMessage());
		   }
	   });
	}


}

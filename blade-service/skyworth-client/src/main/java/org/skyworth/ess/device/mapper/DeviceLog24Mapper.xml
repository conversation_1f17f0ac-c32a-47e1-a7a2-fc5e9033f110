<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceLog24Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="DeviceLog24ResultMap" type="org.skyworth.ess.device.entity.DeviceLog24Entity">
        <result column="id" property="id"/>
        <result column="year" property="year"/>
        <result column="month_day" property="monthDay"/>
        <result column="hours_min" property="hoursMin"/>
        <result column="seconds" property="seconds"/>
        <result column="power_derating_percent_by_modbus" property="powerDeratingPercentByModbus"/>
        <result column="modbus_address" property="modbusAddress"/>
        <result column="rS485_baud_rate" property="rs485BaudRate"/>
        <result column="wifi_sta_ssid" property="wifiStaSsid"/>
        <result column="wifi_sta_password" property="wifiStaPassword"/>
        <result column="digital_meter_modbus_address" property="digitalMeterModbusAddress"/>
        <result column="digital_meter_type" property="digitalMeterType"/>
        <result column="power_flow_direction" property="powerFlowDirection"/>
        <result column="power_limit_function" property="powerLimitFunction"/>
        <result column="power_limit_ct_ratio" property="powerLimitCtRatio"/>
        <result column="meter_location" property="meterLocation"/>
        <result column="maximum_feed_in_grid_power" property="maximumFeedInGridPower"/>
        <result column="first_connect_start_time" property="firstConnectStartTime"/>
        <result column="reconnect_time" property="reconnectTime"/>
        <result column="grid_frequency_high_loss_level1_limit" property="gridFrequencyHighLossLevel1Limit"/>
        <result column="grid_frequency_low_loss_level1_limit" property="gridFrequencyLowLossLevel1Limit"/>
        <result column="grid_voltage_high_loss_level1_limit" property="gridVoltageHighLossLevel1Limit"/>
        <result column="grid_voltage_low_loss_level1_limit" property="gridVoltageLowLossLevel1Limit"/>
        <result column="grid_frequency_high_loss_level1_trip_time" property="gridFrequencyHighLossLevel1TripTime"/>
        <result column="grid_frequency_low_loss_level1_trip_time" property="gridFrequencyLowLossLevel1TripTime"/>
        <result column="grid_voltage_high_loss_level1_trip_time" property="gridVoltageHighLossLevel1TripTime"/>
        <result column="grid_voltage_low_loss_level1_trip_time" property="gridVoltageLowLossLevel1TripTime"/>
        <result column="grid_frequency_high_loss_level2_limit" property="gridFrequencyHighLossLevel2Limit"/>
        <result column="grid_frequency_low_loss_level2_limit" property="gridFrequencyLowLossLevel2Limit"/>
        <result column="grid_voltage_high_loss_level2_limit" property="gridVoltageHighLossLevel2Limit"/>
        <result column="grid_voltage_low_loss_level2_limit" property="gridVoltageLowLossLevel2Limit"/>
        <result column="grid_frequency_high_loss_level2_trip_time" property="gridFrequencyHighLossLevel2TripTime"/>
        <result column="grid_frequency_low_loss_level2_trip_time" property="gridFrequencyLowLossLevel2TripTime"/>
        <result column="grid_voltage_high_loss_level2_trip_time" property="gridVoltageHighLossLevel2TripTime"/>
        <result column="grid_voltage_low_loss_level2_trip_time" property="gridVoltageLowLossLevel2TripTime"/>
        <result column="grid_frequency_high_level1_back" property="gridFrequencyHighLevel1Back"/>
        <result column="grid_frequency_low_level1_back" property="gridFrequencyLowLevel1Back"/>
        <result column="ten_min_average_sustained_voltage" property="tenMinAverageSustainedVoltage"/>
        <result column="reconnect_soft_output_power_percent" property="reconnectSoftOutputPowerPercent"/>
        <result column="over_frequency_power_reduction_droop" property="overFrequencyPowerReductionDroop"/>
        <result column="insulation_resistance_active_limit" property="insulationResistanceActiveLimit"/>
        <result column="grid_over_voltage_deRating_point" property="gridOverVoltageDeratingPoint"/>
        <result column="grid_frequency_high_level1_trip_time" property="gridFrequencyHighLevel1TripTime"/>
        <result column="grid_frequency_low_Level1_trip_time" property="gridFrequencyLowLevel1TripTime"/>
        <result column="grid_over_frequency_deRating_start_point" property="gridOverFrequencyDeratingStartPoint"/>
        <result column="grid_over_frequency_deRating_end_point" property="gridOverFrequencyDeratingEndPoint"/>
        <result column="grid_voltage_high_level1_trip_time" property="gridVoltageHighLevel1TripTime"/>
        <result column="grid_voltage_low_level1_trip_time" property="gridVoltageLowLevel1TripTime"/>
        <result column="grid_voltage_high_level1_back" property="gridVoltageHighLevel1Back"/>
        <result column="grid_voltage_low_level1_back" property="gridVoltageLowLevel1Back"/>
        <result column="first_connect_soft_start_output_power_percent" property="firstConnectSoftStartOutputPowerPercent"/>
        <result column="over_voltage_derating_settling_time" property="overVoltageDeratingSettlingTime"/>
        <result column="connection_and_reconnection_power_ramp_rate" property="connectionAndReconnectionPowerRampRate"/>
        <result column="output_reactive_power_mode" property="outputReactivePowerMode"/>
        <result column="power_factor_setting" property="powerFactorSetting"/>
        <result column="reactive_control_response_time" property="reactiveControlResponseTime"/>
        <result column="curve_node1_percent" property="curveNode1Percent"/>
        <result column="curve_node2_percent" property="curveNode2Percent"/>
        <result column="curve_node3_percent" property="curveNode3Percent"/>
        <result column="curve_node4_percent" property="curveNode4Percent"/>
        <result column="curve_node1_value_setting" property="curveNode1ValueSetting"/>
        <result column="curve_node2_value_setting" property="curveNode2ValueSetting"/>
        <result column="curve_node3_value_setting" property="curveNode3ValueSetting"/>
        <result column="regulation_code" property="regulationCode"/>
        <result column="inverter_control" property="inverterControl"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="factory_reset" property="factoryReset"/>
        <result column="clear_historical_info" property="clearHistoricalInfo"/>
        <result column="synch_status" property="synchStatus"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="modbus_protocol_version" property="modbusProtocolVersion"/>
        <result column="device_date_time" property="deviceDateTime"/>
    </resultMap>

    <select id="selectDataByLatestTime" resultMap="DeviceLog24ResultMap">
        SELECT t1.*
        FROM device_log24 t1
        JOIN (
        SELECT plant_id, MAX(device_date_time) AS max_date,device_serial_number
        FROM device_log24
        GROUP BY plant_id,device_serial_number
        ) t2 ON t1.plant_id = t2.plant_id AND t1.device_date_time = t2.max_date and t1.device_serial_number=t2.device_serial_number where (t1.synch_status is null or t1.synch_status='' or t1.synch_status='N');
    </select>

    <select id="getAllQuickSetup" resultType="java.util.HashMap">
        select
            d24.year as year,
            d24.month_day as monthDay,
            d24.hours_min as hoursMin,
            d24.seconds as seconds,
            d24.digital_meter_modbus_address as digitalMeterModbusAddress,
            d24.digital_meter_type as digitalMeterType,
            d24.power_flow_direction as powerFlowDirection,
            d24.power_limit_function as powerLimitFunction,
            d24.meter_location as meterLocation,
            d24.maximum_feed_in_grid_power as maximumFeedInGridPower,
            d24.regulation_code as regulationCode,
            d24.inverter_control as inverterControl,
            d23.hybrid_work_mode as hybridWorkMode,
            d23.maximum_discharger_power as maximumDischargerPower,
            d23.off_grid_mode as offGridMode,
            d23.feed_in_grid_function as feedInGridFunction,
            d21.rated_voltage as ratedVoltage,
            d21.rated_frequency as ratedFrequency,
            wsp.startup_by_backstage as startupByBackstage,
            wsp.inverter_configure_network as inverterConfigureNetwork,
            d23.parallel_mode_function as parallelModeFunction,
            d23.parallel_system_battery_connect_type as parallelSystemBatteryConnectType,
            d24.power_derating_control_mode as powerDeratingControlMode,
            d23.maximum_input_power_from_grid as maximumInputPowerFromGrid,
            d23.battery_connection_type_note2 as batteryConnectionTypeNote2,
            d23.maximum_charge_power2 as maximumChargePower2,
            d23.maximum_discharge_power2 as maximumDischargePower2,
            d23.capacity_of_charge_end2 as capacityOfChargeEnd2,
            d23.capacity_of_discharge_end2 as capacityOfDischargeEnd2,
            d23.capacity_of_discharge_end_on_grid2 as capacityOfDischargeEndOnGrid2,
            d23.force_charge_start_soc2 as forceChargeStartSoc2,
            d23.force_charge_end_soc2 as forceChargeEndSoc2,
            d24.maximum_permit_consumption_from_grid as maximumPermitConsumptionFromGrid
        from device_24 d24
                 left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
                 left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
                 left join wifi_stick_plant wsp on  d24.device_serial_number = wsp.device_serial_number and d24.plant_id = wsp.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d21.is_deleted = 0 and  d23.is_deleted = 0 and d24.is_deleted = 0
    </select>

    <select id="getAllQuickSetupIsDelete" resultType="java.util.HashMap">
        select
            d24.year as year,
            d24.month_day as monthDay,
            d24.hours_min as hoursMin,
            d24.seconds as seconds,
            d24.digital_meter_modbus_address as digitalMeterModbusAddress,
            d24.digital_meter_type as digitalMeterType,
            d24.power_flow_direction as powerFlowDirection,
            d24.power_limit_function as powerLimitFunction,
            d24.meter_location as meterLocation,
            d24.maximum_feed_in_grid_power as maximumFeedInGridPower,
            d24.regulation_code as regulationCode,
            d24.inverter_control as inverterControl,
            d23.hybrid_work_mode as hybridWorkMode,
            d23.maximum_discharger_power as maximumDischargerPower,
            d23.off_grid_mode as offGridMode,
            d23.feed_in_grid_function as feedInGridFunction,
            d21.rated_voltage as ratedVoltage,
            d21.rated_frequency as ratedFrequency,
            wsp.startup_by_backstage as startupByBackstage,
            wsp.inverter_configure_network as inverterConfigureNetwork,
            d24.power_derating_control_mode as powerDeratingControlMode,
            d24.maximum_permit_consumption_from_grid as maximumPermitConsumptionFromGrid
        from device_24 d24
                 left join device_23 d23 on  d24.device_serial_number = d23.device_serial_number and d24.plant_id = d23.plant_id
                 left join device_21 d21 on  d24.device_serial_number = d21.device_serial_number and d24.plant_id = d21.plant_id
                 left join wifi_stick_plant wsp on  d24.device_serial_number = wsp.device_serial_number and d24.plant_id = wsp.plant_id
        WHERE d21.plant_id = #{plantId} and d21.device_serial_number = #{deviceSerialNumber}
          and d24.is_deleted = 1
    </select>


    <!--    <select id="selectDeviceLog24Page" resultMap="DeviceLog24ResultMap">-->
<!--        select * from device_log24 where is_deleted = 0-->
<!--    </select>-->


<!--    <select id="exportDeviceLog24" resultType="org.skyworth.ess.device.excel.DeviceLog24Excel">-->
<!--        SELECT * FROM device_log24 ${ew.customSqlSegment}-->
<!--    </select>-->

</mapper>

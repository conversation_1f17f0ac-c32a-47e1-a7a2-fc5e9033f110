/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 良信网关站点关系表 实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@TableName("lazzen_gateway_plant")
@ApiModel(value = "GatewayPlant对象", description = "良信网关站点关系表")
@EqualsAndHashCode(callSuper = true)
public class GatewayPlantEntity extends TenantEntity {

	/**
	 * 设备网关唯一编号
	 */
	@ApiModelProperty(value = "设备网关唯一编号")
	private String gatewayUniqueNumber;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	private Long plantId;
	/**
	 * 心跳时间
	 */
	@ApiModelProperty(value = "心跳时间")
	private LocalDateTime heartBeatTime;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "闸位状态(0分闸/1合闸)")
	private Integer turnOnOff;

	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String deviceType;

	/**是否存在用户类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm;
}

package org.skyworth.ess.guardian.realTimeHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.service.IGuardianTimedPowerService;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.guardian.timedswitchgate.service.IGuardianTimedSwitchGateService;
import org.skyworth.ess.util.HumpConvert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 功率设置和定时开关机设置
 */
@Component(CommonConstant.TERMINAL_WARDS_KEY + "_" + CommonConstant.BIZ_POWER_SETTING)
@Slf4j
@AllArgsConstructor
public class PowerSettingServiceImpl implements DataHandler {

	private final DorisSinkService dorisSinkService;
	private final IGuardianPlantService plantService;
	private final IGuardianTimedPowerService guardianTimedPowerService;
	private final IGuardianTimedSwitchGateService guardianTimedSwitchGateService;

	private static final SimpleDateFormat INPUT_FORMAT = new SimpleDateFormat("yyMMddHHmmss");
	private static final ThreadLocal<DateFormat> OUTPUT_FORMAT = ThreadLocal.withInitial(
		() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()));

	@Override
	public void handler(List<JSONObject> dataList, Map<String, Long> resultMap, Map<String, String> partitionMap) {
		List<JSONObject> res = new ArrayList<>();
		try {
			dataList.parallelStream().forEach(data -> {
				log.info("功率设置和定时开关机设置:{}", data.toJSONString());
				processSingleData(data, resultMap, partitionMap, res);
			});
		} catch (Exception e) {
			log.error("功率设置和定时开关机设置异常:{}", e.getMessage());
		}

		if (!res.isEmpty()) {
			dorisSinkService.write(res, "security_guard_timed_current_status");
		}
	}

	private void processSingleData(JSONObject data, Map<String, Long> resultMap, Map<String, String> partitionMap, List<JSONObject> res) {
		String content = data.getString("content");
		String deviceSn = data.getString("deviceSn");
		Long plantId = resultMap.get(deviceSn);
		String partitionDate = LocalDate.now().toString();
		if (ValidationUtil.isNotEmpty(plantId)) {
			partitionDate = partitionMap.get(deviceSn);
		}

		// 解析数据
		ParsedData parsedData = parseContent(content);

		String deviceDateTime = getDeviceDateTime(content.substring(100, 112));

		insertDoris(partitionDate, plantId, deviceDateTime, parsedData, deviceSn, res);

		if (ValidationUtil.isEmpty(plantId)) {
			return;
		}

		updatePowerAndSwitchGate(plantId, parsedData);

		for (int i = 0; i < 4; i++) {
			insertOrUpdatePower(plantId, deviceSn, parsedData.powerSettings[i], i + 1);
			insertOrUpdateGate(plantId, deviceSn, parsedData.gateSettings[i], i + 1);
		}
	}

	private ParsedData parseContent(String content) {
		ParsedData parsedData = new ParsedData();

		parsedData.rssi = content.substring(0, 2);
		parsedData.type = new StringBuilder(BinaryToHexUtils.binaryComplement(content.substring(2, 4), 8)).reverse().toString();
		parsedData.timingTypeGate = "1".equals(parsedData.type.substring(7)) ? "everyday" : parsedData.type.contains("1") ? "custom" : "close";
		parsedData.timingTypeGateCustom = "1".equals(parsedData.type.substring(7)) ? "" : IntStream.range(0, 7).mapToObj(i -> String.valueOf(parsedData.type.charAt(i)))
			.collect(Collectors.joining(","));

		parsedData.powerType = new StringBuilder(BinaryToHexUtils.binaryComplement(content.substring(68, 70), 8)).reverse().toString();
		parsedData.timingTypePower = "1".equals(parsedData.powerType.substring(7)) ? "everyday" : parsedData.powerType.contains("1") ? "custom" : "close";
		parsedData.timingTypePowerCustom = "1".equals(parsedData.type.substring(7)) ? "" : IntStream.range(0, 7).mapToObj(i -> String.valueOf(parsedData.powerType.charAt(i)))
			.collect(Collectors.joining(","));

		for (int i = 0; i < 4; i++) {
			int baseIndex = 4 + i * 16;
			parsedData.gateSettings[i] = new GateSetting(
				formatTime(content.substring(baseIndex, baseIndex + 6)),
				String.valueOf(BinaryToHexUtils.hexToDecimal(content.substring(baseIndex + 6, baseIndex + 8))),
				formatTime(content.substring(baseIndex + 8, baseIndex + 14)),
				String.valueOf(BinaryToHexUtils.hexToDecimal(content.substring(baseIndex + 14, baseIndex + 16)))
			);

			baseIndex = 70 + i * 20;
			parsedData.powerSettings[i] = new PowerSetting(
				formatTime(content.substring(baseIndex, baseIndex + 6)),
				formatTime(content.substring(baseIndex + 6, baseIndex + 12)),
				BinaryToHexUtils.hexToDecimal(content.substring(baseIndex + 12, baseIndex + 16)),
				BinaryToHexUtils.hexToDecimal(content.substring(baseIndex + 16, baseIndex + 20))
			);
		}

		parsedData.lockSite = BinaryToHexUtils.hexToDecimal(content.substring(150, 152));
		parsedData.coexistence = BinaryToHexUtils.hexToDecimal(content.substring(152, 154));

		return parsedData;
	}

	private String getDeviceDateTime(String time) {
		String deviceDateTime = TimeUtils.getCurrentTime();
		try {
			if (!GuardianInstructConstants.TIME_IS_NULL.equals(time)) {
				Date date = INPUT_FORMAT.parse(time);
				deviceDateTime = OUTPUT_FORMAT.get().format(date);
			}
		} catch (Exception e) {
			log.error("时间格式错误:{} ", e.getMessage());
		}
		return deviceDateTime;
	}

	private void insertDoris(String partitionDate, Long plantId, String deviceDateTime, ParsedData parsedData, String deviceSn, List<JSONObject> res) {
		GuardianTimedCurrentStatusEntity entity = new GuardianTimedCurrentStatusEntity();
		entity.setPartitionDate(partitionDate);
		entity.setPlantId(plantId);
		entity.setTimingTypePower(parsedData.timingTypePower);
		entity.setTimingTypeGate(parsedData.timingTypeGate);

		entity.setStartTime1(parsedData.powerSettings[0].startTime);
		entity.setEndTime1(parsedData.powerSettings[0].endTime);
		entity.setMinPower1(BigDecimal.valueOf(parsedData.powerSettings[0].minPower));
		entity.setMaxPower1(BigDecimal.valueOf(parsedData.powerSettings[0].maxPower));

		entity.setStartTime2(parsedData.powerSettings[1].startTime);
		entity.setEndTime2(parsedData.powerSettings[1].endTime);
		entity.setMinPower2(BigDecimal.valueOf(parsedData.powerSettings[1].minPower));
		entity.setMaxPower2(BigDecimal.valueOf(parsedData.powerSettings[1].maxPower));

		entity.setStartTime3(parsedData.powerSettings[2].startTime);
		entity.setEndTime3(parsedData.powerSettings[2].endTime);
		entity.setMinPower3(BigDecimal.valueOf(parsedData.powerSettings[2].minPower));
		entity.setMaxPower3(BigDecimal.valueOf(parsedData.powerSettings[2].maxPower));

		entity.setStartTime4(parsedData.powerSettings[3].startTime);
		entity.setEndTime4(parsedData.powerSettings[3].endTime);
		entity.setMinPower4(BigDecimal.valueOf(parsedData.powerSettings[3].minPower));
		entity.setMaxPower4(BigDecimal.valueOf(parsedData.powerSettings[3].maxPower));

		entity.setClosingTime1("1".equals(parsedData.gateSettings[0].lockSite) ? parsedData.gateSettings[0].planTime1 : parsedData.gateSettings[0].planTime2);
		entity.setOpeningTime1("0".equals(parsedData.gateSettings[0].lockSite) ? parsedData.gateSettings[0].planTime1 : parsedData.gateSettings[0].planTime2);

		entity.setClosingTime2("1".equals(parsedData.gateSettings[1].lockSite) ? parsedData.gateSettings[1].planTime1 : parsedData.gateSettings[1].planTime2);
		entity.setOpeningTime2("0".equals(parsedData.gateSettings[1].lockSite) ? parsedData.gateSettings[1].planTime1 : parsedData.gateSettings[1].planTime2);

		entity.setClosingTime3("1".equals(parsedData.gateSettings[2].lockSite) ? parsedData.gateSettings[2].planTime1 : parsedData.gateSettings[2].planTime2);
		entity.setOpeningTime3("0".equals(parsedData.gateSettings[2].lockSite) ? parsedData.gateSettings[2].planTime1 : parsedData.gateSettings[2].planTime2);

		entity.setClosingTime4("1".equals(parsedData.gateSettings[3].lockSite) ? parsedData.gateSettings[3].planTime1 : parsedData.gateSettings[3].planTime2);
		entity.setOpeningTime4("0".equals(parsedData.gateSettings[3].lockSite) ? parsedData.gateSettings[3].planTime1 : parsedData.gateSettings[3].planTime2);

		entity.setLockSet(parsedData.lockSite);
		entity.setRecloseSet(parsedData.coexistence);
		entity.setSecurityGuardSerialNumber(deviceSn);
		entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);

		JSONObject result = (JSONObject) JSON.toJSON(entity);
		JSONObject newObj = convertKeysToUnderscore(result);
		IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
		Number number = identifierGenerator.nextId(new Object());
		newObj.put("id", number.longValue());
		String currentTime = TimeUtils.getCurrentTime();
		newObj.put("create_time", currentTime);
		newObj.put("update_time", currentTime);
		newObj.put("device_date_time", deviceDateTime);
		res.add(newObj);
	}

	private void updatePowerAndSwitchGate(Long plantId, ParsedData parsedData) {
		LambdaQueryWrapper<GuardianPlantEntity> eq = Wrappers.<GuardianPlantEntity>query().lambda()
			.eq(GuardianPlantEntity::getPlantId, plantId).eq(GuardianPlantEntity::getIsDeleted, 0);
		GuardianPlantEntity guardianPlantEntity = plantService.getOne(eq);
		if (ValidationUtil.isNotEmpty(guardianPlantEntity)) {
			guardianPlantEntity.setLockSet(parsedData.lockSite);
			guardianPlantEntity.setRecloseSet(parsedData.coexistence);
			guardianPlantEntity.setTimingTypePower(parsedData.timingTypePower);
			guardianPlantEntity.setTimingTypePowerCustom(parsedData.timingTypePowerCustom);
			guardianPlantEntity.setTimingTypeGate(parsedData.timingTypeGate);
			guardianPlantEntity.setTimingTypeGateCustom(parsedData.timingTypeGateCustom);
			plantService.updateById(guardianPlantEntity);
		}
	}

	public void insertOrUpdatePower(long plantId, String deviceSn, PowerSetting powerSetting, int sort) {
		LambdaQueryWrapper<GuardianTimedPowerEntity> eq = Wrappers.<GuardianTimedPowerEntity>query().lambda()
			.eq(GuardianTimedPowerEntity::getSetSort, sort).eq(GuardianTimedPowerEntity::getIsDeleted, 0)
			.eq(GuardianTimedPowerEntity::getSecurityGuardSerialNumber, deviceSn)
			.eq(GuardianTimedPowerEntity::getPlantId, plantId);
		GuardianTimedPowerEntity timedPowerEntity = guardianTimedPowerService.getOne(eq);
		GuardianTimedPowerEntity guardianTimedPowerEntity = new GuardianTimedPowerEntity();
		guardianTimedPowerEntity.setPlantId(plantId);
		guardianTimedPowerEntity.setSecurityGuardSerialNumber(deviceSn);
		guardianTimedPowerEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		guardianTimedPowerEntity.setSetSort(sort);
		guardianTimedPowerEntity.setMinPower(BigDecimal.valueOf(powerSetting.minPower));
		guardianTimedPowerEntity.setMaxPower(BigDecimal.valueOf(powerSetting.maxPower));
		guardianTimedPowerEntity.setStartTime(powerSetting.startTime);
		guardianTimedPowerEntity.setEndTime(powerSetting.endTime);
		if (ValidationUtil.isNotEmpty(timedPowerEntity)) {
			long id = timedPowerEntity.getId();
			BeanUtils.copyProperties(guardianTimedPowerEntity, timedPowerEntity);
			timedPowerEntity.setId(id);
			guardianTimedPowerService.updateById(timedPowerEntity);
		} else {
			guardianTimedPowerService.save(guardianTimedPowerEntity);
		}
	}

	public void insertOrUpdateGate(long plantId, String deviceSn, GateSetting gateSetting, int sort) {
		GuardianTimedSwitchGateEntity gate = new GuardianTimedSwitchGateEntity();
		gate.setSetSort(sort);
		gate.setPlantId(plantId);
		gate.setSecurityGuardSerialNumber(deviceSn);
		gate.setClosingTime(gateSetting.planTime1);
		gate.setOpeningTime(gateSetting.planTime2);
		gate.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		LambdaQueryWrapper<GuardianTimedSwitchGateEntity> eq = Wrappers.<GuardianTimedSwitchGateEntity>query().lambda()
			.eq(GuardianTimedSwitchGateEntity::getSetSort, sort)
			.eq(GuardianTimedSwitchGateEntity::getIsDeleted, 0)
			.eq(GuardianTimedSwitchGateEntity::getSecurityGuardSerialNumber, deviceSn)
			.eq(GuardianTimedSwitchGateEntity::getPlantId, plantId);
		GuardianTimedSwitchGateEntity gateEntity = guardianTimedSwitchGateService.getOne(eq);
		if (ValidationUtil.isEmpty(gateEntity)) {
			guardianTimedSwitchGateService.save(gate);
		} else {
			long id = gateEntity.getId();
			BeanUtils.copyProperties(gate, gateEntity);
			gateEntity.setId(id);
			guardianTimedSwitchGateService.updateById(gateEntity);
		}
	}

	private static String formatTime(String input) {
		return input.substring(0, 2) + ":" + input.substring(2, 4) + ":" + input.substring(4, 6);
	}

	private static JSONObject convertKeysToUnderscore(JSONObject jsonObject) {
		JSONObject convertedObject = new JSONObject();
		for (String key : jsonObject.keySet()) {
			String convertedKey = convertToUnderscore(key);
			Object value = jsonObject.get(key);
			if (value instanceof JSONObject) {
				value = convertKeysToUnderscore((JSONObject) value);
			}
			convertedObject.put(convertedKey, value);
		}
		return convertedObject;
	}

	private static String convertToUnderscore(String input) {
		StringBuilder result = new StringBuilder();
		boolean isFirstChar = true;
		for (char c : input.toCharArray()) {
			if (Character.isUpperCase(c)) {
				if (!isFirstChar) {
					result.append('_');
				}
				result.append(Character.toLowerCase(c));
			} else if (Character.isDigit(c)) {
				if (!isFirstChar) {
					result.append('_');
				}
				result.append(c);
			} else {
				result.append(c);
			}
			isFirstChar = false;
		}
		return result.toString();
	}

	private static class ParsedData {
		String rssi;
		String type;
		String timingTypeGate;
		String timingTypeGateCustom;
		String powerType;
		String timingTypePower;
		String timingTypePowerCustom;
		int lockSite;
		int coexistence;
		GateSetting[] gateSettings = new GateSetting[4];
		PowerSetting[] powerSettings = new PowerSetting[4];
	}

	public static class GateSetting {
		String planTime1;
		String planTime2;
		String lockSite;

		public GateSetting(String planTime1, String lockSite1, String planTime2, String lockSite2) {
			this.planTime1 = planTime1;
			this.planTime2 = planTime2;
			this.lockSite = lockSite1;
		}
	}

	public static class PowerSetting {
		String startTime;
		String endTime;
		double minPower;
		double maxPower;

		public PowerSetting(String startTime, String endTime, double minPower, double maxPower) {
			this.startTime = startTime;
			this.endTime = endTime;
			this.minPower = minPower;
			this.maxPower = maxPower;
		}
	}
}

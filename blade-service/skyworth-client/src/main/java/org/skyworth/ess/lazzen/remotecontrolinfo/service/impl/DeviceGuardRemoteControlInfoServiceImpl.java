/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.remotecontrolinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.remotecontrolinfo.entity.DeviceGuardRemoteControlInfoEntity;
import org.skyworth.ess.lazzen.remotecontrolinfo.vo.DeviceGuardRemoteControlInfoVO;
import org.skyworth.ess.lazzen.remotecontrolinfo.excel.DeviceGuardRemoteControlInfoExcel;
import org.skyworth.ess.lazzen.remotecontrolinfo.mapper.DeviceGuardRemoteControlInfoMapper;
import org.skyworth.ess.lazzen.remotecontrolinfo.service.IDeviceGuardRemoteControlInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 遥感信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
@Slf4j
@DS("slave")
public class DeviceGuardRemoteControlInfoServiceImpl extends BaseServiceImpl<DeviceGuardRemoteControlInfoMapper, DeviceGuardRemoteControlInfoEntity> implements IDeviceGuardRemoteControlInfoService {

	@Override
	public IPage<DeviceGuardRemoteControlInfoVO> selectDeviceGuardRemoteControlInfoPage(IPage<DeviceGuardRemoteControlInfoVO> page, DeviceGuardRemoteControlInfoVO deviceGuardRemoteControlInfo) {
		return page.setRecords(baseMapper.selectDeviceGuardRemoteControlInfoPage(page, deviceGuardRemoteControlInfo));
	}


	@Override
	public List<DeviceGuardRemoteControlInfoExcel> exportDeviceGuardRemoteControlInfo(Wrapper<DeviceGuardRemoteControlInfoEntity> queryWrapper) {
		List<DeviceGuardRemoteControlInfoExcel> deviceGuardRemoteControlInfoList = baseMapper.exportDeviceGuardRemoteControlInfo(queryWrapper);
		//deviceGuardRemoteControlInfoList.forEach(deviceGuardRemoteControlInfo -> {
		//	deviceGuardRemoteControlInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceGuardRemoteControlInfo.getType()));
		//});
		return deviceGuardRemoteControlInfoList;
	}

}

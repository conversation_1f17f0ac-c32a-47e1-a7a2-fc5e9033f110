/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.thresholdcurrentstatus.mapper;

import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.vo.GuardianThresholdCurrentStatusVO;
import org.skyworth.ess.guardian.thresholdcurrentstatus.excel.GuardianThresholdCurrentStatusExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 安全卫士-设备上报阈值实时数据 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface GuardianThresholdCurrentStatusMapper extends BaseMapper<GuardianThresholdCurrentStatusEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianThresholdCurrentStatus
	 * @return
	 */
	List<GuardianThresholdCurrentStatusVO> selectGuardianThresholdCurrentStatusPage(IPage page, GuardianThresholdCurrentStatusVO GuardianThresholdCurrentStatus);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianThresholdCurrentStatusExcel> exportGuardianThresholdCurrentStatus(@Param("ew") Wrapper<GuardianThresholdCurrentStatusEntity> queryWrapper);

	int deleteLogicByPlantIdAndSn(@Param("plantId") Long plantId,@Param("securityGuardSerialNumber") String securityGuardSerialNumber);

	GuardianThresholdCurrentStatusEntity getThresholdByLast(String deviceSn);
}

package org.skyworth.ess.dailyStatistics.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.device.vo.InvertStatusReport;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface DeviceLog22ByDorisService extends BaseService<DeviceLog22> {

	List<DeviceLog22> selectDataByLatestTime();

	List<DeviceLog22> selectDailyData(String beginTime, String endTime);

	List<JSONObject> stateCurve(QueryDeviceLog22Condition queryCondition);

	List<InvertStatusReport> selectStatusReportByTime(QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appReportEstimate(QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appReportEstimateV2(QueryDeviceLog22Condition queryCondition);

	DeviceLog22VO appDailyFromPvAndGrid(QueryDeviceLog22Condition queryCondition);

	List<JSONObject> inverterAndLoadSummaryStateCurve();

	void selectStatusReportExport(QueryDeviceLog22Condition queryCondition, HttpServletResponse response);

	DeviceLog22 appParallelDailyFromPvAndGrid(QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appParallelReportEstimateV2(QueryDeviceLog22Condition queryCondition);

	// 查询站点下当天最新的数据
	DeviceLog22 queryLatestData(QueryDeviceLog22Condition queryCondition);
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryEverydayTotalExcel;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.battery.wrapper.BatteryEverydayTotalWrapper;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 储能每日统计 控制器
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("batteryEverydayTotal")
@Api(value = "储能每日统计", tags = "储能每日统计接口")
public class BatteryEverydayTotalController extends BladeController {

	private final IBatteryEverydayTotalService batteryEverydayTotalService;

	/**
	 * 储能每日统计 详情
	 */
	@GetMapping("/dailyEstimate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "每日统计", notes = "传入batteryEverydayTotal")
	public R<List<JSONObject>> dailyEstimate(QueryCondition queryCondition) {
		if (StringUtils.isBlank(queryCondition.getQueryDateType()) || queryCondition.getStartDateTime() == null || queryCondition.getEndDateTime() == null
			|| queryCondition.getPlantId() == null || StringUtils.isBlank(queryCondition.getDeviceSerialNumber())
		) {
			throw new BusinessException("client.battery.query.connot.empty");
		}
		return R.data(batteryEverydayTotalService.dailyEstimate(queryCondition));
	}

	/**
	 * 总量统计 详情
	 */
	@GetMapping("/totalStatistics")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "总量统计", notes = "传入batteryEverydayTotal")
	public R<Map<String, Object>> totalStatistics(QueryCondition queryCondition) {
		return R.data(batteryEverydayTotalService.totalStatistics(queryCondition));
	}

	/**
	 * 储能每日统计 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入batteryEverydayTotal")
	public R<BatteryEverydayTotalVO> detail(BatteryEverydayTotalEntity batteryEverydayTotal) {
		BatteryEverydayTotalEntity detail = batteryEverydayTotalService.getOne(Condition.getQueryWrapper(batteryEverydayTotal));
		return R.data(BatteryEverydayTotalWrapper.build().entityVO(detail));
	}

	/**
	 * 储能每日统计 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入batteryEverydayTotal")
	public R<IPage<BatteryEverydayTotalVO>> list(@ApiIgnore @RequestParam Map<String, Object> batteryEverydayTotal, Query query) {
		IPage<BatteryEverydayTotalEntity> pages = batteryEverydayTotalService.page(Condition.getPage(query), Condition.getQueryWrapper(batteryEverydayTotal, BatteryEverydayTotalEntity.class));
		return R.data(BatteryEverydayTotalWrapper.build().pageVO(pages));
	}

	/**
	 * 储能每日统计 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入batteryEverydayTotal")
	public R<IPage<BatteryEverydayTotalVO>> page(BatteryEverydayTotalVO batteryEverydayTotal, Query query) {
		IPage<BatteryEverydayTotalVO> pages = batteryEverydayTotalService.selectBatteryEverydayTotalPage(Condition.getPage(query), batteryEverydayTotal);
		return R.data(pages);
	}

	/**
	 * 储能每日统计 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入batteryEverydayTotal")
	public R save(@Valid @RequestBody BatteryEverydayTotalEntity batteryEverydayTotal) {
		return R.status(batteryEverydayTotalService.save(batteryEverydayTotal));
	}

	/**
	 * 储能每日统计 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入batteryEverydayTotal")
	public R update(@Valid @RequestBody BatteryEverydayTotalEntity batteryEverydayTotal) {
		return R.status(batteryEverydayTotalService.updateById(batteryEverydayTotal));
	}

	/**
	 * 储能每日统计 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入batteryEverydayTotal")
	public R submit(@Valid @RequestBody BatteryEverydayTotalEntity batteryEverydayTotal) {
		return R.status(batteryEverydayTotalService.saveOrUpdate(batteryEverydayTotal));
	}

	/**
	 * 储能每日统计 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(batteryEverydayTotalService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-batteryEverydayTotal")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入batteryEverydayTotal")
	public void exportBatteryEverydayTotal(@ApiIgnore @RequestParam Map<String, Object> batteryEverydayTotal, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<BatteryEverydayTotalEntity> queryWrapper = Condition.getQueryWrapper(batteryEverydayTotal, BatteryEverydayTotalEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(BatteryEverydayTotal::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(BatteryEverydayTotalEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<BatteryEverydayTotalExcel> list = batteryEverydayTotalService.exportBatteryEverydayTotal(queryWrapper);
		ExcelUtil.export(response, "储能每日统计数据" + DateUtil.time(), "储能每日统计数据表", list, BatteryEverydayTotalExcel.class);
	}

	/**
	 * 储能每日统计导出
	 */
	@GetMapping("/dailyEstimate/export")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "储能每日统计导出", notes = "传入batteryEverydayTotal")
	public void dailyEstimateDataExport(QueryCondition queryCondition,HttpServletResponse response) {
		batteryEverydayTotalService.dailyEstimateExport(queryCondition,response);
	}

}

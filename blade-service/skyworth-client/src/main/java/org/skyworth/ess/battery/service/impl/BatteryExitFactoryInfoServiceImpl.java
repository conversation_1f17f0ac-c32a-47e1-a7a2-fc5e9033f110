/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.vo.AppBatteryExitFactoryInfoVO;
import org.skyworth.ess.app.vo.AppVO;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryExitFactoryInfoExcel;
import org.skyworth.ess.battery.mapper.BatteryExitFactoryInfoMapper;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 储能出厂信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
@AllArgsConstructor
public class BatteryExitFactoryInfoServiceImpl extends BaseServiceImpl<BatteryExitFactoryInfoMapper, BatteryExitFactoryInfoEntity> implements IBatteryExitFactoryInfoService {
	private final IDictBizClient dictBizClient;

	public final static DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	public IPage<BatteryExitFactoryInfoVO> selectBatteryExitFactoryInfoPage(IPage<BatteryExitFactoryInfoVO> page, BatteryExitFactoryInfoVO BatteryExitFactoryInfo) {
		return page.setRecords(baseMapper.selectBatteryExitFactoryInfoPage(page, BatteryExitFactoryInfo));
	}


	@Override
	public List<BatteryExitFactoryInfoExcel> exportBatteryExitFactoryInfo(Wrapper<BatteryExitFactoryInfoEntity> queryWrapper) {
		List<BatteryExitFactoryInfoExcel> BatteryExitFactoryInfoList = baseMapper.exportBatteryExitFactoryInfo(queryWrapper);
		R<List<DictBiz>> companyMatch = dictBizClient.getList("device_company");
		R<List<DictBiz>> modeTypeDict = dictBizClient.getList("battery_mode_type");
		List<DictBiz> companyList = companyMatch.getData();
		List<DictBiz> modeTypeList = modeTypeDict.getData();
		for (BatteryExitFactoryInfoExcel excel : BatteryExitFactoryInfoList) {
			for (DictBiz company : companyList) {
				if (company.getDictKey().equalsIgnoreCase(excel.getCompany())) {
					excel.setCompany(company.getDictValue());
					break;
				}
			}
			for (DictBiz modeType : modeTypeList) {
				if (modeType.getDictKey().equalsIgnoreCase(excel.getBatteryType())) {
					excel.setBatteryType(modeType.getDictValue());
					break;
				}
			}
		}
		return BatteryExitFactoryInfoList;
	}

	@Override
	public List<AppBatteryExitFactoryInfoVO> queryAppBatteryExitFactoryInfo(Long plantId) {
		return baseMapper.queryAppBatteryExitFactoryInfo(plantId);
	}

	@Override
	public List<AppBatteryExitFactoryInfoVO> queryAppBatteryDeviceInfo(AppVO appVO) {
		return baseMapper.queryAppBatteryDeviceInfo(appVO);
	}
	@Override
	public boolean deleteLogicBatteryExitFactory(List<Long> ids) {
		BladeUser user = AuthUtil.getUser();
		return baseMapper.deleteLogicBatteryExitFactory(user.getUserId(), user.getAccount(), ids);
	}

	@Override
	public String importAddExcel(List<BatteryExitFactoryInfoExcel> data, Boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(BatteryExitFactoryInfoExcel::getBatterySerialNumber).collect(Collectors.toList());
		List<BatteryExitFactoryInfoEntity> dbList = baseMapper.queryByBatterySerialNumbers(deviceSerialNumbers);
		if (CollectionUtil.isNotEmpty(dbList)) {
			List<String> collect = dbList.stream().map(BatteryExitFactoryInfoEntity::getBatterySerialNumber).collect(Collectors.toList());
			String join = Joiner.on(",").skipNulls().join(collect);
			return join + " had exists, please use modify import.";
		}
		List<BatteryExitFactoryInfoEntity> insert = new ArrayList<>();
		data.forEach(userExcel -> {
			BatteryExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, BatteryExitFactoryInfoEntity.class));
			entity.setCreateUserAccount(user.getAccount());
			entity.setCreateUser(user.getUserId());
			entity.setStatus(CommonConstant.NOT_SEALED_ID);
			insert.add(entity);
		});
		this.saveOrUpdateBatch(insert);
		return "";
	}

	@Override
	public List<BatteryExitFactoryInfoEntity> queryByBatterySerialNumbers(List<String> deviceSerialNumbers) {
		return baseMapper.queryByBatterySerialNumbers(deviceSerialNumbers);
	}

	@Override
	public boolean addBatteryExitFactoryInfo(BatteryExitFactoryInfoEntity batteryExitFactoryInfo) {
		BladeUser user = AuthUtil.getUser();
		batteryExitFactoryInfo.setCreateUserAccount(user.getAccount());
		batteryExitFactoryInfo.setStatus(0);
		BatteryExitFactoryInfoEntity one = this.getOne(Wrappers.<BatteryExitFactoryInfoEntity>query().lambda().eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batteryExitFactoryInfo.getBatterySerialNumber()));
		if (ObjectUtil.isNotEmpty(one)){
			throw new BusinessException("client.battery.exit.add.sn.exist");
		}
		boolean save = this.save(batteryExitFactoryInfo);
		return save;
	}

	@Override
	public String importModifyExcel(List<BatteryExitFactoryInfoExcel> data, Boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(BatteryExitFactoryInfoExcel::getBatterySerialNumber).collect(Collectors.toList());
		List<BatteryExitFactoryInfoEntity> dbData = baseMapper.queryByBatterySerialNumbers(deviceSerialNumbers);
		// 如果入参中个数 和 DB中个数不相等，则表示有新增数据，提示使用新增导入
		if (deviceSerialNumbers.size() != dbData.size()) {
			List<String> dbSn = dbData.stream().map(BatteryExitFactoryInfoEntity::getBatterySerialNumber).collect(Collectors.toList());
			StringBuilder join = new StringBuilder();
			deviceSerialNumbers.forEach(p -> {
				if (!dbSn.contains(p)) {
					join.append(p).append(",");
				}
			});
			return join + " had not exists, please use add import.";
		}
		List<BatteryExitFactoryInfoEntity> update = new ArrayList<>();
		data.forEach(userExcel -> {
			BatteryExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, BatteryExitFactoryInfoEntity.class));
			entity.setUpdateUserAccount(user.getAccount());
			entity.setUpdateUser(user.getUserId());
			update.add(entity);
		});
		baseMapper.updateBatchBySn(update);
		return "";
	}

	@Override
	public int batchUpdate(List<String> serialNumberList, int batteryUseStatus) {
		List<BatteryExitFactoryInfoEntity> update = new ArrayList<>();
		BladeUser user = AuthUtil.getUser();
		for (String serialNumber : serialNumberList) {
			BatteryExitFactoryInfoEntity entity = new BatteryExitFactoryInfoEntity();
			entity.setUpdateUser(user.getUserId());
			entity.setUpdateUserAccount(user.getAccount());
			entity.setBatterySerialNumber(serialNumber);
			entity.setUpdateTime(new Date());
			entity.setStatus(batteryUseStatus);
			update.add(entity);
		}
		return baseMapper.updateBatchBySn(update);
	}

	@Override
	public BigDecimal queryTotalInstalledPower() {
		return baseMapper.queryTotalInstalledPower();
	}

	@Override
	public List<BatteryExitFactoryInfoVO> deviceInformation(QueryCondition queryCondition) {
		return baseMapper.deviceInformation(queryCondition);
	}

	@Override
	public List<BatteryExitFactoryInfoVO> deviceInformationIsDelete(QueryCondition queryCondition) {
		return baseMapper.deviceInformationIsDelete(queryCondition);
	}

	/**
	 * 扫描储能，更新质保开始时间，截止时间
	 * type为1代表
	 */
	@Override
	public int updateForScanBattery(String serialNumber, int batteryUseStatus) {
		BatteryExitFactoryInfoEntity batteryExitFactoryInfo = baseMapper.selectOne(Wrappers.<BatteryExitFactoryInfoEntity>query().lambda().eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber, serialNumber));
		if (ValidationUtil.isNotEmpty(batteryExitFactoryInfo)) {
			List<BatteryExitFactoryInfoEntity> update = new ArrayList<>();
			BladeUser user = AuthUtil.getUser();
			BatteryExitFactoryInfoEntity entity = new BatteryExitFactoryInfoEntity();
			entity.setUpdateUser(user.getUserId());
			entity.setUpdateUserAccount(user.getAccount());
			entity.setBatterySerialNumber(serialNumber);
			entity.setUpdateTime(new Date());
			entity.setStatus(batteryUseStatus);
			LocalDate currentDate = LocalDate.now();
			//激活日期
			String activationDate = currentDate.format(FORMATTER);
			Date exitFactoryDate = batteryExitFactoryInfo.getExitFactoryDate();
			boolean flag = diffDate(exitFactoryDate, 3);
			//质保日期
			String warrantyStartDate;
			if (flag) {
				warrantyStartDate = DATE_FORMAT.format(exitFactoryDate);
			} else {
				warrantyStartDate = activationDate;
			}
			entity.setActivationDate(activationDate);
			entity.setWarrantyStartDate(warrantyStartDate);
			//编辑储能
			update.add(entity);
			return baseMapper.updateBatchBySn(update);
		}
		return 0;
	}

	@Cached(name = "batteryModelProtocol.", key = "#batteryExitFactoryInfoEntity.batterySerialNumber", expire = 12, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	@Override
	public BatteryExitFactoryInfoEntity getModelAndProtocolBySn(BatteryExitFactoryInfoEntity batteryExitFactoryInfoEntity) {
		return this.getOne(Condition.getQueryWrapper(batteryExitFactoryInfoEntity));
	}

	/**
	 * 计算开始日期与当前日期是否超过指定月数
	 */
	public static boolean diffDate(Date startDate, int len) {
		Instant instant = startDate.toInstant();
		// 将Instant转换为ZonedDateTime（使用系统默认时区）
		ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
		// 将ZonedDateTime转换为LocalDate
		LocalDate localDate = zonedDateTime.toLocalDate();
		LocalDate currentDate = LocalDate.now();
		if (localDate.plusMonths(len).compareTo(currentDate) >= 0){
			return false;
		}
		return true;
//		LocalDate exitFactoryDateToLocalData = LocalDate.parse(DATE_FORMAT.format(startDate), FORMATTER);
//		// 计算两个日期之间的差异
//		Period period = Period.between(exitFactoryDateToLocalData, currentDate);
//		// 检查差异是否超过 len 月
//		if (period.getMonths() > len || (period.getMonths() == len && period.getDays() > 0)) {
//			return true;
//		}
//		return false;
	}

	/**
	 * 截止日期
	 */
	public static String deadline(int num, String startDate) {
		// 将日期字符串转换为LocalDate对象
		LocalDate date = LocalDate.parse(startDate, FORMATTER);
		// 添加年数并计算总年限日期
		LocalDate totalYearsDate = date.plusYears(num);
		return totalYearsDate.toString();
	}
}

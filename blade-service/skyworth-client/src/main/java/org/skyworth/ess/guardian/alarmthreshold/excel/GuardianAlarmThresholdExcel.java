/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.excel;


import lombok.Data;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 安全卫士阈值&amp;闸位状态 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GuardianAlarmThresholdExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 欠压告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("欠压告警阈值")
	private BigDecimal undervoltageAlarmThreshold;
	/**
	 * 过压告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("过压告警阈值")
	private BigDecimal overvoltageAlarmThreshold;
	/**
	 * 过流告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("过流告警阈值")
	private BigDecimal overcurrentAlarmThreshold;
	/**
	 * 漏电告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("漏电告警阈值")
	private BigDecimal leakageAlarmThreshold;
	/**
	 * A温度过高告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("A温度过高告警阈值")
	private BigDecimal aTemperatureHighAlarmThreshold;
	/**
	 * B温度过高告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("B温度过高告警阈值")
	private BigDecimal bTemperatureHighAlarmThreshold;
	/**
	 * C温度过高告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("C温度过高告警阈值")
	private BigDecimal cTemperatureHighAlarmThreshold;
	/**
	 * N温度过高告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("N温度过高告警阈值")
	private BigDecimal nTemperatureHighAlarmThreshold;
	/**
	 * 电弧等级告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("电弧等级告警阈值")
	private BigDecimal arcLevelAlarmThreshold;
	/**
	 * 限电度数告警阈值
	 */
	@ColumnWidth(20)
	@ExcelProperty("限电度数告警阈值")
	private BigDecimal alarmThresholdForPowerRestrictionDegree;
	/**
	 * 欠压开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("欠压开关(0关闭/1打开)")
	private Integer undervoltageSwitch;
	/**
	 * 过压开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("过压开关(0关闭/1打开)")
	private Integer overvoltageSwitch;
	/**
	 * 过流开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("过流开关(0关闭/1打开)")
	private Integer overcurrentSwitch;
	/**
	 * 漏电开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("漏电开关(0关闭/1打开)")
	private Integer leakageSwitch;
	/**
	 * 温度过高告警开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("温度过高告警开关(0关闭/1打开)")
	private Integer highTemperatureAlarmSwitch;
	/**
	 * 电弧等级开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("电弧等级开关(0关闭/1打开)")
	private Integer arcLevelSwitch;
	/**
	 * 限电度数开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("限电度数开关(0关闭/1打开)")
	private Integer powerLimitSwitch;
	/**
	 * 缺相保护开关(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("缺相保护开关(0关闭/1打开)")
	private Integer phaseLossProtectionSwitch;
	/**
	 * 闸位状态(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("闸位状态(0关闭/1打开)")
	private Integer gatePositionStatus;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

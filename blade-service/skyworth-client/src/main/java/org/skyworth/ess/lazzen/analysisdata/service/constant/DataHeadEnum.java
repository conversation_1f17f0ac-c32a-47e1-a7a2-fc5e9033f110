package org.skyworth.ess.lazzen.analysisdata.service.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * 消息头定义
 */
@Getter
public enum DataHeadEnum {
    fa("fa","网关数据操作"),
    fb("fb","设备上线/离线状态上报"),
    fc("fc","网关应答"),
    fd("fd","网关参数设置，断路器参数设置，测量运维数据上报，网关上报请求数据"),
    fe("fe","网关参数上报");

    private final String code;
    private final String comment;

    DataHeadEnum(String code, String comment) {
        this.code = code;
        this.comment = comment;
    }

}

package org.skyworth.ess.alarmlog.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警信息列表查询
 * @description:
 * @author: SDT50545
 * @since: 2024-08-10 17:26
 **/
@Data
public class AlarmLogPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("记录id")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long id;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
    @ApiModelProperty(value = "站点ID")
    private Long plantId;

    @ApiModelProperty("所属电站")
    private String plantName;

    @ApiModelProperty("告警类型，inverter：智能能量变换器；battery：储能包；guardian：安全卫士")
    private String exceptionType;

    @ApiModelProperty("告警类型名称，inverter：智能能量变换器；battery：储能包；guardian：安全卫士")
    private String exceptionTypeName;

    @ApiModelProperty("设备SN")
    private String serialNumber;

    @ApiModelProperty("告警时间")
    private String deviceDateTime;

    @ApiModelProperty("恢复时间")
    private String recoveryTime;

    @ApiModelProperty("故障描述")
    private String exceptionMessage;

    @ApiModelProperty("告警和恢复时间的时区")
    private String timeZone;

    @ApiModelProperty("设备型号")
    private String deviceType;

    @ApiModelProperty("告警状态，0：待处理，1：已处理，2：待跟进")
    private Integer status;

	@ApiModelProperty("处理建议")
	private String operationSuggestion;

	@ApiModelProperty(value = "地址码")
	private String addressCode;

	@ApiModelProperty(value = "告警序号，对应业务字典device_alarm_mapping_config的dict_key;")
	private Integer alarmNumber;

	@ApiModelProperty(value = "处理人")
	private String alarmHandler;

}

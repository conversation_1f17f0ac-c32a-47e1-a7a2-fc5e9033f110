package org.skyworth.ess.guardian.realTimeHandler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.feign.IGuardHeartBeatClient;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 安全卫生心跳信息  断线或者离线
 */
@RestController
@Slf4j
@AllArgsConstructor
public class GuardHeartBeatClientBiz implements IGuardHeartBeatClient {


	private IGuardianPlantService plantService;

	@Override
	@PostMapping(API_PREFIX_GUARD)
	public R<String> updateHeartBeat(JSONObject jsonObject) {
		String status=jsonObject.getString("status");
		String deviceSn=jsonObject.getString("deviceSn");
		LambdaQueryWrapper<GuardianPlantEntity> eq = Wrappers.<GuardianPlantEntity>query().lambda()
			.eq(GuardianPlantEntity::getSecurityGuardSerialNumber,deviceSn).eq(GuardianPlantEntity::getIsDeleted,0) ;
		GuardianPlantEntity guardianPlantEntity=plantService.getOne(eq);
		if(ValidationUtil.isNotEmpty(guardianPlantEntity)){
			guardianPlantEntity.setSecurityGuardStatus(Integer.parseInt(status));
			plantService.updateById(guardianPlantEntity);
		}
		return R.success("更新成功");
	}


}

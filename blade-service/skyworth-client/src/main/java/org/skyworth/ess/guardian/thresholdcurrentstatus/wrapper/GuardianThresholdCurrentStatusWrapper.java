/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.thresholdcurrentstatus.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.vo.GuardianThresholdCurrentStatusVO;
import java.util.Objects;

/**
 * 安全卫士-设备上报阈值实时数据 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public class GuardianThresholdCurrentStatusWrapper extends BaseEntityWrapper<GuardianThresholdCurrentStatusEntity, GuardianThresholdCurrentStatusVO>  {

	public static GuardianThresholdCurrentStatusWrapper build() {
		return new GuardianThresholdCurrentStatusWrapper();
 	}

	@Override
	public GuardianThresholdCurrentStatusVO entityVO(GuardianThresholdCurrentStatusEntity GuardianThresholdCurrentStatus) {
		GuardianThresholdCurrentStatusVO GuardianThresholdCurrentStatusVO = Objects.requireNonNull(BeanUtil.copy(GuardianThresholdCurrentStatus, GuardianThresholdCurrentStatusVO.class));

		//User createUser = UserCache.getUser(GuardianThresholdCurrentStatus.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianThresholdCurrentStatus.getUpdateUser());
		//GuardianThresholdCurrentStatusVO.setCreateUserName(createUser.getName());
		//GuardianThresholdCurrentStatusVO.setUpdateUserName(updateUser.getName());

		return GuardianThresholdCurrentStatusVO;
	}


}

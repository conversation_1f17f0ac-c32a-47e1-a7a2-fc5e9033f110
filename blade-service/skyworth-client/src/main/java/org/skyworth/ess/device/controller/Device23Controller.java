/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.excel.Device23Excel;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.vo.Device23VO;
import org.skyworth.ess.device.wrapper.Device23Wrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备/智能能量变换器23数据 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("skyworth-Device23/Device23")
@Api(value = "设备/智能能量变换器23数据", tags = "设备/智能能量变换器23数据接口")
public class Device23Controller extends BladeController {

	private final IDevice23Service Device23Service;

	/**
	 * 设备/智能能量变换器23数据 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入Device23")
	public R<Device23VO> detail(Device23Entity Device23) {
		Device23Entity entity = new Device23Entity();
		QueryWrapper<Device23Entity> queryWrapper = Condition.getQueryWrapper(entity);
		LambdaQueryWrapper<Device23Entity> lambdaQueryWrapper = queryWrapper.lambda().eq(Device23Entity::getPlantId, Device23.getPlantId())
			.eq(Device23Entity::getDeviceSerialNumber, Device23.getDeviceSerialNumber());
		Device23Entity device23 = Device23Service.getOne(lambdaQueryWrapper);
		return R.data(Device23Wrapper.build().entityVO(device23));
	}
	/**
	 * 设备/智能能量变换器23数据 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入Device23")
	public R<IPage<Device23VO>> list(@ApiIgnore @RequestParam Map<String, Object> Device23, Query query) {
		IPage<Device23Entity> pages = Device23Service.page(Condition.getPage(query), Condition.getQueryWrapper(Device23, Device23Entity.class));
		return R.data(Device23Wrapper.build().pageVO(pages));
	}

	/**
	 * 设备/智能能量变换器23数据 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入Device23")
	public R<IPage<Device23VO>> page(Device23VO Device23, Query query) {
		IPage<Device23VO> pages = Device23Service.selectDevice23Page(Condition.getPage(query), Device23);
		return R.data(pages);
	}

	/**
	 * 设备/智能能量变换器23数据 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入Device23")
	public R save(@Valid @RequestBody Device23Entity Device23) {
		return R.status(Device23Service.save(Device23));
	}

	/**
	 * 设备/智能能量变换器23数据 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入Device23")
	public R update(@Valid @RequestBody Device23Entity Device23) {
		return R.status(Device23Service.updateById(Device23));
	}

	/**
	 * 设备/智能能量变换器23数据 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入Device23")
	public R submit(@Valid @RequestBody Device23Entity Device23) {
		return R.status(Device23Service.saveOrUpdate(Device23));
	}

	/**
	 * 设备/智能能量变换器23数据 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(Device23Service.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-Device23")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入Device23")
	public void exportDevice23(@ApiIgnore @RequestParam Map<String, Object> Device23, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<Device23Entity> queryWrapper = Condition.getQueryWrapper(Device23, Device23Entity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Device23::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(Device23Entity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<Device23Excel> list = Device23Service.exportDevice23(queryWrapper);
		ExcelUtil.export(response, "设备/智能能量变换器23数据数据" + DateUtil.time(), "设备/智能能量变换器23数据数据表", list, Device23Excel.class);
	}

}

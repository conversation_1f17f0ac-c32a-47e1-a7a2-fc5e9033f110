/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 安全卫士对应站点 视图实体类
 *
 * <AUTHOR>
 * @since
 */
@Data
public class GatewayPlantOverviewVO {

	public GatewayPlantOverviewVO() {
	}

	public GatewayPlantOverviewVO(Integer lazzenDeviceTotal) {
		this.lazzenDeviceTotal = lazzenDeviceTotal;
	}


	@ApiModelProperty(value = "安全卫士/全屋断路器总数")
	private Integer lazzenDeviceTotal;

}

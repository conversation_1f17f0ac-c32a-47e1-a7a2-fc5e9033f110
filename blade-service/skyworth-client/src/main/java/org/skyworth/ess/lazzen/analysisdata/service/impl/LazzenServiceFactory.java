package org.skyworth.ess.lazzen.analysisdata.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.analysisdata.service.constant.DataHeadEnum;
import org.skyworth.ess.lazzen.analysisdata.service.constant.MegTag4DataHeadToFdEnum;
import org.skyworth.ess.lazzen.analysisdata.service.LazzenService;
import org.skyworth.ess.lazzen.analysisdata.service.constant.Saddr4DataHeadToFeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LazzenServiceFactory {
    @Autowired
    private ApplicationContext applicationContext ;
    private static Map<String,LazzenService> lazzenServiceMap ;
    @PostConstruct
    public void init() {
        // 初始化逻辑
        log.info("MyBean is initialized");
        lazzenServiceMap = new ConcurrentHashMap<>();
        // fc 组合 ：网关应答
        lazzenServiceMap.put(DataHeadEnum.fc.getCode() ,
                applicationContext.getBean("lazzenGatewayAckServiceImpl",LazzenService.class));
        // fd 组合 ： 产品信息
        lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_01.getCode(),
                applicationContext.getBean("lazzenProductInfoServiceImpl",LazzenService.class));
        // 运行状态
        lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_03.getCode(),
                applicationContext.getBean("lazzenRunningStatusServiceImpl",LazzenService.class));
        // 测量数据/数据读取
        lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_04.getCode(),
                applicationContext.getBean("lazzenMeasurementDataServiceImpl",LazzenService.class));
        // 遥控数据（指令参数）
//        lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_07.getCode(),
//                applicationContext.getBean("lazzenRemoteControlServiceImpl",LazzenService.class));
        // fe 组合 ：网关上报心跳
        lazzenServiceMap.put(DataHeadEnum.fe.getCode() + "_"+ Saddr4DataHeadToFeEnum.code_0001.getCode(),
                applicationContext.getBean("lazzenGatewayParamServiceImpl",LazzenService.class));
    }
    public LazzenService getLazzenServiceFactory(String dataHead,String megTag,String saddr) {
        LazzenService lazzenService = null;
        lazzenService = lazzenServiceMap.get(dataHead);
        if(lazzenService!=null) {
            return lazzenService;
        }
        lazzenService = lazzenServiceMap.get(dataHead + "_" + megTag);
        if(lazzenService!=null) {
            return lazzenService;
        }
        lazzenService = lazzenServiceMap.get(dataHead + "_" + saddr);
    	return lazzenService;
    }
}

package org.skyworth.ess.device.service;

import org.skyworth.ess.device.entity.TimeZoneDevice;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

public interface TimeZoneDeviceService extends BaseService<TimeZoneDevice> {
    List<TimeZoneDevice> getListBySnList(List<String> deviceSnList);

	Map<String, String> getMapFromCacheByPlantIdList(List<Long> plantIdList);
}

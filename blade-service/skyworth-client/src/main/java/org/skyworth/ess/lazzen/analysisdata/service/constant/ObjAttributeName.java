package org.skyworth.ess.lazzen.analysisdata.service.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ObjAttributeName {
    gatewayUniqueNumber("gatewayUniqueNumber","设备网关唯一编号"),
    circuitBreakerAddress("circuitBreakerAddress","断路器地址，前2位"),
    feSaddr("saddr","0001  地址00，功能码01，网关上线02"),
    fcCmdId("cmdId","下发流水号"),
    fcCmdAck("cmdAck","应答码：CmdAck4DataHeaderToFcEnum"),
    deviceDateTime("deviceDateTime","上报时间");
    private final String attributeName;
    private final String comment;
    ObjAttributeName(String attributeName, String comment) {
        this.attributeName = attributeName;
        this.comment = comment;
    }
}

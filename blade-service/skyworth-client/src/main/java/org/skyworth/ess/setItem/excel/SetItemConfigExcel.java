/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * APP设置项配置 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SetItemConfigExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	@ColumnWidth(20)
	@ExcelProperty("fakeId")
	private String fakeId;

	/**
	 * 父级id
	 */
	@ColumnWidth(20)
	@ExcelProperty("父级id")
	private Long parentId;
	/**
	 * 设备类型0智能能量变换器1储能2光伏组件3充电桩
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备类型0智能能量变换器1储能2光伏组件3充电桩")
	private String deviceType;
	/**
	 * 设备型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备型号")
	private String deviceModel;
	/**
	 * 设置类型0高级设置，1开机设置，2分时设置，3设备参数
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置类型0高级设置，1开机设置，2分时设置，3设备参数")
	private String setCategory;
	/**
	 * 设置项大类
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项大类")
	private String setItemBigType;
	/**
	 * 设置项类型0button，1select，2input
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项类型0button，1select，2input")
	private String setItemType;
	/**
	 * 设置项名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项名称")
	private String setItemName;
	/**
	 * 设置项键
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项键")
	private String setItemKey;
	/**
	 * 设置项值
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项值")
	private String setItemValue;
	/**
	 * 设置项默认值
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项默认值")
	private String setItemDefault;
	/**
	 * 设置项排序
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项排序")
	private String setItemSort;
	/**
	 * 设置项对应数据库表
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项对应数据库表")
	private String setItemTable;
	/**
	 * 设置项对应数据库字段
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项对应数据库字段")
	private String setItemTableColumn;
	/**
	 * 设置项对应协议协议版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项对应协议协议版本")
	private String setItemProtocolVersion;
	/**
	 * 输入框设置项最小值
	 */
	@ColumnWidth(20)
	@ExcelProperty("输入框设置项最小值")
	private String setItemRangeMin;
	/**
	 * 输入框设置项最大值
	 */
	@ColumnWidth(20)
	@ExcelProperty("输入框设置项最大值")
	private String setItemRangeMax;
	/**
	 * 设置项对应协议映射地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("设置项对应协议映射地址")
	private String setItemProtocolAddress;
	/**
	 * 是否子设置项0否1是
	 */
	@ColumnWidth(20)
	@ExcelProperty("是否子设置项0否1是")
	private String isSubItem;
	/**
	 * 备注
	 */
	@ColumnWidth(20)
	@ExcelProperty("备注")
	private String remark;
	/**
	 * 保留字段1
	 */
	@ColumnWidth(20)
	@ExcelProperty("保留字段1")
	private String attribute1;
	/**
	 * 保留字段2
	 */
	@ColumnWidth(20)
	@ExcelProperty("保留字段2")
	private String attribute2;
	/**
	 * 保留字段3
	 */
	@ColumnWidth(20)
	@ExcelProperty("保留字段3")
	private String attribute3;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

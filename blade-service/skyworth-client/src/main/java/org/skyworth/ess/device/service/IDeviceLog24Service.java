/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import org.skyworth.ess.device.entity.DeviceLog24Entity;
import org.springblade.core.mp.base.BaseService;

import java.util.HashMap;
import java.util.List;

/**
 * DeviceLog24 服务类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface IDeviceLog24Service extends BaseService<DeviceLog24Entity> {

	List<DeviceLog24Entity> selectDataByLatestTime();

    HashMap<String, Object> getAllQuickSetup(Long plantId, String deviceSerialNumber);
//	/**
//	 * 自定义分页
//	 *
//	 * @param page
//	 * @param DeviceLog24
//	 * @return
//	 */
//	IPage<DeviceLog24VO> selectDeviceLog24Page(IPage<DeviceLog24VO> page, DeviceLog24VO DeviceLog24);
//
//
//	/**
//	 * 导出数据
//	 *
//	 * @param queryWrapper
//	 * @return
//	 */
//	List<DeviceLog24Excel> exportDeviceLog24(Wrapper<DeviceLog24Entity> queryWrapper);

}

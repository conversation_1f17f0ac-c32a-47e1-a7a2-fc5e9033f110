package org.skyworth.ess.device.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.excel.Device23Excel;
import org.skyworth.ess.device.mapper.Device23Mapper;
import org.skyworth.ess.device.service.IDevice23Service;
import org.skyworth.ess.device.vo.Device23VO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class Device23ServiceImpl extends BaseServiceImpl<Device23Mapper, Device23Entity> implements IDevice23Service {

    private static final int num=20;
    private IDeviceIssueBiz deviceIssueBiz;
    private BladeRedis redis;
	private IWifiStickPlantService wifiStickPlantService;
	@Resource
	Device23Mapper device23Mapper;
    @Override
    public List<Device23Entity> getEntity(Wrapper<Device23Entity> queryWrapper) {
        return baseMapper.getEntity(queryWrapper);
    }

    @Override
    public R<String> workModeIssueToDevice(DeviceCustomModeEntity deviceCustomModeEntity) {
        JSONObject jsonObject=new JSONObject();
		String deviceModel=deviceCustomModeEntity.getHybridWorkMode();
		LambdaQueryWrapper<WifiStickPlantEntity> wifiEq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getPlantId, deviceCustomModeEntity.getPlantId())
			.eq(WifiStickPlantEntity::getDeviceSerialNumber, deviceCustomModeEntity.getDeviceSerialNumber());  //查出在线设备
		List<WifiStickPlantEntity> stickPlantEntityList=wifiStickPlantService.list(wifiEq);
        if(!stickPlantEntityList.isEmpty()){

			jsonObject.put("deviceSn",deviceCustomModeEntity.getDeviceSerialNumber());
			String requestId= TimeUtils.generateRequestId();
			jsonObject.put("requestId", requestId);
			jsonObject.put("topic", Constants.SETTING_ISSUE);
			JSONArray msg=new JSONArray();

			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getHybridWorkMode())) {
				JSONObject workModel=new JSONObject();
				workModel.put("address",8448);
				workModel.put("data",Integer.parseInt(deviceModel));
				workModel.put("len",1);
				msg.add(workModel);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getTimeBasedControlEnable())){
				JSONObject timebase=new JSONObject();
				timebase.put("address",8524);
				timebase.put("data",Integer.parseInt(deviceCustomModeEntity.getTimeBasedControlEnable()));
				timebase.put("len",1);
				msg.add(timebase);
			}


			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getOnceEveryday())){
				JSONObject onceEveryday=new JSONObject();
				onceEveryday.put("address",8449);
				onceEveryday.put("data",Integer.parseInt(deviceCustomModeEntity.getOnceEveryday()));
				onceEveryday.put("len",1);
				msg.add(onceEveryday);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeStartTime1())) {
				String[] chargeStartTime1 = deviceCustomModeEntity.getChargeStartTime1().split(":");
				String startTime1=chargeStartTime1[0];
				String startTime2=chargeStartTime1[1];
				int res=(Integer.parseInt(startTime1)<<8) | Integer.parseInt(startTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8450);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeEndTime1())){
				String[] chargeEndTime1=deviceCustomModeEntity.getChargeEndTime1().split(":");
				String endTime1=chargeEndTime1[0];
				String endTime2=chargeEndTime1[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8451);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeStartTime1())) {
				String[] dischargeStartTime1 = deviceCustomModeEntity.getDischargeStartTime1().split(":");
				String endTime1=dischargeStartTime1[0];
				String endTime2=dischargeStartTime1[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8452);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeEndTime1())) {
				String[] dischargeEndTime1 = deviceCustomModeEntity.getDischargeEndTime1().split(":");
				String endTime1=dischargeEndTime1[0];
				String endTime2=dischargeEndTime1[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8453);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}

			String chargeEndSocInTime1 = deviceCustomModeEntity.getChargeEndSocInTime1();
			if(chargeEndSocInTime1!=null) {
				JSONObject capacity=new JSONObject();
				capacity.put("address",8554);
				capacity.put("data",Integer.parseInt(chargeEndSocInTime1));
				capacity.put("len",1);
				msg.add(capacity);
			}
			String dischargeEndSocInTime1=deviceCustomModeEntity.getDischargeEndSocInTime1();
			if(dischargeEndSocInTime1!=null) {
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8558);
				chargeEnd.put("data",Integer.parseInt(dischargeEndSocInTime1));
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getChargePowerInTime1HighWord())){
				JSONObject maximumChargerPower=new JSONObject();
				maximumChargerPower.put("address",8552);
				maximumChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getChargePowerInTime1HighWord()));
				maximumChargerPower.put("len",2);
				msg.add(maximumChargerPower);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getDischargePowerInTime1HighWord())){
				JSONObject maximumDisChargerPower=new JSONObject();
				maximumDisChargerPower.put("address",8556);
				maximumDisChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getDischargePowerInTime1HighWord()));
				maximumDisChargerPower.put("len",2);
				msg.add(maximumDisChargerPower);
			}

			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getOnceEveryday2())){
				JSONObject onceEveryday=new JSONObject();
				onceEveryday.put("address",8454);
				onceEveryday.put("data",Integer.parseInt(deviceCustomModeEntity.getOnceEveryday2()));
				onceEveryday.put("len",1);
				msg.add(onceEveryday);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeStartTime2())) {
				String[] chargeStartTime = deviceCustomModeEntity.getChargeStartTime2().split(":");
				String startTime1=chargeStartTime[0];
				String startTime2=chargeStartTime[1];
				int res=(Integer.parseInt(startTime1)<<8) | Integer.parseInt(startTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8455);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeEndTime2())){
				String[] chargeEndTime2=deviceCustomModeEntity.getChargeEndTime2().split(":");
				String endTime1=chargeEndTime2[0];
				String endTime2=chargeEndTime2[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8456);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeStartTime2())) {
				String[] dischargeStartTime2 = deviceCustomModeEntity.getDischargeStartTime2().split(":");
				String endTime1=dischargeStartTime2[0];
				String endTime2=dischargeStartTime2[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8457);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeEndTime2())) {
				String[] dischargeEndTime2 = deviceCustomModeEntity.getDischargeEndTime2().split(":");
				String endTime1=dischargeEndTime2[0];
				String endTime2=dischargeEndTime2[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8458);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}

			String chargeEndSocInTime2 = deviceCustomModeEntity.getChargeEndSocInTime2();
			if(chargeEndSocInTime2!=null) {
				JSONObject capacity=new JSONObject();
				capacity.put("address",8562);
				capacity.put("data",Integer.parseInt(chargeEndSocInTime2));
				capacity.put("len",1);
				msg.add(capacity);
			}
			String dischargeEndSocInTime2=deviceCustomModeEntity.getDischargeEndSocInTime2();
			if(dischargeEndSocInTime2!=null) {
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8566);
				chargeEnd.put("data",Integer.parseInt(dischargeEndSocInTime2));
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getChargePowerInTime2HighWord())){
				JSONObject maximumChargerPower=new JSONObject();
				maximumChargerPower.put("address",8560);
				maximumChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getChargePowerInTime2HighWord()));
				maximumChargerPower.put("len",2);
				msg.add(maximumChargerPower);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getDischargePowerInTime2HighWord())){
				JSONObject maximumDisChargerPower=new JSONObject();
				maximumDisChargerPower.put("address",8564);
				maximumDisChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getDischargePowerInTime2HighWord()));
				maximumDisChargerPower.put("len",2);
				msg.add(maximumDisChargerPower);
			}

			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getOnceEveryday3())){
				JSONObject onceEveryday=new JSONObject();
				onceEveryday.put("address",8459);
				onceEveryday.put("data",Integer.parseInt(deviceCustomModeEntity.getOnceEveryday3()));
				onceEveryday.put("len",1);
				msg.add(onceEveryday);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeStartTime3())) {
				String[] chargeStartTime3 = deviceCustomModeEntity.getChargeStartTime3().split(":");
				String startTime1=chargeStartTime3[0];
				String startTime2=chargeStartTime3[1];
				int res=(Integer.parseInt(startTime1)<<8) | Integer.parseInt(startTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8460);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getChargeEndTime3())){
				String[] chargeEndTime3=deviceCustomModeEntity.getChargeEndTime3().split(":");
				String endTime1=chargeEndTime3[0];
				String endTime2=chargeEndTime3[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8461);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeStartTime3())) {
				String[] dischargeStartTime3 = deviceCustomModeEntity.getDischargeStartTime3().split(":");
				String endTime1=dischargeStartTime3[0];
				String endTime2=dischargeStartTime3[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeStart=new JSONObject();
				chargeStart.put("address",8462);
				chargeStart.put("data",res);
				chargeStart.put("len",1);
				msg.add(chargeStart);
			}
			if(StringUtils.isNotEmpty(deviceCustomModeEntity.getDischargeEndTime3())) {
				String[] dischargeEndTime3 = deviceCustomModeEntity.getDischargeEndTime3().split(":");
				String endTime1=dischargeEndTime3[0];
				String endTime2=dischargeEndTime3[1];
				int res=(Integer.parseInt(endTime1)<<8) | Integer.parseInt(endTime2);
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8463);
				chargeEnd.put("data",res);
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}

			String chargeEndSocInTime3 = deviceCustomModeEntity.getChargeEndSocInTime3();
			if(chargeEndSocInTime3!=null) {
				JSONObject capacity=new JSONObject();
				capacity.put("address",8570);
				capacity.put("data",Integer.parseInt(chargeEndSocInTime3));
				capacity.put("len",1);
				msg.add(capacity);
			}
			String dischargeEndSocInTime3=deviceCustomModeEntity.getDischargeEndSocInTime3();
			if(dischargeEndSocInTime3!=null) {
				JSONObject chargeEnd=new JSONObject();
				chargeEnd.put("address",8574);
				chargeEnd.put("data",Integer.parseInt(dischargeEndSocInTime3));
				chargeEnd.put("len",1);
				msg.add(chargeEnd);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getChargePowerInTime3HighWord())){
				JSONObject maximumChargerPower=new JSONObject();
				maximumChargerPower.put("address",8568);
				maximumChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getChargePowerInTime3HighWord()));
				maximumChargerPower.put("len",2);
				msg.add(maximumChargerPower);
			}
			if(ObjectUtil.isNotEmpty(deviceCustomModeEntity.getDischargePowerInTime3HighWord())){
				JSONObject maximumDisChargerPower=new JSONObject();
				maximumDisChargerPower.put("address",8572);
				maximumDisChargerPower.put("data",Integer.parseInt(deviceCustomModeEntity.getDischargePowerInTime3HighWord()));
				maximumDisChargerPower.put("len",2);
				msg.add(maximumDisChargerPower);
			}
			jsonObject.put("msg",msg);
			Map<String,String> res=deviceIssueBiz.setting(jsonObject);
			if(res!=null){
				if(res.containsKey("200")){//下发成功
					String currentLanguage = CommonUtil.getCurrentLanguage();
					return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
				}else if(res.containsKey("401")) {//下发失败
					R<String> r = new R<>();
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100012.getCode());
					res.forEach((key, value) -> r.setMsg(value));
					return r;
				}
			}
		}

		R<String> r = new R<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		r.setCode(I18nMsgCode.SKYWORTH_SYSTEM_INVERTER_100011.getCode());
		r.setMsg(I18nMsgCode.SKYWORTH_SYSTEM_INVERTER_100011.autoGetMessage(currentLanguage));
		return r;
    }

	@Override
	public IPage<Device23VO> selectDevice23Page(IPage<Object> page, Device23VO device23) {
		return null;
	}

	@Override
	public List<Device23Excel> exportDevice23(QueryWrapper<Device23Entity> queryWrapper) {
		return null;
	}

    @Override
    public int updateSetup(Map<String, Object> device23, Long plantId, String deviceSerialNumber) {
        return device23Mapper.updateSetup(device23,plantId,deviceSerialNumber);
    }

	@Override
	public int updateByPlantId(DeviceCustomModeEntity deviceCustomModeEntity) {
		return device23Mapper.updateByPlantId(deviceCustomModeEntity);
	}

	@Override
	public int deleteByPlantId(Long plantId, String deviceSerialNumber) {
		LambdaUpdateWrapper<Device23Entity> update = Wrappers.<Device23Entity>update().lambda()
			.set(Device23Entity::getIsDeleted, 1)
			.eq(Device23Entity::getPlantId, plantId)
			.eq(Device23Entity::getDeviceSerialNumber, deviceSerialNumber);
		return device23Mapper.delete(update);
	}

	@Override
	public int updatePlantMode(Long plantId, String deviceSerialNumber) {
		return device23Mapper.updatePlantMode(plantId,deviceSerialNumber);
	}


}

package org.skyworth.ess.agreement.util;

import java.util.List;
import java.util.Map;

/**
 * @ClassName DeviceControlService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/31 17:07
 */
public interface DeviceControlService {
	/**
	 * 下发设备控制指令
	 * @param company 公司标识（用于区分不同协议）
	 * @param deviceId 设备ID
	 * @param command 控制命令
	 * @param params 控制参数
	 * @return 下发是否成功
	 */
	boolean sendControlCommand(String company, String deviceId, String command, Map<String, Object> params);

	/**
	 * 批量下发设备控制指令
	 * @param company 公司标识
	 * @param deviceIds 设备ID列表
	 * @param command 控制命令
	 * @param params 控制参数
	 * @return 成功下发的设备数量
	 */
	int batchSendControlCommand(String company, List<String> deviceIds, String command, Map<String, Object> params);

	/**
	 * 获取支持的协议类型
	 * @return 支持的协议类型列表
	 */
	List<String> getSupportedProtocols();

	/**
	 * 初始化方法（可选）
	 */
	default void init() {
		// 默认空实现
	}

	/**
	 * 销毁方法（可选）
	 */
	default void destroy() {
		// 默认空实现
	}
}

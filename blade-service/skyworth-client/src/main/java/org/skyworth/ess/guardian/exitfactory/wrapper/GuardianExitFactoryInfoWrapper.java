/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.vo.GuardianExitFactoryInfoVO;
import java.util.Objects;

/**
 * 安全卫士储能出厂信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
public class GuardianExitFactoryInfoWrapper extends BaseEntityWrapper<GuardianExitFactoryInfoEntity, GuardianExitFactoryInfoVO>  {

	public static GuardianExitFactoryInfoWrapper build() {
		return new GuardianExitFactoryInfoWrapper();
 	}

	@Override
	public GuardianExitFactoryInfoVO entityVO(GuardianExitFactoryInfoEntity guardianExitFactoryInfo) {
		GuardianExitFactoryInfoVO guardianExitFactoryInfoVO = Objects.requireNonNull(BeanUtil.copy(guardianExitFactoryInfo, GuardianExitFactoryInfoVO.class));

		//User createUser = UserCache.getUser(guardianExitFactoryInfo.getCreateUser());
		//User updateUser = UserCache.getUser(guardianExitFactoryInfo.getUpdateUser());
		//guardianExitFactoryInfoVO.setCreateUserName(createUser.getName());
		//guardianExitFactoryInfoVO.setUpdateUserName(updateUser.getName());

		return guardianExitFactoryInfoVO;
	}


}

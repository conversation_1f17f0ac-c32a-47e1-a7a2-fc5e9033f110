/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedpower.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 安全卫士定时设置-功率设置 实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@TableName("guardian_timed_power")
@ApiModel(value = "GuardianTimedPower对象", description = "安全卫士定时设置-功率设置")
@EqualsAndHashCode(callSuper = true)
public class GuardianTimedPowerEntity extends SkyWorthEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ApiModelProperty(value = "安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	/**
	 * 最小功率
	 */
	@ApiModelProperty(value = "最小功率")
	private BigDecimal minPower;
	/**
	 * 最大功率
	 */
	@ApiModelProperty(value = "最大功率")
	private BigDecimal maxPower;

	@ApiModelProperty(value = "设置排序")
	private Integer setSort;
}

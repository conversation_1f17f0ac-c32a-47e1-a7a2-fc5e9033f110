/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 消息推送日志表 实体类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@TableName("message_push_record")
@ApiModel(value = "MessagePushRecord对象", description = "消息推送日志表")
@EqualsAndHashCode(callSuper = true)
public class MessagePushRecordEntity extends TenantEntity {

	/**
	 * 业务表ID
	 */
	@ApiModelProperty(value = "业务表ID")
	private Long businessId;
	/**
	 * 消息类型，业务字典message_type
	 */
	@ApiModelProperty(value = "消息类型，业务字典message_type")
	private String messageType;
	/**
	 * 设备类型 battery/device
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;
	/**
	 * 消息接收人id
	 */
	@ApiModelProperty(value = "消息接收人id")
	private Long messageRecipients;
	/**
	 * 消息主体
	 */
	@ApiModelProperty(value = "消息主体")
	private String messageContent;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 设备
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备序列号")
	private String serialNumber;
	/**
	 * 上报时间
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "上报时间")
	private Date deviceDateTime;

	@TableField(exist = false)
	private JSONObject inverterKindArr;

	@TableField(exist = false)
	private JSONObject inverterDeviceTypeArr;

	@TableField(exist = false)
	private String deviceModel;

	@TableField(exist = false)
	private String inverterModel;
}

package org.skyworth.ess.balconypv.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.service.IEnergySystemService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 能源系统 控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/energySystem")
@Api(value = "能源系统", tags = "能源系统接口")
public class EnergySystemController extends BladeController {

	private final IEnergySystemService energySystemService;

	/**
	 * 能源系统 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增或修改", notes = "传入energySystem")
	public R submit(@Valid @RequestBody EnergySystemEntity energySystem) {
		return R.status(energySystemService.saveOrUpdate(energySystem));
	}

	/**
	 * 能源系统 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(energySystemService.deleteLogic(Func.toLongList(ids)));
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.Device23Mapper">

    <resultMap id="Device23ResultMap" type="org.skyworth.ess.device.entity.Device23Entity">
        <id property="id" column="id"/>
        <result property="hybridWorkMode" column="hybrid_work_mode"/>
        <result property="onceEveryday" column="once_everyday"/>
        <result property="chargeStartTime1" column="charge_start_time1"/>
        <result property="chargeEndTime1" column="charge_end_time1"/>
        <result property="dischargeStartTime1" column="discharge_start_time1"/>
        <result property="dischargeEndTime1" column="discharge_end_time1"/>
        <result property="batteryTypeSelection" column="battery_type_selection"/>
        <result property="commAddress" column="comm_address"/>
        <result property="batteryAh" column="battery_ah"/>
        <result property="stopDischargeVoltage" column="stop_discharge_voltage"/>
        <result property="stopChargeVoltage" column="stop_charge_voltage"/>
        <result property="gridCharge" column="grid_charge"/>
        <result property="maximumGridChargerPower" column="maximum_grid_charger_power"/>
        <result property="capacityOfGridChargerEnd" column="capacity_of_grid_charger_end"/>
        <result property="maximumChargerPower" column="maximum_charger_power"/>
        <result property="capacityOfChargerEnd" column="capacity_of_charger_end"/>
        <result property="maximumDischargerPower" column="maximum_discharger_power"/>
        <result property="capacityOfDischargerEnd" column="capacity_of_discharger_end"/>
        <result property="offGridMode" column="off_grid_mode"/>
        <result property="ratedOutputVoltage" column="rated_output_voltage"/>
        <result property="ratedOutputFrequency" column="rated_output_frequency"/>
        <result property="offGridStartUpBatteryCapacity" column="off_grid_startup_battery_capacity"/>
        <result property="maximumDischargeCurrent" column="maximum_discharge_current"/>
        <result property="maximumChargerCurrent" column="maximum_charger_current"/>
        <result property="plantId" column="plant_id"/>
        <result property="modbusProtocolVersion" column="modbus_protocol_version"/>
        <result property="synchStatus" column="synch_status"/>
        <result property="deviceSerialNumber" column="device_serial_number"/>
        <result property="createUserAccount" column="create_user_account"/>
        <result property="updateUserAccount" column="update_user_account"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="time_based_control_enable" property="timeBasedControlEnable"/>
        <result column="generator_start_soc" property="generatorStartSoc"/>
        <result column="generator_end_soc" property="generatorEndSoc"/>
        <result column="maximum_input_power_from_grid" property="maximumInputPowerFromGrid"/>
        <result column="capacity_of_discharge_end_on_grid" property="capacityOfDischargeEndOnGrid"/>
        <result column="force_charge_start_soc" property="forceChargeStartSoc"/>
        <result column="force_charge_end_soc" property="forceChargeEndSoc"/>
        <result column="backup_minimum_output_voltage" property="backupMinimumOutputVoltage"/>
        <result column="backup_maximum_output_voltage" property="backupMaximumOutputVoltage"/>
        <result column="generator_dry_force_on_or_off" property="generatorDryForceOnOrOff"/>
        <result column="parallel_mode_function" property="parallelModeFunction"/>
        <result column="parallel_system_battery_connect_type" property="parallelSystemBatteryConnectType"/>
    </resultMap>


    <select id="getEntity" resultMap="Device23ResultMap">
        SELECT * FROM device_23 ${ew.customSqlSegment}
    </select>


    <select id="selectDevice23Page" resultMap="Device23ResultMap">
        select * from device_23 where is_deleted = 0
    </select>


    <select id="exportDevice23" resultType="org.skyworth.ess.device.excel.Device23Excel">
        SELECT * FROM device_23 ${ew.customSqlSegment}
    </select>

    <update id="updateSetup">
        UPDATE device_23 SET
        update_time = now(),
        <foreach collection="device23Map" item="val" index="key" separator=",">
            ${key} = #{val}
        </foreach>
        WHERE plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </update>

    <update id="updateByPlantId">
        UPDATE device_23
        <set>
        <if test="hybridWorkMode!=null and hybridWorkMode!=''">
            `hybrid_work_mode`=#{hybridWorkMode},
        </if>
        <if test="timeBasedControlEnable!=null and timeBasedControlEnable!=''">
            `time_based_control_enable`=#{timeBasedControlEnable},
        </if>
        <if test="maximumDischargerPower!=null and maximumDischargerPower!=''">
            `maximum_discharger_power`=#{maximumDischargerPower},
        </if>
        <if test="maximumChargerPower!=null and maximumChargerPower!=''">
            `maximum_charger_power`=#{maximumChargerPower},
        </if>
        <if test="capacityOfDischargerEnd!=null and capacityOfDischargerEnd!=''">
            `capacity_of_discharger_end`=#{capacityOfDischargerEnd},
        </if>
        <if test="capacityOfChargerEnd!=null and capacityOfChargerEnd!=''">
            `capacity_of_charger_end`=#{capacityOfChargerEnd},
        </if>

        <if test="onceEveryday!=null and onceEveryday!=''">
            `once_everyday`=#{onceEveryday},
        </if>
        <if test="chargeStartTime1!=null and chargeStartTime1!=''">
            `charge_start_time1`=#{chargeStartTime1},
        </if>
        <if test="chargeEndTime1!=null and chargeEndTime1!=''">
            `charge_end_time1`=#{chargeEndTime1},
        </if>
        <if test="chargePowerInTime1HighWord!=null and chargePowerInTime1HighWord!=''">
            `charge_power_in_time1_high_word`=#{chargePowerInTime1HighWord},
        </if>
        <if test="chargeEndSocInTime1!=null and chargeEndSocInTime1!=''">
            `charge_end_soc_in_time1`=#{chargeEndSocInTime1},
        </if>
        <if test="dischargeStartTime1!=null and dischargeStartTime1!=''">
            `discharge_start_time1`=#{dischargeStartTime1},
        </if>
        <if test="dischargeEndTime1!=null and dischargeEndTime1!=''">
            `discharge_end_time1`=#{dischargeEndTime1},
        </if>
        <if test="dischargePowerInTime1HighWord!=null and dischargePowerInTime1HighWord!=''">
            `discharge_power_in_time1_high_word`=#{dischargePowerInTime1HighWord},
        </if>
        <if test="dischargeEndSocInTime1!=null and dischargeEndSocInTime1!=''">
            `discharge_end_soc_in_time1`=#{dischargeEndSocInTime1},
        </if>

        <if test="onceEveryday2!=null and onceEveryday2!=''">
            `once_everyday2`=#{onceEveryday2},
        </if>
        <if test="chargeStartTime2!=null and chargeStartTime2!=''">
            `charge_start_time2`=#{chargeStartTime2},
        </if>
        <if test="chargeEndTime2!=null and chargeEndTime2!=''">
            `charge_end_time2`=#{chargeEndTime2},
        </if>
        <if test="chargePowerInTime2HighWord!=null and chargePowerInTime2HighWord!=''">
            `charge_power_in_time2_high_word`=#{chargePowerInTime2HighWord},
        </if>
        <if test="chargeEndSocInTime2!=null and chargeEndSocInTime2!=''">
            `charge_end_soc_in_time2`=#{chargeEndSocInTime2},
        </if>
        <if test="dischargeStartTime2!=null and dischargeStartTime2!=''">
            `discharge_start_time2`=#{dischargeStartTime2},
        </if>
        <if test="dischargeEndTime2!=null and dischargeEndTime2!=''">
            `discharge_end_time2`=#{dischargeEndTime2},
        </if>
        <if test="dischargePowerInTime2HighWord!=null and dischargePowerInTime2HighWord!=''">
            `discharge_power_in_time2_high_word`=#{dischargePowerInTime2HighWord},
        </if>
        <if test="dischargeEndSocInTime2!=null and dischargeEndSocInTime2!=''">
            `discharge_end_soc_in_time2`=#{dischargeEndSocInTime2},
        </if>

        <if test="onceEveryday3!=null and onceEveryday3!=''">
            `once_everyday3`=#{onceEveryday3},
        </if>
        <if test="chargeStartTime3!=null and chargeStartTime3!=''">
            `charge_start_time3`=#{chargeStartTime3},
        </if>
        <if test="chargeEndTime3!=null and chargeEndTime3!=''">
            `charge_end_time3`=#{chargeEndTime3},
        </if>
        <if test="chargePowerInTime3HighWord!=null and chargePowerInTime3HighWord!=''">
            `charge_power_in_time3_high_word`=#{chargePowerInTime3HighWord},
        </if>
        <if test="chargeEndSocInTime3!=null and chargeEndSocInTime3!=''">
            `charge_end_soc_in_time3`=#{chargeEndSocInTime3},
        </if>
        <if test="dischargeStartTime3!=null and dischargeStartTime3!=''">
            `discharge_start_time3`=#{dischargeStartTime3},
        </if>
        <if test="dischargeEndTime3!=null and dischargeEndTime3!=''">
            `discharge_end_time3`=#{dischargeEndTime3},
        </if>
        <if test="dischargePowerInTime3HighWord!=null and dischargePowerInTime3HighWord!=''">
            `discharge_power_in_time3_high_word`=#{dischargePowerInTime3HighWord},
        </if>
        <if test="dischargeEndSocInTime3!=null and dischargeEndSocInTime3!=''">
            `discharge_end_soc_in_time3`=#{dischargeEndSocInTime3},
        </if>
        </set>
        where `plant_id` = #{plantId} and `device_serial_number` = #{deviceSerialNumber}

    </update>
    <update id="updatePlantMode">
        update  plant set is_parallel_mode ='1'
        where
        (select count(1) as cnt  from device_23 d
        inner join  (select device_serial_number  from wifi_stick_plant wsp where plant_id =#{plantId} and is_deleted =0) tmp on tmp.device_serial_number=d.device_serial_number
        where d.device_serial_number !=#{deviceSerialNumber} and d.is_deleted =0 and d.parallel_mode_function='1')  <![CDATA[ > ]]> 0 and id=#{plantId}
    </update>
</mapper>


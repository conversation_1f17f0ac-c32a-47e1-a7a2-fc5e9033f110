/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 储能当前状态 实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@TableName("battery_current_status")
@ApiModel(value = "BatteryCurrentStatus对象", description = "储能当前状态")
@EqualsAndHashCode(callSuper = true)
public class BatteryCurrentStatusEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 设备/智能能量变换器SN
	 */
	@ApiModelProperty(value = "设备/智能能量变换器SN")
	private String deviceSerialNumber;

	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 储能SOC
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能SOC")
	private BigDecimal batterySoc;
	/**
	 * 电压
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "电压")
	private BigDecimal batteryVoltage;
	/**
	 * 电流
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "电流")
	private BigDecimal batteryCurrent;
	/**
	 * 功率
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "功率")
	private BigDecimal batteryPower;
	/**
	 * 储能温度
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能温度")
	private BigDecimal batteryTemperature;
	/**
	 * 日均充电能量  200b
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "当日充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 日均放电能量  200F
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "当日放电能量")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 累计充电能量  200d
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "累计充电能量")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 累计放电能量  2011
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "累计放电能量")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 储能电状态
	 */
	@ApiModelProperty(value = "储能当前状态")
	private String batteryStatus;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "最高单体储能电压")
	private BigDecimal batteryMaximumCellVoltage;

	@ApiModelProperty(value = "最低单体储能电压")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal batteryMinimumCellVoltage;

	@ApiModelProperty(value = "最高单体储能温度")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal batteryMaximumCellTemperature;

	@ApiModelProperty(value = "最低单体储能温度")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal batteryMinimumCellTemperature;

	@ApiModelProperty(value = "当日光伏发电量")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal todayEnergy;

	@ApiModelProperty(value = "光伏总发电量")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal totalEnergy;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能数量")
	private BigDecimal numberOfBattery;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能SOH")
	private BigDecimal soh;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能循环次数")
	private BigDecimal batteryMaximumCyclesTimes;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "PV当日发电")
	private BigDecimal pvlDailyGeneratingEnergySum;

	@ApiModelProperty(value = "负载累计能量总和")
	private BigDecimal accumulatedEnergyOfLoadSum;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能当日充电")
	private BigDecimal batteryDailyChargeEnergyParallel;

	@ApiModelProperty(value = "今日输入能量")
	private BigDecimal todayImportEnergy ;
	/**  */
	@ApiModelProperty(value = "今日输出能量")
	private BigDecimal todayExportEnergy ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedDischargeEnergyParallel;

	@ApiModelProperty(value = "")
	private BigDecimal batteryDailyDischargeEnergyParallel;

	@ApiModelProperty(value="PV并机所有能量")
	private BigDecimal pvlAccumulatedEnergySum;

	@ApiModelProperty(value = "每日负载总和")
	private BigDecimal dailyEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totallyInputDcWattSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattSumOfBackup;

	@ApiModelProperty(value = "每日支持能量总和")
	private BigDecimal dailySupportEnergySumToBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfGridSum;

	@ApiModelProperty(value = "储能功率总和")
	private BigDecimal batteryPowerSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedChargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	// 1306
	private BigDecimal accumulatedEnergyOfPositive ;

	// 2025.4.15 begin
	// 2200
	private BigDecimal batterySoc2;
	// 2209
	private BigDecimal batteryPower2;
	// 220B
	private BigDecimal batteryTodayChargeEnergy2;
	// 220F
	private BigDecimal batteryTodayDischargeEnergy2;
	// 220D
	private BigDecimal batteryAccumulatedChargeEnergy2;
	// 2211
	private BigDecimal batteryAccumulatedDischargeEnergy2;
	// 2216
	private BigDecimal batteryAverageOfAllModulesSoh2;
	// 2214
	private BigDecimal batteryMaximumCyclesTimes2;
	// 2206
	private BigDecimal batteryVoltage2;
	// 2207
	private BigDecimal batteryCurrent2;
	// 221C
	private BigDecimal batteryMaximumCellTemperature2;
	// 221E
	private BigDecimal batteryMinimumCellTemperature2;
	// 221B
	private BigDecimal batteryAverageCellTemperature2;
	// 2217
	private BigDecimal batteryMaximumCellVoltage2;
	// 2219
	private BigDecimal batteryMinimumCellVoltage2;
	// 2025.4.15 end
	/**是否存在用户类告警(0/1:不存在/存在)*/
	/*@ApiModelProperty("是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm;

	*//**是否存在代理类告警(0/1:不存在/存在)*//*
	@ApiModelProperty("是否存在代理类告警(0/1:不存在/存在)")
	private Integer existAgentTypeAlarm;

	*//**第2路电池是否存在用户类告警(0/1:不存在/存在)*//*
	@ApiModelProperty("第2路电池是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm2;

	*//**第2路电池是否存在代理类告警(0/1:不存在/存在)*//*
	@ApiModelProperty("第2路电池是否存在代理类告警(0/1:不存在/存在)")
	private Integer existAgentTypeAlarm2;*/
}

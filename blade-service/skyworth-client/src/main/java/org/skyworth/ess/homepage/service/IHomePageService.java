package org.skyworth.ess.homepage.service;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.homepage.vo.BatteryHomePageVO;
import org.skyworth.ess.homepage.vo.InverterHomePageVO;

import java.util.List;

/**
 * 首页
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-16 16:16
 **/
public interface IHomePageService {
	/**
	 * 首页智能能量变换器和负载状态曲线
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	List<JSONObject> inverterAndLoadSummaryStateCurve();

	/**
	 * 首页智能能量变换器数据统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	InverterHomePageVO inverterPerformance();

	/**
	 * 首页储能数据统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	BatteryHomePageVO batteryPerformance();

	/**
	 * 首页储能状态统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	JSONObject plantStatusSummary();
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.excel.GatewayPlantExcel;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantCurrentStatusVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantOverviewVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantReportQueryVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 良信网关站点关系表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface IGatewayPlantService extends BaseService<GatewayPlantEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param gatewayPlant
	 * @return
	 */
	IPage<GatewayPlantVO> selectGatewayPlantPage(IPage<GatewayPlantVO> page, GatewayPlantVO gatewayPlant);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GatewayPlantExcel> exportGatewayPlant(Wrapper<GatewayPlantEntity> queryWrapper);


	boolean batchUpdateHeartTimeByGatewayUniqueNumber(List<GatewayPlantEntity> updateList);

	boolean batchUpdateStatusByGatewayUniqueNumber(List<GatewayPlantEntity> updateList);

	Map<String, Object> getAllGuardianGatePositionSetup(Long plantId, String serialNumber);

	GatewayPlantOverviewVO getOverview(@Valid GatewayPlantVO gatewayPlantVO);

	IPage<GatewayPlantVO> page(Query query, GatewayPlantVO gatewayPlantVO);

	GatewayPlantVO detail(GatewayPlantEntity gatewayPlant);

	GatewayPlantCurrentStatusVO detailCurrentStatus(GatewayPlantEntity gatewayPlantEntity);

	Map<String, List<Object>> selectStatusReport(GatewayPlantReportQueryVO gatewayPlantReportQueryVO);

	void selectStatusReportExport(GatewayPlantReportQueryVO gatewayPlantReportQueryVO, HttpServletResponse response);

    void batchDeleteLogicByPlantId(List<Long> longList, String account);

	List<GatewayPlantEntity> queryOwnerData(Long userId);

	int updateDataByCondition(GatewayPlantEntity updateGuardianPlantEntity);
}

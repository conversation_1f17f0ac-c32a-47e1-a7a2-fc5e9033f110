/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmoperationrecord.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.vo.AlarmLogOperationRecordVO;
import java.util.Objects;

/**
 * 告警日志操作记录表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public class AlarmLogOperationRecordWrapper extends BaseEntityWrapper<AlarmLogOperationRecordEntity, AlarmLogOperationRecordVO>  {

	public static AlarmLogOperationRecordWrapper build() {
		return new AlarmLogOperationRecordWrapper();
 	}

	@Override
	public AlarmLogOperationRecordVO entityVO(AlarmLogOperationRecordEntity alarmLogOperationRecord) {
		AlarmLogOperationRecordVO alarmLogOperationRecordVO = Objects.requireNonNull(BeanUtil.copy(alarmLogOperationRecord, AlarmLogOperationRecordVO.class));

		//User createUser = UserCache.getUser(alarmLogOperationRecord.getCreateUser());
		//User updateUser = UserCache.getUser(alarmLogOperationRecord.getUpdateUser());
		//alarmLogOperationRecordVO.setCreateUserName(createUser.getName());
		//alarmLogOperationRecordVO.setUpdateUserName(updateUser.getName());

		return alarmLogOperationRecordVO;
	}


}

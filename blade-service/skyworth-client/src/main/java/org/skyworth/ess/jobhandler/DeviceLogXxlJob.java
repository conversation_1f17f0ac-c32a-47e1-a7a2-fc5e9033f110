package org.skyworth.ess.jobhandler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.app.service.impl.BusinessServiceUtil;
import org.skyworth.ess.app.service.setup.AppSetModeServiceImpl;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.BatteryStatusExtendEntity;
import org.skyworth.ess.battery.service.IBatteryCurrentStatusService;
import org.skyworth.ess.battery.service.IBatteryEverydayTotalService;
import org.skyworth.ess.battery.service.IBatteryStatusExtendService;
import org.skyworth.ess.constant.SourceEnum;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.service.IAdvancedSettingsOperationLogService;
import org.skyworth.ess.plant.entity.PlantEntity;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.constant.DictConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class DeviceLogXxlJob {

	private IDeviceLog21Service deviceLog21Service;

	private IDevice21Service device21Service;

	private IDeviceLog23Service deviceLog23Service;

	private IDevice23Service device23Service;

	private IBatteryCurrentStatusService batteryCurrentStatusService;

	private IDeviceCurrentStatusService deviceCurrentStatusService;

	private DeviceLog22ByDorisService deviceLog22ByDorisService;

	private IBatteryEverydayTotalService batteryEverydayTotalService;

	private IDeviceEverydayTotalService deviceEverydayTotalService;

	private static final long INVERT_TIME = 60 * 2000;

	private IWifiStickPlantService wifiStickPlantService;

	private IPlantService plantService;

	private IBatteryCurrentStatusService currentStatusService;

	private IDeviceIssueBiz deviceIssueBiz;

	private IDeviceLog24Service deviceLog24Service;

	private IDevice24Service device24Service;

	private IDeviceCustomModeService deviceCustomModeService;

	private AppSetModeServiceImpl appSetModeServiceImpl;

	private IBatteryStatusExtendService batteryStatusExtendServiceImpl;

	private BusinessServiceUtil businessServiceUtil;

	/**
	 * wifi棒心跳数据
	 */
	@XxlJob("heartBeatWifi")
	public ReturnT<String> heartBeatWifi(String param) {
		//查出在线设备
		log.info("<<<开始执行心跳任务>>>");
		LambdaQueryWrapper<WifiStickPlantEntity> eq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getWifiStickStatus, "1");
		List<WifiStickPlantEntity> stickPlantEntityList = wifiStickPlantService.list(eq);
		if (!stickPlantEntityList.isEmpty()) {
			stickPlantEntityList.forEach(data -> {
				String str = data.getHeartBeatTime();
				try {
					long heartBeatTime = TimeUtils.getDateLongTime(str);
					long nowTime = System.currentTimeMillis();
					if (nowTime - heartBeatTime > INVERT_TIME) { //超过俩分钟设置为离线
						data.setWifiStickStatus(BizConstant.CLIENT_WIFI_STICK_STATUS_OFFLINE);
						wifiStickPlantService.updateById(data);
						JSONObject jsonObject = new JSONObject();
						jsonObject.put("topic", Constants.Device_Disconnected);
						jsonObject.put("deviceSn", data.getDeviceSerialNumber());
						jsonObject.put("cont", "设备已断开连接");
						deviceIssueBiz.dataIssueToDevice(jsonObject);//发送mqtt给app,设备已断线
						LambdaQueryWrapper<Device21Entity> device21 = Wrappers.<Device21Entity>query().lambda()
							.eq(Device21Entity::getDeviceSerialNumber, data.getDeviceSerialNumber()).eq(Device21Entity::getPlantId, data.getPlantId());
						List<Device21Entity> device21EntityList = device21Service.list(device21);
						if (device21EntityList != null && !device21EntityList.isEmpty()) {
							Device21Entity device211 = device21EntityList.get(0);
							device211.setDeviceStatus("0");
							device21Service.updateById(device211); //更新智能能量变换器状态
						}

						LambdaQueryWrapper<WifiStickPlantEntity> wifiStickPlantEntityEq=Wrappers.<WifiStickPlantEntity>query().lambda().
							eq(WifiStickPlantEntity::getDeviceSerialNumber,data.getDeviceSerialNumber()).
							eq(WifiStickPlantEntity::getPlantId,data.getPlantId()).
							eq(WifiStickPlantEntity::getParallelDeviceType,BizConstant.CLIENT_INVERTER_MASTER_MODEL);
						List<WifiStickPlantEntity> wifiStickPlantEntityList=wifiStickPlantService.list(wifiStickPlantEntityEq);
						if(ValidationUtil.isNotEmpty(wifiStickPlantEntityList)&&!wifiStickPlantEntityList.isEmpty()){
							LambdaQueryWrapper<PlantEntity> plant = Wrappers.<PlantEntity>query().lambda()
								.eq(PlantEntity::getId, data.getPlantId());  //查出在线设备
							List<PlantEntity> plantEntities = plantService.list(plant);
							if (plantEntities != null && !plantEntities.isEmpty()) {
								PlantEntity plantEntity = plantEntities.get(0);
								plantEntity.setPlantStatus("0");
								plantService.updateById(plantEntity); //更新站点状态
							}
						}

						LambdaQueryWrapper<BatteryCurrentStatusEntity> eq2 = Wrappers.<BatteryCurrentStatusEntity>query().lambda()
							.eq(BatteryCurrentStatusEntity::getDeviceSerialNumber, data.getDeviceSerialNumber()).eq(BatteryCurrentStatusEntity::getPlantId, data.getPlantId());
						List<BatteryCurrentStatusEntity> batteryCurrentStatusEntities = currentStatusService.list(eq2);
						if (ValidationUtil.isNotEmpty(batteryCurrentStatusEntities) && !batteryCurrentStatusEntities.isEmpty()) {
							batteryCurrentStatusEntities.stream().parallel().forEach(v -> {
								v.setBatteryStatus("5"); //设置为离线
								currentStatusService.updateById(v);
								batteryStatusExtendServiceImpl.lambdaUpdate().eq(BatteryStatusExtendEntity::getPlantId, v.getPlantId())
									.set(BatteryStatusExtendEntity::getBatteryStatus, "5")
									.update();
							});
						}
					}
				} catch (ParseException e) {
					log.error("转换心跳时间失败{}", e.getMessage());
				}
			});
		}
		return ReturnT.SUCCESS;
	}


	/**
	 * 同步日志表最新数据到设备表 2.1  2.3  2.4
	 */
	@XxlJob("deviceLog")
	public ReturnT<String> deviceLogHandler(String param) {
//        syncDevice21(); //同步2.1设备数据
//        syncDevice23(); //同步2.3设备数据
//		  syncDevice24(); //同步2.4设备数据
		syncCurrentStatus(); //同步储能,智能能量变换器当前状态数据
		return ReturnT.SUCCESS;
	}

	/**
	 * 每日统计
	 */
	@XxlJob("dailyStatistics")
	public ReturnT<String> dailyStatistics(String param) throws Exception {
		LocalDate localDate = LocalDate.now().minusDays(1);
		log.info("dailyStatistics param : {}", param);
		if(StringUtils.isNotEmpty(param)) {
			localDate = LocalDate.parse(param);
		}
		String beginTime = localDate + " 00:00:00";
		String endTime = localDate + " 23:59:59";
		//从2.2获取最新的数据
		List<DeviceLog22> deviceLog22List = deviceLog22ByDorisService.selectDailyData(beginTime, endTime);
		for (DeviceLog22 data : deviceLog22List) {
			LambdaQueryWrapper<PlantEntity> eq = Wrappers.<PlantEntity>query().lambda()
				.eq(PlantEntity::getId, data.getPlantId()).eq(PlantEntity::getIsDeleted, 0);
			List<PlantEntity> plantEntityList=plantService.list(eq);
			if(ValidationUtil.isEmpty(plantEntityList)||plantEntityList.isEmpty()){
				continue;
			}
			PlantEntity plantEntity = plantEntityList.get(0);
			// 处理并机数据
			this.dealParallelModeData(data, plantEntity, localDate, beginTime, endTime);

		}

		return ReturnT.SUCCESS;
	}

	// 定时根据天气设置模式
	@XxlJob("inverterModeSet")
	public ReturnT<String>  inverterModeSet(String param) {
		DeviceCustomModeEntity entity = new DeviceCustomModeEntity();
		entity.setHybridWorkMode(DictConstant.INVERTER_MODE_AI);
		List<DeviceCustomModeEntity> modelist = deviceCustomModeService.list(Condition.getQueryWrapper(entity));
		if(CollectionUtil.isEmpty(modelist)) {
			log.info("inverterModeSet ai is null");
			return ReturnT.SUCCESS;
		}
		for(DeviceCustomModeEntity deviceCustomModeEntity : modelist) {
			DeviceCustomModeEntity updateCustomModeEntity = new DeviceCustomModeEntity();
			updateCustomModeEntity.setPlantId(deviceCustomModeEntity.getPlantId());
			updateCustomModeEntity.setDeviceSerialNumber(deviceCustomModeEntity.getDeviceSerialNumber());
			updateCustomModeEntity.setHybridWorkMode(deviceCustomModeEntity.getHybridWorkMode());
			try {
			R r1 = appSetModeServiceImpl.inverterModeIssue(updateCustomModeEntity);
			appSetModeServiceImpl.saveLog(deviceCustomModeEntity,r1);
			if(r1.getCode()== I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100012.getCode()){
				log.info("inverterModeSet ai is error : {} ",r1.getMsg());
			}
			}catch (Exception e) {
				log.info("inverterModeSet saveLog error deviceSerialNumber : {}, {} ",deviceCustomModeEntity.getDeviceSerialNumber(),e.getMessage());
			}
		}
		log.info("inverterModeSet ai end");
		return ReturnT.SUCCESS;
	}


	private void dealParallelModeData(DeviceLog22 data, PlantEntity plantEntity, LocalDate localDate, String beginTime, String endTime) {
		// 如果最新的数据是并机模式，那么取最新的数据作为当天统计数据
		if (BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES.equals(data.getIsParallelMode())) {
			syncEveryDayBatter(data, localDate, plantEntity);  //同步每日储能统计信息
			syncEveryDayInverter(data, localDate, plantEntity);  //同步每日智能能量变换器统计信息
		}else {
			// 如果最新的数据不是并机模式，那么查询看当天是否有并机数据：
			// 1、如果有没有，则不处理（最后一条作为当天的统计数据）
			// 2、如果当天有并机数据，则取并机最大的， 和非并机的数据合并作为当天的统计数据
			QueryDeviceLog22Condition queryDeviceLog22Condition = new QueryDeviceLog22Condition();
			queryDeviceLog22Condition.setPlantId(data.getPlantId());
			queryDeviceLog22Condition.setDeviceSerialNumber(data.getDeviceSerialNumber());
			queryDeviceLog22Condition.setBeginTime(beginTime);
			queryDeviceLog22Condition.setEndTime(endTime);
			queryDeviceLog22Condition.setIsParallelMode(BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES);
			DeviceLog22 deviceLog22Db = deviceLog22ByDorisService.queryLatestData(queryDeviceLog22Condition);
			if(ValidationUtil.isEmpty(deviceLog22Db)) {
				log.info("没有并机数据，直接处理");
			} else {
				log.info("有并机数据，将并机数据 合并到 最后一条非并机数据上");
				data.setPvlDailyGeneratingEnergySum(deviceLog22Db.getPvlDailyGeneratingEnergySum());
				data.setBatteryDailyChargeEnergyParallel(deviceLog22Db.getBatteryDailyChargeEnergyParallel());
				data.setDailyEnergyOfLoadSum(deviceLog22Db.getDailyEnergyOfLoadSum());
				data.setDailySupportEnergySumToBackup(deviceLog22Db.getDailySupportEnergySumToBackup());
				data.setBatteryDailyDischargeEnergyParallel(deviceLog22Db.getBatteryDailyDischargeEnergyParallel());
				data.setGeneratorTodayEnergySum(deviceLog22Db.getGeneratorTodayEnergySum());
			}
			syncEveryDayBatter(data, localDate, plantEntity);  //同步每日储能统计信息
			syncEveryDayInverter(data, localDate, plantEntity);  //同步每日智能能量变换器统计信息
		}
	}

	/**
	 * 同步储能每天统计信息
	 */
	private void syncEveryDayBatter(DeviceLog22 data, LocalDate localDate,PlantEntity plantEntity) {
		Date date;
		try {
			date = TimeUtils.getCurrentTimeForDate(localDate.toString());
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		LambdaQueryWrapper<BatteryEverydayTotalEntity> eq = Wrappers.<BatteryEverydayTotalEntity>query().lambda()
			.eq(BatteryEverydayTotalEntity::getPlantId, data.getPlantId()).eq(BatteryEverydayTotalEntity::getDeviceSerialNumber, data.getDeviceSerialNumber()).eq(BatteryEverydayTotalEntity::getTotalDate, date);
		List<BatteryEverydayTotalEntity> batteryEverydayTotalEntityList = batteryEverydayTotalService.list(eq);
		if (batteryEverydayTotalEntityList.isEmpty()) {
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = new BatteryEverydayTotalEntity();
			BeanUtils.copyProperties(data, batteryEverydayTotalEntity);
			batteryEverydayTotalEntity.setId(null);
			batteryEverydayTotalEntity.setCreateTime(null);
			batteryEverydayTotalEntity.setUpdateTime(null);
			batteryEverydayTotalEntity.setTotalDate(date);
			batteryEverydayTotalEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			// 计算app饼图
			this.calAppPieReport(data, batteryEverydayTotalEntity,plantEntity);
			batteryEverydayTotalService.save(batteryEverydayTotalEntity);
		} else {
			BatteryEverydayTotalEntity batteryEverydayTotalEntity = batteryEverydayTotalEntityList.get(0);
			long id = batteryEverydayTotalEntity.getId();
			Date createTime = batteryEverydayTotalEntity.getCreateTime();
			BeanUtils.copyProperties(data, batteryEverydayTotalEntity);
			batteryEverydayTotalEntity.setId(id);
			batteryEverydayTotalEntity.setCreateTime(createTime);
			batteryEverydayTotalEntity.setTotalDate(date);
			batteryEverydayTotalEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			// 计算app饼图
			this.calAppPieReport(data, batteryEverydayTotalEntity,plantEntity);
			batteryEverydayTotalService.updateById(batteryEverydayTotalEntity);
		}
	}

	private void calAppPieReport(DeviceLog22 data, BatteryEverydayTotalEntity batteryEverydayTotalEntity,PlantEntity plantEntity) {
		// 如果是并机状态，则需要计算并机数据，同时计算非并机数据，不管并机非并机都计算
//		if (BizConstant.CLIENT_PLANT_PARALLEL_MODE_YES.equals(data.getIsParallelMode())) {
			// 原始数据为 kwh，转换成 wh
			BigDecimal todayEnergyParallel = data.getPvlDailyGeneratingEnergySum() == null ? BigDecimal.ZERO : data.getPvlDailyGeneratingEnergySum().multiply(BizConstant.THOUSAND);
			BigDecimal todayExportEnergy = data.getTodayExportEnergy() == null ? BigDecimal.ZERO : data.getTodayExportEnergy();
			BigDecimal dailyEnergyOfLoadSumParallel = data.getDailyEnergyOfLoadSum() == null ? BigDecimal.ZERO : data.getDailyEnergyOfLoadSum().multiply(BizConstant.THOUSAND);
			BigDecimal dailySupportEnergySumToBackupParallel = data.getDailySupportEnergySumToBackup() == null ? BigDecimal.ZERO : data.getDailySupportEnergySumToBackup().multiply(BizConstant.THOUSAND);
			BigDecimal batteryDailyChargeEnergyParallel = data.getBatteryDailyChargeEnergyParallel() == null ? BigDecimal.ZERO : data.getBatteryDailyChargeEnergyParallel().multiply(BizConstant.THOUSAND);
			BigDecimal todayImportEnergy = data.getTodayImportEnergy() == null ? BigDecimal.ZERO : data.getTodayImportEnergy();
			if (todayEnergyParallel.compareTo(todayExportEnergy) >= 0) {
				BigDecimal selfConsumed = todayEnergyParallel.subtract(todayExportEnergy);
				batteryEverydayTotalEntity.setParallelSelfConsumed(selfConsumed.setScale(3, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setParallelFedToGrid(todayExportEnergy.setScale(3, RoundingMode.HALF_UP));
			} else {
				batteryEverydayTotalEntity.setParallelSelfConsumed(todayEnergyParallel.setScale(3, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setParallelFedToGrid(BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP));
			}
			BigDecimal addResult = dailyEnergyOfLoadSumParallel.add(dailySupportEnergySumToBackupParallel).add(batteryDailyChargeEnergyParallel);
			BigDecimal selfSufficiency = addResult.subtract(todayImportEnergy);
			if (selfSufficiency.compareTo(BigDecimal.ZERO) < 0) {
				batteryEverydayTotalEntity.setParallelSelfSufficiency(todayEnergyParallel.setScale(3, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setParallelFromGrid(todayImportEnergy.setScale(3, RoundingMode.HALF_UP));
			} else {
				batteryEverydayTotalEntity.setParallelSelfSufficiency(selfSufficiency);
				batteryEverydayTotalEntity.setParallelFromGrid(todayImportEnergy.setScale(3, RoundingMode.HALF_UP));
			}
//		}
			BigDecimal todayEnergy = data.getTodayEnergy() == null ? BigDecimal.ZERO : data.getTodayEnergy();
//			BigDecimal todayExportEnergy = data.getTodayExportEnergy() == null ? BigDecimal.ZERO : data.getTodayExportEnergy();
			BigDecimal todayLoadEnergy = data.getTodayLoadEnergy() == null ? BigDecimal.ZERO : data.getTodayLoadEnergy();
			BigDecimal dailyEnergyToEps = data.getDailyEnergyToEps() == null ? BigDecimal.ZERO : data.getDailyEnergyToEps();
			BigDecimal batteryDailyChargeEnergy = data.getBatteryDailyChargeEnergy() == null ? BigDecimal.ZERO : data.getBatteryDailyChargeEnergy();
//			BigDecimal todayImportEnergy = data.getTodayImportEnergy() == null ? BigDecimal.ZERO : data.getTodayImportEnergy();
			if (todayEnergy.compareTo(todayExportEnergy) >= 0) {
				BigDecimal selfConsumed = todayEnergy.subtract(todayExportEnergy);
				batteryEverydayTotalEntity.setSelfConsumed(selfConsumed.setScale(2, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setFedToGrid(todayExportEnergy.setScale(2, RoundingMode.HALF_UP));
			} else {
				batteryEverydayTotalEntity.setSelfConsumed(todayEnergy.setScale(2, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setFedToGrid(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			}
			BigDecimal addResult2 = todayLoadEnergy.add(dailyEnergyToEps).add(batteryDailyChargeEnergy);
			BigDecimal selfSufficiency2 = addResult2.subtract(todayImportEnergy);
			if (selfSufficiency2.compareTo(BigDecimal.ZERO) < 0) {
				batteryEverydayTotalEntity.setSelfSufficiency(todayEnergy.setScale(2, RoundingMode.HALF_UP));
				batteryEverydayTotalEntity.setFromGrid(todayImportEnergy.setScale(2, RoundingMode.HALF_UP));
			} else {
				batteryEverydayTotalEntity.setSelfSufficiency(selfSufficiency2);
				batteryEverydayTotalEntity.setFromGrid(todayImportEnergy.setScale(2, RoundingMode.HALF_UP));
			}


	}

	/**
	 * 同步每天统计信息
	 */
	private void syncEveryDayInverter(DeviceLog22 data, LocalDate localDate,PlantEntity plantEntity) {
		LambdaQueryWrapper<DeviceEverydayTotalEntity> eq = Wrappers.<DeviceEverydayTotalEntity>query().lambda()
			.eq(DeviceEverydayTotalEntity::getPlantId, data.getPlantId()).eq(DeviceEverydayTotalEntity::getDeviceSerialNumber, data.getDeviceSerialNumber()).eq(DeviceEverydayTotalEntity::getTotalDate, localDate.toString());
		List<DeviceEverydayTotalEntity> deviceEverydayTotalEntityList = deviceEverydayTotalService.list(eq);
		if (deviceEverydayTotalEntityList.isEmpty()) {
			DeviceEverydayTotalEntity deviceEverydayTotalEntity = new DeviceEverydayTotalEntity();
			BeanUtils.copyProperties(data, deviceEverydayTotalEntity);
			deviceEverydayTotalEntity.setId(null);
			deviceEverydayTotalEntity.setCreateTime(null);
			deviceEverydayTotalEntity.setUpdateTime(null);
			deviceEverydayTotalEntity.setTotalDate(localDate.toString());
			deviceEverydayTotalEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			deviceEverydayTotalService.save(deviceEverydayTotalEntity);
		} else {
			DeviceEverydayTotalEntity deviceEverydayTotalEntity = deviceEverydayTotalEntityList.get(0);
			long id = deviceEverydayTotalEntity.getId();
			Date createTime = deviceEverydayTotalEntity.getCreateTime();
			BeanUtils.copyProperties(data, deviceEverydayTotalEntity);
			deviceEverydayTotalEntity.setId(id);
			deviceEverydayTotalEntity.setCreateTime(createTime);
			deviceEverydayTotalEntity.setTotalDate(localDate.toString());
			deviceEverydayTotalEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			deviceEverydayTotalService.updateById(deviceEverydayTotalEntity);
		}
	}

	/**
	 * 同步2.1日志表数据到设备表2。1
	 */
	private void syncDevice21() {
		List<DeviceLog21> deviceLog21List = deviceLog21Service.selectDataByLatestTime();
		if (!deviceLog21List.isEmpty()) {
			deviceLog21List.forEach(data -> {
				long plantId = data.getPlantId();
				String deviceSn = data.getDeviceSerialNumber();
				Map<String, Object> device21 = new HashMap<>();
				device21.put("plantId", plantId);
				device21.put("deviceSerialNumber", deviceSn);
				QueryWrapper<Device21Entity> queryWrapper = Condition.getQueryWrapper(device21, Device21Entity.class);
				queryWrapper.lambda().eq(Device21Entity::getPlantId, plantId);
				queryWrapper.lambda().eq(Device21Entity::getDeviceSerialNumber, deviceSn);
				List<Device21Entity> device21EntityList = device21Service.getEntity(queryWrapper);//查看数据是否存在

				if (device21EntityList.isEmpty()) {
					Device21Entity device21Entity = new Device21Entity();
					BeanUtils.copyProperties(data, device21Entity);
					device21Entity.setId(null);
					device21Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device21Service.save(device21Entity);
				} else {
					Device21Entity device21Entity = device21EntityList.get(0);
					long id = device21Entity.getId();
					BeanUtils.copyProperties(data, device21Entity);
					device21Entity.setId(id);
					device21Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device21Service.updateById(device21Entity);
				}
				data.setSynchStatus("Y");
				deviceLog21Service.updateById(data);  //更新同步状态
			});
		}
	}

	/**
	 * 同步2.3日志表数据到设备表2.3
	 */
	private void syncDevice23() {
		List<DeviceLog23> deviceLog23List = deviceLog23Service.selectDataByLatestTime();
		if (!deviceLog23List.isEmpty()) {
			deviceLog23List.forEach(data -> {
				long plantId = data.getPlantId();
				String deviceSn = data.getDeviceSerialNumber();
				Map<String, Object> device23 = new HashMap<>();
				device23.put("plantId", plantId);
				device23.put("deviceSerialNumber", deviceSn);
				QueryWrapper<Device23Entity> queryWrapper = Condition.getQueryWrapper(device23, Device23Entity.class);
				queryWrapper.lambda().eq(Device23Entity::getPlantId, plantId);
				queryWrapper.lambda().eq(Device23Entity::getDeviceSerialNumber, deviceSn);
				List<Device23Entity> device23EntityList = device23Service.getEntity(queryWrapper); //查看数据是否存在

				if (device23EntityList.isEmpty()) {
					Device23Entity device23Entity = new Device23Entity();
					BeanUtils.copyProperties(data, device23Entity);
					device23Entity.setId(null);
					device23Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device23Service.save(device23Entity);
				} else {
					Device23Entity device23Entity = device23EntityList.get(0);
					long id = device23Entity.getId();
					BeanUtils.copyProperties(data, device23Entity);
					device23Entity.setId(id);
					device23Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device23Service.updateById(device23Entity);
				}
				data.setSynchStatus("Y");
				deviceLog23Service.updateById(data);  //更新同步状态
			});
		}
	}


	/**
	 * 同步2.4日志表数据到设备表2.4
	 */
	private void syncDevice24() {
		List<DeviceLog24Entity> deviceLog24List = deviceLog24Service.selectDataByLatestTime();
		if (!deviceLog24List.isEmpty()) {
			deviceLog24List.forEach(data -> {
				long plantId = data.getPlantId();
				String deviceSn = data.getDeviceSerialNumber();
				LambdaQueryWrapper<Device24Entity> eq = Wrappers.<Device24Entity>query().lambda()
					.eq(Device24Entity::getDeviceSerialNumber, deviceSn).eq(Device24Entity::getPlantId, plantId);
				List<Device24Entity> device24EntityList = device24Service.list(eq);
				if (device24EntityList.isEmpty()) {
					Device24Entity device24Entity = new Device24Entity();
					BeanUtils.copyProperties(data, device24Entity);
					device24Entity.setId(null);
					device24Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device24Service.save(device24Entity);
				} else {
					Device24Entity device24Entity = device24EntityList.get(0);
					long id = device24Entity.getId();
					BeanUtils.copyProperties(data, device24Entity);
					device24Entity.setId(id);
					device24Entity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
					device24Service.updateById(device24Entity);
				}
				data.setSynchStatus("Y");
				deviceLog24Service.updateById(data);  //更新同步状态
			});
		}
	}

	/**
	 * 同步状态信息
	 */
	private void syncCurrentStatus() {
		//从2.2获取最新的数据
		List<DeviceLog22> deviceLog22List = deviceLog22ByDorisService.selectDataByLatestTime();
		List<BatteryCurrentStatusEntity> batteryCurrentStatusEntityList = batteryCurrentStatusService.list();
		List<DeviceCurrentStatusEntity> deviceCurrentStatusEntityList = deviceCurrentStatusService.list();
		LambdaQueryWrapper<WifiStickPlantEntity> eq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getIsDeleted, 0);
		List<WifiStickPlantEntity> stickPlantEntityList = wifiStickPlantService.list(eq);
		deviceLog22List.forEach(data -> {
			syncCurrentBattery(data, batteryCurrentStatusEntityList, stickPlantEntityList);
			syncInverterStatus(data, deviceCurrentStatusEntityList, stickPlantEntityList);
		});
	}

	/**
	 * 获取所有站点id查询站点并机模式
	 */
	@NotNull
	private Map<Long, String> getLongStringMap(List<DeviceLog22> deviceLog22List) {
		List<Long> plantIdList = deviceLog22List.stream()
			.map(DeviceLog22::getPlantId)
			.distinct()
			.collect(Collectors.toList());
		LambdaQueryWrapper<PlantEntity> plantEq = Wrappers.<PlantEntity>query().lambda()
			.eq(PlantEntity::getIsDeleted, 0).in(PlantEntity::getId,plantIdList);
		List<PlantEntity> plantEntities=plantService.list(plantEq);
        return plantEntities.stream()
			.collect(Collectors.toMap(PlantEntity::getId, PlantEntity::getIsParallelMode));
	}

	/**
	 * 同步当前储能状态信息
	 */
	private void syncCurrentBattery(DeviceLog22 data, List<BatteryCurrentStatusEntity> batteryCurrentStatusEntityList, List<WifiStickPlantEntity> stickPlantEntityList) {
//        LambdaQueryWrapper<BatteryCurrentStatusEntity> eq = Wrappers.<BatteryCurrentStatusEntity>query().lambda()
//                .eq(BatteryCurrentStatusEntity::getPlantId, data.getPlantId()).eq(BatteryCurrentStatusEntity::getDeviceSerialNumber, data.getDeviceSerialNumber());
//        List<BatteryCurrentStatusEntity> batteryCurrentStatusEntityList=batteryCurrentStatusService.list(eq);
		List<BatteryCurrentStatusEntity> collect = batteryCurrentStatusEntityList.stream().filter(b -> Func.equals(b.getPlantId(), data.getPlantId()))
			.filter(b -> Func.equals(b.getDeviceSerialNumber(), data.getDeviceSerialNumber()))
			.collect(Collectors.toList());
		List<WifiStickPlantEntity> deviceCollect = stickPlantEntityList.stream().filter(b -> Func.equals(b.getPlantId(), data.getPlantId()))
			.filter(b -> Func.equals(b.getDeviceSerialNumber(), data.getDeviceSerialNumber()))
			.collect(Collectors.toList());

		if (!deviceCollect.isEmpty()) {
			if (collect.isEmpty()) {
				BatteryCurrentStatusEntity batteryCurrentStatusEntity = new BatteryCurrentStatusEntity();
				BeanUtils.copyProperties(data, batteryCurrentStatusEntity);
				batteryCurrentStatusEntity.setId(null);
				batteryCurrentStatusEntity.setCreateTime(null);
				batteryCurrentStatusEntity.setUpdateTime(null);
				batteryCurrentStatusEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				batteryCurrentStatusService.save(batteryCurrentStatusEntity);
				this.saveBatteryStatusExtend(CommonConstant.OPERATE_TYPE_INSERT,batteryCurrentStatusEntity);
			} else {//更新
				BatteryCurrentStatusEntity batteryCurrentStatusEntity = collect.get(0);
				long id = batteryCurrentStatusEntity.getId();
				Date createTime = batteryCurrentStatusEntity.getCreateTime();
				// 此处不根据doris数据来更新电池状态，由设备告警的topic来更新，避免两边冲突
				data.setBatteryStatus(batteryCurrentStatusEntity.getBatteryStatus());
				BeanUtils.copyProperties(data, batteryCurrentStatusEntity);
				batteryCurrentStatusEntity.setId(id);
				batteryCurrentStatusEntity.setCreateTime(createTime);
				batteryCurrentStatusEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				batteryCurrentStatusService.updateById(batteryCurrentStatusEntity);
				this.saveBatteryStatusExtend(CommonConstant.OPERATE_TYPE_UPDATE,batteryCurrentStatusEntity);
			}
		}

	}

	private void saveBatteryStatusExtend(String operate,BatteryCurrentStatusEntity batteryCurrentStatusEntity) {
		log.info("saveBatteryStatusExtend -> :  {}",operate);
		if(CommonConstant.OPERATE_TYPE_INSERT.equals(operate)) {
			List<BatteryStatusExtendEntity> insertList = new ArrayList<>();
			BatteryStatusExtendEntity insertExtendEntity = new BatteryStatusExtendEntity();
			insertExtendEntity.setPlantId(batteryCurrentStatusEntity.getPlantId());
			insertExtendEntity.setBatteryEnergyStorageNumber(1);
			insertExtendEntity.setBatteryStatus(batteryCurrentStatusEntity.getBatteryStatus());
			insertExtendEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
			insertList.add(insertExtendEntity);
			// 如果是30kw的，需要写入两条
			if(businessServiceUtil.needEnergyStorage2ByDeviceModel(batteryCurrentStatusEntity.getDeviceSerialNumber())) {
				BatteryStatusExtendEntity insertExtendEntityTwo = new BatteryStatusExtendEntity();
				insertExtendEntityTwo.setPlantId(batteryCurrentStatusEntity.getPlantId());
				insertExtendEntityTwo.setBatteryEnergyStorageNumber(2);
				insertExtendEntityTwo.setBatteryStatus(batteryCurrentStatusEntity.getBatteryStatus());
				insertExtendEntityTwo.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				insertList.add(insertExtendEntityTwo);
			}
			batteryStatusExtendServiceImpl.saveBatch(insertList);
		} else {
			// 5分钟不更新状态，由设备告警来更新，设备充电、放电、告警都会实时推送消息到 告警topic
//			batteryStatusExtendServiceImpl.lambdaUpdate().eq(BatteryStatusExtendEntity::getPlantId, batteryCurrentStatusEntity.getPlantId())
//				.ne(BatteryStatusExtendEntity::getBatteryStatus,"4")
//				.set(BatteryStatusExtendEntity::getBatteryStatus, batteryCurrentStatusEntity.getBatteryStatus())
//				.update();
		}
	}

	/**
	 * 同步智能能量变换器当前状态信息
	 */
	private void syncInverterStatus(DeviceLog22 data, List<DeviceCurrentStatusEntity> deviceCurrentStatusEntityList, List<WifiStickPlantEntity> stickPlantEntityList) {
//        LambdaQueryWrapper<DeviceCurrentStatusEntity> eq = Wrappers.<DeviceCurrentStatusEntity>query().lambda()
//                .eq(DeviceCurrentStatusEntity::getPlantId, data.getPlantId()).eq(DeviceCurrentStatusEntity::getDeviceSerialNumber, data.getDeviceSerialNumber());
//        List<DeviceCurrentStatusEntity> deviceCurrentStatusEntityList=deviceCurrentStatusService.list(eq);
		List<DeviceCurrentStatusEntity> collect = deviceCurrentStatusEntityList.stream().filter(b -> Func.equals(b.getPlantId(), data.getPlantId()))
			.filter(b -> Func.equals(b.getDeviceSerialNumber(), data.getDeviceSerialNumber()))
			.collect(Collectors.toList());
		List<WifiStickPlantEntity> deviceCollect = stickPlantEntityList.stream().filter(b -> Func.equals(b.getPlantId(), data.getPlantId()))
			.filter(b -> Func.equals(b.getDeviceSerialNumber(), data.getDeviceSerialNumber()))
			.collect(Collectors.toList());

		if (!deviceCollect.isEmpty()) {
			if (collect.isEmpty()) {
				DeviceCurrentStatusEntity deviceCurrentStatus = new DeviceCurrentStatusEntity();
				BeanUtils.copyProperties(data, deviceCurrentStatus);
				deviceCurrentStatus.setId(null);
				deviceCurrentStatus.setCreateTime(null);
				deviceCurrentStatus.setUpdateTime(null);
				deviceCurrentStatus.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				deviceCurrentStatusService.save(deviceCurrentStatus);
			} else {//更新
				DeviceCurrentStatusEntity deviceCurrentStatus = collect.get(0);
				long id = deviceCurrentStatus.getId();
				Date createTime = deviceCurrentStatus.getCreateTime();
				BeanUtils.copyProperties(data, deviceCurrentStatus);
				deviceCurrentStatus.setId(id);
				deviceCurrentStatus.setCreateTime(createTime);
				deviceCurrentStatus.setTenantId(CommonConstant.CLIENT_TENANT_ID);
				deviceCurrentStatusService.updateById(deviceCurrentStatus);
				this.updateInverterMode4Ai( data.getPlantId(),data.getDeviceSerialNumber(),deviceCurrentStatus.getHybridWorkMode());
			}
		}

	}

	private void updateInverterMode4Ai(Long plantId, String deviceSerialNumber, String mode) {
		DeviceCustomModeEntity queryEntity = new DeviceCustomModeEntity();
		queryEntity.setDeviceSerialNumber(deviceSerialNumber);
		queryEntity.setPlantId(plantId);
		queryEntity.setTenantId(CommonConstant.CLIENT_TENANT_ID);
		List<DeviceCustomModeEntity> modelist = deviceCustomModeService.list(Condition.getQueryWrapper(queryEntity));
		if(CollectionUtil.isNotEmpty(modelist)) {
			DeviceCustomModeEntity dbCustomMode = modelist.get(0);
			// 非智能模式时，设备模式和 customMode表 一致
			if(!DictConstant.INVERTER_MODE_AI.equals(dbCustomMode.getHybridWorkMode())) {
				DeviceCustomModeEntity updateEntity = new DeviceCustomModeEntity();
				updateEntity.setId(dbCustomMode.getId());
				updateEntity.setHybridWorkMode(mode);
				deviceCustomModeService.updateCustomMode(updateEntity);
			}
		}
	}
}

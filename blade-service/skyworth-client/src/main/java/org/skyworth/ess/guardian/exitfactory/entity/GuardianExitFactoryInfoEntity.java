/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 安全卫士储能出厂信息 实体类
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Data
@TableName("guardian_exit_factory_info")
@ApiModel(value = "GuardianExitFactoryInfo对象", description = "安全卫士储能出厂信息")
@EqualsAndHashCode(callSuper = true)
public class GuardianExitFactoryInfoEntity extends SkyWorthEntity {

	/**
	 * 安全卫士SN
	 */
	@ApiModelProperty(value = "安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String deviceType;
	/**
	 * 卡号
	 */
	@ApiModelProperty(value = "卡号")
	private String simCardNumber;
	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String company;
	/**
	 * 质保年限
	 */
	@ApiModelProperty(value = "质保年限")
	private Integer qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ApiModelProperty(value = "出厂日期")
	private LocalDate exitFactoryDate;
	/**
	 * 激活日期
	 */
	@ApiModelProperty(value = "激活日期")
	private LocalDate activationDate;
	/**
	 * 质保开始日期
	 */
	@ApiModelProperty(value = "质保开始日期")
	private LocalDate warrantyStartDate;
	/**
	 * 质保结束日期
	 */
	@ApiModelProperty(value = "质保结束日期")
	private LocalDate warrantyEndDate;

}

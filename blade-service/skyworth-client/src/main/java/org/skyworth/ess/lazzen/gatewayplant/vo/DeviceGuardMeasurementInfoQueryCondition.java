package org.skyworth.ess.lazzen.gatewayplant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DeviceGuardMeasurementInfoQueryCondition {
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点id")
	private Long plantId;

	@ApiModelProperty(value = "安全卫士SN")
	private String gatewayUniqueNumber;

	@ApiModelProperty(value = "日期")
	private String date;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.mapper.AlarmLogMapper;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 异常日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
@AllArgsConstructor
public class AlarmLogServiceImpl extends BaseServiceImpl<AlarmLogMapper, AlarmLogEntity> implements IAlarmLogService {

	private final IDictBizClient dictBizClient;
	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	private final IGuardianExitFactoryInfoService guardianExitFactoryInfoService;
	private final String INVERTER_TYPE = BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_INVERTER;
	private final String BATTERY_TYPE = BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BATTERY;
	private final String GUARDIAN_TYPE = BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN;
	private final IBatteryMapDeviceService batteryMapDeviceService;
	private final IGatewayPlantService gatewayPlantService;
	private final BladeRedis bladeRedis;
	private final static List<String> ALARM_LOG_NORMAL_TYPE_CODE = new ArrayList<>();

	static {
		ALARM_LOG_NORMAL_TYPE_CODE.add("00000");
		ALARM_LOG_NORMAL_TYPE_CODE.add("00001");
		ALARM_LOG_NORMAL_TYPE_CODE.add("10010");
		ALARM_LOG_NORMAL_TYPE_CODE.add("10100");
		ALARM_LOG_NORMAL_TYPE_CODE.add("11000");
		ALARM_LOG_NORMAL_TYPE_CODE.add("11101");
	}

	/**
	 * 分页查询告警日志
	 *
	 * @param alarmLogPageCondition 告警日志查询条件
	 * @param query                 分页查询对象
	 * @return 返回分页查询结果，包含告警日志列表
	 */
	@Override
	public IPage<AlarmLogPageVO> list(AlarmLogPageCondition alarmLogPageCondition, Query query) {
		// 初始化分页对象
		IPage<AlarmLogPageVO> page = Condition.getPage(query);
		// 设置查询条件中的用户ID和部门ID
		setUserIdAndDeptIdInCondition(alarmLogPageCondition);
		// 执行分页查询
		List<AlarmLogPageVO> alarmLogEntityList = baseMapper.list(alarmLogPageCondition, page);
		// 获取并处理字典数据
		if (!CollectionUtils.isNullOrEmpty(alarmLogEntityList)) {
			processDictionaryData(alarmLogEntityList);
		}
		// 设置查询结果集
		page.setRecords(alarmLogEntityList);
		// 返回分页查询结果
		return page;
	}

	@Override
	public AlarmLogPageVO detail(String id) {
		AlarmLogPageVO detail = baseMapper.detail(id);
		if (ObjectUtil.isNotEmpty(detail)) {
			processDictionaryData(ListUtil.of(detail));
		}
		return detail;
	}

	/**
	 * 批量插入懒报警信息
	 * 该方法主要用于处理一批报警信息，将其插入或更新到数据库中
	 *
	 * @param jsonObjectList 包含报警信息的JSON对象列表
	 */
	@Override
	public void batchInsertLazzenAlarm(List<JSONObject> jsonObjectList) {
		// 检查输入列表是否为空或为null，如果是，则直接返回
		if (jsonObjectList == null || jsonObjectList.isEmpty()) {
			return;
		}
		// 提取报警信息中的网关实体，并检查结果是否为空
		List<GatewayPlantEntity> gatewayPlantEntityMysqlList = extractGatewayPlantEntities(jsonObjectList);
		if (CollectionUtils.isNullOrEmpty(gatewayPlantEntityMysqlList)) {
			return;
		}
		// 初始化报警日志实体列表和新网关实体列表
		List<AlarmLogEntity> alarmLogEntityList = new ArrayList<>();
		// 在线和告警状态切换实体,开合闸状态切换实体列表
		List<GatewayPlantEntity> gatewayPlantEntityStatusList = new ArrayList<>();
		// 处理报警日志，更新相关实体
		processAlarmLogs(jsonObjectList, gatewayPlantEntityMysqlList, alarmLogEntityList,
			gatewayPlantEntityStatusList);
		// 更新网关状态
		updateGatewayStatuses(gatewayPlantEntityStatusList, alarmLogEntityList);
	}

	/**
	 * 提取网关实体
	 * 从JSON对象列表中提取网关实体信息，用于后续处理
	 *
	 * @param jsonObjectList 包含网关信息的JSON对象列表
	 * @return 网关实体列表
	 */
	private List<GatewayPlantEntity> extractGatewayPlantEntities(List<JSONObject> jsonObjectList) {
		// 检查输入列表是否为空或为null，如果是，则返回空列表
		if (jsonObjectList == null || jsonObjectList.isEmpty()) {
			return Collections.emptyList();
		}
		// 提取所有唯一的网关编号
		List<String> gatewayUniqueNumberList = new ArrayList<>();
		for (JSONObject a : jsonObjectList) {
			String gatewayUniqueNumber = a.getString("gatewayUniqueNumber");
			if (StringUtils.isNotBlank(gatewayUniqueNumber)) {
				gatewayUniqueNumberList.add(gatewayUniqueNumber);
			}
		}
		// 查询并返回网关实体信息
		return gatewayPlantService.list(Wrappers.<GatewayPlantEntity>lambdaQuery()
			.in(GatewayPlantEntity::getGatewayUniqueNumber, gatewayUniqueNumberList)
			.select(GatewayPlantEntity::getGatewayUniqueNumber, GatewayPlantEntity::getPlantId,
				GatewayPlantEntity::getId, GatewayPlantEntity::getStatus, GatewayPlantEntity::getDeviceType));
	}

	/**
	 * 处理报警日志
	 * 根据输入的JSON对象列表和网关实体信息，处理报警日志并更新相关实体
	 *
	 * @param jsonObjectList              包含报警信息的JSON对象列表
	 * @param gatewayPlantEntityMysqlList 网关实体字典，用于快速查找
	 * @param alarmLogEntityList          报警日志实体列表
	 * @param gatewayPlantEntityNewList   新网关实体列表
	 */
	private void processAlarmLogs(List<JSONObject> jsonObjectList,
								  List<GatewayPlantEntity> gatewayPlantEntityMysqlList,
								  List<AlarmLogEntity> alarmLogEntityList,
								  List<GatewayPlantEntity> gatewayPlantEntityNewList) {
		// 将安全卫士实体列表转换为字典，以便快速查找
		Map<String, GatewayPlantEntity> gateWayGuardianPlantIdMap =
			gatewayPlantEntityMysqlList.stream().filter(a -> BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN.equalsIgnoreCase(a.getDeviceType())).collect(Collectors.toMap(
				GatewayPlantEntity::getGatewayUniqueNumber, Function.identity(), (a, b) -> a));
		// 将配电箱实体列表转换为字典，以便快速查找
		Map<String, GatewayPlantEntity> gateWayBackupBoxPlantIdMap =
			gatewayPlantEntityMysqlList.stream().filter(a -> BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BACKUP_BOX.equalsIgnoreCase(a.getDeviceType())).collect(Collectors.toMap(
				GatewayPlantEntity::getGatewayUniqueNumber, Function.identity(), (a, b) -> a));
		// 获取设备报警字典
		List<DictBiz> dictBizList =
			DictBizCache.getListByParentCode(DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(),
				BizConstant.CLIENT_GUARDIAN_ADDRESS_CODE,
				CommonConstant.CURRENT_LANGUAGE_ZH);
		Map<String, String> alarmConfigureMap =
			Optional.ofNullable(dictBizList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
		// 遍历JSON对象列表，处理每个报警信息
		for (JSONObject a : jsonObjectList) {
			String gatewayUniqueNumber = a.getString("gatewayUniqueNumber");
			Integer runningStatus1 = a.getInteger("runningStatus1");
			// 安全卫士告警信息+状态信息处理
			if (gateWayGuardianPlantIdMap.containsKey(gatewayUniqueNumber)) {
				Integer alarmStatus = a.getInteger("alarmStatus");
				String circuitBreakerAddress = a.getString("circuitBreakerAddress");
				GatewayPlantEntity gatewayPlantEntity = gateWayGuardianPlantIdMap.get(gatewayUniqueNumber);
				Long plantId = gatewayPlantEntity.getPlantId();
				// 根据运行状态更新网关实体状态
				if (runningStatus1 != null) {
					statusModificationList(runningStatus1, gatewayPlantEntityNewList, gatewayPlantEntity,
						gatewayUniqueNumber, BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
				}
				// 根据报警状态更新报警日志,数字0代表无异常
				if (alarmStatus != null && alarmStatus > BizConstant.NUMBER_ZERO) {
					LocalDateTime deviceDateTime = a.getObject("deviceDateTime", LocalDateTime.class);
					alarmInformationList(alarmStatus, alarmLogEntityList, gatewayUniqueNumber, circuitBreakerAddress,
						plantId, alarmConfigureMap, deviceDateTime);
				}
			}
			// 配电箱只有在线离线状态，不需要告警状态，不记录告警信息
			else if (gateWayBackupBoxPlantIdMap.containsKey(gatewayUniqueNumber)) {
				GatewayPlantEntity gatewayPlantEntity = gateWayBackupBoxPlantIdMap.get(gatewayUniqueNumber);
				// 根据运行状态更新网关实体状态
				if (runningStatus1 != null) {
					statusModificationList(runningStatus1, gatewayPlantEntityNewList, gatewayPlantEntity,
						gatewayUniqueNumber, BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_BACKUP_BOX);
				}
			}
		}
	}

	/**
	 * 更新网关状态
	 * 根据新网关实体列表更新网关的状态
	 *
	 * @param gatewayPlantEntityNewList 新网关实体列表
	 */
	private void updateGatewayStatuses(List<GatewayPlantEntity> gatewayPlantEntityNewList,
									   List<AlarmLogEntity> alarmLogEntityList) {
		// 如果报警日志实体列表不为空，则批量保存
		if (!CollectionUtils.isNullOrEmpty(alarmLogEntityList)) {
			this.saveBatch(alarmLogEntityList);
		}
		// 检查新网关实体列表是否为空，如果是，则直接返回
		if (!CollectionUtils.isNullOrEmpty(gatewayPlantEntityNewList)) {
			gatewayPlantService.updateBatchById(gatewayPlantEntityNewList);
		}
	}


	/**
	 * 封装需要调整状态的集合
	 *
	 * @param runningStatus1            10进制运行状态
	 * @param gatewayPlantEntityNewList 网关地址集合
	 * <AUTHOR>
	 * @date 2024/11/28 14:17
	 */
	private void statusModificationList(Integer runningStatus1, List<GatewayPlantEntity> gatewayPlantEntityNewList,
										GatewayPlantEntity gatewayPlantEntity, String gatewayUniqueNumber,
										String deviceType) {
		if (runningStatus1 == null) {
			return;
		}
		// 判断记录是否需要更新
		boolean isModify = false;
		// 2进制的运行状态
		String runningStatus1Binary = BinaryToHexUtils.decimalToBinary(runningStatus1);
		// 左边补0，补齐8位2进制
		runningStatus1Binary = BinaryToHexUtils.padWithZeros(runningStatus1Binary, BizConstant.NUMBER_EIGHT);
		// 2进制运行状态顺序反转
		// runningStatus1Binary = new StringBuilder(runningStatus1Binary).reverse().toString();
		// 0-5位是当前状态状态码
		String currentStatusBinary = runningStatus1Binary.substring(BizConstant.NUMBER_THREE);
		// 数据库断路器当前状态
		Integer status = gatewayPlantEntity.getStatus();
		// 在线
		if (ALARM_LOG_NORMAL_TYPE_CODE.contains(currentStatusBinary)) {
			// 上报状态为在线，当前数据库状态不为在线，则修改
			if (!BizConstant.NUMBER_ONE.equals(status)) {
				isModify = true;
				gatewayPlantEntity.setStatus(BizConstant.NUMBER_ONE);
			}
		}
		// 告警
		else {
			// 上报状态为告警&设备为安全卫士，当前数据库状态不为告警，则修改
			if (!BizConstant.NUMBER_TWO.equals(status) && BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN.equalsIgnoreCase(deviceType)) {
				isModify = true;
				gatewayPlantEntity.setStatus(BizConstant.NUMBER_TWO);
			}
		}
		// 5-6位是开关状态码
		String turnOnOffBinary = runningStatus1Binary.substring(BizConstant.NUMBER_ONE, BizConstant.NUMBER_THREE);
		Integer turnOnOffStatus = gatewayPlantEntity.getTurnOnOff();
		String msgKey = BizConstant.CLIENT_GUARDIAN_TURN_ON_OFF_STATUS_KEY + gatewayUniqueNumber;
		// 上报状态为开闸，当前数据库状态为合闸，则修改
		if ("00".equals(turnOnOffBinary) && !BizConstant.NUMBER_ZERO.equals(turnOnOffStatus)) {
			isModify = true;
			gatewayPlantEntity.setTurnOnOff(BizConstant.NUMBER_ZERO);
			bladeRedis.setEx(msgKey,
				BizConstant.NUMBER_ZERO,
				Duration.ofSeconds(30));
		} else if ("01".equals(turnOnOffBinary) && !BizConstant.NUMBER_ONE.equals(turnOnOffStatus)) {
			isModify = true;
			gatewayPlantEntity.setTurnOnOff(BizConstant.NUMBER_ONE);
			bladeRedis.setEx(msgKey,
				BizConstant.NUMBER_ONE,
				Duration.ofSeconds(30));
		}
		// 需要调整的记录
		if (isModify) {
			gatewayPlantEntityNewList.add(gatewayPlantEntity);
		}
	}


	/**
	 * @param alarmStatus        10进制告警状态
	 * @param alarmLogEntityList 告警信息集合
	 * <AUTHOR>
	 * @date 2024/11/28 14:18
	 */
	private void alarmInformationList(Integer alarmStatus, List<AlarmLogEntity> alarmLogEntityList,
									  String gatewayUniqueNumber, String circuitBreakerAddress, Long plantId,
									  Map<String, String> alarmConfigureMap, LocalDateTime deviceDateTime) {
		Boolean isExists = checkParametersAndGenerateCacheKey(alarmStatus, gatewayUniqueNumber,
			circuitBreakerAddress, plantId, alarmConfigureMap);
		if (isExists) {
			return;
		}
		// 异常信息整合返回，使用","分割
		String errorMessage = processAlarmStatusAndGenerateErrorMessage(alarmStatus, alarmConfigureMap);
		// 异常信息保存
		createAndAddAlarmLogEntity(alarmStatus, alarmLogEntityList, gatewayUniqueNumber,
			plantId, errorMessage, deviceDateTime);
	}

	/**
	 * 创建并添加报警日志实体，并设置缓存。
	 *
	 * @param alarmStatus         报警状态
	 * @param alarmLogEntityList  报警日志实体列表
	 * @param gatewayUniqueNumber 网关唯一编号
	 * @param plantId             工厂ID
	 * @param errorMessage        错误消息
	 */
	private void createAndAddAlarmLogEntity(Integer alarmStatus, List<AlarmLogEntity> alarmLogEntityList,
											String gatewayUniqueNumber, Long plantId,
											String errorMessage, LocalDateTime deviceDateTime) {
		AlarmLogEntity alarmLogEntity = new AlarmLogEntity();
		alarmLogEntity.setSerialNumber(gatewayUniqueNumber);
		// localDateTime转Date
		if (deviceDateTime != null) {
			ZonedDateTime zonedDateTime = deviceDateTime.atZone(ZoneId.systemDefault());
			alarmLogEntity.setDeviceDateTime(
				Date.from(zonedDateTime.toInstant()));
		} else {
			alarmLogEntity.setDeviceDateTime(new Date());
		}
		alarmLogEntity.setExceptionType(BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN);
		alarmLogEntity.setTimeZone("UTC+8");
		alarmLogEntity.setAddressCode(BizConstant.CLIENT_GUARDIAN_ADDRESS_CODE);
		alarmLogEntity.setAlarmNumber(alarmStatus);
		alarmLogEntity.setStatus(BizConstant.NUMBER_THREE);
		alarmLogEntity.setPlantId(plantId);
		alarmLogEntity.setCreateUserAccount(CommonConstant.COMMON_SYSTEM_USER_ACCOUNT);
		alarmLogEntity.setUpdateUserAccount(CommonConstant.COMMON_SYSTEM_USER_ACCOUNT);
		alarmLogEntity.setExceptionMessage(errorMessage);
		alarmLogEntityList.add(alarmLogEntity);
		// 缓存不存该异常，则添加到列表中,缓存一天结束
		String msgKey = String.format("%d:%s:%s:%d", plantId, gatewayUniqueNumber,
			BizConstant.CLIENT_GUARDIAN_ADDRESS_CODE, alarmStatus);
		bladeRedis.setEx(msgKey, LocalDate.now().toString(), Duration.ofDays(1));
	}

	/**
	 * 处理报警状态并生成错误消息。
	 *
	 * @param alarmStatus       报警状态
	 * @param alarmConfigureMap 报警配置映射
	 * @return 错误消息
	 */
	private String processAlarmStatusAndGenerateErrorMessage(Integer alarmStatus,
															 Map<String, String> alarmConfigureMap) {
		// 10进制转2进制
		String alarmStatusBinary = Integer.toBinaryString(alarmStatus);
		// 2进制顺序反转，并获取1字节所在位数
		List<Integer> errorStatusList = getAlarmStatusList(new StringBuilder(alarmStatusBinary).reverse().toString()
		);
		StringBuilder errorMsg = new StringBuilder();
		for (Integer key : errorStatusList) {
			String errorMessage = alarmConfigureMap.get(key.toString());
			if (errorMessage != null) {
				errorMsg.append(errorMessage).append(",");
			}
		}
		if (errorMsg.length() > 0) {
			errorMsg.setLength(errorMsg.length() - 1);
		}
		return errorMsg.toString();
	}

	/**
	 * 检查输入参数是否为空，并生成缓存键。
	 *
	 * @param alarmStatus           报警状态
	 * @param gatewayUniqueNumber   网关唯一编号
	 * @param circuitBreakerAddress 断路器地址
	 * @param plantId               工厂ID
	 * @param alarmConfigureMap     报警配置映射
	 * @return 缓存键
	 */
	private Boolean checkParametersAndGenerateCacheKey(Integer alarmStatus, String gatewayUniqueNumber,
													   String circuitBreakerAddress, Long plantId,
													   Map<String, String> alarmConfigureMap) {
		if (alarmStatus == null || gatewayUniqueNumber == null || circuitBreakerAddress == null ||
			plantId == null || alarmConfigureMap == null) {
			return false;
		}
		String msgKey = String.format("%d:%s:%s:%d", plantId, gatewayUniqueNumber,
			BizConstant.CLIENT_GUARDIAN_ADDRESS_CODE, alarmStatus);
		// 如果缓存中存在该异常，则返回
		return ValidationUtil.isNotEmpty(bladeRedis.get(msgKey));
	}


	/**
	 * 计算当前异常编码
	 *
	 * @param alarmStatusBinary 异常信息2进制数字
	 * @return List<Integer>
	 * <AUTHOR>
	 * @date 2024/11/28 15:24
	 */
	private List<Integer> getAlarmStatusList(String alarmStatusBinary) {
		List<Integer> positionList = new ArrayList<>();
		// 遍历字符串，找到字符'1'的位置，并将位置-1后存入列表
		for (int i = 0; i < alarmStatusBinary.length(); i++) {
			if (alarmStatusBinary.charAt(i) == '1') {
				positionList.add(i);
			}
		}
		return positionList;
	}

	/**
	 * 设置查询条件中的用户ID和部门ID
	 *
	 * @param condition 查询条件对象
	 */
	private void setUserIdAndDeptIdInCondition(AlarmLogPageCondition condition) {
		BladeUser user = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(user);
		condition.setUserId(user.getUserId());
		condition.setDepartmentId(deptId);
	}

	/**
	 * 获取并处理字典数据
	 *
	 * @param alarmLogEntityList 告警日志实体列表
	 */
	private void processDictionaryData(List<AlarmLogPageVO> alarmLogEntityList) {
		// 批量获取字典数据
		Map<String, List<DictBiz>> dictBizMap =
			dictBizClient.batchGetList(Arrays.asList(DictBizCodeEnum.DEVICE_TYPE.getDictCode(),
				DictBizCodeEnum.BATTERY_MODE_TYPE.getDictCode(), DictBizCodeEnum.DEVICE_MODE_TYPE.getDictCode(),
				DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode(),
				DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode())).getData();
		// 获取当前语言环境
		String currentLanguage = CommonUtil.getCurrentLanguage();
		// 根据语言环境筛选设备类型字典数据
		Map<String, String> dictDeviceTypeBizMap = getDictDeviceTypeBizMap(dictBizMap, currentLanguage);
		// 获取系统默认时区配置
		//String timeZone = getTimeZone(dictBizMap, currentLanguage);
		// 处理每个告警日志的显示数据
		processAlarms(alarmLogEntityList, dictDeviceTypeBizMap, dictBizMap, currentLanguage);
	}

	/**
	 * 生成处理建议Map对象
	 *
	 * @param dictBizMap      字典Map
	 * @param currentLanguage 入参
	 * @return Map<String, String>
	 * <AUTHOR>
	 * @since 2024/9/6 17:27
	 **/
	private Map<String, String> getDictSuggestionsHandlingMap(List<AlarmLogPageVO> alarmLogEntityList, Map<String,
		List<DictBiz>> dictBizMap, String currentLanguage) {
		Map<String, String> dictsuggestionsHandlingMap = new HashMap<>();
		// 空值检查
		if (alarmLogEntityList == null || alarmLogEntityList.isEmpty() || dictBizMap == null) {
			return dictsuggestionsHandlingMap;
		}
		// 获取地址码列表
		List<String> addressCodeList = alarmLogEntityList.stream()
			.map(AlarmLogPageVO::getAddressCode)
			.distinct()
			.collect(Collectors.toList());
		// 获取字典业务列表
		List<DictBiz> dictBizList = dictBizMap.getOrDefault(DictBizCodeEnum.DEVICE_ALARM_MAPPING_CONFIG.getDictCode(),
			Collections.emptyList());
		// 获取一级菜单的code
		Map<Long, String> dictParentMap = dictBizList.stream()
			.filter(a -> addressCodeList.contains(a.getDictKey()) && a.getLanguage().equalsIgnoreCase(currentLanguage))
			.collect(Collectors.toMap(DictBiz::getId, DictBiz::getDictKey, (a, b) -> a));
		// 处理字典建议
		dictBizList.forEach(a -> {
			if (dictParentMap.containsKey(a.getParentId()) && a.getLanguage().equalsIgnoreCase(currentLanguage) && StringUtils.isNotBlank(a.getRemark())) {
				dictsuggestionsHandlingMap.put(dictParentMap.get(a.getParentId()) + a.getDictKey(), a.getRemark());
			}
		});
		return dictsuggestionsHandlingMap;
	}


	/**
	 * 根据语言环境筛选设备类型字典数据
	 *
	 * @param dictBizMap      字典业务数据Map
	 * @param currentLanguage 当前语言环境
	 * @return 包含设备类型的字典映射
	 */
	private Map<String, String> getDictDeviceTypeBizMap(Map<String, List<DictBiz>> dictBizMap,
														String currentLanguage) {
		return dictBizMap.get(DictBizCodeEnum.DEVICE_TYPE.getDictCode()).stream()
			.filter(dictBiz -> dictBiz.getLanguage().equalsIgnoreCase(currentLanguage))
			.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
	}

	/**
	 * 获取系统默认时区配置
	 *
	 * @param dictBizMap      字典业务数据Map
	 * @param currentLanguage 当前语言环境
	 * @return 默认时区字符串
	 */
	private String getTimeZone(Map<String, List<DictBiz>> dictBizMap, String currentLanguage) {
		Optional<DictBiz> timeZoneBiz =
			dictBizMap.get(DictBizCodeEnum.SYSTEM_DEFAULT_CONFIGURATION.getDictCode()).stream()
				.filter(dictBiz -> dictBiz.getLanguage().equalsIgnoreCase(currentLanguage) && CommonConstant.DEFAULT_TIME_ZONE.equalsIgnoreCase(dictBiz.getDictKey()))
				.findFirst();
		return timeZoneBiz.map(DictBiz::getDictValue).orElse("");
	}

	/**
	 * 封装查询结果
	 *
	 * @param alarmLogEntityList   告警列表
	 * @param dictDeviceTypeBizMap 设备类型map
	 * @param dictBizMap           数据字典map
	 * @param currencyLanguage     当前语言
	 * <AUTHOR>
	 * @since 2024/8/13 17:12
	 **/
	public void processAlarms(List<AlarmLogPageVO> alarmLogEntityList, Map<String, String> dictDeviceTypeBizMap,
							  Map<String, List<DictBiz>> dictBizMap, String currencyLanguage) {
		List<String> allSerialNumbers = extractSerialNumbers(alarmLogEntityList);
		Map<String, String> deviceSerialNumberMap = fetchDeviceSerialNumbers(allSerialNumbers, dictBizMap,
			currencyLanguage);
		// 生成处理建议
		Map<String, String> dictSuggestionsHandlingMap = getDictSuggestionsHandlingMap(alarmLogEntityList, dictBizMap,
			currencyLanguage);
		processAlarmLogEntities(alarmLogEntityList, dictDeviceTypeBizMap, deviceSerialNumberMap,
			dictSuggestionsHandlingMap);
	}

	/**
	 * 从报警日志实体列表中提取所有序列号。
	 *
	 * @param alarmLogEntityList 报警日志实体列表
	 * @return 序列号列表
	 */
	private List<String> extractSerialNumbers(List<AlarmLogPageVO> alarmLogEntityList) {
		return alarmLogEntityList.stream()
			.filter(alarmLogPageVO -> alarmLogPageVO.getExceptionType() != null &&
				(alarmLogPageVO.getExceptionType().equalsIgnoreCase(INVERTER_TYPE) ||
					alarmLogPageVO.getExceptionType().equalsIgnoreCase(BATTERY_TYPE) ||
					alarmLogPageVO.getExceptionType().equalsIgnoreCase(GUARDIAN_TYPE)))
			.map(AlarmLogPageVO::getSerialNumber)
			.distinct()
			.collect(Collectors.toList());
	}

	/**
	 * 根据序列号查询设备信息。
	 *
	 * @param serialNumbers    序列号列表
	 * @param dictBizMap       字典数据映射
	 * @param currencyLanguage 当前语言
	 * @return 设备序列号与类型映射
	 */
	private Map<String, String> fetchDeviceSerialNumbers(List<String> serialNumbers,
														 Map<String, List<DictBiz>> dictBizMap,
														 String currencyLanguage) {
		// 创建一个HashMap来存储设备的序列号及其对应的状态
		Map<String, String> deviceSerialNumberMap = new HashMap<>();
		// 检查传入的序列号列表是否为空
		if (!CollectionUtils.isNullOrEmpty(serialNumbers)) {
			List<BatteryMapDeviceEntity> batteryMapDeviceList =
				batteryMapDeviceService.list(Wrappers.<BatteryMapDeviceEntity>lambdaQuery().in(BatteryMapDeviceEntity::getDeviceSerialNumber, serialNumbers));
			List<BatteryExitFactoryInfoEntity> batteryInfoList = null;
			if (!CollectionUtils.isNullOrEmpty(batteryMapDeviceList)) {
				// 收集储能设备列表中的序列号
				List<String> batterySerialNumbers =
					batteryMapDeviceList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).collect(Collectors.toList());
				// 查询储能出口工厂信息，并将结果存储在列表中
				batteryInfoList =
					batteryExitFactoryInfoService.list(Wrappers.<BatteryExitFactoryInfoEntity>lambdaQuery().in(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batterySerialNumbers));
			}
			// 查询智能能量变换器出口工厂信息，并将结果存储在列表中
			List<DeviceExitFactoryInfoEntity> deviceInfoList =
				deviceExitFactoryInfoService.list(Wrappers.<DeviceExitFactoryInfoEntity>lambdaQuery().in(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, serialNumbers));
			// 查询安全卫士出口工厂信息，并将结果存储在列表中
			List<GuardianExitFactoryInfoEntity> safetyGuardInfoList =
				guardianExitFactoryInfoService.list(Wrappers.<GuardianExitFactoryInfoEntity>lambdaQuery().in(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, serialNumbers));

			// 根据设备模式类型字典码获取设备类型映射
			Map<String, String> batteryTypeMap =
				dictBizMap.get(DictBizCodeEnum.BATTERY_MODE_TYPE.getDictCode()).stream()
					.filter(dictBiz -> currencyLanguage.equalsIgnoreCase(dictBiz.getLanguage()))
					.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));

			// 根据设备模式类型字典码获取设备类型映射
			Map<String, String> deviceTypeMap = dictBizMap.get(DictBizCodeEnum.DEVICE_MODE_TYPE.getDictCode()).stream()
				.filter(dictBiz -> currencyLanguage.equalsIgnoreCase(dictBiz.getLanguage()))
				.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));

			// 根据安全卫士模式类型字典码获取安全卫士类型映射
			Map<String, String> safetyGuardDeviceTypeMap =
				dictBizMap.get(DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode()).stream()
					.filter(dictBiz -> currencyLanguage.equalsIgnoreCase(dictBiz.getLanguage()))
					.collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));

			// 检查储能设备信息列表是否为空
			if (!CollectionUtils.isNullOrEmpty(batteryInfoList)) {
				// 设置储能的类型
				batteryInfoList.forEach(a -> a.setBatteryType(batteryTypeMap.get(a.getBatteryType())));
				// 将储能序列号和类型添加到设备序列号映射中
				deviceSerialNumberMap.putAll(batteryInfoList.stream().filter(a -> StringUtils.isNotEmpty(a.getBatteryType())).collect(Collectors.toMap(BatteryExitFactoryInfoEntity::getBatterySerialNumber, BatteryExitFactoryInfoEntity::getBatteryType)));
			}
			// 检查智能能量变换器设备信息列表是否为空
			if (!CollectionUtils.isNullOrEmpty(deviceInfoList)) {
				// 设置设备的类型
				deviceInfoList.forEach(a -> a.setDeviceType(deviceTypeMap.get(a.getDeviceType())));
				// 将设备序列号和类型添加到设备序列号映射中
				deviceSerialNumberMap.putAll(deviceInfoList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType)));
			}

			// 检查安全卫士信息列表是否为空
			if (!CollectionUtils.isNullOrEmpty(safetyGuardInfoList)) {
				// 设置安全卫士的类型
				safetyGuardInfoList.forEach(a -> a.setDeviceType(safetyGuardDeviceTypeMap.get(a.getDeviceType())));
				// 将安全卫士序列号和类型添加到设备序列号映射中
				deviceSerialNumberMap.putAll(safetyGuardInfoList.stream().filter(a -> StringUtils.isNotEmpty(a.getSecurityGuardSerialNumber())).filter(a -> StringUtils.isNotEmpty(a.getDeviceType())).collect(Collectors.toMap(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, GuardianExitFactoryInfoEntity::getDeviceType)));
			}
		}
		return deviceSerialNumberMap;
	}

	/**
	 * 处理报警日志实体列表，更新相关信息。
	 *
	 * @param alarmLogEntityList    报警日志实体列表
	 * @param dictDeviceTypeBizMap  异常类型字典映射
	 * @param deviceSerialNumberMap 设备序列号与类型映射
	 */
	private void processAlarmLogEntities(List<AlarmLogPageVO> alarmLogEntityList,
										 Map<String, String> dictDeviceTypeBizMap,
										 Map<String, String> deviceSerialNumberMap,
										 Map<String, String> dictSuggestionsHandlingMap) {
		alarmLogEntityList.forEach(alarmLogEntity -> {
			// 根据异常类型ID获取异常类型名称
			alarmLogEntity.setExceptionTypeName(dictDeviceTypeBizMap.get(alarmLogEntity.getExceptionType()));
			// 获取处理建议,安全卫士没有处理建议
			if (!BizConstant.CLIENT_IMPORTANT_EVENT_TYPE_GUARDIAN.equals(alarmLogEntity.getExceptionType())) {
				alarmLogEntity.setOperationSuggestion(dictSuggestionsHandlingMap.get(alarmLogEntity.getAddressCode() + alarmLogEntity.getAlarmNumber()));
			}
			// 根据设备类型获取对应的类型名称
			String serialNumber = alarmLogEntity.getSerialNumber();
			if (StringUtils.isNotBlank(serialNumber)) {
				if (BATTERY_TYPE.equals(alarmLogEntity.getExceptionType())) {
					// 如果2013，则取储能1；如果2213，则取储能2
					int batteryEnergyStorageNumber = 1;
					if (alarmLogEntity.getAddressCode().startsWith("2213")){
						batteryEnergyStorageNumber = 2;
					}
					List<BatteryMapDeviceEntity> batteryMapDeviceEntityList =
						batteryMapDeviceService.queryListByPlantIdAndSnAndBattery(alarmLogEntity.getPlantId(),
							serialNumber,batteryEnergyStorageNumber);
					if (CollectionUtil.isNotEmpty(batteryMapDeviceEntityList)) {
						// 获取储能序列号，去重，返回list
						List<String> batterySerialNumberList =
							batteryMapDeviceEntityList.stream().map(BatteryMapDeviceEntity::getBatterySerialNumber).distinct().collect(Collectors.toList());
						// 遍历batterySerialNumberList，从deviceSerialNumberMap获取对应的储能型号，排除空的和重复的储能型号，再用逗号进行拼接
						String batteryTypeSetStr =
							batterySerialNumberList.stream().filter(a -> StringUtils.isNotEmpty(deviceSerialNumberMap.get(a))).distinct().map(deviceSerialNumberMap::get).distinct().collect(Collectors.joining(","));
						alarmLogEntity.setDeviceType(batteryTypeSetStr);
					}
				} else {
					alarmLogEntity.setDeviceType(deviceSerialNumberMap.get(serialNumber));
				}
			}
		});
	}
}

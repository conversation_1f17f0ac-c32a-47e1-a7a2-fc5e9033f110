<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.lazzen.productinfo.mapper.DeviceGuardProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceGuardProductInfoResultMap" type="org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity">
        <result column="gateway_unique_number" property="gatewayUniqueNumber"/>
        <result column="circuit_breaker_address" property="circuitBreakerAddress"/>
        <result column="id" property="id"/>
        <result column="product_standard" property="productStandard"/>
        <result column="product_model" property="productModel"/>
        <result column="frame_current" property="frameCurrent"/>
        <result column="rated_current" property="ratedCurrent"/>
        <result column="work_frequency" property="workFrequency"/>
        <result column="product_pole_number" property="productPoleNumber"/>
        <result column="product_date_year" property="productDateYear"/>
        <result column="product_date_month_day" property="productDateMonthDay"/>
        <result column="product_sn" property="productSn"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceGuardProductInfoPage" resultMap="deviceGuardProductInfoResultMap">
        select * from lazzen_device_guard_product_info where is_deleted = 0
    </select>


    <select id="exportDeviceGuardProductInfo" resultType="org.skyworth.ess.lazzen.productinfo.excel.DeviceGuardProductInfoExcel">
        SELECT * FROM lazzen_device_guard_product_info ${ew.customSqlSegment}
    </select>

</mapper>

package org.skyworth.ess.battery.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.entity.BatteryDeviceInstallVO;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tenant.annotation.NonDS;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * 储能信息
 *
 * <AUTHOR>
 * @date 2023/9/14 16:38
 **/
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/battery")
@Api(value = "储能信息", tags = "储能信息")
public class BatteryMapDeviceController extends BladeController {

	private final IBatteryMapDeviceService IBatteryMapDeviceService;

	@GetMapping("view/{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询单个储能信息", notes = "查询单个储能信息")
	@PreAuth("hasPermission('client:battery:detail')")
	public R<BatteryMapDeviceVO> queryById(@PathVariable("id") Integer id) {
		return R.data(this.IBatteryMapDeviceService.queryById(id));
	}

	@GetMapping("/listSummary")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询储能储能列表汇总项", notes = "查询储能储能列表汇总项")
	@PreAuth("hasPermission('client:battery:list')")
	public R<Map<String, Object>> listSummary(@ApiIgnore QueryCondition queryPageCondition) {
		return R.data(IBatteryMapDeviceService.listSummary(queryPageCondition));
	}

	@GetMapping("/installation")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "储能箱安装信息", notes = "储能箱安装信息")
	@PreAuth("hasPermission('client:battery:detail')")
	public R<List<BatteryDeviceInstallVO>> installation(QueryCondition queryPageCondition) {
		return R.data(IBatteryMapDeviceService.installation(queryPageCondition));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页查询储能列表信息", notes = "分页查询储能列表信息")
	@PreAuth("hasPermission('client:battery:list')")
	public R<IPage<BatteryPageResultVO>> page(@ApiIgnore QueryCondition queryPageCondition, Query query) {
		IPage<BatteryPageResultVO> pages = IBatteryMapDeviceService.queryPage(queryPageCondition, Condition.getPage(query));
		return R.data((pages));
	}

	@GetMapping("/deviceInformation")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "储能箱设备信息", notes = "储能箱设备信息")
	@PreAuth("hasPermission('client:battery:detail')")
	public R<List<BatteryExitFactoryInfoVO>> deviceInformation(QueryCondition queryPageCondition) {
		return R.data(IBatteryMapDeviceService.deviceInformation(queryPageCondition));
	}

	@GetMapping("/importantEvents")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "储能箱重要事件", notes = "储能箱重要事件")
	@PreAuth("hasPermission('client:battery:detail')")
	public R<IPage<ImportantEventEntity>> importantEvents(QueryCondition queryCondition, Query query) {
		return R.data(IBatteryMapDeviceService.importantEvents(queryCondition, Condition.getPage(query)));
	}

	@GetMapping("/exceptionLog")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "储能箱异常日志", notes = "储能箱异常日志")
	@PreAuth("hasPermission('client:battery:detail')")
	public R<IPage<ExceptionLogEntity>> exceptionLog(QueryCondition queryCondition, Query query) {
		return R.data(IBatteryMapDeviceService.exceptionLog(queryCondition, Condition.getPage(query)));
	}


}


package org.skyworth.ess.guardian.issue;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.skyworth.ess.app.vo.AppAdvancedSetup;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/8/9 17:08:31
 */
@Data
public class IssueStrategyEntity {

	public IssueStrategyEntity(String deviceSerialNumber,String bizType, AppAdvancedSetup appAdvancedSetups) {
		this.deviceSerialNumber = deviceSerialNumber;
		this.bizType = bizType;
		this.appAdvancedSetups = appAdvancedSetups;
	}

	public IssueStrategyEntity(String deviceSerialNumber,String bizType, JSONObject dataObject) {
		this.deviceSerialNumber = deviceSerialNumber;
		this.bizType = bizType;
		this.dataObject = dataObject;
	}

	String deviceSerialNumber;

	String bizType;

	//APP、web 端下发设置时，需要用设置项数据填补该字段
	AppAdvancedSetup appAdvancedSetups;

	//除此APP 端下发之外，可以把设置项数据放到此
	JSONObject dataObject;

	//流水号
	String requestId;
}

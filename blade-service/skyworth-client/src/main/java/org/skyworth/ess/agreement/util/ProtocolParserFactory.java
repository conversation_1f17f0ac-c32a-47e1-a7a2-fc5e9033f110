package org.skyworth.ess.agreement.util;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ProtocolParserFactory
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/31 17:12
 */
public class ProtocolParserFactory {
	private final Map<String, ProtocolParser> parsers = new HashMap<>();

	public void registerParser(String company, ProtocolParser parser) {
		parsers.put(company, parser);
	}

	public ProtocolParser getParser(String company) {
		ProtocolParser parser = parsers.get(company);
		if (parser == null) {
			throw new IllegalArgumentException("不支持的协议类型: " + company);
		}
		return parser;
	}
}

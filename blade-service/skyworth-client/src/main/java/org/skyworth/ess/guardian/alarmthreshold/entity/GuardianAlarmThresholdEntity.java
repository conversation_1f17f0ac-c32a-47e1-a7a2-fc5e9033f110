/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 安全卫士阈值&amp;闸位状态 实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@TableName("guardian_alarm_threshold")
@ApiModel(value = "GuardianAlarmThreshold对象", description = "安全卫士阈值&amp;闸位状态")
@EqualsAndHashCode(callSuper = true)
public class GuardianAlarmThresholdEntity extends SkyWorthEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ApiModelProperty(value = "安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 欠压告警阈值
	 */
	@ApiModelProperty(value = "欠压告警阈值")
	private BigDecimal undervoltageAlarmThreshold;
	/**
	 * 过压告警阈值
	 */
	@ApiModelProperty(value = "过压告警阈值")
	private BigDecimal overvoltageAlarmThreshold;
	/**
	 * 过流告警阈值
	 */
	@ApiModelProperty(value = "过流告警阈值")
	private BigDecimal overcurrentAlarmThreshold;
	/**
	 * 漏电告警阈值
	 */
	@ApiModelProperty(value = "漏电告警阈值")
	private BigDecimal leakageAlarmThreshold;
	/**
	 * A温度过高告警阈值
	 */
	@ApiModelProperty(value = "A温度过高告警阈值")
	private BigDecimal aTemperatureHighAlarmThreshold;
	/**
	 * B温度过高告警阈值
	 */
	@ApiModelProperty(value = "B温度过高告警阈值")
	private BigDecimal bTemperatureHighAlarmThreshold;
	/**
	 * C温度过高告警阈值
	 */
	@ApiModelProperty(value = "C温度过高告警阈值")
	private BigDecimal cTemperatureHighAlarmThreshold;
	/**
	 * N温度过高告警阈值
	 */
	@ApiModelProperty(value = "N温度过高告警阈值")
	private BigDecimal nTemperatureHighAlarmThreshold;
	/**
	 * 电弧等级告警阈值
	 */
	@ApiModelProperty(value = "电弧等级告警阈值")
	private BigDecimal arcLevelAlarmThreshold;
	/**
	 * 限电度数告警阈值
	 */
	@ApiModelProperty(value = "限电度数告警阈值")
	private BigDecimal alarmThresholdForPowerRestrictionDegree;
	/**
	 * 欠压开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "欠压开关(0关闭/1打开)")
	private Integer undervoltageSwitch;
	/**
	 * 过压开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "过压开关(0关闭/1打开)")
	private Integer overvoltageSwitch;
	/**
	 * 过流开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "过流开关(0关闭/1打开)")
	private Integer overcurrentSwitch;
	/**
	 * 漏电开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "漏电开关(0关闭/1打开)")
	private Integer leakageSwitch;
	/**
	 * 温度过高告警开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "温度过高告警开关(0关闭/1打开)")
	private Integer highTemperatureAlarmSwitch;
	/**
	 * 电弧等级开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "电弧等级开关(0关闭/1打开)")
	private Integer arcLevelSwitch;
	/**
	 * 限电度数开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "限电度数开关(0关闭/1打开)")
	private Integer powerLimitSwitch;
	/**
	 * 缺相保护开关(0关闭/1打开)
	 */
	@ApiModelProperty(value = "缺相保护开关(0关闭/1打开)")
	private Integer phaseLossProtectionSwitch;

}

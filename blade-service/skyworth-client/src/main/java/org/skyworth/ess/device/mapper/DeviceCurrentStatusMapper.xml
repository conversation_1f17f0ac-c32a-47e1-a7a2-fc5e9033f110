<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceCurrentStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="DeviceCurrentStatusResultMap" type="org.skyworth.ess.device.entity.DeviceCurrentStatusEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="total_energy" property="totalEnergy"/>
        <result column="total_generation_time" property="totalGenerationTime"/>
        <result column="today_energy" property="todayEnergy"/>
        <result column="accumulated_energy_of_positive" property="accumulatedEnergyOfPositive"/>
        <result column="accumulated_energy_of_negative" property="accumulatedEnergyOfNegative"/>
        <result column="accumulated_energy_of_load" property="accumulatedEnergyOfLoad"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_load_energy" property="todayLoadEnergy"/>
        <result column="daily_energy_to_eps" property="dailyEnergyToEps"/>
        <result column="accumulated_energy_to_eps" property="accumulatedEnergyToEps"/>
        <result column="battery_soc" property="batterySoc"/>
        <result column="battery_daily_charge_energy" property="batteryDailyChargeEnergy"/>
        <result column="battery_daily_discharge_energy" property="batteryDailyDischargeEnergy"/>
        <result column="battery_accumulated_charge_energy" property="batteryAccumulatedChargeEnergy"/>
        <result column="battery_accumulated_discharge_energy" property="batteryAccumulatedDischargeEnergy"/>
        <result column="inverter_mode" property="inverterMode"/>
        <result column="pv1_voltage" property="pv1Voltage"/>
        <result column="pv1_current" property="pv1Current"/>
        <result column="mppt1_power" property="mppt1Power"/>
        <result column="pv2_voltage" property="pv2Voltage"/>
        <result column="pv2_current" property="pv2Current"/>
        <result column="mppt2_power" property="mppt2Power"/>
        <result column="pv3_voltage" property="pv3Voltage"/>
        <result column="pv3_current" property="pv3Current"/>
        <result column="mppt3_power" property="mppt3Power"/>
        <result column="pv4_voltage" property="pv4Voltage"/>
        <result column="pv4_current" property="pv4Current"/>
        <result column="mppt4_power" property="mppt4Power"/>
        <result column="battery_voltage" property="batteryVoltage"/>
        <result column="battery_current" property="batteryCurrent"/>
        <result column="battery_power" property="batteryPower"/>
        <result column="phase_a_voltage" property="phaseAVoltage"/>
        <result column="phase_a_current" property="phaseACurrent"/>
        <result column="phase_a_power" property="phaseAPower"/>
        <result column="phase_b_voltage" property="phaseBVoltage"/>
        <result column="phase_b_current" property="phaseBCurrent"/>
        <result column="phase_b_power" property="phaseBPower"/>
        <result column="phase_c_voltage" property="phaseCVoltage"/>
        <result column="phase_c_current" property="phaseCCurrent"/>
        <result column="phase_c_power" property="phaseCPower"/>
        <result column="l1_n_phase_voltage_of_grid" property="l1NPhaseVoltageOfGrid"/>
        <result column="l1_current_of_grid" property="l1CurrentOfGrid"/>
        <result column="phase_r_watt_of_grid" property="phaseRWattOfGrid"/>
        <result column="l2_n_phase_voltage_of_grid" property="l2NPhaseVoltageOfGrid"/>
        <result column="l2_current_of_grid" property="l2CurrentOfGrid"/>
        <result column="phase_s_watt_of_grid" property="phaseSWattOfGrid"/>
        <result column="l3_n_phase_voltage_of_grid" property="l3NPhaseVoltageOfGrid"/>
        <result column="l3_current_of_grid" property="l3CurrentOfGrid"/>
        <result column="phase_t_watt_of_grid" property="phaseTWattOfGrid"/>
        <result column="l1_n_phase_voltage_of_load" property="l1NPhaseVoltageOfLoad"/>
        <result column="l1_current_of_load" property="l1CurrentOfLoad"/>
        <result column="phase_r_watt_of_load" property="phaseRWattOfLoad"/>
        <result column="l2_n_phase_voltage_of_load" property="l2NPhaseVoltageOfLoad"/>
        <result column="l2_current_of_load" property="l2CurrentOfLoad"/>
        <result column="phase_s_watt_of_load" property="phaseSWattOfLoad"/>
        <result column="l3_n_phase_voltage_of_load" property="l3NPhaseVoltageOfLoad"/>
        <result column="l3_current_of_load" property="l3CurrentOfLoad"/>
        <result column="phase_t_watt_of_load" property="phaseTWattOfLoad"/>
        <result column="phase_r_voltage_of_eps" property="phaseRVoltageOfEps"/>
        <result column="phase_r_current_of_eps" property="phaseRCurrentOfEps"/>
        <result column="phase_r_watt_of_eps" property="phaseRWattOfEps"/>
        <result column="phase_s_voltage_of_eps" property="phaseSVoltageOfEps"/>
        <result column="phase_s_current_of_eps" property="phaseSCurrentOfEps"/>
        <result column="phase_s_watt_of_eps" property="phaseSWattOfEps"/>
        <result column="phase_t_voltage_of_eps" property="phaseTVoltageOfEps"/>
        <result column="phase_t_current_of_eps" property="phaseTCurrentOfEps"/>
        <result column="phase_t_watt_of_eps" property="phaseTWattOfEps"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectDeviceCurrentStatusPage" resultMap="DeviceCurrentStatusResultMap">
        select *
        from device_current_status
        where is_deleted = 0
    </select>

    <select id="selectDeviceCurrentStatusIsDelete" resultMap="DeviceCurrentStatusResultMap">
        select *
        from device_current_status
        where plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber} and is_deleted = 1
    </select>


    <select id="exportDeviceCurrentStatus" resultType="org.skyworth.ess.device.excel.DeviceCurrentStatusExcel">
        SELECT *
        FROM device_current_status ${ew.customSqlSegment}
    </select>

    <select id="selectEnergyTotalStat" resultType="com.alibaba.fastjson.JSONObject">
        select
        device_serial_number deviceSerialNumber,
        COALESCE(total_energy, 0) AS totalEnergy,
        COALESCE(accumulated_energy_of_positive, 0) AS accumulatedEnergyOfPositive,
        COALESCE(accumulated_energy_of_negative, 0) AS accumulatedEnergyOfNegative,
        COALESCE(accumulated_energy_of_load, 0) AS accumulatedEnergyOfLoad,
        COALESCE(accumulated_energy_to_eps, 0) AS accumulatedEnergyToEps
        from device_current_status
        <where>
            is_deleted = 0
            and device_serial_number=#{queryCondition.deviceSerialNumber}
            and plant_id=#{queryCondition.plantId}
            <if test="queryCondition.startDateTime != null">
                <![CDATA[ and device_date_time >= #{queryCondition.startDateTime} ]]>
            </if>
            <if test="queryCondition.endDateTime != null">
                <![CDATA[ and device_date_time <= #{queryCondition.endDateTime} ]]>
            </if>
        </where>
    </select>

    <select id="inverterTodaySummary" resultType="com.alibaba.fastjson.JSONObject">
        select sum(ifnull(dcs.mppt1_power, 0) + ifnull(dcs.mppt2_power, 0) + ifnull(dcs.mppt3_power, 0) +
                   ifnull(dcs.mppt4_power, 0))          currentTotalPower,
               sum(ifnull(dcs.today_energy, 0))      as dailyPowerGeneration,
               sum(ifnull(dcs.today_load_energy, 0)) as dailyLoadEnergy
        from device_current_status dcs
        where dcs.is_deleted = 0
          and dcs.device_date_time >= DATE_FORMAT(now(), '%Y-%m-%d 00:00:00')
    </select>

    <select id="inverterAccumulateSummary" resultType="com.alibaba.fastjson.JSONObject">
        select sum(ifnull(dcs.total_energy, 0))               as accumulatedPowerGeneration,
               sum(ifnull(dcs.accumulated_energy_of_load, 0)) as accumulatedLoadEnergy
        from device_current_status dcs
        where dcs.is_deleted = 0
    </select>

    <select id="batteryTodaySummary" resultType="com.alibaba.fastjson.JSONObject">
        select
               sum(ifnull(dcs.battery_daily_charge_energy, 0))      as chargingEnergyToday,
               sum(ifnull(dcs.battery_daily_discharge_energy, 0)) as disChargingEnergyToday
        from device_current_status dcs
        where dcs.is_deleted = 0
          and dcs.device_date_time >= DATE_FORMAT(now(), '%Y-%m-%d 00:00:00')
    </select>

    <select id="batteryAccumulateSummary" resultType="com.alibaba.fastjson.JSONObject">
        select sum(ifnull(dcs.battery_accumulated_charge_energy, 0))    as accumulatedChargingEnergy,
               sum(ifnull(dcs.battery_accumulated_discharge_energy, 0)) as accumulatedDisChargingEnergy
        from device_current_status dcs
        where dcs.is_deleted = 0
    </select>

    <update id="batchDeleteLogicByPlantId">
        update device_current_status set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now() where
        plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchDeleteLogicByPlantIdAndSn">
        update device_current_status set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now() where
        plant_id = #{plantId} and device_serial_number=#{deviceSerialNumber}
    </update>
</mapper>

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmoperationrecord.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.skyworth.ess.alarmoperationrecord.vo.AlarmLogOperationRecordVO;
import org.skyworth.ess.alarmoperationrecord.wrapper.AlarmLogOperationRecordWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Map;

/**
 * 告警日志操作记录表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("alarmLogOperationRecord")
@Api(value = "告警日志操作记录表", tags = "告警日志操作记录表接口")
public class AlarmLogOperationRecordController extends BladeController {

	private final IAlarmLogOperationRecordService alarmLogOperationRecordService;

	private final IAlarmLogService alarmLogService;

	private final BladeRedis bladeRedis;


	/**
	 * 告警日志操作记录表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入alarmLogOperationRecord")
	public R<AlarmLogOperationRecordVO> detail(AlarmLogOperationRecordEntity alarmLogOperationRecord) {
		AlarmLogOperationRecordEntity detail =
			alarmLogOperationRecordService.getOne(Condition.getQueryWrapper(alarmLogOperationRecord));
		return R.data(AlarmLogOperationRecordWrapper.build().entityVO(detail));
	}

	/**
	 * 告警日志操作记录表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入alarmLogOperationRecord")
	public R<IPage<AlarmLogOperationRecordVO>> list(@ApiIgnore @RequestParam Map<String, Object> alarmLogOperationRecord, Query query) {
		query.setDescs("id");
		IPage<AlarmLogOperationRecordEntity> pages = alarmLogOperationRecordService.page(Condition.getPage(query),
			Condition.getQueryWrapper(alarmLogOperationRecord, AlarmLogOperationRecordEntity.class));
		return R.data(AlarmLogOperationRecordWrapper.build().pageVO(pages));
	}

	/**
	 * 告警日志操作记录表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入alarmLogOperationRecord")
	public R save(@Valid @RequestBody AlarmLogOperationRecordEntity alarmLogOperationRecord) {
		BladeUser user = getUser();
		String userName = user.getUserName();
		alarmLogOperationRecord.setCreateUserAccount(userName);
		alarmLogOperationRecord.setUpdateUserAccount(userName);
		// 保存操作日志
		alarmLogOperationRecordService.save(alarmLogOperationRecord);
		// 日志表id
		Long alarmLogId = alarmLogOperationRecord.getAlarmLogId();
		// 更新主表状态
		alarmLogService.update(
			Wrappers.<AlarmLogEntity>lambdaUpdate().set(AlarmLogEntity::getStatus,
				alarmLogOperationRecord.getStatus()).set(
				AlarmLogEntity::getUpdateTime, DateUtil.now()
			).set(AlarmLogEntity::getUpdateUser, user.getUserId()).set(AlarmLogEntity::getUpdateUserAccount, userName).eq(AlarmLogEntity::getId, alarmLogId));
		// 如果为已处理，则需要更新缓存状态
		if (BizConstant.NUMBER_ONE.equals(alarmLogOperationRecord.getStatus())) {
			AlarmLogEntity alarmLogEntity = alarmLogService.getById(alarmLogId);
			if (alarmLogEntity != null) {
				String key =
					alarmLogEntity.getPlantId() + ":" + alarmLogEntity.getSerialNumber() + ":" + alarmLogEntity.getExceptionType() + ":" + alarmLogEntity.getAddressCode() +
						":" + alarmLogEntity.getAlarmNumber();
				bladeRedis.del(key);
			}
		}
		return R.status(true);
	}


}

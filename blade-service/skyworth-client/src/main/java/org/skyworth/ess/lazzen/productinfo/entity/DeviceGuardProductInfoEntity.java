/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.productinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 产品信息 实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@TableName("lazzen_device_guard_product_info")
@ApiModel(value = "DeviceGuardProductInfo对象", description = "产品信息")
@EqualsAndHashCode(callSuper = true)
public class DeviceGuardProductInfoEntity extends TenantEntity {

	/**
	 * 设备网关唯一编号
	 */
	@ApiModelProperty(value = "设备网关唯一编号")
	private String gatewayUniqueNumber;
	/**
	 * 设备断路器地址64为第一位置
	 */
	@ApiModelProperty(value = "设备断路器地址64为第一位置")
	private String circuitBreakerAddress;
	/**
	 * 产品规格 0x0200
	 */
	@ApiModelProperty(value = "产品规格 0x0200")
	private String productStandard;
	/**
	 * 产品型号 0x0201
	 */
	@ApiModelProperty(value = "产品型号 0x0201")
	private String productModel;
	/**
	 * 壳架电流 0x0202
	 */
	@ApiModelProperty(value = "壳架电流 0x0202")
	private String frameCurrent;
	/**
	 * 额定电流 0x0203
	 */
	@ApiModelProperty(value = "额定电流 0x0203")
	private String ratedCurrent;
	/**
	 * 工作频率(电网频率) 0x0206
	 */
	@ApiModelProperty(value = "工作频率(电网频率) 0x0206")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal workFrequency;
	/**
	 * 产品极数 0x0209
	 */
	@ApiModelProperty(value = "产品极数 0x0209")
	private String productPoleNumber;
	/**
	 * 生产日期-年 0x020E
	 */
	@ApiModelProperty(value = "生产日期-年 0x020E")
	private String productDateYear;
	/**
	 * 生产日期-月日 0x020F
	 */
	@ApiModelProperty(value = "生产日期-月日 0x020F")
	private String productDateMonthDay;
	/**
	 * 产品序列号 0x0210 - 0x0214
	 */
	@ApiModelProperty(value = "产品序列号 0x0210 - 0x0214")
	private String productSn;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

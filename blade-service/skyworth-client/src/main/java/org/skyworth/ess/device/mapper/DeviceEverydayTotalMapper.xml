<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceEverydayTotalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceEverydayTotalResultMap" type="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="total_date" property="totalDate"/>
        <result column="today_energy" property="todayEnergy"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_load_energy" property="todayLoadEnergy"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectDeviceEverydayTotalPage" resultMap="deviceEverydayTotalResultMap">
        select *
        from device_everyday_total
        where is_deleted = 0
    </select>


    <select id="exportDeviceEverydayTotal" resultType="org.skyworth.ess.device.excel.DeviceEverydayTotalExcel">
        SELECT *
        FROM device_everyday_total ${ew.customSqlSegment}
    </select>

    <select id="appFromPvAndGrid" resultType="org.skyworth.ess.device.vo.DeviceEverydayTotalVO">
        select sum(ifnull( b.today_energy,0)) as sumTodayEnergy ,
        sum(ifnull(d.today_export_energy ,0)) as sumTodayExportEnergy,
        sum(ifnull(b.today_load_energy,0)) as sumTodayLoadEnergy ,
        sum(ifnull(b.daily_energy_to_eps,0)) as sumDailyEnergyToEps ,
        sum(ifnull(b.battery_daily_charge_energy ,0)) as sumBatteryDailyChargeEnergy,
        sum(ifnull(d.today_import_energy ,0)) as sumTodayImportEnergy
        from battery_everyday_total b inner join device_everyday_total d on b.plant_id =d.plant_id and
        b.device_serial_number =d.device_serial_number and d.is_deleted =0 and b.total_date = d.total_date
        where b.is_deleted =0 and b.plant_id = #{queryCondition.plantId}
        <if test="queryCondition.deviceSerialNumber != null">
            and b.device_serial_number=#{queryCondition.deviceSerialNumber}
        </if>
        <if test="queryCondition.startDateTime != null">
            <![CDATA[ and b.total_date >= STR_TO_DATE(#{queryCondition.startDateTime},'%Y-%m-%d')]]>
        </if>
        <if test="queryCondition.endDateTime != null">
            <![CDATA[ and b.total_date <= STR_TO_DATE(#{queryCondition.endDateTime},'%Y-%m-%d') ]]>
        </if>
    </select>

    <select id="queryDailyStatisticalDataOfInverters"
            resultType="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        select det.total_date,
        det.today_energy,
        det.today_import_energy,
        det.today_export_energy,
        det.today_load_energy,
        det.daily_energy_to_eps,
        bet.pvl_daily_generating_energy_sum,
        bet.generator_today_energy_sum ,
        bet.daily_energy_of_load_sum ,
        bet.daily_support_energy_sum_to_backup
        from device_everyday_total det
        inner join battery_everyday_total bet on det.device_serial_number =bet.device_serial_number and det.plant_id
        =bet.plant_id
        and det.total_date = bet.total_date
        where det.is_deleted = 0 and det.is_deleted = 0
        <if test="ew.plantId !=null">
            and det.plant_id = #{ew.plantId}
        </if>
        <if test="ew.deviceSerialNumber!=null and ew.deviceSerialNumber!=''">
            and det.device_serial_number = #{ew.deviceSerialNumber}
        </if>
        <if test="ew.startDateTime!=null">
            and det.total_date  <![CDATA[ >= ]]> STR_TO_DATE(#{ew.startDateTime}, '%Y-%m-%d')
        </if>
        <if test="ew.endDateTime!=null">
            and det.total_date <![CDATA[ <= ]]> STR_TO_DATE(#{ew.endDateTime}, '%Y-%m-%d')
        </if>
    </select>

    <select id="queryMonthlyStatisticalDataOfInverters" resultType="org.skyworth.ess.device.entity.DeviceEverydayTotalEntity">
        select DATE_FORMAT(det.total_date,'%Y-%m') as totalDate,
        SUM(det.today_energy) as todayEnergy,
        sum(det.today_import_energy) as todayImportEnergy,
        sum(det.today_export_energy) as todayExportEnergy,
        sum(det.today_load_energy) as todayLoadEnergy,
        sum(det.daily_energy_to_eps) as dailyEnergyToEps,
        sum(bet.pvl_daily_generating_energy_sum) as pvlDailyGeneratingEnergySum,
        sum(bet.generator_today_energy_sum) as generatorTodayEnergySum,
        sum(bet.daily_energy_of_load_sum) as dailyEnergyOfLoadSum,
        sum(bet.daily_support_energy_sum_to_backup) as dailySupportEnergySumToBackup
        from device_everyday_total det
        inner join battery_everyday_total bet on det.device_serial_number =bet.device_serial_number and det.plant_id=bet.plant_id and det.total_date =bet.total_date
        where det.is_deleted = 0 and det.is_deleted = 0
        <if test="ew.plantId !=null">
            and det.plant_id = #{ew.plantId}
        </if>
        <if test="ew.deviceSerialNumber!=null and ew.deviceSerialNumber!=''">
            and det.device_serial_number = #{ew.deviceSerialNumber}
        </if>
        <if test="ew.startDateTime!=null">
            and det.total_date  <![CDATA[ >= ]]> STR_TO_DATE(#{ew.startDateTime}, '%Y-%m-%d')
        </if>
        <if test="ew.endDateTime!=null">
            and det.total_date <![CDATA[ <= ]]> STR_TO_DATE(#{ew.endDateTime}, '%Y-%m-%d')
        </if>
        group by totalDate
    </select>
</mapper>

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.service.IAppLazzenGatewayService;
import org.skyworth.ess.jobhandler.LazzenDeviceGuardXxlJob;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantCurrentStatusVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantOverviewVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantReportQueryVO;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantVO;
import org.skyworth.ess.lazzen.gatewayplant.wrapper.GatewayPlantWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 良信网关站点关系表 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/gatewayPlant")
@Api(value = "良信网关站点关系表", tags = "良信网关站点关系表接口")
public class GatewayPlantController extends BladeController {

	private final IGatewayPlantService gatewayPlantService;
	private final LazzenDeviceGuardXxlJob lazzenDeviceGuardXxlJob;
	private final IAppLazzenGatewayService appLazzenGatewayService;

	/**
	 * 良信网关站点关系表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<GatewayPlantVO> detail(GatewayPlantEntity gatewayPlant) {
		GatewayPlantVO detail = gatewayPlantService.detail(gatewayPlant);
		return R.data(detail);
	}

	/**
	 * 良信网关站点关系表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<IPage<GatewayPlantVO>> list(@ApiIgnore @RequestParam Map<String, Object> gatewayPlant, Query query) {
		IPage<GatewayPlantEntity> pages = gatewayPlantService.page(Condition.getPage(query),
			Condition.getQueryWrapper(gatewayPlant, GatewayPlantEntity.class));
		return R.data(GatewayPlantWrapper.build().pageVO(pages));
	}

	/**
	 * 良信网关站点关系表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<IPage<GatewayPlantVO>> page(GatewayPlantVO gatewayPlant, Query query) {
		IPage<GatewayPlantVO> pages = gatewayPlantService.selectGatewayPlantPage(Condition.getPage(query),
			gatewayPlant);
		return R.data(pages);
	}

	/**
	 * 良信网关站点关系表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:update')")
	public R save(@Valid @RequestBody GatewayPlantEntity gatewayPlant) {
		return R.status(gatewayPlantService.save(gatewayPlant));
	}

	/**
	 * 良信网关站点关系表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:update')")
	public R update(@Valid @RequestBody GatewayPlantEntity gatewayPlant) {
		return R.status(gatewayPlantService.updateById(gatewayPlant));
	}

	/**
	 * 良信网关站点关系表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入gatewayPlant")
	//	@PreAuth("hasPermission('client:guardianPlant:update')")
	public R submit(@Valid @RequestBody GatewayPlantEntity gatewayPlant) {
		return R.status(gatewayPlantService.saveOrUpdate(gatewayPlant));
	}

	/**
	 * 良信网关站点关系表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	//	@PreAuth("hasPermission('client:guardianPlant:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(gatewayPlantService.deleteLogic(Func.toLongList(ids)));
	}

	@GetMapping("/heartBeatGuardAndBackUp")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "心跳", notes = "心跳")
	public R heartBeatGuardAndBackUp() {
		lazzenDeviceGuardXxlJob.heartBeatGuardAndBackUp(null);
		return R.status(true);
	}


	/**
	 * 安全卫士信息总览
	 */
	@PostMapping("/overview")
	@ApiOperationSupport(order = 0)
	@ApiOperation(value = "总览", notes = "传入guardianPlantVO")
//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<GatewayPlantOverviewVO> getOverview(@Valid @RequestBody GatewayPlantVO gatewayPlantVO) {
		return R.data(gatewayPlantService.getOverview(gatewayPlantVO));
	}

	/**
	 * 安全卫士信息 分页
	 */
	@PostMapping("/list/{pageSize}/{current}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入GuardianPlantVO")
//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<IPage<GatewayPlantVO>> list(@Valid @RequestBody GatewayPlantVO gatewayPlantVO,
										 @ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
										 @ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
		IPage<GatewayPlantVO> pages = gatewayPlantService.page(new Query().setCurrent(current).setSize(pageSize), gatewayPlantVO);
		return R.data(pages);
	}


	/**
	 * 安全卫士信息 当前状态
	 */
	@GetMapping("/detail/currentStatus")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "当前状态", notes = "gatewayPlantEntity")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<GatewayPlantCurrentStatusVO> detailCurrentStatus(GatewayPlantEntity gatewayPlantEntity) {
		GatewayPlantCurrentStatusVO gatewayPlantCurrentStatusVO = appLazzenGatewayService.getStatusAndDeviceInfo(gatewayPlantEntity);
		return R.data(gatewayPlantCurrentStatusVO);
	}


	/**
	 * 状态曲线
	 *
	 * @param gatewayPlantReportQueryVO
	 * @return
	 */
	@GetMapping("/detail/statusStat")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "详情-状态曲线", notes = "传入guardianPlantReportQueryVO")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<Map<String, List<Object>>> statusStat(GatewayPlantReportQueryVO gatewayPlantReportQueryVO) {
		return R.data(gatewayPlantService.selectStatusReport(gatewayPlantReportQueryVO));
	}

	/**
	 * 状态曲线 导出
	 *
	 * @param gatewayPlantReportQueryVO
	 * @return
	 */
	@GetMapping("/detail/statusStat/export")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "详情-状态曲线导出", notes = "传入guardianPlantReportQueryVO")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public void statusStatExport(GatewayPlantReportQueryVO gatewayPlantReportQueryVO, HttpServletResponse response) {
		gatewayPlantService.selectStatusReportExport(gatewayPlantReportQueryVO, response);
	}
}

package org.skyworth.ess.balconypv.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.vo.EnergySystemVO;
import java.util.Objects;

/**
 * 能源系统 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public class EnergySystemWrapper extends BaseEntityWrapper<EnergySystemEntity, EnergySystemVO>  {

	public static EnergySystemWrapper build() {
		return new EnergySystemWrapper();
 	}

	@Override
	public EnergySystemVO entityVO(EnergySystemEntity energySystem) {
		EnergySystemVO energySystemVO = Objects.requireNonNull(BeanUtil.copy(energySystem, EnergySystemVO.class));

		//User createUser = UserCache.getUser(energySystem.getCreateUser());
		//User updateUser = UserCache.getUser(energySystem.getUpdateUser());
		//energySystemVO.setCreateUserName(createUser.getName());
		//energySystemVO.setUpdateUserName(updateUser.getName());

		return energySystemVO;
	}


}

package org.skyworth.ess.lazzen.analysisdata.service.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * 消息类型码定义 : DataHead 为 fe 时 ，Saddr 对应的值
 */
@Getter
public enum Saddr4DataHeadToFeEnum {
    code_0000("0000","请求校时"),
    code_0001("0001","心跳"),
    code_0002("0002","网关上线"),
    code_6400("6400","从机个数及地址");

    private final String code;
    private final String comment;
    Saddr4DataHeadToFeEnum(String code, String comment) {
        this.code = code;
        this.comment = comment;
    }
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.remotecontrolinfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.lazzen.remotecontrolinfo.entity.DeviceGuardRemoteControlInfoEntity;
import org.skyworth.ess.lazzen.remotecontrolinfo.vo.DeviceGuardRemoteControlInfoVO;
import java.util.Objects;

/**
 * 遥感信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public class DeviceGuardRemoteControlInfoWrapper extends BaseEntityWrapper<DeviceGuardRemoteControlInfoEntity, DeviceGuardRemoteControlInfoVO>  {

	public static DeviceGuardRemoteControlInfoWrapper build() {
		return new DeviceGuardRemoteControlInfoWrapper();
 	}

	@Override
	public DeviceGuardRemoteControlInfoVO entityVO(DeviceGuardRemoteControlInfoEntity deviceGuardRemoteControlInfo) {
		DeviceGuardRemoteControlInfoVO deviceGuardRemoteControlInfoVO = Objects.requireNonNull(BeanUtil.copy(deviceGuardRemoteControlInfo, DeviceGuardRemoteControlInfoVO.class));

		//User createUser = UserCache.getUser(deviceGuardRemoteControlInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceGuardRemoteControlInfo.getUpdateUser());
		//deviceGuardRemoteControlInfoVO.setCreateUserName(createUser.getName());
		//deviceGuardRemoteControlInfoVO.setUpdateUserName(updateUser.getName());

		return deviceGuardRemoteControlInfoVO;
	}


}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.excel.AddressMapDefinitionExcel;
import org.skyworth.ess.addressmap.mapper.AddressMapDefinitionMapper;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.addressmap.vo.AddressMapDefinitionVO;
import org.skyworth.ess.device.entity.Constants;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 物理地址映射属性名称 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Service
public class AddressMapDefinitionServiceImpl extends BaseServiceImpl<AddressMapDefinitionMapper, AddressMapDefinitionEntity> implements IAddressMapDefinitionService {


	public static final String ID = "id";
	public static final String TENANT_ID = "tenantId";
	public static final String COMPANY = "company";
	public static final String MODBUS_PROTOCOL_VERSION = "modbusProtocolVersion";
	public static final String DEFINITION = "definition";
	public static final String DATA_TYPE = "dataType";
	public static final String UNIT = "unit";
	public static final String LENGTH = "length";
	public static final String DECIMAL_ADDRESS = "decimalAddress";

	@Override
	public IPage<AddressMapDefinitionVO> selectAddressMapDefinitionPage(IPage<AddressMapDefinitionVO> page, AddressMapDefinitionVO addressMapDefinition) {
		return page.setRecords(baseMapper.selectAddressMapDefinitionPage(page, addressMapDefinition));
	}


	@Override
	public List<AddressMapDefinitionExcel> exportAddressMapDefinition(Wrapper<AddressMapDefinitionEntity> queryWrapper) {
		List<AddressMapDefinitionExcel> addressMapDefinitionList = baseMapper.exportAddressMapDefinition(queryWrapper);
		return addressMapDefinitionList;
	}





	@Override
	@Cached(name="allAddressMapDefinitionByAddress",  expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	public Map<String, Map<String, String>> getAllAddressMapByAddress(Integer beginAddressNum, Integer endAddressNum, String modbusProtocolVersion) {
		HashMap<String, Object> addressMapDefinition = new HashMap<>();
		QueryWrapper<AddressMapDefinitionEntity> queryWrapper = Condition.getQueryWrapper(addressMapDefinition, AddressMapDefinitionEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(AddressMapDefinition::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(AddressMapDefinitionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.eq(AddressMapDefinitionEntity::getModbusProtocolVersion,modbusProtocolVersion)
			.ge(AddressMapDefinitionEntity::getDecimalAddress,beginAddressNum)
			.le(AddressMapDefinitionEntity::getDecimalAddress,endAddressNum)
			.orderByAsc(AddressMapDefinitionEntity::getDecimalAddress);
		List<AddressMapDefinitionEntity> list = this.list(queryWrapper);

		JSONArray array = JSON.parseArray(JSON.toJSONString(list));

		Map<String, Map<String, String>> resultMap = array.stream()
			.map(JSONObject.class::cast)
			.collect(Collectors.toMap(
				obj -> obj.getString("address"),
				obj -> {
					Map<String, String> innerMap = new HashMap<>();
					innerMap.put(ID, String.valueOf(obj.getLong(ID)));
					innerMap.put(TENANT_ID, obj.getString(TENANT_ID));
					innerMap.put(COMPANY, obj.getString(COMPANY));
					innerMap.put(MODBUS_PROTOCOL_VERSION, obj.getString(MODBUS_PROTOCOL_VERSION));
					innerMap.put(DEFINITION, obj.getString(DEFINITION));
					innerMap.put("setItemDefinition", obj.getString("setItemDefinition"));
					innerMap.put(DATA_TYPE, obj.getString(DATA_TYPE));
					innerMap.put(UNIT, obj.getString(UNIT));
					innerMap.put(LENGTH, String.valueOf(obj.getInteger(LENGTH)));
					innerMap.put(DECIMAL_ADDRESS, String.valueOf(obj.getInteger(DECIMAL_ADDRESS)));
					return innerMap;
				}
			));

		return new TreeMap<>(resultMap);
	}

	@Override
	public String addImportExcel(List<AddressMapDefinitionExcel> data, Boolean aBoolean) {
		// 导入的list转map
		Map<String, AddressMapDefinitionExcel> addImportDataMap = data.stream()
			.collect(Collectors.toMap(
				entry -> "[Company:" + entry.getCompany() + " Type:" + entry.getDataType() + " ModbusProtocolVersion:" + entry.getModbusProtocolVersion() + " Address:" + entry.getAddress() + "]",
				entry -> entry
			));

		// 数据库的list转map
		List<AddressMapDefinitionEntity> entityList = baseMapper.selectList(new QueryWrapper<>());
		Map<String, AddressMapDefinitionEntity> DbDataMap = entityList.stream()
			.collect(Collectors.toMap(
				entry -> "[Company:" + entry.getCompany() + " Type:" + entry.getDataType() + " ModbusProtocolVersion:" + entry.getModbusProtocolVersion() + " Address:" + entry.getAddress() + "]",
				entry -> entry
			));

		// 对比map找出key交集
		Set<String> commonKeys = new HashSet<>(DbDataMap.keySet());
		commonKeys.retainAll(addImportDataMap.keySet());
		boolean hasCommonKeys = !commonKeys.isEmpty();

		// ②若导入数据中，这4项厂商+协议类型+版本+物理地址，原数据库已存在，则报错，提示走修改
		if(hasCommonKeys) {

			String join = Joiner.on(",").skipNulls().join(commonKeys);
			return join + " had exists, please use modify import.";
		}

		BladeUser user = AuthUtil.getUser();
		List<AddressMapDefinitionEntity> insert = new ArrayList<>();
		data.forEach(userExcel -> {
			AddressMapDefinitionEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, AddressMapDefinitionEntity.class));
			entity.setCreateUserAccount(user.getAccount());
			buildDecimalAddress(entity);
			entity.setSystemRequiredConfiguration(Constants.DIGITAL_ZERO);
			insert.add(entity);
		});
		this.saveOrUpdateBatch(insert);
		return BizConstant.IMPORT_SUCCESS;
	}

	@Override
	public String updateImportExcel(List<AddressMapDefinitionExcel> data, Boolean aBoolean) {
		// 导入的list转map
		Map<String, AddressMapDefinitionExcel> updateImportDataMap = data.stream()
			.collect(Collectors.toMap(
				entry -> "[Company:" + entry.getCompany() + " Type:" + entry.getDataType() + " ModbusProtocolVersion:" + entry.getModbusProtocolVersion() + " Address:" + entry.getAddress() + "]",
				entry -> entry
			));

		// 数据库的list转map
		List<AddressMapDefinitionEntity> entityList = baseMapper.selectList(new QueryWrapper<>());
		Map<String, AddressMapDefinitionEntity> DbDataMap = entityList.stream()
			.collect(Collectors.toMap(
				entry -> "[Company:" + entry.getCompany() + " Type:" + entry.getDataType() + " ModbusProtocolVersion:" + entry.getModbusProtocolVersion() + " Address:" + entry.getAddress() + "]",
				entry -> entry
			));

		// ③若导入表格出现原数据中不存在的厂商+协议类型+版本+物理地址，则报错，提示走新增
		Set<String> keysNotPresent = updateImportDataMap.keySet().stream()
			.filter(key -> !DbDataMap.containsKey(key))
			.collect(Collectors.toSet());

		if (!keysNotPresent.isEmpty()){
			String join = Joiner.on(",").skipNulls().join(keysNotPresent);
			return join + " had not exists, please use add import.";
		}

		// 如果要修改的数据中，存在是系统默认的数据，则提示系统默认的数据不能修改
		Set<String> keysSystemRequiredConfiguration = DbDataMap.keySet().stream()
			.filter(updateImportDataMap::containsKey)
			.filter(key -> DbDataMap.get(key).getSystemRequiredConfiguration().equals(Constants.DIGITAL_ONE))
			.collect(Collectors.toSet());

		if (!keysSystemRequiredConfiguration.isEmpty()){
			String join = Joiner.on(",").skipNulls().join(keysSystemRequiredConfiguration);
			return join + "  are required by the system and cannot be modified.";
		}


		BladeUser user = AuthUtil.getUser();
		List<AddressMapDefinitionEntity> update = new ArrayList<>();
		data.forEach(userExcel -> {
			AddressMapDefinitionEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, AddressMapDefinitionEntity.class));
			entity.setUpdateUserAccount(user.getAccount());
			buildDecimalAddress(entity);
			update.add(entity);
		});
		baseMapper.updateBatchByCondition(update);
		return BizConstant.IMPORT_SUCCESS;
	}

	@Override
	public boolean saveAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition) {
		buildDecimalAddress(addressMapDefinition);
		return this.save(addressMapDefinition);
	}

	@Override
	public boolean updateAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition) {
		buildDecimalAddress(addressMapDefinition);
		return this.updateById(addressMapDefinition);
	}

	@Override
	public boolean saveOrUpdateAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition) {
		buildDecimalAddress(addressMapDefinition);
		return this.saveOrUpdate(addressMapDefinition);
	}

	@Override
	@Cached(name="allAddressMap4Definition",  expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	public Map<String, AddressMapDefinitionEntity> getAllAddressMap4Definition() {
		Map<String, Object> addressMapDefinition = new HashMap<>();
		QueryWrapper<AddressMapDefinitionEntity> queryWrapper = Condition.getQueryWrapper(addressMapDefinition, AddressMapDefinitionEntity.class);
		queryWrapper.lambda().eq(AddressMapDefinitionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED)
			.orderByAsc(AddressMapDefinitionEntity::getDecimalAddress);
		List<AddressMapDefinitionEntity> list = this.list(queryWrapper);

		Map<String, AddressMapDefinitionEntity> defMap = new HashMap<>(list.size());
		for (AddressMapDefinitionEntity addressMapDefinitionEntity : list) {
			if (ObjectUtil.isNull(addressMapDefinitionEntity.getSetItemDefinition())){
				defMap.put(addressMapDefinitionEntity.getDefinition(),addressMapDefinitionEntity);
			}else {
				defMap.put(addressMapDefinitionEntity.getSetItemDefinition(),addressMapDefinitionEntity);
			}
		}

		return defMap;
	}

	@Override
	@Cached(name="getMappingBySetItem",  expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	public List<JSONObject> getMappingBySetItem(AddressMapDefinitionVO addressMapDefinition) {
		List<AddressMapDefinitionEntity> list = this.list(Condition.getQueryWrapper(addressMapDefinition));
		return list.stream()
			.map(entity -> {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put(DEFINITION, entity.getDefinition());
				jsonObject.put("setItemDefinition", entity.getSetItemDefinition());
				jsonObject.put("address", entity.getAddress());
				return jsonObject;
			})
			.collect(Collectors.toList());
	}

	private void buildDecimalAddress(AddressMapDefinitionEntity addressMapDefinition) {
		String address = addressMapDefinition.getAddress();
		if (CharSequenceUtil.isNotEmpty(address)){
			int hexToDecimal = BinaryToHexUtils.hexToDecimal(address);
			addressMapDefinition.setDecimalAddress(hexToDecimal);
		}
		String definition = addressMapDefinition.getDefinition();
		String camelCase = CommonUtil.toCamelCase(definition);
		addressMapDefinition.setSetItemDefinition(camelCase);
	}
}

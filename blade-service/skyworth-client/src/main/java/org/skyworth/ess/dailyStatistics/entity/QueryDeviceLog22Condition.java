package org.skyworth.ess.dailyStatistics.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 储能列表查询条件
 *
 * @description
 * @author: SDT50545
 * @since: 2023-09-14 17:58
 **/
@Data
@ApiModel(value = "储能列表查询条件", description = "储能列表查询条件")
public class QueryDeviceLog22Condition implements Serializable {
	private static final long serialVersionUID = -1;

	@ApiModelProperty(value = "站点id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "储能SN")
	private String batterySerialNumber;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;

	@ApiModelProperty(value = "智能能量变换器SN")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "日期")
	private String date;

	@ApiModelProperty(value = "状态曲线查询模块：batteryPower/overallVoltage/batterySOC/cellVoltage/cellTemperature/parallel_batteryPower")
	private String queryPowerType;
	// 是否并机
	private String isParallelMode;
	private String beginTime;
	private String endTime;

	@ApiModelProperty("电池归属1,或者归属2,默认1")
	private Integer batteryEnergyStorageNumber = 1;

}

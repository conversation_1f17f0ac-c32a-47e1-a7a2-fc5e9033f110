package org.skyworth.ess.battery.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-14 18:35
 **/
@Data
@ApiModel(value = "储能列表查询展示", description = "储能列表查询展示")
public class BatteryPageResultVO implements Serializable {
	private static final long serialVersionUID = -42053547556775997L;

	@ApiModelProperty(value = "plantId")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "智能能量变换器SN")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "站点名称")
	private String plantName;

	@ApiModelProperty(value = "用户账号")
	private String createUserAccount;

	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty("储能数量")
	private Integer batteryNumber;

	@ApiModelProperty("运维团队id")
	private Long operationCompanyId;

	@ApiModelProperty("运维人员id")
	private Long operationUserId;

	@ApiModelProperty("运维团队")
	private String operationCompanyName;

	@ApiModelProperty("运维人员")
	private String operationUserName;

	@ApiModelProperty("区号")
	private String phoneDiallingCode;

	@ApiModelProperty("手机号")
	private String phone;

	@ApiModelProperty("真实名称")
	private String realName;

	@ApiModelProperty("额定容量")
	private String ratedBatteryCapacity;

	@ApiModelProperty(value = "储能SN")
	private String batterySerialNumber;

	@ApiModelProperty(value = "用户id")
	private Long createUser;

	@ApiModelProperty(value = "储能容量")
	private BigDecimal batteryPower;

	@ApiModelProperty(value = "电池2储能容量")
	private BigDecimal batteryPower2;

	/**是否存在用户类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm;

	/**是否存在代理类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在代理类告警(0/1:不存在/存在)")
	private Integer existAgentTypeAlarm;


	// 1为储能1,2为储能2...
	private Integer batteryEnergyStorageNumber;
}

package org.skyworth.ess.guardian.currentstatus.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.currentstatus.excel.GuardianCurrentStatusExcel;
import org.skyworth.ess.guardian.currentstatus.wrapper.GuardianCurrentStatusWrapper;
import org.skyworth.ess.guardian.currentstatus.service.IGuardianCurrentStatusService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士当前状态 控制器
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianCurrentStatus")
@Api(value = "安全卫士当前状态", tags = "安全卫士当前状态接口")
public class GuardianCurrentStatusController extends BladeController {

	private final IGuardianCurrentStatusService GuardianCurrentStatusService;

	/**
	 * 安全卫士当前状态 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianCurrentStatus")
	public R<GuardianCurrentStatusVO> detail(GuardianCurrentStatusEntity GuardianCurrentStatus) {
		GuardianCurrentStatusEntity detail = GuardianCurrentStatusService.getOne(Condition.getQueryWrapper(GuardianCurrentStatus));
		return R.data(GuardianCurrentStatusWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士当前状态 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianCurrentStatus")
	public R<IPage<GuardianCurrentStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianCurrentStatus, Query query) {
		IPage<GuardianCurrentStatusEntity> pages = GuardianCurrentStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianCurrentStatus, GuardianCurrentStatusEntity.class));
		return R.data(GuardianCurrentStatusWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士当前状态 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianCurrentStatus")
	public R<IPage<GuardianCurrentStatusVO>> page(GuardianCurrentStatusVO GuardianCurrentStatus, Query query) {
		IPage<GuardianCurrentStatusVO> pages = GuardianCurrentStatusService.selectGuardianCurrentStatusPage(Condition.getPage(query), GuardianCurrentStatus);
		return R.data(pages);
	}

	/**
	 * 安全卫士当前状态 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianCurrentStatus")
	public R save(@Valid @RequestBody GuardianCurrentStatusEntity GuardianCurrentStatus) {
		return R.status(GuardianCurrentStatusService.save(GuardianCurrentStatus));
	}

	/**
	 * 安全卫士当前状态 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianCurrentStatus")
	public R update(@Valid @RequestBody GuardianCurrentStatusEntity GuardianCurrentStatus) {
		return R.status(GuardianCurrentStatusService.updateById(GuardianCurrentStatus));
	}

	/**
	 * 安全卫士当前状态 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianCurrentStatus")
	public R submit(@Valid @RequestBody GuardianCurrentStatusEntity GuardianCurrentStatus) {
		return R.status(GuardianCurrentStatusService.saveOrUpdate(GuardianCurrentStatus));
	}

	/**
	 * 安全卫士当前状态 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianCurrentStatusService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianCurrentStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianCurrentStatus")
	public void exportGuardianCurrentStatus(@ApiIgnore @RequestParam Map<String, Object> GuardianCurrentStatus, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianCurrentStatusEntity> queryWrapper = Condition.getQueryWrapper(GuardianCurrentStatus, GuardianCurrentStatusEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianCurrentStatus::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianCurrentStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianCurrentStatusExcel> list = GuardianCurrentStatusService.exportGuardianCurrentStatus(queryWrapper);
		ExcelUtil.export(response, "安全卫士当前状态数据" + DateUtil.time(), "安全卫士当前状态数据表", list, GuardianCurrentStatusExcel.class);
	}

}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.skyworth.ess.app.vo.AppBatteryCurrentStatusInfo;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryCurrentStatusVO;

import java.util.List;

/**
 * 储能当前状态 服务类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface IBatteryCurrentStatusService extends IService<BatteryCurrentStatusEntity> {
	/**
	 * 根据plantId查询当前储能状态
	 *
	 * @param queryCondition 入参
	 * @return BatteryCurrentStatusEntity
	 * <AUTHOR>
	 * @since 2023/9/16 11:00
	 **/
	BatteryCurrentStatusVO view(QueryCondition queryCondition);

	BatteryCurrentStatusEntity queryIsDelete(Long plantId, String deviceSerialNumber);

	List<AppBatteryCurrentStatusInfo> queryAppBatteryInfo(Long plantId, String deviceSerialNumber);

	List<AppBatteryCurrentStatusInfo> queryAppBatteryInfoV2(Long plantId, String deviceSerialNumber);

	List<BatteryCurrentStatusEntity> batchQueryAppBatteryCurrentStatus(List<QueryCondition> list);

    int batchDeleteLogicByPlantId(List<Long> longList, String account);

	int batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account);
}

/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.vo;

import io.swagger.annotations.ApiModelProperty;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 安全卫士对应站点 视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GuardianPlantVO extends GuardianPlantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 安全卫士状态，在线、离线、故障、告警
	 */
	@ApiModelProperty(value = "安全卫士状态名称，在线、离线、故障、告警")
	private String securityGuardStatusName;
	/**
	 * 站点名称
	 */
	@ApiModelProperty(value = "站点名称")
	private String plantName;
	/**
	 * 国家
	 */
	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;
	/**
	 * 详细地址
	 */
	@ApiModelProperty(value = "详细地址")
	private String detailAddress;

	/**
	 * 国家名称
	 */
	@ApiModelProperty(value = "国家名称")
	private String countryName;
	/**
	 * 一级行政区域名称
	 */
	@ApiModelProperty(value = "一级行政区域名称")
	private String provinceName;
	/**
	 * 二级行政区域名称
	 */
	@ApiModelProperty(value = "二级行政区域名称")
	private String cityName;
	/**
	 * 三级行政区域名称
	 */
	@ApiModelProperty(value = "三级行政区域名称")
	private String countyName;

	/**
	 * 完整地址
	 */
	@ApiModelProperty(value = "完整地址")
	private String address;

	/**
	 * 电话区号
	 */
	@ApiModelProperty(value = "电话区号")
	private String phoneDiallingCode;

	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long userId;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String userName;
	/**
	 * 用户手机号
	 */
	@ApiModelProperty(value = "用户手机号")
	private String userPhone;

	/**
	 * 用户的ids
	 */
	@ApiModelProperty(value = "用户的ids")
	private List<Long> userIds;

	/**
	 * 运维人员id
	 *
	 */
	@ApiModelProperty(value = "运维人员id")
	private Long agentUserId;

	/**
	 * 运维人员
	 * */
	@ApiModelProperty(value = "运维人员")
	private String agentUserName;

	/**
	 * 运维团队id
	 */
	@ApiModelProperty(value = "运维团队id")
	private Long agentCompanyId;

	/**
	 * 运维团队名称
	 */
	@ApiModelProperty(value = "运维团队名称")
	private String agentCompanyName;

	/**
	 * 运维人员的ids
	 */
	private List<Long> agentUserIds;

	/**
	 * 运维团队的ids
	 */
	private List<Long> agentCompanyIds;

}

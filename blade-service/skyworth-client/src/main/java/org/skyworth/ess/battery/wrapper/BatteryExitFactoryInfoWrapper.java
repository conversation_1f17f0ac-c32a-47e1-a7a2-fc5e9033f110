/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.wrapper;

import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 储能出厂信息表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public class BatteryExitFactoryInfoWrapper extends BaseEntityWrapper<BatteryExitFactoryInfoEntity, BatteryExitFactoryInfoVO>  {

	public static BatteryExitFactoryInfoWrapper build() {
		return new BatteryExitFactoryInfoWrapper();
 	}

	@Override
	public BatteryExitFactoryInfoVO entityVO(BatteryExitFactoryInfoEntity BatteryExitFactoryInfo) {
		if (BatteryExitFactoryInfo == null) {
			return null;
		}
		BatteryExitFactoryInfoVO BatteryExitFactoryInfoVO = Objects.requireNonNull(BeanUtil.copy(BatteryExitFactoryInfo, BatteryExitFactoryInfoVO.class));

		//User createUser = UserCache.getUser(BatteryExitFactoryInfo.getCreateUser());
		//User updateUser = UserCache.getUser(BatteryExitFactoryInfo.getUpdateUser());
		//BatteryExitFactoryInfoVO.setCreateUserName(createUser.getName());
		//BatteryExitFactoryInfoVO.setUpdateUserName(updateUser.getName());

		return BatteryExitFactoryInfoVO;
	}


}

package org.skyworth.ess.setItem.controller;

import lombok.RequiredArgsConstructor;
import org.skyworth.ess.setItem.excel.SetItemConfigExcel;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.core.excel.support.ExcelImporter;

import java.util.List;

/**
 * <AUTHOR> - x<PERSON>hongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/4/2 15:04:29
 */
@RequiredArgsConstructor
public class SetItemConfigServiceImporter implements ExcelImporter<SetItemConfigExcel> {
	private final ISetItemConfigService service;
	private final Boolean isCovered;

	@Override
	public void save(List<SetItemConfigExcel> data) {
		service.importExcel(data, isCovered);
	}
}

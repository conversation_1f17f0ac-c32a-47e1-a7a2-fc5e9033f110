<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.messagepushrecord.mapper.MessagePushRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="messagePushRecordResultMap" type="org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity">
        <result column="id" property="id"/>
        <result column="business_id" property="businessId"/>
        <result column="message_type" property="messageType"/>
        <result column="device_type" property="deviceType"/>
        <result column="message_recipients" property="messageRecipients"/>
        <result column="message_content" property="messageContent"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectMessagePushRecordPage" resultMap="messagePushRecordResultMap">
        select * from message_push_record where is_deleted = 0
    </select>


    <select id="exportMessagePushRecord" resultType="org.skyworth.ess.messagepushrecord.excel.MessagePushRecordExcel">
        SELECT * FROM message_push_record ${ew.customSqlSegment}
    </select>

</mapper>

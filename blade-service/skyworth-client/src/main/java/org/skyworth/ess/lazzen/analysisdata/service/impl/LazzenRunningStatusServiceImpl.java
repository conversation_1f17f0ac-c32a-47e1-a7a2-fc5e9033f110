package org.skyworth.ess.lazzen.analysisdata.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.alarmlog.service.IAlarmLogService;
import org.springblade.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * 运行状态
 */
@Service("lazzenRunningStatusServiceImpl")
@Slf4j
public class LazzenRunningStatusServiceImpl extends LazzenAbstractService {
	@Autowired
	private IAlarmLogService alarmLogService;

	@Override
	public void saveData(List<JSONObject> listMap) {
		if (CollectionUtils.isNullOrEmpty(listMap)) {
			return;
		}
		alarmLogService.batchInsertLazzenAlarm(listMap);
		log.info("lazzenRunningStatusServiceImpl save data : {}", listMap);
	}
}

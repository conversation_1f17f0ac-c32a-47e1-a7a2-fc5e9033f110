package org.skyworth.ess.lazzen.analysisdata.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.lazzen.analysisdata.service.constant.MegTag4DataHeadToFdEnum;
import org.skyworth.ess.lazzen.analysisdata.service.constant.Saddr4DataHeadToFeEnum;
import org.skyworth.ess.lazzen.analysisdata.service.impl.*;
import org.skyworth.ess.lazzen.analysisdata.service.constant.DataHeadEnum;
import org.skyworth.ess.lazzen.analysisdata.service.LazzenService;
import org.skyworth.ess.kafka.common.KafkaTopicContent;
import org.skyworth.ess.kafka.common.TopicProperty;
import org.skyworth.ess.kafka.consumer.KafkaConsumerService;
import org.skyworth.ess.util.LazzenDataCrcUtil;
import org.skyworth.ess.vo.LazzenConsumerDataVO;
import org.skyworth.ess.vo.LazzenConsumerVO;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LazzenkafkaCommonConsumer implements KafkaConsumerService<JSONObject> {
	@Autowired
	LazzenServiceFactory lazzenServiceFactory;
	@Autowired
	IAddressMapDefinitionService addressMapDefinitionServiceImpl;
	@TopicProperty(topicName = CommonConstant.LAZZEN_KAFKA_COMMON_CONSUMER_TOPIC, key = CommonConstant.LAZZEN_COMMON_CONSUMER_KEY,
		groupId = CommonConstant.LAZZEN_COMMON_CONSUMER_GROUP_ID)
	@Override
	public boolean consumer(List<KafkaTopicContent<JSONObject>> topiContent) {
		log.info("kafka consumer : {} ",topiContent);
		if(CollectionUtil.isEmpty(topiContent)) {
			log.info("lazzen kafka topic content is empty");
			return true;
		}
//		List<KafkaTopicContent<JSONObject>> testTopiContent = this.getTestContent();
		// 将数据分类并映射到不同的 bean上
		Map<LazzenService, List<LazzenConsumerDataVO>> map = this.getLazzenServiceListMap(topiContent);
		if (map == null) {
			return false;
		}
		try {
			// 获取所有地址映射
			Map<String, List<AddressMapDefinitionEntity>> dbAllAddressInfoMap = this.getDbAllAddressInfoMap();
			for (Map.Entry<LazzenService, List<LazzenConsumerDataVO>> entry : map.entrySet()) {
				LazzenService lazzenServiceImpl = entry.getKey();
				List<LazzenConsumerDataVO> valueList = entry.getValue();
				List<JSONObject> dbFieldList = new ArrayList<>();
				for (LazzenConsumerDataVO vo : valueList) {
					// 解析通用地址
					JSONObject jsonObject = lazzenServiceImpl.analysisData(vo, dbAllAddressInfoMap);
					dbFieldList.add(jsonObject);
				}
				//调用各自的业务实现
				lazzenServiceImpl.saveData(dbFieldList);
			}
		}catch (Exception e) {
			log.info("kafka consumer analysisData error : {} ",e.getMessage());
			log.error("kafka consumer analysisData error ",e);
			e.printStackTrace();
		}
		return true;
	}

	@Nullable
	private Map<LazzenService, List<LazzenConsumerDataVO>> getLazzenServiceListMap(List<KafkaTopicContent<JSONObject>> topiContent) {
		Map<LazzenService,List<LazzenConsumerDataVO>> map = new HashMap<>();
		for(KafkaTopicContent<JSONObject> kafkaTopicContent: topiContent) {
			log.info("kafka consumer kafkaTopicContent : {} ",kafkaTopicContent);
//			KafkaTopicContent<JSONObject> lazzenContent = topiContent.get(0);
			JSONObject topicData = kafkaTopicContent.getTopicData();
			String parse = JSON.toJSONString(topicData);
			LazzenConsumerVO lazzenConsumerVO1 = JSON.parseObject(parse, new TypeReference<LazzenConsumerVO>(){});
			LazzenConsumerDataVO params = lazzenConsumerVO1.getParams();
			log.info("kafka consumer lazzenConsumerVO1 : {} ",lazzenConsumerVO1);
			// fd类型 计算校验和，不相等 则不处理
			if(!this.checkCrc(params)) {
				log.info("lazzen kafka content crc check fail");
				continue;
			}
			params.setDeviceDateTime(kafkaTopicContent.getTopicTime());
			LazzenService lazzenServiceImpl = lazzenServiceFactory.getLazzenServiceFactory(params.getDataHead(), params.getMegTag(), params.getSaddr());
			if(lazzenServiceImpl==null) {
				log.info("lazzen kafka content not deal DataHead : {} , MegTag : {} , MacAddr : {}" ,params.getDataHead(), params.getMegTag(), params.getSaddr());
				continue;
			}
			// 相同的数据类型 放入同一个list中，使用对应的 beanName作为 key
			if (map.containsKey(lazzenServiceImpl)) {
				log.info("kafka getLazzenServiceListMap containsKey : {}", lazzenServiceImpl);
				List<LazzenConsumerDataVO> tmpList = map.get(lazzenServiceImpl);
				tmpList.add(params);
			} else {
				log.info("kafka getLazzenServiceListMap not containsKey : {}", lazzenServiceImpl);
				List<LazzenConsumerDataVO> tmpList = new ArrayList<>();
				tmpList.add(params);
				map.put(lazzenServiceImpl, tmpList);
			}
		}
		return map;
	}

	private boolean checkCrc(LazzenConsumerDataVO params) {
		// 不是fd类型不校验
		if(!DataHeadEnum.fd.getCode().equals(params.getDataHead())) {
			return true;
		}
		StringBuilder sb = new StringBuilder();
		sb.append(params.getDataHead()).append(params.getDataTag()).append(params.getDataLenth()).append(params.getMacAddr()).append(params.getGwType())
				.append(params.getPortAddr()).append(params.getMegTag()).append(params.getPackCount()).append(params.getPackId()).append(params.getDataValue())
				;
		String result = LazzenDataCrcUtil.calculateSum(sb.toString());
		if(result.equals(params.getDataCRC())) {
			return true;
		}
		return false;
	}

	private Map<String, List<AddressMapDefinitionEntity>> getDbAllAddressInfoMap() {
		QueryWrapper<AddressMapDefinitionEntity> addressQueryWrapper = new QueryWrapper<>();
		addressQueryWrapper.eq("modbus_protocol_version","1.0.0");
		List<AddressMapDefinitionEntity> addressList = addressMapDefinitionServiceImpl.list(addressQueryWrapper);
//		log.info("addressList : {}", addressList);
		return  addressList.stream().collect(Collectors.groupingBy(AddressMapDefinitionEntity::getAddress));
	}

	public static void main(String[] args) {

		KafkaTopicContent<LazzenConsumerVO> kafkaTopicContent = new KafkaTopicContent<>();
		kafkaTopicContent.setTopicTime(LocalDateTime.now());
		// 良信产品示例
//		String str = "{'params': {'DataHead': 'fd','DataTag': '01','DataLenth': '00EE','MacAddr': '98D863B915A4000000','GwType': '03','PortAddr': '01','MegTag': '01','PackCount': '0001','PackId': '0001', 'DataValue': '644235454C00000000000000500050000100E6000002002A0007000600500050000100E6003200020001000400020001006496002023100839393338333838330008000000000000000000004E444235452D312D303642303030304D47443032323331313135413033000000654235454C0000000000000050005000010190000002002A000700060050005000010190003200020001000300020001006596002023100939393338323632310008000000000000000000004E444235452D312D303642303030304D47443032323331313135413033000000','DataCRC': '211B','DataRead': '0'},'method': 'thing.event.property.post'}";
		// 实验室 产品
//		String str = "{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'0082','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'01','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E6000002002A000700060050003F000100E6003200020001000400020001006496002024110237393231383030373838000000000005000000014E444235452D312D303742303230304D47443031323430373038413037303031','DataCRC':'13AD','DataRead':'0'},'method':'thing.event.property.post'}";
		// 实验室 测量数据
//		String str = "{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'016E','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'04','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E600000260A000000002000000000000000200000000000000000000000000000000000000000000000000000000000000000948095E096C0000000000000000000001F401F401F40002000000000000000000000000000000000000000000000000000000000000000000180023000100100000000000000000000000000000000000000000000000000000000000000000000000010000000000000001000000030102000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000','DataCRC':'0CC6','DataRead':'0'},'method':'thing.event.property.post'}";
		// 运行状态
		String str = "{\n" +
				"    \"params\":{\"DataHead\":\"fd\",\n" +
				"    \"DataTag\":\"01\",\n" +
				"    \"DataLenth\":\"004A\",\n" +
				"    \"MacAddr\":\"98D863B91366000000\",\n" +
				"    \"GwType\":\"03\",\n" +
				"    \"PortAddr\":\"01\",\n" +
				"    \"MegTag\":\"03\",\n" +
				"    \"PackCount\":\"0001\",\n" +
				"    \"PackId\":\"0001\",\n" +
				"    \"DataValue\":\"644235454C0000000000000050003F000100E6000002500E003A00000000400000AC000000000000000000000000000300020000\",\n" +
				"    \"DataCRC\":\"07C6\",\n" +
				"    \"DataRead\":\"0\"\n" +
				"    },\n" +
				"\"method\":\"thing.event.property.post\"\n" +
				"}";
		LazzenConsumerVO lazzenConsumerVO1 = JSON.parseObject(str, new TypeReference<LazzenConsumerVO>() {
		});
		kafkaTopicContent.setTopicData(lazzenConsumerVO1);
		LazzenConsumerDataVO params = lazzenConsumerVO1.getParams();
		LazzenkafkaCommonConsumer lazzenkafkaCommonConsumer = new LazzenkafkaCommonConsumer();
		if(!lazzenkafkaCommonConsumer.checkCrc(params)) {
			log.info("lazzen kafka content crc check fail");
			return;
		}
		params.setDeviceDateTime(kafkaTopicContent.getTopicTime());
		LazzenService lazzenServiceImpl = lazzenkafkaCommonConsumer.getLazzenServiceFactory(params.getDataHead(), params.getMegTag(), params.getSaddr());
		String allAddressStr = "[{\"address\":\"0x0200\",\"addressDescription\":\"产品规格\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"product standard\",\"id\":3500,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表1\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productStandard\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0201\",\"addressDescription\":\"产品型号\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Product Model\",\"id\":3501,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表1\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productModel\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0202\",\"addressDescription\":\"壳架电流\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Frame current\",\"id\":3502,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表2\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"frameCurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0203\",\"addressDescription\":\"额定电流\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Rated current\",\"id\":3503,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表 4 与壳架电流相关见表3\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"ratedCurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"A\",\"updateTime\":1733170649000}, {\"address\":\"0x0204\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3504,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0205\",\"addressDescription\":\"额定电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3505,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表5\",\"reserveFlag\":\"\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0206\",\"addressDescription\":\"工作频率(电网频率) \",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"work frequency\",\"id\":3506,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表6\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"workFrequency\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"Hz\",\"updateTime\":1733170649000}, {\"address\":\"0x0207\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3507,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0209\",\"addressDescription\":\"产品极数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Product pole number\",\"id\":3508,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表 7\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productPoleNumber\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x020A\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3509,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x020C\",\"addressDescription\":\"通信地址\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3510,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认100范围100~240\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x020D\",\"addressDescription\":\"通信波特率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3511,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表8,控制器固定38400\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x020E\",\"addressDescription\":\"生产日期 - 年\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"BCD\",\"definition\":\"product date year\",\"id\":3512,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"BCD码,例:0x2019\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productDateYear\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x020F\",\"addressDescription\":\"H：生产日期-月\\r\\nL：生产日期-日\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"BCD\",\"definition\":\"product Date Month Day\",\"id\":3513,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"BCD 码\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productDateMonthDay\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0210\",\"addressDescription\":\"产品序列号\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"ASCII\",\"definition\":\"product sn\",\"id\":3514,\"isDeleted\":0,\"length\":5,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"ASCII码,例如1652498856\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"productSn\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0215\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3515,\"isDeleted\":0,\"length\":15,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0224\",\"addressDescription\":\"软件信息(SOFT VER)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"ASCII\",\"definition\":\"\",\"id\":3516,\"isDeleted\":0,\"length\":6,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"ASCII码\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0260\",\"addressDescription\":\"A相电流\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"a_phase_current\",\"id\":3517,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认为0.01A\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseACurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"A\",\"updateTime\":1733170649000}, {\"address\":\"0x0262\",\"addressDescription\":\"B相电流\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"b_phase_current\",\"id\":3518,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseBCurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"A\",\"updateTime\":1733170649000}, {\"address\":\"0x0264\",\"addressDescription\":\"C相电流\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"c_phase_current\",\"id\":3519,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseCCurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"A\",\"updateTime\":1733170649000}, {\"address\":\"0x0266\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3520,\"isDeleted\":0,\"length\":16,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"预留\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0276\",\"addressDescription\":\"A相电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"a phase voltage\",\"id\":3521,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseAVoltage\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x0277\",\"addressDescription\":\"B相电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"b_phase_voltage\",\"id\":3522,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseBVoltage\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x0278\",\"addressDescription\":\"C相电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"c_phase_voltage\",\"id\":3523,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseCVoltage\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x0279\",\"addressDescription\":\"AB线电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3524,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"非3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x027A\",\"addressDescription\":\"BC线电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3525,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"非3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x027B\",\"addressDescription\":\"CA线电压\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3526,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"非3P产品此处值为0\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"V\",\"updateTime\":1733170649000}, {\"address\":\"0x027C\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3527,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x027E\",\"addressDescription\":\"A相/AB线电压频率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3528,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"Hz\",\"updateTime\":1733170649000}, {\"address\":\"0x027F\",\"addressDescription\":\"B相/BC线电压频率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3529,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"Hz\",\"updateTime\":1733170649000}, {\"address\":\"0x0280\",\"addressDescription\":\"C相/CA线电压频率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3530,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"Hz\",\"updateTime\":1733170649000}, {\"address\":\"0x0281\",\"addressDescription\":\"相序状态\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3531,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"0:无(相位差),1:正序,2:反序\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0282\",\"addressDescription\":\"A相有功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"a_phase_power\",\"id\":3532,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相功率默认0.1kW\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseAPower\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kW\",\"updateTime\":1733170649000}, {\"address\":\"0x0283\",\"addressDescription\":\"B相有功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"b_phase_power\",\"id\":3533,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseBPower\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kW\",\"updateTime\":1733170649000}, {\"address\":\"0x0284\",\"addressDescription\":\"C相有功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"c_phase_power\",\"id\":3534,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"phaseCPower\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kW\",\"updateTime\":1733170649000}, {\"address\":\"0x0285\",\"addressDescription\":\"总有功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"active_power\",\"id\":3535,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kW\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"totalActivePower\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kW\",\"updateTime\":1733170649000}, {\"address\":\"0x0286\",\"addressDescription\":\"A相无功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3536,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVar 3P无单相功率\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvar \",\"updateTime\":1733170649000}, {\"address\":\"0x0287\",\"addressDescription\":\"B相无功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3537,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvar \",\"updateTime\":1733170649000}, {\"address\":\"0x0288\",\"addressDescription\":\"C相无功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3538,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvar \",\"updateTime\":1733170649000}, {\"address\":\"0x0289\",\"addressDescription\":\"总无功功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"reactive_power\",\"id\":3539,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVar\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"reactivePower\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvar \",\"updateTime\":1733170649000}, {\"address\":\"0x028A\",\"addressDescription\":\"A相视在功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3540,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相功率默认0.1kVA\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVA \",\"updateTime\":1733170649000}, {\"address\":\"0x028B\",\"addressDescription\":\"B相视在功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3541,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVA \",\"updateTime\":1733170649000}, {\"address\":\"0x028C\",\"addressDescription\":\"C相视在功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3542,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVA \",\"updateTime\":1733170649000}, {\"address\":\"0x028D\",\"addressDescription\":\"总视在功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3543,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVA\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVA \",\"updateTime\":1733170649000}, {\"address\":\"0x028E\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3544,\"isDeleted\":0,\"length\":4,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0291\",\"addressDescription\":\"总需用功率\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3545,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kW\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kW\",\"updateTime\":1733170649000}, {\"address\":\"0x0292\",\"addressDescription\":\"A相功率因数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"int16\",\"definition\":\"\",\"id\":3546,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相功率因数\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0293\",\"addressDescription\":\"B相功率因数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"int16\",\"definition\":\"\",\"id\":3547,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相功率因数\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0294\",\"addressDescription\":\"C相功率因数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"int16\",\"definition\":\"\",\"id\":3548,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相功率因数\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0295\",\"addressDescription\":\"总功率因数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"int16\",\"definition\":\"total_power_factor\",\"id\":3549,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"totalPowerFactor\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.01\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0296\",\"addressDescription\":\"A相有功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3550,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x0298\",\"addressDescription\":\"B相有功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3551,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x029A\",\"addressDescription\":\"C相有功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3552,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x029C\",\"addressDescription\":\"总有功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"total_active_electrical_energy\",\"id\":3553,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"totalActiveElectricalEnergy\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x029E\",\"addressDescription\":\"A相无功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3554,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVarh 3P无单相电能\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvarh \",\"updateTime\":1733170649000}, {\"address\":\"0x02A0\",\"addressDescription\":\"B相无功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3555,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvarh \",\"updateTime\":1733170649000}, {\"address\":\"0x02A2\",\"addressDescription\":\"C相无功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3556,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvarh \",\"updateTime\":1733170649000}, {\"address\":\"0x02A4\",\"addressDescription\":\"总无功电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"reactive_energy\",\"id\":3557,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVarh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"reactiveEnergy\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kvarh \",\"updateTime\":1733170649000}, {\"address\":\"0x02A6\",\"addressDescription\":\"A相视在电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3558,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kVAh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVAh\",\"updateTime\":1733170649000}, {\"address\":\"0x02A8\",\"addressDescription\":\"B相视在电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3559,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVAh\",\"updateTime\":1733170649000}, {\"address\":\"0x02AA\",\"addressDescription\":\"C相视在电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3560,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVAh\",\"updateTime\":1733170649000}, {\"address\":\"0x02AC\",\"addressDescription\":\"总视在电能\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3561,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVAh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVAh\",\"updateTime\":1733170649000}, {\"address\":\"0x02AE\",\"addressDescription\":\"温度\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"int16\",\"definition\":\"temperature\",\"id\":3562,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"temperature\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"°C\",\"updateTime\":1733170649000}, {\"address\":\"0x02AF\",\"addressDescription\":\"漏电流值\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"leakage_current\",\"id\":3563,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"漏电产品中有\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"leakageCurrent\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"mA\",\"updateTime\":1733170649000}, {\"address\":\"0x02B0\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3564,\"isDeleted\":0,\"length\":32,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x02D0\",\"addressDescription\":\"A相有功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3565,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh \",\"updateTime\":1733170649000}, {\"address\":\"0x02D2\",\"addressDescription\":\"B相有功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3566,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh \",\"updateTime\":1733170649000}, {\"address\":\"0x02D4\",\"addressDescription\":\"C相有功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3567,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh \",\"updateTime\":1733170649000}, {\"address\":\"0x02D6\",\"addressDescription\":\"总有功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3568,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh \",\"updateTime\":1733170649000}, {\"address\":\"0x02D8\",\"addressDescription\":\"A相无功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3569,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kVarh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02DA\",\"addressDescription\":\"B相无功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3570,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02DC\",\"addressDescription\":\"C相无功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3571,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02DE\",\"addressDescription\":\"总无功电能(正向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3572,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVarh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02E0\",\"addressDescription\":\"A相有功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3573,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x02E2\",\"addressDescription\":\"B相有功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3574,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x02E4\",\"addressDescription\":\"C相有功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3575,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x02E6\",\"addressDescription\":\"总有功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3576,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kWh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kWh\",\"updateTime\":1733170649000}, {\"address\":\"0x02E8\",\"addressDescription\":\"A相无功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3577,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"3P无单相电能默认0.1kVarh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02EA\",\"addressDescription\":\"B相无功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3578,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02EC\",\"addressDescription\":\"C相无功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3579,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x02EE\",\"addressDescription\":\"总无功电能(反向)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3580,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认0.1kVarh\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"0.1\",\"unitDescription\":\"kVarh\",\"updateTime\":1733170649000}, {\"address\":\"0x0400\",\"addressDescription\":\"遥控指令(非广播)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3581,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表16(注1)\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0401\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3582,\"isDeleted\":0,\"length\":5,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"NDB5E预留\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0406\",\"addressDescription\":\"漏电定期自检\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3583,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"默认值:0x9000试调、定期 试调跳闸默认使能 bit15:遥控试调跳闸位 0 - 关闭、1 - 使能 bit13:定期自检使能 0 - 关闭、1 - 使能 bit12:定期试调跳闸位 0 - 关闭、1 - 使能 bit11~bit6:自检日期(1~28) bit5~bit0:自检小时(0~23)\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0407\",\"addressDescription\":\"定时0分闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3584,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"数据格式说明,见表17\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0409\",\"addressDescription\":\"定时0合闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3585,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x040B\",\"addressDescription\":\"定时1分闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3586,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x040D\",\"addressDescription\":\"定时1合闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3587,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x040F\",\"addressDescription\":\"定时2分闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3588,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0411\",\"addressDescription\":\"定时2合闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3589,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0413\",\"addressDescription\":\"定时3分闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3590,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0415\",\"addressDescription\":\"定时3合闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3591,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0417\",\"addressDescription\":\"定时4分闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3592,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0419\",\"addressDescription\":\"定时4合闸\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3593,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"同上\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"1\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x041B\",\"addressDescription\":\"预留\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint32\",\"definition\":\"\",\"id\":3594,\"isDeleted\":0,\"length\":2,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x041F\",\"addressDescription\":\"一键分合指令(广播)\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"turn_on_off\",\"id\":3595,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"0x55AA:合闸 0xAA55:分闸 与0x0420 0423配合使用\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"turnOnOff\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0250\",\"addressDescription\":\"操作次数\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3596,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"以合闸次数计算\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0251\",\"addressDescription\":\"\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3597,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"预留\",\"reserveFlag\":\"Y\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0252\",\"addressDescription\":\"自诊断报警\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"\",\"id\":3598,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表 9\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0253\",\"addressDescription\":\"报警状态\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Alarm status\",\"id\":3599,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表 10\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"alarmStatus\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}, {\"address\":\"0x0254\",\"addressDescription\":\"运行状态 1\",\"company\":\"良信\",\"createTime\":1733170649000,\"dataType\":\"uint16\",\"definition\":\"Running status\",\"id\":3600,\"isDeleted\":0,\"length\":1,\"modbusProtocolVersion\":\"1.0.0\",\"remark\":\"见表 11\",\"reserveFlag\":\"N\",\"setItemDefinition\":\"runningStatus1\",\"systemRequiredConfiguration\":1,\"tenantId\":\"111111\",\"unit\":\"\",\"unitDescription\":\"\",\"updateTime\":1733170649000}]";
		List<AddressMapDefinitionEntity> allAddressList = JSON.parseObject(allAddressStr, new TypeReference<List<AddressMapDefinitionEntity>>() {
		});
		Map<String, List<AddressMapDefinitionEntity>> allAddressMap = allAddressList.stream().collect(Collectors.groupingBy(AddressMapDefinitionEntity::getAddress));
		lazzenServiceImpl.analysisData(params, allAddressMap);
	}

	public LazzenService getLazzenServiceFactory(String dataHead,String megTag,String saddr) {
		Map<String,LazzenService> lazzenServiceMap = new ConcurrentHashMap<>();
		// fc 组合 ：网关应答
		lazzenServiceMap.put(DataHeadEnum.fc.getCode() ,new LazzenGatewayAckServiceImpl());
		// fd 组合 ： 产品信息
		lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_01.getCode(),
				new LazzenProductInfoServiceImpl());
		// 运行状态
		lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_03.getCode(),
				new LazzenRunningStatusServiceImpl());
		// 测量数据/数据读取
		lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_04.getCode(),
				new LazzenMeasurementDataServiceImpl());
		// 遥控数据（指令参数）
//        lazzenServiceMap.put(DataHeadEnum.fd.getCode() + "_"+ MegTag4DataHeadToFdEnum.code_07.getCode(),
//                applicationContext.getBean("lazzenRemoteControlServiceImpl",LazzenService.class));
		// fe 组合 ：网关上报心跳
		lazzenServiceMap.put(DataHeadEnum.fe.getCode() + "_"+ Saddr4DataHeadToFeEnum.code_0001.getCode(),
				new LazzenGatewayParamServiceImpl());
		LazzenService lazzenService = null;
		lazzenService = lazzenServiceMap.get(dataHead);
		if(lazzenService!=null) {
			return lazzenService;
		}
		lazzenService = lazzenServiceMap.get(dataHead + "_" + megTag);
		if(lazzenService!=null) {
			return lazzenService;
		}
		lazzenService = lazzenServiceMap.get(dataHead + "_" + saddr);
		return lazzenService;
	}

	private List<KafkaTopicContent<JSONObject>> getTestContent() {
		List<KafkaTopicContent<JSONObject>> list = new ArrayList<>();
		// 心跳测试数据
		KafkaTopicContent<JSONObject> content = new KafkaTopicContent<>();
		content.setTopicData(JSON.parseObject("{'params':{'DataHead':'fe','Saddr':'0001','MacAddr':'98D863B91366000000','PortAddr':'01','DataRead':'0'},'method':'thing.event.property.post'}"));
		content.setTopicTime(LocalDateTime.now());
		KafkaTopicContent<JSONObject> content1 = new KafkaTopicContent<>();
		content1.setTopicData(JSON.parseObject("{'params':{'DataHead':'fe','Saddr':'0001','MacAddr':'98D863B91366000000','PortAddr':'01','DataRead':'0'},'method':'thing.event.property.post'}"));
		content1.setTopicTime(LocalDateTime.now().plusHours(1));
		// 测量数据
		KafkaTopicContent<JSONObject> content2 = new KafkaTopicContent<>();
		content2.setTopicData(JSON.parseObject("{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'016E','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'04','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E600000260A000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000934094009480000000000000000000001F401F401F4000200000000000000000000000000000000000000000000000000000000000000000018002200030015000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001010E000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000','DataCRC':'0C7B','DataRead':'0'},'method':'thing.event.property.post'}"));
		content2.setTopicTime(LocalDateTime.now().plusHours(4));
		KafkaTopicContent<JSONObject> content3 = new KafkaTopicContent<>();
		content3.setTopicData(JSON.parseObject("{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'016E','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'04','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E600000260A000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000932094209490000000000000000000001F401F401F4000200000000000000000000000000000000000000000000000000000000000000000032001A0006001B000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001010E000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000','DataCRC':'0C97','DataRead':'0'},'method':'thing.event.property.post'}"));
		content3.setTopicTime(LocalDateTime.now().plusHours(6));

		list.add(content);
		list.add(content1);
		list.add(content2);
		list.add(content3);
		return list;
	}
}

package org.skyworth.ess.lazzen.issue;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.guardian.issue.IssueStrategyEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity;
import org.skyworth.ess.lazzen.productinfo.service.IDeviceGuardProductInfoService;
import org.skyworth.ess.lazzen.productinfo.vo.LazzenSwitchOnOffCodeEnum;
import org.skyworth.ess.util.LazzenDataCrcUtil;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.SerialNumberGenerator;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class IssueStrategy4LazzenGatePosition extends LazzenGuardianIssueStrategy {


	public IDeviceGuardProductInfoService deviceGuardProductInfoService = SpringUtil.getBean(IDeviceGuardProductInfoService.class);
	public IGatewayPlantService gatewayPlantService = SpringUtil.getBean(IGatewayPlantService.class);


	/**
	 * 02.拼接content内容
	 *
	 * @param issueStrategyEntity
	 * @return
	 */
	@Override
	public String assembleContent(IssueStrategyEntity issueStrategyEntity) {
		String content = "";
		List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
		String deviceSerialNumber = issueStrategyEntity.getDeviceSerialNumber();
		DeviceGuardProductInfoEntity productInfoServiceOne = deviceGuardProductInfoService.getOne(Wrappers.<DeviceGuardProductInfoEntity>query().lambda()
			.eq(DeviceGuardProductInfoEntity::getGatewayUniqueNumber, deviceSerialNumber));
		// 默认断路器地址为64
		String address = "64";
		if (ObjectUtil.isNotNull(productInfoServiceOne)) {
			address = productInfoServiceOne.getCircuitBreakerAddress();
		}
		if (CollectionUtil.isNotEmpty(setupItems)) {
			AppAdvancedSetup.SetupItem setupItem = setupItems.get(0);
			// 固定头+功能码+数据长度+端口地址+设备地址+modbus寄存器地址+寄存器个数+指令值（参考通信表）+CRC校验码
			String issueData = GuardianInstructConstants.LAZZEN_FIXED_HEADER +
				GuardianInstructConstants.LAZZEN_FUNCTION_CODE_WRITE_DATA +
				GuardianInstructConstants.LAZZEN_DEFAULT_PORT_ADDRESS +
				address +
				GuardianInstructConstants.LAZZEN_FUNCTION_CODE_REMOTE_CONTROL +
				"01" +
				LazzenSwitchOnOffCodeEnum.getIssueCodeByDecimalCode((String) setupItem.getData());
			// +数据长度+CRC校验码
			int length = issueData.length() + 4 + 4;

			String hexadecimalNumber = BinaryToHexUtils.toFourDigitHex(length, 4);

			String issueData2 = GuardianInstructConstants.LAZZEN_FIXED_HEADER +
				GuardianInstructConstants.LAZZEN_FUNCTION_CODE_WRITE_DATA +
				hexadecimalNumber +
				GuardianInstructConstants.LAZZEN_DEFAULT_PORT_ADDRESS +
				address +
				GuardianInstructConstants.LAZZEN_FUNCTION_CODE_REMOTE_CONTROL +
				"01" +
				LazzenSwitchOnOffCodeEnum.getIssueCodeByDecimalCode((String) setupItem.getData());

			byte[] aByte = LazzenDataCrcUtil.getByte(issueData2);
			String crc16 = LazzenDataCrcUtil.calculateCRC16(aByte, aByte.length);
			String issueData3 = issueData2 + crc16;

			String uniqueSerialNumber = SerialNumberGenerator.generateUniqueSerialNumber();
			String currentTime = TimeUtils.getCurrentTime();
			content = uniqueSerialNumber + ":" + issueData3+","+currentTime;

			issueStrategyEntity.setRequestId(uniqueSerialNumber);
		} else {
			throw new BusinessException("client.guardian.setitem.cannot.empty");
		}
		return content;
	}


	/**
	 * 05.处理业务结果
	 *
	 * @param invokeResult
	 * @return
	 */
	@Override
	public R handleBusinessResult(Map<String, String> invokeResult, IssueStrategyEntity issueStrategyEntity) {
		if (invokeResult.containsKey("400")) {
			 throw new BusinessException("client.guardian.issue.timeout");
		} else if (invokeResult.containsKey("200")){
			// 业务操作，更新数据库等
			List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
			if (!CollectionUtils.isEmpty(setupItems)) {
				String deviceSerialNumber = issueStrategyEntity.getDeviceSerialNumber();
				Long plantId = issueStrategyEntity.getAppAdvancedSetups().getPlantId();
				for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
					if ("turnOnOff".equals(setupItem.getDefinition())) {
						Object data = setupItem.getData();
//						gatewayPlantService.update(Wrappers.<GatewayPlantEntity>update().lambda().set(GatewayPlantEntity::getTurnOnOff, data)
//								.eq(GatewayPlantEntity::getGatewayUniqueNumber, deviceSerialNumber)
//								.eq(GatewayPlantEntity::getPlantId,plantId));
						return R.success("setup success");
					}
				}
			}
		}
		throw new BusinessException("client.guardian.issue.gateposition.fail");
	}
}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.mapper;

import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.exception.excel.ExceptionLogExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 异常日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface ExceptionLogMapper extends BaseMapper<ExceptionLogEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param exceptionLog
	 * @return
	 */
	List<ExceptionLogVO> selectExceptionLogPage(IPage page,@Param("exceptionLog") ExceptionLogVO exceptionLog);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<ExceptionLogExcel> exportExceptionLog(@Param("ew") Wrapper<ExceptionLogEntity> queryWrapper);

	/**
	 * 储能异常日志自定义分页
	 *
	 * @param page
	 * @param queryCondition
	 * @return
	 */
	List<ExceptionLogEntity> selectExceptionLogPageByBattery(IPage page,@Param("queryCondition") QueryCondition queryCondition);

}

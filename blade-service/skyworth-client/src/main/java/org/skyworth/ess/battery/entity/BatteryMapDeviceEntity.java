/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 储能智能能量变换器映射关系 实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@TableName("battery_map_device")
@ApiModel(value = "BatteryMapDevice对象", description = "储能智能能量变换器映射关系")
@EqualsAndHashCode(callSuper = true)
public class BatteryMapDeviceEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 智能能量变换器/设备SN
	 */
	@ApiModelProperty(value = "智能能量变换器/设备SN")
	private String deviceSerialNumber;
	/**
	 * 储能SN
	 */
	@ApiModelProperty(value = "储能SN")
	private String batterySerialNumber;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	// 储能和智能能量变换器是否匹配
	private String batteryMatchDeviceFlag;
	// 1为储能1,2为储能2...
	private Integer batteryEnergyStorageNumber;
}

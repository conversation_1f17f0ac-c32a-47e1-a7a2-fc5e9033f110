/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import com.alibaba.fastjson.JSONObject;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.vo.DeviceCurrentStatusVO;
import org.skyworth.ess.device.excel.DeviceCurrentStatusExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.device.vo.InverterReportQueryVO;
import org.skyworth.ess.homepage.vo.InverterHomePageVO;

import java.util.List;

/**
 * 设备/智能能量变换器当前状态 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface DeviceCurrentStatusMapper extends BaseMapper<DeviceCurrentStatusEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param DeviceCurrentStatus
	 * @return
	 */
	List<DeviceCurrentStatusVO> selectDeviceCurrentStatusPage(IPage page, DeviceCurrentStatusVO DeviceCurrentStatus);

	List<DeviceCurrentStatusEntity> selectDeviceCurrentStatusIsDelete(@Param("plantId") Long plantId,@Param("deviceSerialNumber") String deviceSerialNumber);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceCurrentStatusExcel> exportDeviceCurrentStatus(@Param("ew") Wrapper<DeviceCurrentStatusEntity> queryWrapper);

	JSONObject selectEnergyTotalStat(@Param("queryCondition") InverterReportQueryVO queryCondition);

	JSONObject inverterTodaySummary();

	JSONObject inverterAccumulateSummary();

	JSONObject batteryTodaySummary();

	JSONObject batteryAccumulateSummary();

    int batchDeleteLogicByPlantId(@Param("list") List<Long> plantIdList, @Param("updateUserAccount") String updateUserAccount);

    int batchDeleteLogicByPlantIdAndSn(@Param("plantId")Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber,@Param("updateUserAccount") String updateUserAccount);
}

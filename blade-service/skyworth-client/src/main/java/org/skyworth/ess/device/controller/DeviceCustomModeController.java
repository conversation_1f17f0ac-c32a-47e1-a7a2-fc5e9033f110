/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.vo.DeviceCustomModeVO;
import org.skyworth.ess.device.excel.DeviceCustomModeExcel;
import org.skyworth.ess.device.wrapper.DeviceCustomModeWrapper;
import org.skyworth.ess.device.service.IDeviceCustomModeService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备/智能能量变换器自定义模式表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-deviceCustomMode/deviceCustomMode")
@Api(value = "设备/智能能量变换器自定义模式表", tags = "设备/智能能量变换器自定义模式表接口")
public class DeviceCustomModeController extends BladeController {

	private final IDeviceCustomModeService deviceCustomModeService;

	/**
	 * 设备/智能能量变换器自定义模式表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceCustomMode")
	public R<DeviceCustomModeVO> detail(DeviceCustomModeEntity deviceCustomMode) {
		DeviceCustomModeEntity detail = deviceCustomModeService.getOne(Condition.getQueryWrapper(deviceCustomMode));
		return R.data(DeviceCustomModeWrapper.build().entityVO(detail));
	}
	/**
	 * 设备/智能能量变换器自定义模式表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceCustomMode")
	public R<IPage<DeviceCustomModeVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceCustomMode, Query query) {
		IPage<DeviceCustomModeEntity> pages = deviceCustomModeService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceCustomMode, DeviceCustomModeEntity.class));
		return R.data(DeviceCustomModeWrapper.build().pageVO(pages));
	}

	/**
	 * 设备/智能能量变换器自定义模式表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceCustomMode")
	public R<IPage<DeviceCustomModeVO>> page(DeviceCustomModeVO deviceCustomMode, Query query) {
		IPage<DeviceCustomModeVO> pages = deviceCustomModeService.selectDeviceCustomModePage(Condition.getPage(query), deviceCustomMode);
		return R.data(pages);
	}

	/**
	 * 设备/智能能量变换器自定义模式表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceCustomMode")
	public R save(@Valid @RequestBody DeviceCustomModeEntity deviceCustomMode) {
		return R.status(deviceCustomModeService.save(deviceCustomMode));
	}

	/**
	 * 设备/智能能量变换器自定义模式表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceCustomMode")
	public R update(@Valid @RequestBody DeviceCustomModeEntity deviceCustomMode) {
		return R.status(deviceCustomModeService.updateById(deviceCustomMode));
	}

	/**
	 * 设备/智能能量变换器自定义模式表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceCustomMode")
	public R submit(@Valid @RequestBody DeviceCustomModeEntity deviceCustomMode) {
		return R.status(deviceCustomModeService.saveOrUpdate(deviceCustomMode));
	}

	/**
	 * 设备/智能能量变换器自定义模式表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceCustomModeService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-deviceCustomMode")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入deviceCustomMode")
	public void exportDeviceCustomMode(@ApiIgnore @RequestParam Map<String, Object> deviceCustomMode, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceCustomModeEntity> queryWrapper = Condition.getQueryWrapper(deviceCustomMode, DeviceCustomModeEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(DeviceCustomMode::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(DeviceCustomModeEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceCustomModeExcel> list = deviceCustomModeService.exportDeviceCustomMode(queryWrapper);
		ExcelUtil.export(response, "设备/智能能量变换器自定义模式表数据" + DateUtil.time(), "设备/智能能量变换器自定义模式表数据表", list, DeviceCustomModeExcel.class);
	}

}

package org.skyworth.ess.agreement.balconyPv;

import org.skyworth.ess.agreement.util.ProtocolParser;

import java.util.Map;

/**
 * @ClassName BalconyPvProtocolParser
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/31 17:32
 */
public class BalconyPvProtocolParser implements ProtocolParser {
	@Override
	public String parseCommand(String command, Map<String, Object> params) {
		System.out.println("command: " + command);
		return "";
	}

	@Override
	public boolean validateDeviceId(String deviceId) {
		return true;
	}
}

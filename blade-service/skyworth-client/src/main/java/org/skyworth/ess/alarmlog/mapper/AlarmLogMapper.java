/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.alarmlog.entity.AlarmLogEntity;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageCondition;
import org.skyworth.ess.alarmlog.vo.AlarmLogPageVO;

import java.util.List;
import java.util.Map;

/**
 * 异常日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface AlarmLogMapper extends BaseMapper<AlarmLogEntity> {
	/**
	 * 查询异常日志列表
	 *
	 * @param alarmLogPageCondition 查询条件
	 * @param page                  入参
	 * @return List<AlarmLogEntity>
	 * <AUTHOR>
	 * @since 2024/8/10 16:09
	 **/
	List<AlarmLogPageVO> list(@Param("params") AlarmLogPageCondition alarmLogPageCondition, @Param("page") IPage<AlarmLogPageVO> page);

	AlarmLogPageVO detail(@Param("id") String id);


	/**
	 * 查询需要更新的记录列表
	 *
	 * @param plantId            站点id
	 * @param type               异常类型
	 * @param serialNumber       设备SN
	 * @param alarmLogEntityList 入参
	 * @return List<AlarmLogEntity>
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	List<AlarmLogPageVO> queryUpdateRecord(@Param("plantId") String plantId, @Param("type") String type, @Param(
		"serialNumber") String serialNumber,@Param(
			"addressCode")String addressCode, @Param("list") List<AlarmLogEntity> alarmLogEntityList);

	/**
	 * 删除记录列表
	 *
	 * @param plantId 入参
	 * @return List<AlarmLogEntity>
	 * <AUTHOR>
	 * @since 2024/11/21 10:06
	 **/
	List<AlarmLogPageVO> queryDeleteRecord(@Param("plantId") String plantId, @Param(
		"serialNumber") String serialNumber, @Param("list") List<AlarmLogEntity> alarmLogEntityList);
}

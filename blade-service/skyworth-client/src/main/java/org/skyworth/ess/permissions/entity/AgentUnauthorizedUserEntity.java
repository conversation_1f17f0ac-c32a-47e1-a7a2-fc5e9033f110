/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.permissions.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 站点挂的代理商下，无权限操作的用户id 实体类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Data
@TableName("plant_agent_unauthorized_user")
@ApiModel(value = "AgentUnauthorizedUser对象", description = "站点挂的代理商下，无权限操作的用户id")
@EqualsAndHashCode(callSuper = true)
public class AgentUnauthorizedUserEntity extends TenantEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 无权限userId
	 */
	@ApiModelProperty(value = "无权限userId")
	private Long unauthorizedUserId;
	/**
	 * 无权限userName
	 */
	@ApiModelProperty(value = "无权限操作站点的用户姓名")
	@TableField(exist = false)
	private String userName;
	/**
	 * 无权限user电话
	 */
	@ApiModelProperty(value = "无权限操作站点的用户电话")
	@TableField(exist = false)
	private String userPhone;

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.excel.GuardianPlantExcel;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantOverviewVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantReportQueryVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO;
import org.skyworth.ess.guardian.issue.GuardianIssueStrategy;
import org.skyworth.ess.guardian.issue.IssueStrategy4HostIp;
import org.skyworth.ess.guardian.issue.IssueStrategyEntity;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 安全卫士信息 控制器
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianPlant")
@Api(value = "安全卫士信息", tags = "安全卫士信息接口")
@Slf4j
public class GuardianPlantController extends BladeController {

	private final IGuardianPlantService guardianPlantService;

	/**
	 * 安全卫士信息总览
	 */
	@PostMapping("/overview")
	@ApiOperationSupport(order = 0)
	@ApiOperation(value = "总览", notes = "传入guardianPlantVO")
//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<GuardianPlantOverviewVO> getGuardianPlantOverview(@Valid @RequestBody GuardianPlantVO guardianPlantVO) {
		return R.data(guardianPlantService.getGuardianPlantOverview(guardianPlantVO));
	}

	/**
	 * 安全卫士信息 分页
	 */
	@PostMapping("/list/{pageSize}/{current}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入GuardianPlantVO")
//	@PreAuth("hasPermission('client:guardianPlant:list')")
	public R<IPage<GuardianPlantVO>> list(@Valid @RequestBody GuardianPlantVO guardianPlantVO,
										  @ApiParam(value = "每页大小", required = true) @PathVariable("pageSize") int pageSize,
										  @ApiParam(value = "当前页", required = true) @PathVariable("current") int current) {
		IPage<GuardianPlantVO> pages = guardianPlantService.page(new Query().setCurrent(current).setSize(pageSize), guardianPlantVO);
		return R.data(pages);
	}


	/**
	 * 安全卫士信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入guardianPlantEntity")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<GuardianPlantVO> detail(GuardianPlantEntity guardianPlantEntity) {
		GuardianPlantVO guardianPlantVO = guardianPlantService.detail(guardianPlantEntity);
		return R.data(guardianPlantVO);
	}

	/**
	 * 安全卫士信息 当前状态
	 */
	@GetMapping("/detail/currentStatus")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "当前状态", notes = "传入guardianPlantEntity")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<GuardianCurrentStatusVO> detailCurrentStatus(GuardianPlantEntity guardianPlantEntity) {
		GuardianCurrentStatusVO guardianCurrentStatusVO = guardianPlantService.detailCurrentStatus(guardianPlantEntity);
		return R.data(guardianCurrentStatusVO);
	}

	@GetMapping("/statusAndDeviceInfo")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "详情", notes = "传入GuardianCurrentStatus")
	public R<GuardianCurrentStatusVO> getGuardianStatusAndDeviceInfo(GuardianPlantEntity guardianPlantEntity) {
		//关联关系表&出厂表
		GuardianCurrentStatusVO detail = guardianPlantService.getGuardianStatusAndDeviceInfo(guardianPlantEntity);
		return R.data(detail);
	}
	/**
	 * 安全卫士信息 解绑
	 */
	@PostMapping("/unbind")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "解绑", notes = "传入guardianPlantEntity")
	//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<Boolean> unbind(@Valid @RequestBody GuardianPlantEntity guardianPlantEntity) {
		return R.status(guardianPlantService.unbind(guardianPlantEntity));
	}


	/**
	 * 单个智能能量变换器 状态曲线
	 *
	 * @param guardianPlantReportQueryVO
	 * @return
	 */
	@GetMapping("/detail/statusStat")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "详情-状态曲线", notes = "传入guardianPlantReportQueryVO")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public R<Map<String, List<Object>>> statusStat(GuardianPlantReportQueryVO guardianPlantReportQueryVO) {
		return R.data(guardianPlantService.selectStatusReport(guardianPlantReportQueryVO));
	}

	/**
	 * 单个智能能量变换器 状态曲线
	 *
	 * @param guardianPlantReportQueryVO
	 * @return
	 */
	@GetMapping("/detail/statusStat/export")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "详情-状态曲线导出", notes = "传入guardianPlantReportQueryVO")
//	@PreAuth("hasPermission('client:guardianPlant:detail')")
	public void statusStatExport(GuardianPlantReportQueryVO guardianPlantReportQueryVO, HttpServletResponse response) {
		guardianPlantService.selectStatusReportExport(guardianPlantReportQueryVO, response);
	}

	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianPlant")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianPlant")
	public void exportGuardianPlant(@ApiIgnore @RequestParam Map<String, Object> GuardianPlant, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianPlantEntity> queryWrapper = Condition.getQueryWrapper(GuardianPlant, GuardianPlantEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianPlant::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianPlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianPlantExcel> list = guardianPlantService.exportGuardianPlant(queryWrapper);
		ExcelUtil.export(response, "安全卫士对应站点数据" + DateUtil.time(), "安全卫士对应站点数据表", list, GuardianPlantExcel.class);
	}


	/**
	 * 配置服务器ip与端口
	 * */
	@PostMapping("/configIpAndPort")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "配置服务器ip与端口", notes = "传入Ip与端口")
	public R configIpAndPort(@RequestParam("ip") String ip,@RequestParam("port") String port,@RequestParam("deviceSn") String deviceSn) {
		if(port.length()!=4){
			return R.fail("端口必须为4位");
		}
		JSONObject dataObject = new JSONObject();
		dataObject.put("ip",ip);
		dataObject.put("port",port);
		dataObject.put("deviceSn",deviceSn);
		IssueStrategyEntity issueStrategyEntity = new IssueStrategyEntity(deviceSn,CommonConstant.BIZ_TYPE_ISSUE_DATA,dataObject);
		GuardianIssueStrategy strategy = new IssueStrategy4HostIp();
		return strategy.executeStrategy(issueStrategyEntity);
	}
}

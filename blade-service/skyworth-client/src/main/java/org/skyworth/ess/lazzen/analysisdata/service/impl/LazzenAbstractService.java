package org.skyworth.ess.lazzen.analysisdata.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.lazzen.analysisdata.service.constant.DataHeadEnum;
import org.skyworth.ess.lazzen.analysisdata.service.constant.MegTag4DataHeadToFdEnum;
import org.skyworth.ess.lazzen.analysisdata.service.LazzenService;
import org.skyworth.ess.lazzen.analysisdata.service.constant.ObjAttributeName;
import org.skyworth.ess.util.HumpConvert;
import org.skyworth.ess.vo.LazzenConsumerDataVO;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class LazzenAbstractService implements LazzenService {

    public abstract void saveData(List<JSONObject> listMap);

    public JSONObject analysisData(LazzenConsumerDataVO lazzenConsumerDataVO,Map<String, List<AddressMapDefinitionEntity>> dbAddressMap) {
        JSONObject resultMap = new JSONObject();
        String dataValue = lazzenConsumerDataVO.getDataValue();

        // 网关地址
        resultMap.put(ObjAttributeName.gatewayUniqueNumber.getAttributeName(),lazzenConsumerDataVO.getMacAddr());
        // mqtt上报时间
        resultMap.put(ObjAttributeName.deviceDateTime.getAttributeName(),lazzenConsumerDataVO.getDeviceDateTime());
        // fc,fe 不需要解析地址码
        if(!DataHeadEnum.fd.getCode().equals(lazzenConsumerDataVO.getDataHead())) {
            resultMap.put(ObjAttributeName.feSaddr.getAttributeName(),lazzenConsumerDataVO.getSaddr());
            resultMap.put(ObjAttributeName.fcCmdId.getAttributeName(),lazzenConsumerDataVO.getCmdId());
            resultMap.put(ObjAttributeName.fcCmdAck.getAttributeName(),lazzenConsumerDataVO.getCmdAck());
//            log.info("analysisData end is fc or fe : {} ", resultMap);
            return resultMap;
        }
        // 断路器地址，前2位
        resultMap.put(ObjAttributeName.circuitBreakerAddress.getAttributeName(),dataValue.substring(0,2));
        // 第二位到40位 为 产品信息，后面的为 地址和数据
        String addressData = dataValue.substring(42);
        // 起始地址
        String startAddress = addressData.substring(0, 4);
        // 寄存器个数 16进制
        String addressNumberHex = addressData.substring(4, 6);
        // 寄存器数据值
        String data = addressData.substring(6);
        // 未合并的实际寄存器个数 10进制
        int addressNumber = BinaryToHexUtils.hexToDecimal(addressNumberHex);
        // 合并后的寄存器个数
        int settingAddressNumber = dbAddressMap.keySet().size();
        String tempAddress = startAddress;
        for(int i = 0; i < settingAddressNumber; i++) {
            List<AddressMapDefinitionEntity> dbAddressList = dbAddressMap.get("0x"+tempAddress);
            if(CollectionUtils.isNullOrEmpty(dbAddressList)) {
//                log.info("analysisData ，address_map_definition address is not setting，address：{}",startAddress);
                if(DataHeadEnum.fd.getCode().equals(lazzenConsumerDataVO.getDataHead())
                        && MegTag4DataHeadToFdEnum.code_07.getCode().equals(lazzenConsumerDataVO.getMegTag())) {
//                    log.info("analysisData ，address_map_definition address is not setting，fd and remote control address：{}",startAddress);
                    break;
                }
                break;
            }
            AddressMapDefinitionEntity addressMapDefinitionEntity = dbAddressList.get(0);
            // 表对象字段名称
            String entityField = addressMapDefinitionEntity.getSetItemDefinition();
            // 16进制数据长度
            Integer addressDataLength = addressMapDefinitionEntity.getLength();
            // 16进制数据单位
            String unit = addressMapDefinitionEntity.getUnit();
            // 数据类型
            String dataType = addressMapDefinitionEntity.getDataType();
            // 当前地址16进制数据
            String currentAddressDataHex = data.substring(0, addressDataLength * 4);
//            log.info("当前地址 tempAddress  : {}",tempAddress);
//            log.info("当前地址数据 currentAddressDataHex  : {}",currentAddressDataHex);
            // 计算剩余数据长度
            data = data.substring(addressDataLength * 4);
//            log.info("剩余data长度  : {}",data);
            if(StringUtil.isNotBlank(entityField)) {
                resultMap.put(entityField + "DataAddr", tempAddress);
            }
            // 计算下一个 16进制地址
            tempAddress = BinaryToHexUtils.hexAddLengthToHex(tempAddress,addressDataLength);
            // 如果数据对象字段为空，则数据不保存至db
            if(StringUtil.isBlank(entityField)) {
//                log.info("analysisData address_map_definition entityField is null, not save data before address: {}", tempAddress);
                continue;
            }

            // 判断数据类型 是 int32，便于计算有符号数据或者无符号数据
            this.judgeDataType(dataType, resultMap, entityField, currentAddressDataHex, unit);

        }
        log.info("analysisData end DataHead: {}, MegTag : {}, Saddr : {} ",lazzenConsumerDataVO.getDataHead(),lazzenConsumerDataVO.getMegTag()
                ,lazzenConsumerDataVO.getSaddr());
        log.info("analysisData end  resultMap : {}",resultMap);
//        JSONObject newObj = HumpConvert.convertKeysToUnderscore(resultMap);
//        log.info("after analysisData db field : {}",newObj);
        return resultMap;
    }

    private void judgeDataType(String dataType, JSONObject resultMap, String entityField, String currentAddressDataHex, String unit) {
        if("BCD".equals(dataType)) {
            // 如果是BCD码， 则为 2019 表示2019年
            resultMap.put(entityField, currentAddressDataHex);
        } else if("ASCII".equals(dataType)) {
            String asciiValue = BinaryToHexUtils.hex16ToAscii(currentAddressDataHex).trim();
            resultMap.put(entityField,asciiValue);
        } else if(Constants.UINT16.equals(dataType) || Constants.UINT32.equals(dataType)){
            // 计算当前地址10进制数据
            Integer currentAddressData = BinaryToHexUtils.hexToDecimal(currentAddressDataHex);
            BigDecimal currentAddressDataBig = new BigDecimal(currentAddressData);
            if(StringUtil.isNotBlank(unit)) {
                BigDecimal unitBig = new BigDecimal(unit);
                // 上报数据 乘以 数据的实际单位值，比如 上报 1000，单位为0.1（w），则实际值为 100，表示实际只有 100w ，保留3位小数
                currentAddressDataBig = currentAddressDataBig.multiply(unitBig).setScale(3);
            }

            resultMap.put(entityField,currentAddressDataBig);
        } else if(Constants.INT16.equals(dataType)){
            int currentAddressData = BinaryToHexUtils.hexToDecimalForSymbol(currentAddressDataHex);
            BigDecimal currentAddressDataBig = new BigDecimal(currentAddressData);
            if(StringUtil.isNotBlank(unit)) {
                BigDecimal unitBig = new BigDecimal(unit);
                // 上报数据 乘以 数据的实际单位值，比如 上报 1000，单位为0.1（w），则实际值为 100，表示实际只有 100w ，保留3位小数
                currentAddressDataBig = currentAddressDataBig.multiply(unitBig).setScale(3);
            }
            resultMap.put(entityField,currentAddressDataBig);
        }
    }


}

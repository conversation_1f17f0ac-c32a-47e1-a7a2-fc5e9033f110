/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.currentstatus.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import java.util.Objects;

/**
 * 安全卫士当前状态 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public class GuardianCurrentStatusWrapper extends BaseEntityWrapper<GuardianCurrentStatusEntity, GuardianCurrentStatusVO>  {

	public static GuardianCurrentStatusWrapper build() {
		return new GuardianCurrentStatusWrapper();
 	}

	@Override
	public GuardianCurrentStatusVO entityVO(GuardianCurrentStatusEntity GuardianCurrentStatus) {
		GuardianCurrentStatusVO GuardianCurrentStatusVO = Objects.requireNonNull(BeanUtil.copy(GuardianCurrentStatus, GuardianCurrentStatusVO.class));

		//User createUser = UserCache.getUser(GuardianCurrentStatus.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianCurrentStatus.getUpdateUser());
		//GuardianCurrentStatusVO.setCreateUserName(createUser.getName());
		//GuardianCurrentStatusVO.setUpdateUserName(updateUser.getName());

		return GuardianCurrentStatusVO;
	}


}

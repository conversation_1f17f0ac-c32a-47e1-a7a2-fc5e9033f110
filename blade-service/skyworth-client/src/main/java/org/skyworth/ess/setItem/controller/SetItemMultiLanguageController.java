/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity;
import org.skyworth.ess.setItem.vo.SetItemMultiLanguageVO;
import org.skyworth.ess.setItem.excel.SetItemMultiLanguageExcel;
import org.skyworth.ess.setItem.wrapper.SetItemMultiLanguageWrapper;
import org.skyworth.ess.setItem.service.ISetItemMultiLanguageService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * APP设置项配置名称多语言 控制器
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@RestController
@AllArgsConstructor
@RequestMapping(" /setItemMultiLanguage")
@Api(value = "APP设置项配置名称多语言", tags = "APP设置项配置名称多语言接口")
public class SetItemMultiLanguageController extends BladeController {

	private final ISetItemMultiLanguageService setItemMultiLanguageService;

	/**
	 * APP设置项配置名称多语言 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入setItemMultiLanguage")
	public R<SetItemMultiLanguageVO> detail(SetItemMultiLanguageEntity setItemMultiLanguage) {
		SetItemMultiLanguageEntity detail = setItemMultiLanguageService.getOne(Condition.getQueryWrapper(setItemMultiLanguage));
		return R.data(SetItemMultiLanguageWrapper.build().entityVO(detail));
	}
	/**
	 * APP设置项配置名称多语言 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入setItemMultiLanguage")
	public R<IPage<SetItemMultiLanguageVO>> list(@ApiIgnore @RequestParam Map<String, Object> setItemMultiLanguage, Query query) {
		IPage<SetItemMultiLanguageEntity> pages = setItemMultiLanguageService.page(Condition.getPage(query), Condition.getQueryWrapper(setItemMultiLanguage, SetItemMultiLanguageEntity.class));
		return R.data(SetItemMultiLanguageWrapper.build().pageVO(pages));
	}

	/**
	 * APP设置项配置名称多语言 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入setItemMultiLanguage")
	public R<IPage<SetItemMultiLanguageVO>> page(SetItemMultiLanguageVO setItemMultiLanguage, Query query) {
		IPage<SetItemMultiLanguageVO> pages = setItemMultiLanguageService.selectSetItemMultiLanguagePage(Condition.getPage(query), setItemMultiLanguage);
		return R.data(pages);
	}

	/**
	 * APP设置项配置名称多语言 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入setItemMultiLanguage")
	public R save(@Valid @RequestBody SetItemMultiLanguageEntity setItemMultiLanguage) {
		return R.status(setItemMultiLanguageService.save(setItemMultiLanguage));
	}

	/**
	 * APP设置项配置名称多语言 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入setItemMultiLanguage")
	public R update(@Valid @RequestBody SetItemMultiLanguageEntity setItemMultiLanguage) {
		return R.status(setItemMultiLanguageService.updateById(setItemMultiLanguage));
	}

	/**
	 * APP设置项配置名称多语言 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入setItemMultiLanguage")
	public R submit(@Valid @RequestBody SetItemMultiLanguageEntity setItemMultiLanguage) {
		return R.status(setItemMultiLanguageService.saveOrUpdate(setItemMultiLanguage));
	}

	/**
	 * APP设置项配置名称多语言 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(setItemMultiLanguageService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-setItemMultiLanguage")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入setItemMultiLanguage")
	public void exportSetItemMultiLanguage(@ApiIgnore @RequestParam Map<String, Object> setItemMultiLanguage, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SetItemMultiLanguageEntity> queryWrapper = Condition.getQueryWrapper(setItemMultiLanguage, SetItemMultiLanguageEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SetItemMultiLanguage::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SetItemMultiLanguageEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SetItemMultiLanguageExcel> list = setItemMultiLanguageService.exportSetItemMultiLanguage(queryWrapper);
		ExcelUtil.export(response, "APP设置项配置名称多语言数据" + DateUtil.time(), "APP设置项配置名称多语言数据表", list, SetItemMultiLanguageExcel.class);
	}

}

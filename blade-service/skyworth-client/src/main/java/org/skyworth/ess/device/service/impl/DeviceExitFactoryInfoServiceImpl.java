/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.service.impl.BatteryExitFactoryInfoServiceImpl;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel;
import org.skyworth.ess.device.mapper.DeviceExitFactoryInfoMapper;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.ota.service.IDeviceSoftwareVersionInfoService;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备/智能能量变换器出厂信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Service
@AllArgsConstructor
public class DeviceExitFactoryInfoServiceImpl extends BaseServiceImpl<DeviceExitFactoryInfoMapper, DeviceExitFactoryInfoEntity> implements IDeviceExitFactoryInfoService {
	private final IDictBizClient dictBizClient;
	private final IWifiStickPlantService wifiStickPlantService;
	private final IDeviceSoftwareVersionInfoService deviceSoftwareVersionInfoService;

	@Override
	public IPage<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage<DeviceExitFactoryInfoVO> page, DeviceExitFactoryInfoVO deviceExitFactoryInfo) {
		return page.setRecords(baseMapper.selectDeviceExitFactoryInfoPage(page, deviceExitFactoryInfo));
	}


	@Override
	public List<DeviceExitFactoryInfoExcel> exportDeviceExitFactoryInfo(Wrapper<DeviceExitFactoryInfoEntity> queryWrapper) {
		List<DeviceExitFactoryInfoExcel> deviceExitFactoryInfoList = baseMapper.exportDeviceExitFactoryInfo(queryWrapper);
		String belongingSceneDictKey = "belonging_scene";
		Map<String, List<DictBiz>> dictMap = dictBizClient.batchGetList(Arrays.asList("device_company", "device_net_type", "deive_single_third_phase",
			"device_mode_type", "device_firmware_batch",belongingSceneDictKey)).getData();
		Map<String, String> companyMap = listDictToMap(dictMap.get("device_company"));
		Map<String, String> deviceNetTypeMap = listDictToMap(dictMap.get("device_net_type"));
		Map<String, String> deiveSingleThirdPhaseMap = listDictToMap(dictMap.get("deive_single_third_phase"));
		Map<String, String> modeTypeMap = listDictToMap(dictMap.get("device_mode_type"));
		Map<String, String> firmwareBatchMap = listDictToMap(dictMap.get("device_firmware_batch"));
		Map<String, String> belongingSceneDictMap = listDictToMap(dictMap.get(belongingSceneDictKey));
		for (DeviceExitFactoryInfoExcel excel : deviceExitFactoryInfoList) {
			if (StringUtil.isNotBlank(excel.getCompany()) && companyMap.containsKey(excel.getCompany())) {
				excel.setCompany(companyMap.get(excel.getCompany()));
			}
			if (StringUtil.isNotBlank(excel.getNetType()) && deviceNetTypeMap.containsKey(excel.getNetType())) {
				excel.setNetType(deviceNetTypeMap.get(excel.getNetType()));
			}
			if (StringUtil.isNotBlank(excel.getSingleThirdPhase()) && deiveSingleThirdPhaseMap.containsKey(excel.getSingleThirdPhase())) {
				excel.setSingleThirdPhase(deiveSingleThirdPhaseMap.get(excel.getSingleThirdPhase()));
			}
			if (StringUtil.isNotBlank(excel.getDeviceType()) && modeTypeMap.containsKey(excel.getDeviceType())) {
				excel.setDeviceType(modeTypeMap.get(excel.getDeviceType()));
			}
			if (StringUtil.isNotBlank(excel.getFirmwareBatch()) && firmwareBatchMap.containsKey(excel.getFirmwareBatch())) {
				excel.setFirmwareBatch(firmwareBatchMap.get(excel.getFirmwareBatch()));
			}
			if (StringUtil.isNotBlank(excel.getBelongingScene()) && belongingSceneDictMap.containsKey(excel.getBelongingScene())) {
				excel.setBelongingScene(belongingSceneDictMap.get(excel.getBelongingScene()));
			}
		}
		return deviceExitFactoryInfoList;
	}

	private Map<String, String> listDictToMap(List<DictBiz> dictBizList) {
		return Optional.ofNullable(dictBizList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
	}

	@Override
	public List<DeviceExitFactoryInfoVO> queryDeviceExitFactoryByPlant(Long plantId) {
		return baseMapper.queryDeviceExitFactoryByPlant(plantId);
	}

	@Override
	public R deleteLogicDeviceExitFactory(List<Long> ids) {
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntities = baseMapper.queryByIds(ids);
		if (deviceExitFactoryInfoEntities == null || deviceExitFactoryInfoEntities.isEmpty()) {
			return R.fail("数据不存在");
		}
		List<String> deviceSerialNumberList = deviceExitFactoryInfoEntities.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
		List<WifiStickPlantEntity> wifiStickPlantEntities = wifiStickPlantService.queryByDeviceSerialNumberList(deviceSerialNumberList);
		if (wifiStickPlantEntities != null && !wifiStickPlantEntities.isEmpty()) {
			return R.fail("智能能量变换器SN被使用，不能删除");
		}
		BladeUser user = AuthUtil.getUser();
		baseMapper.deleteLogicDeviceExitFactory(user.getUserId(), user.getAccount(), ids);
		// 删除升级记录信息
		deviceSoftwareVersionInfoService.batchDeleteByDeviceSn(deviceSerialNumberList);
		return R.data(true);
	}

	@Override
	public String importAddExcel(List<DeviceExitFactoryInfoExcel> data, boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(DeviceExitFactoryInfoExcel::getDeviceSerialNumber).collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntities = baseMapper.queryByDeviceSerialNumbers(deviceSerialNumbers);
		if (CollectionUtil.isNotEmpty(deviceExitFactoryInfoEntities)) {
			List<String> collect = deviceExitFactoryInfoEntities.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			String join = Joiner.on(",").skipNulls().join(collect);
			return join + " had exists, please use modify import.";
		}
		List<DeviceExitFactoryInfoEntity> insert = new ArrayList<>();
		data.forEach(userExcel -> {
			DeviceExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, DeviceExitFactoryInfoEntity.class));
			entity.setCreateUserAccount(user.getAccount());
			entity.setCreateUser(user.getUserId());
			entity.setExitFactoryDate(new java.sql.Date(userExcel.getExitFactoryDate().getTime()));
			entity.setStatus(CommonConstant.NOT_SEALED_ID);
			insert.add(entity);
		});
		this.saveOrUpdateBatch(insert);
		return "";
	}

	@Override
	public String importModifyExcel(List<DeviceExitFactoryInfoExcel> data, boolean isCovered) {
		BladeUser user = AuthUtil.getUser();
		List<String> deviceSerialNumbers = data.stream().map(DeviceExitFactoryInfoExcel::getDeviceSerialNumber).collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntities = baseMapper.queryByDeviceSerialNumbers(deviceSerialNumbers);
		// 如果入参中个数 和 DB中个数不相等，则表示有新增数据，提示使用新增导入
		if (deviceSerialNumbers.size() != deviceExitFactoryInfoEntities.size()) {
			List<String> dbDeviceSerialNumbers = deviceExitFactoryInfoEntities.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			StringBuilder join = new StringBuilder();
			deviceSerialNumbers.forEach(p -> {
				if (!dbDeviceSerialNumbers.contains(p)) {
					join.append(p).append(",");
				}
			});
			return join + " had not exists, please use add import.";
		}
		List<DeviceExitFactoryInfoEntity> update = new ArrayList<>();
		data.forEach(userExcel -> {
			DeviceExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, DeviceExitFactoryInfoEntity.class));
			entity.setUpdateUserAccount(user.getAccount());
			entity.setUpdateUser(user.getUserId());
			update.add(entity);
		});
		baseMapper.updateBatchBySn(update);
		return "";
	}

	@Override
	public int batchUpdate(List<String> deviceSerialNumberList) {
		List<DeviceExitFactoryInfoEntity> update = new ArrayList<>();
		BladeUser user = AuthUtil.getUser();
		for (String deviceSerialNumber : deviceSerialNumberList) {
			DeviceExitFactoryInfoEntity entity = new DeviceExitFactoryInfoEntity();
			entity.setUpdateUser(user.getUserId());
			entity.setUpdateUserAccount(user.getAccount());
			entity.setDeviceSerialNumber(deviceSerialNumber);
			entity.setUpdateTime(new Date());
			entity.setStatus(0);
			update.add(entity);
		}
		return baseMapper.updateBatchBySn(update);
	}

	@Override
	public BigDecimal queryRatedTotalPowerOfInverter() {
		return baseMapper.queryRatedTotalPowerOfInverter();
	}

	@Override
	public int updateNewQualityQuaranteeYear(DeviceExitFactoryInfoEntity entity) {
		return baseMapper.updateNewQualityQuaranteeYear(entity);
	}

	@Override
	public boolean updateDeadlineInfo(DeviceExitFactoryInfoEntity entity) {
		QueryWrapper<DeviceExitFactoryInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("device_serial_number", entity.getDeviceSerialNumber());
		List<DeviceExitFactoryInfoEntity> list = baseMapper.selectList(queryWrapper);
		if (!list.isEmpty()) {
			DeviceExitFactoryInfoEntity deviceExitFactoryInfo = list.get(0);
			String startDate = deviceExitFactoryInfo.getWarrantyStartDate();
			String qualityGuaranteeYear = deviceExitFactoryInfo.getQualityGuaranteeYear();
			if (ValidationUtil.isNotEmpty(startDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
				entity.setWarrantyDeadline(BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear), startDate));
			}
			baseMapper.updateById(entity);
			return true;
		}
		return false;
	}

	@Cached(name = "deviceModelProtocol.", key = "#deviceExitFactoryInfoEntity.deviceSerialNumber", expire = 12, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	@Override
	public DeviceExitFactoryInfoEntity getModelAndProtocolBySn(DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity) {
		return this.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
	}

	@Override
	public List<DeviceExitFactoryInfoEntity> getListByDeviceSerialNumberCollect(List<String> deviceSerialNumberCollect) {
		return baseMapper.getListByDeviceSerialNumberCollect(deviceSerialNumberCollect);
	}
}

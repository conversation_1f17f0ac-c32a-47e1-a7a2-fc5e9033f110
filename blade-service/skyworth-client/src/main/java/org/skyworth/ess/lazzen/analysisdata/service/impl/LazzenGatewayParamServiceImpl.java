package org.skyworth.ess.lazzen.analysisdata.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.analysisdata.service.constant.ObjAttributeName;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.util.HumpConvert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 网关心跳
 */
@Service("lazzenGatewayParamServiceImpl")
@Slf4j
public class LazzenGatewayParamServiceImpl extends LazzenAbstractService{
    @Autowired
    IGatewayPlantService gatewayPlantServiceImpl;
    @Override
    public void saveData(List<JSONObject> listMap) {
        log.info("LazzenGatewayParamServiceImpl heart beat saveData params : {}", listMap);
        List<GatewayPlantEntity> updateList = new ArrayList<>();
        for(JSONObject jsonObject : listMap) {
            GatewayPlantEntity updateEntity = new GatewayPlantEntity();
            String gatewayUniqueNumber = (String) jsonObject.get(ObjAttributeName.gatewayUniqueNumber.getAttributeName());
            LocalDateTime deviceDateTime = (LocalDateTime) jsonObject.get(ObjAttributeName.deviceDateTime.getAttributeName());
            updateEntity.setGatewayUniqueNumber(gatewayUniqueNumber);
            updateEntity.setHeartBeatTime(deviceDateTime);
            updateEntity.setStatus(1);
            updateEntity.setIsDeleted(0);
            updateList.add(updateEntity);
        }
        // 更新心跳
        gatewayPlantServiceImpl.batchUpdateHeartTimeByGatewayUniqueNumber(updateList);
        // 更新状态，除了告警
        gatewayPlantServiceImpl.batchUpdateStatusByGatewayUniqueNumber(updateList);
    }
}

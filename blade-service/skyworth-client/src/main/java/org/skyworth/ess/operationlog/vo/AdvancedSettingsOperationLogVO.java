/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.operationlog.vo;

import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 高级设置操作日志 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdvancedSettingsOperationLogVO extends AdvancedSettingsOperationLogEntity {
	private static final long serialVersionUID = 1L;

}

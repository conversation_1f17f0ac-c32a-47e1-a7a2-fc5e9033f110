/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedswitchgate.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.guardian.timedswitchgate.vo.GuardianTimedSwitchGateVO;
import org.skyworth.ess.guardian.timedswitchgate.excel.GuardianTimedSwitchGateExcel;
import org.skyworth.ess.guardian.timedswitchgate.mapper.GuardianTimedSwitchGateMapper;
import org.skyworth.ess.guardian.timedswitchgate.service.IGuardianTimedSwitchGateService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安全卫士定时设置-开关闸设置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class GuardianTimedSwitchGateServiceImpl extends BaseServiceImpl<GuardianTimedSwitchGateMapper, GuardianTimedSwitchGateEntity> implements IGuardianTimedSwitchGateService {

	@Override
	public IPage<GuardianTimedSwitchGateVO> selectGuardianTimedSwitchGatePage(IPage<GuardianTimedSwitchGateVO> page, GuardianTimedSwitchGateVO GuardianTimedSwitchGate) {
		return page.setRecords(baseMapper.selectGuardianTimedSwitchGatePage(page, GuardianTimedSwitchGate));
	}


	@Override
	public List<GuardianTimedSwitchGateExcel> exportGuardianTimedSwitchGate(Wrapper<GuardianTimedSwitchGateEntity> queryWrapper) {
		List<GuardianTimedSwitchGateExcel> GuardianTimedSwitchGateList = baseMapper.exportGuardianTimedSwitchGate(queryWrapper);
		//GuardianTimedSwitchGateList.forEach(GuardianTimedSwitchGate -> {
		//	GuardianTimedSwitchGate.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianTimedSwitchGate.getType()));
		//});
		return GuardianTimedSwitchGateList;
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		LambdaUpdateWrapper<GuardianTimedSwitchGateEntity> update = Wrappers.<GuardianTimedSwitchGateEntity>update().lambda()
			.set(GuardianTimedSwitchGateEntity::getIsDeleted, 1)
			.eq(GuardianTimedSwitchGateEntity::getPlantId, plantId)
			.eq(GuardianTimedSwitchGateEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber);
		return baseMapper.delete(update);
	}

}

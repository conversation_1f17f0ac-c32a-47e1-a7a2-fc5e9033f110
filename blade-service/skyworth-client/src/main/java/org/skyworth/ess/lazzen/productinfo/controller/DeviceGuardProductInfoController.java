/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.productinfo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity;
import org.skyworth.ess.lazzen.productinfo.vo.DeviceGuardProductInfoVO;
import org.skyworth.ess.lazzen.productinfo.excel.DeviceGuardProductInfoExcel;
import org.skyworth.ess.lazzen.productinfo.wrapper.DeviceGuardProductInfoWrapper;
import org.skyworth.ess.lazzen.productinfo.service.IDeviceGuardProductInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 产品信息 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("deviceGuardProductInfo/deviceGuardProductInfo")
@Api(value = "产品信息", tags = "产品信息接口")
public class DeviceGuardProductInfoController extends BladeController {

	private final IDeviceGuardProductInfoService deviceGuardProductInfoService;

	/**
	 * 产品信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceGuardProductInfo")
	public R<DeviceGuardProductInfoVO> detail(DeviceGuardProductInfoEntity deviceGuardProductInfo) {
		DeviceGuardProductInfoEntity detail = deviceGuardProductInfoService.getOne(Condition.getQueryWrapper(deviceGuardProductInfo));
		return R.data(DeviceGuardProductInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 产品信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceGuardProductInfo")
	public R<IPage<DeviceGuardProductInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceGuardProductInfo, Query query) {
		IPage<DeviceGuardProductInfoEntity> pages = deviceGuardProductInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceGuardProductInfo, DeviceGuardProductInfoEntity.class));
		return R.data(DeviceGuardProductInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 产品信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceGuardProductInfo")
	public R<IPage<DeviceGuardProductInfoVO>> page(DeviceGuardProductInfoVO deviceGuardProductInfo, Query query) {
		IPage<DeviceGuardProductInfoVO> pages = deviceGuardProductInfoService.selectDeviceGuardProductInfoPage(Condition.getPage(query), deviceGuardProductInfo);
		return R.data(pages);
	}

	/**
	 * 产品信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceGuardProductInfo")
	public R save(@Valid @RequestBody DeviceGuardProductInfoEntity deviceGuardProductInfo) {
		return R.status(deviceGuardProductInfoService.save(deviceGuardProductInfo));
	}

	/**
	 * 产品信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceGuardProductInfo")
	public R update(@Valid @RequestBody DeviceGuardProductInfoEntity deviceGuardProductInfo) {
		return R.status(deviceGuardProductInfoService.updateById(deviceGuardProductInfo));
	}

	/**
	 * 产品信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceGuardProductInfo")
	public R submit(@Valid @RequestBody DeviceGuardProductInfoEntity deviceGuardProductInfo) {
		return R.status(deviceGuardProductInfoService.saveOrUpdate(deviceGuardProductInfo));
	}

	/**
	 * 产品信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceGuardProductInfoService.deleteLogic(Func.toLongList(ids)));
	}
}

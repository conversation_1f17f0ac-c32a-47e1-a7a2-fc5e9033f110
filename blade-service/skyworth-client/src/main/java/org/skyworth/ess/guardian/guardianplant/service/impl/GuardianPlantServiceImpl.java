/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.constant.SecurityGuardStatusEnum;
import org.skyworth.ess.device.vo.QueryPowerTypeEnum;
import org.skyworth.ess.feign.FeignConfig;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.feign.IConsoleIssueClient;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity;
import org.skyworth.ess.guardian.currentstatus.service.IGuardianCurrentStatusService;
import org.skyworth.ess.guardian.currentstatus.vo.GuardianCurrentStatusVO;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.guardian.guardianlog.service.IGuardianLogService;
import org.skyworth.ess.guardian.guardianplant.dto.GuardianLogQueryCondition;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.excel.GuardianPlantExcel;
import org.skyworth.ess.guardian.guardianplant.mapper.GuardianPlantMapper;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianLogDataStatVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantOverviewVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantReportQueryVO;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.service.IGuardianTimedPowerService;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.guardian.timedswitchgate.service.IGuardianTimedSwitchGateService;
import org.springblade.common.utils.SerialNumberGenerator;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 安全卫士对应站点 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
@Slf4j
public class GuardianPlantServiceImpl extends BaseServiceImpl<GuardianPlantMapper, GuardianPlantEntity> implements IGuardianPlantService {

	public static final DateTimeFormatter ymdhmFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
	public static final DateTimeFormatter ymdFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public static final DateTimeFormatter hmFormatter = DateTimeFormatter.ofPattern("HH:mm");

	@Resource
	private IGuardianCurrentStatusService guardianCurrentStatusService;
	@Resource
	private IGuardianAlarmThresholdService guardianAlarmThresholdService;
	@Resource
	private IGuardianTimedPowerService guardianTimedPowerService;
	@Resource
	private IGuardianTimedSwitchGateService guardianTimedSwitchGateService;
	@Resource
	@Lazy
	private IGuardianExitFactoryInfoService guardianExitFactoryInfoService;
	@Resource
	private IGuardianLogService iGuardianLogService;
	@Resource
	private IUserSearchClient userSearchClient;
	@Resource
	private IAgentClient agentClient;
	@Resource
	private ISysClient sysClient;
	@Resource
	private BladeRedis bladeRedis;
	@Resource
	private IConsoleIssueClient iConsoleIssueClient;


	@Override
	public GuardianPlantOverviewVO getGuardianPlantOverview(GuardianPlantVO guardianPlantVO) {
		GuardianPlantOverviewVO guardianPlantOverviewVO = new GuardianPlantOverviewVO(0);

		Set<Long> userIdSet = new HashSet<>();
		//搜索用户名
		String userName = guardianPlantVO.getUserName();
		if (ValidationUtil.isNotEmpty(userName)) {
			List<User> userNameList = userSearchClient.listByRealName(userName).getData();
			if (ValidationUtil.isNotEmpty(userNameList) && !userNameList.isEmpty()) {
				String userIds = userNameList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return guardianPlantOverviewVO;
			}
		}

		//搜索用户手机号
		String userPhone = guardianPlantVO.getUserPhone();
		if (ValidationUtil.isNotEmpty(userPhone)) {
			List<User> userPhoneList = userSearchClient.listByPhone(userPhone).getData();
			if (ValidationUtil.isNotEmpty(userPhoneList) && !userPhoneList.isEmpty()) {
				String userIds = userPhoneList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return guardianPlantOverviewVO;
			}
		}
		guardianPlantVO.setUserIds(List.copyOf(userIdSet));

		//搜索运维人员
		List<User> agentUserList;
		String agentUserName = guardianPlantVO.getAgentUserName();
		if (ValidationUtil.isNotEmpty(agentUserName)) {
			agentUserList = userSearchClient.listByRealName(agentUserName).getData();
			if (ValidationUtil.isNotEmpty(agentUserList) && !agentUserList.isEmpty()) {
				String userIds = agentUserList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				guardianPlantVO.setAgentUserIds(Func.toLongList(userIds));
			} else {
				return guardianPlantOverviewVO;
			}
		}

		//搜索运维团队
		String companyName = guardianPlantVO.getAgentCompanyName();
		List<AgentCompanyVO> agentCompanyVOList;
		if (ValidationUtil.isNotEmpty(companyName)) {
			agentCompanyVOList = agentClient.agentCompany(companyName).getData();
			if (ValidationUtil.isNotEmpty(agentCompanyVOList) && !agentCompanyVOList.isEmpty()) {
				String companyIds = agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				guardianPlantVO.setAgentCompanyIds(Func.toLongList(companyIds));
			} else {
				return guardianPlantOverviewVO;
			}
		}

		IPage<GuardianPlantVO> page = Condition.getPage(new Query());
		baseMapper.selectGuardianPlantPage(page, guardianPlantVO);
		guardianPlantOverviewVO.setSecurityGuardTotal(Func.toInt(page.getTotal()));
		return guardianPlantOverviewVO;
	}

	@Override
	public IPage<GuardianPlantVO> page(Query query, GuardianPlantVO guardianPlantVO) {
		IPage<GuardianPlantVO> page = Condition.getPage(query);

		Set<Long> userIdSet = new HashSet<>();
		//搜索用户名
		String userName = guardianPlantVO.getUserName();
		if (ValidationUtil.isNotEmpty(userName)) {
			List<User> userNameList = userSearchClient.listByRealName(userName).getData();
			if (ValidationUtil.isNotEmpty(userNameList) && !userNameList.isEmpty()) {
				String userIds = userNameList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索用户手机号
		String userPhone = guardianPlantVO.getUserPhone();
		if (ValidationUtil.isNotEmpty(userPhone)) {
			List<User> userPhoneList = userSearchClient.listByPhone(userPhone).getData();
			if (ValidationUtil.isNotEmpty(userPhoneList) && !userPhoneList.isEmpty()) {
				String userIds = userPhoneList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}
		guardianPlantVO.setUserIds(List.copyOf(userIdSet));

		//搜索运维人员
		List<User> agentUserList;
		String agentUserName = guardianPlantVO.getAgentUserName();
		if (ValidationUtil.isNotEmpty(agentUserName)) {
			agentUserList = userSearchClient.listByRealName(agentUserName).getData();
			if (ValidationUtil.isNotEmpty(agentUserList) && !agentUserList.isEmpty()) {
				String userIds = agentUserList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				guardianPlantVO.setAgentUserIds(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索运维团队
		String companyName = guardianPlantVO.getAgentCompanyName();
		List<AgentCompanyVO> agentCompanyVOList;
		if (ValidationUtil.isNotEmpty(companyName)) {
			agentCompanyVOList = agentClient.agentCompany(companyName).getData();
			if (ValidationUtil.isNotEmpty(agentCompanyVOList) && !agentCompanyVOList.isEmpty()) {
				String companyIds = agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				guardianPlantVO.setAgentCompanyIds(Func.toLongList(companyIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		List<GuardianPlantVO> guardianPlantVOList = baseMapper.selectGuardianPlantPage(page, guardianPlantVO);
		guardianPlantVOListHandle(guardianPlantVOList);

		return page.setRecords(guardianPlantVOList);
	}

	@Override
	public GuardianPlantVO detail(GuardianPlantEntity guardianPlantEntity) {
		GuardianPlantVO guardianPlantVO = new GuardianPlantVO();
		if (ObjectUtil.isEmpty(guardianPlantEntity.getPlantId()) || StringUtil.isBlank(guardianPlantEntity.getSecurityGuardSerialNumber())) {
			log.warn("参数异常，无法查询详情");
			return guardianPlantVO;
		}
		GuardianPlantEntity guardianPlantEntityResp = baseMapper.detail(guardianPlantEntity.getPlantId(), guardianPlantEntity.getSecurityGuardSerialNumber());
		BeanUtil.copy(guardianPlantEntityResp, guardianPlantVO);
		List<GuardianPlantVO> guardianPlantVOList = Collections.singletonList(guardianPlantVO);
		guardianPlantVOListHandle(guardianPlantVOList);
		return guardianPlantVO;
	}

	@Override
	public GuardianCurrentStatusVO detailCurrentStatus(GuardianPlantEntity guardianPlantEntity) {
		GuardianCurrentStatusVO guardianCurrentStatusVO = new GuardianCurrentStatusVO();
		GuardianPlantEntity guardianPlantEntityResp = this.getOne(Condition.getQueryWrapper(guardianPlantEntity));
		// 查询doris：security_guard_current_status，获取安全卫士当前状态
		if (ObjectUtil.isNotEmpty(guardianPlantEntityResp)) {
			LambdaQueryWrapper<GuardianCurrentStatusEntity> guardianCurrentStatusEntityWrapper = new LambdaQueryWrapper<>();
			guardianCurrentStatusEntityWrapper.eq(GuardianCurrentStatusEntity::getSecurityGuardSerialNumber, guardianPlantEntityResp.getSecurityGuardSerialNumber()).eq(GuardianCurrentStatusEntity::getPlantId, guardianPlantEntityResp.getPlantId());
			GuardianCurrentStatusEntity guardianCurrentStatusEntity = guardianCurrentStatusService.getOne(guardianCurrentStatusEntityWrapper);
			BeanUtil.copy(guardianCurrentStatusEntity, guardianCurrentStatusVO);
			guardianCurrentStatusVO.setSecurityGuardStatus(guardianPlantEntityResp.getSecurityGuardStatus());
			guardianCurrentStatusVO.setGatePositionStatus(guardianPlantEntityResp.getGatePositionStatus());
			guardianCurrentStatusVO.setGatePositionStatusName((guardianPlantEntityResp.getGatePositionStatus() == 0 || ObjectUtil.isEmpty(guardianPlantEntityResp.getGatePositionStatus())) ? "关闸" : "合闸");
			guardianCurrentStatusVO.setDeviceType(guardianPlantEntityResp.getDeviceType());
			// 查询GuardianExitFactoryInfoEntity，获取安全卫士sim卡号
			LambdaQueryWrapper<GuardianExitFactoryInfoEntity> exitFactoryInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
			exitFactoryInfoEntityLambdaQueryWrapper.eq(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, guardianPlantEntityResp.getSecurityGuardSerialNumber());
			GuardianExitFactoryInfoEntity guardianExitFactoryInfoEntity = guardianExitFactoryInfoService.getOne(exitFactoryInfoEntityLambdaQueryWrapper);
			if (ObjectUtil.isNotEmpty(guardianExitFactoryInfoEntity)) {
				guardianCurrentStatusVO.setSimCardNumber(guardianExitFactoryInfoEntity.getSimCardNumber());
			}
		}
		return guardianCurrentStatusVO;
	}

	@Override
	public Boolean unbind(GuardianPlantEntity guardianPlantEntity) {
		// 安全卫士-高级设置-设备解绑
		if (ObjectUtil.isEmpty(guardianPlantEntity) || ObjectUtil.isEmpty(guardianPlantEntity.getPlantId()) || StringUtil.isBlank(guardianPlantEntity.getSecurityGuardSerialNumber())) {
			log.warn("安全卫士-高级设置-设备解绑失败，参数为空");
			return false;
		}

		// 调用解除netty连接的接口
		String remoteChannelIp = bladeRedis.get(GuardianInstructConstants.PRE_HEADER + guardianPlantEntity.getSecurityGuardSerialNumber());
		FeignConfig.setDynamicIp(remoteChannelIp);
		JSONObject issueObject = new JSONObject();
		issueObject.put("bizType", "1");
		issueObject.put("deviceSn", guardianPlantEntity.getSecurityGuardSerialNumber());
		issueObject.put("serialNumber", SerialNumberGenerator.generateSerialNumber());
		issueObject.put("content", "unbind guardian " + guardianPlantEntity.getSecurityGuardSerialNumber());
		R<String> issueGuardR = iConsoleIssueClient.issueGuard(issueObject);
		FeignConfig.remove();
		if (!issueGuardR.isSuccess()) {
			// 设备解绑把guardian_plant对应记录删除掉
			boolean remove = this.remove(Condition.getQueryWrapper(guardianPlantEntity));
			// 删除成功后再删除参数设置（下面3个表）相关记录
			if (remove) {
				// 删除表guardian_alarm_threshold对应记录
				GuardianAlarmThresholdEntity guardianAlarmThresholdEntity = new GuardianAlarmThresholdEntity();
				guardianAlarmThresholdEntity.setPlantId(guardianPlantEntity.getPlantId());
				guardianAlarmThresholdEntity.setSecurityGuardSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
				guardianAlarmThresholdService.remove(Condition.getQueryWrapper(guardianAlarmThresholdEntity));

				// 删除表guardian_timed_power对应记录
				GuardianTimedPowerEntity guardianTimedPowerEntity = new GuardianTimedPowerEntity();
				guardianTimedPowerEntity.setPlantId(guardianPlantEntity.getPlantId());
				guardianTimedPowerEntity.setSecurityGuardSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
				guardianTimedPowerService.remove(Condition.getQueryWrapper(guardianTimedPowerEntity));

				// 删除表guardian_timed_switch_gate对应记录
				GuardianTimedSwitchGateEntity guardianTimedSwitchGateEntity = new GuardianTimedSwitchGateEntity();
				guardianTimedSwitchGateEntity.setPlantId(guardianPlantEntity.getPlantId());
				guardianTimedSwitchGateEntity.setSecurityGuardSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
				guardianTimedSwitchGateService.remove(Condition.getQueryWrapper(guardianTimedSwitchGateEntity));

				// 恢复出厂信息表
				GuardianExitFactoryInfoEntity guardianExitFactoryInfoEntity = new GuardianExitFactoryInfoEntity();
				guardianExitFactoryInfoEntity.setSecurityGuardSerialNumber(guardianPlantEntity.getSecurityGuardSerialNumber());
				guardianExitFactoryInfoEntity.setStatus(BizConstant.CLIENT_EXIT_INFO_STATUS_UNUSE);
				guardianExitFactoryInfoService.update(Condition.getQueryWrapper(guardianExitFactoryInfoEntity));
			} else {
				log.warn("安全卫士-高级设置-设备解绑失败，删除guardian_plant对应记录失败");
				return false;
			}
		} else {
			log.warn("安全卫士-高级设置-设备解绑失败，调用接口失败，返回信息：{}", issueGuardR.getMsg());
			return false;
		}
		return true;
	}

	@Override
	public Map<String, List<Object>> selectStatusReport(GuardianPlantReportQueryVO queryCondition) {
		List<GuardianLogDataStatVO> guardianLogDataStatVOS = getGuardianLogDataStatVOS(queryCondition);
		return processStatusListData(guardianLogDataStatVOS, queryCondition.getQueryPowerType());
	}

	@Override
	public void selectStatusReportExport(GuardianPlantReportQueryVO queryCondition, HttpServletResponse response) {
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("Guardian_Everyday_Data", StandardCharsets.UTF_8) + ".xlsx");
			queryCondition.setQueryPowerType("voltage,current,temperature,leakage_current");
			List<GuardianLogDataStatVO> guardianLogDataStatVOS = this.getGuardianLogDataStatVOS(queryCondition);
			Map<String, List<Object>> statusListDataMap = this.processStatusListDataForExcel(guardianLogDataStatVOS);
			List<Object> dateTimeForDay = statusListDataMap.get("deviceDateTimeForDay");
			List<Object> minuteTime = statusListDataMap.get("deviceDateTime");
			// 分钟去重
			List<String> distinctMinuteTime = minuteTime.stream()
					.distinct()
					.map(Object::toString)
					.collect(Collectors.toList());
			// 日期去重
			List<String> firstHeadList = dateTimeForDay.stream()
					.distinct() // 去重
					.map(obj -> LocalDate.parse((String) obj, ymdFormatter)) // 转换为LocalDateTime类型
					.sorted(Comparator.naturalOrder()) // 按照时间顺序排序
					.map(dateTime -> dateTime.format(ymdFormatter)) // 格式化为字符串
					.collect(Collectors.toList()); // 转换为列表

			List<String> secondHeadVoltage = Arrays.asList("A PHASE VOLTAGE(V)", "B PHASE VOLTAGE(V)", "C PHASE VOLTAGE(V)");
			List<String> secondHeadCurrent = Arrays.asList("A PHASE CURRENT(A)", "B PHASE CURRENT(A)", "C PHASE CURRENT(A)");
			List<String> secondHeadTemperature = Arrays.asList("A PHASE TEMPERATURE(°C)", "B PHASE TEMPERATURE(°C)", "C PHASE TEMPERATURE(°C)", "N NEUTRAL LINE TEMPERATURE(°C)");
			List<String> secondHeadLeakageCurrent = List.of("LEAKAGE CURRENT(mA)");
			List<List<String>> voltageDataList = new ArrayList<>();
			List<List<String>> currentDataList = new ArrayList<>();
			List<List<String>> temperatureDataList = new ArrayList<>();
			List<List<String>> leakageCurrentDataList = new ArrayList<>();

			// 根据时间分组、再根据日期分组
			Map<String, Map<String, GuardianLogDataStatVO>> groupedMap = guardianLogDataStatVOS.stream()
					.collect(Collectors.groupingBy(GuardianLogDataStatVO::getDeviceDateTime,
							Collectors.toMap(GuardianLogDataStatVO::getDeviceDateTimeForDay, report -> report)));

			// 循环外层：按每1分钟的行数据
			for (String mintStr : distinctMinuteTime) {
				List<String> rowVoltage = Lists.newArrayList(mintStr);
				List<String> rowCurrent = Lists.newArrayList(mintStr);
				List<String> rowTemperature = Lists.newArrayList(mintStr);
				List<String> rowLeakageCurrent = Lists.newArrayList(mintStr);
				if (groupedMap.containsKey(mintStr)) {
					Map<String, GuardianLogDataStatVO> stringListMap = groupedMap.get(mintStr);
					// 循环内层：按日的列数据
					// 限制流的大小为总元素数减1(去除最后一天冗余日期)
					for (String dayData : firstHeadList) {
						if (stringListMap.containsKey(dayData)) {
							GuardianLogDataStatVO guardianLogDataStatVO = stringListMap.get(dayData);
							rowVoltage.add(StrUtil.toString(guardianLogDataStatVO.getAPhaseVoltage()));
							rowVoltage.add(StrUtil.toString(guardianLogDataStatVO.getBPhaseVoltage()));
							rowVoltage.add(StrUtil.toString(guardianLogDataStatVO.getCPhaseVoltage()));
							rowCurrent.add(StrUtil.toString(guardianLogDataStatVO.getAPhaseCurrent()));
							rowCurrent.add(StrUtil.toString(guardianLogDataStatVO.getBPhaseCurrent()));
							rowCurrent.add(StrUtil.toString(guardianLogDataStatVO.getCPhaseCurrent()));
							rowTemperature.add(StrUtil.toString(guardianLogDataStatVO.getAPhaseTemperature()));
							rowTemperature.add(StrUtil.toString(guardianLogDataStatVO.getBPhaseTemperature()));
							rowTemperature.add(StrUtil.toString(guardianLogDataStatVO.getCPhaseTemperature()));
							rowTemperature.add(StrUtil.toString(guardianLogDataStatVO.getNNeutralLineTemperature()));
							rowLeakageCurrent.add(StrUtil.toString(guardianLogDataStatVO.getLeakageCurrent()));
						}
					}
				}
				voltageDataList.add(rowVoltage);
				currentDataList.add(rowCurrent);
				temperatureDataList.add(rowTemperature);
				leakageCurrentDataList.add(rowLeakageCurrent);
			}

			ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
			// 构建sheet对象&写数据
			WriteSheet writeSheet1 = EasyExcelFactory.writerSheet("Voltage").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadVoltage, "Time")).build();
			excelWriter.write(voltageDataList, writeSheet1);
			WriteSheet writeSheet2 = EasyExcelFactory.writerSheet("Current").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadCurrent, "Time")).build();
			excelWriter.write(currentDataList, writeSheet2);
			WriteSheet writeSheet3 = EasyExcelFactory.writerSheet("Temperature").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadTemperature, "Time")).build();
			excelWriter.write(temperatureDataList, writeSheet3);
			WriteSheet writeSheet4 = EasyExcelFactory.writerSheet("LeakageCurrent").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadLeakageCurrent, "Time")).build();
			excelWriter.write(leakageCurrentDataList, writeSheet4);
			excelWriter.finish();
		} catch (Exception var6) {
			log.error("export guardian data error: {}", var6.getMessage(), var6);
		}
	}

	private List<List<String>> getHeadList(List<String> firstHeadList, List<String> secondHeadList, String leftmostRow) {
		List<List<String>> headList = new ArrayList<>();
		headList.add(Lists.newArrayList(leftmostRow));
		for (String first : firstHeadList) {
			for (String second : secondHeadList) {
				List<String> secondList = new ArrayList<>();
				secondList.add(first);
				secondList.add(second);
				headList.add(secondList);
			}
		}
		return headList;
	}

	@Override
	public List<GuardianPlantExcel> exportGuardianPlant(Wrapper<GuardianPlantEntity> queryWrapper) {
		List<GuardianPlantExcel> GuardianPlantList = baseMapper.exportGuardianPlant(queryWrapper);
		//GuardianPlantList.forEach(GuardianPlant -> {
		//	GuardianPlant.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianPlant.getType()));
		//});
		return GuardianPlantList;
	}

	@Override
	public List<GuardianPlantEntity> queryByGuardianSerialNumberList(List<String> serialNumberList) {
		return baseMapper.queryByGuardianSerialNumberList(serialNumberList);
	}

	@Override
	public List<GuardianPlantEntity> queryGuardianSerialNumberList(List<Long> longList) {
		return baseMapper.queryGuardianSerialNumberList(longList);
	}

	@Override
	public int batchDeleteLogicByPlantId(List<Long> longList, String account) {
		return baseMapper.batchDeleteLogicByPlantId(longList,account);
	}

	@Override
	public List<GuardianPlantEntity> queryOwnerData(Long userId) {
		return baseMapper.selectList(Wrappers.<GuardianPlantEntity>query().lambda()
			.eq(GuardianPlantEntity::getCreateUser,userId)
			.eq(GuardianPlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
	}

	@Override
	public int updateDataByCondition(GuardianPlantEntity updateGuardianPlantEntity) {
		return baseMapper.updateDataByCondition(updateGuardianPlantEntity);
	}

	@Override
	public Map<String, Object> getAllGuardianPowerSetup(Long plantId, String serialNumber) {
		return baseMapper.getAllGuardianPowerSetup(plantId, serialNumber);
	}

	@Override
	public Map<String, Object> getAllGuardianSwitchGateSetup(Long plantId, String serialNumber) {
		return baseMapper.getAllGuardianSwitchGateSetup(plantId, serialNumber);
	}

	@Override
	public GuardianCurrentStatusVO getGuardianStatusAndDeviceInfo(GuardianPlantEntity guardianPlantEntity) {
		GuardianCurrentStatusVO guardianCurrentStatusVO = new GuardianCurrentStatusVO();
		Long plantId = guardianPlantEntity.getPlantId();
		String securityGuardSerialNumber = guardianPlantEntity.getSecurityGuardSerialNumber();
		guardianCurrentStatusVO.setPlantId(plantId);
		guardianCurrentStatusVO.setSecurityGuardSerialNumber(securityGuardSerialNumber);
		//查询当前状态
		GuardianCurrentStatusEntity one = guardianCurrentStatusService.getOne(Wrappers.<GuardianCurrentStatusEntity>query().lambda().eq(GuardianCurrentStatusEntity::getPlantId, plantId).eq(GuardianCurrentStatusEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber));
		if(ObjectUtil.isNotNull(one)){
			BeanUtils.copyBeanProp(guardianCurrentStatusVO, one);
		}
		//查询站点信息
		GuardianPlantEntity guardianPlantVO = new GuardianPlantEntity();
		guardianPlantVO.setPlantId(plantId);
		guardianPlantVO.setSecurityGuardSerialNumber(securityGuardSerialNumber);
		GuardianPlantEntity two = this.getOne(Wrappers.<GuardianPlantEntity>query().lambda().eq(GuardianPlantEntity::getPlantId, plantId).eq(GuardianPlantEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber));
		if(ObjectUtil.isNotNull(two)){
			guardianCurrentStatusVO.setSecurityGuardStatus(two.getSecurityGuardStatus());
			guardianCurrentStatusVO.setGatePositionStatus(two.getGatePositionStatus());
		}
		//查询出厂信息
		GuardianExitFactoryInfoEntity guardianExitFactoryInfoVO = new GuardianExitFactoryInfoEntity();
		guardianExitFactoryInfoVO.setSecurityGuardSerialNumber(securityGuardSerialNumber);
		GuardianExitFactoryInfoEntity three = guardianExitFactoryInfoService.getOne(Wrappers.<GuardianExitFactoryInfoEntity>query().lambda().eq(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber));
		if(ObjectUtil.isNotNull(three)){
			guardianCurrentStatusVO.setSimCardNumber(three.getSimCardNumber());
			String clientGuardianType = DictBizCache.getValueByLanguage(DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode(), three.getDeviceType(), CommonUtil.getCurrentLanguage());
			guardianCurrentStatusVO.setDeviceType(clientGuardianType);
		}
		return guardianCurrentStatusVO;
	}

	@Override
	public Map<String, Object> getAllGuardianGatePositionSetup(Long plantId, String serialNumber) {
		return baseMapper.getAllGuardianGatePositionSetup(plantId,serialNumber);
	}

	@Override
	public List<Map<String, Object>> getPlantInfo(List<Long> plantIdList) {
		return baseMapper.getPlantInfo(plantIdList);
	}


	private @NotNull List<GuardianLogDataStatVO> getGuardianLogDataStatVOS(GuardianPlantReportQueryVO queryCondition) {
		Date startDateTime = queryCondition.getStartDateTime();
		Date endDateTime = queryCondition.getEndDateTime();
		// 查询模块状态曲线查询模块：枚举-QueryPowerTypeEnum
		String queryPowerType = queryCondition.getQueryPowerType();
		// 设置默认时间
		if (startDateTime == null || endDateTime == null) {
			Calendar start = Calendar.getInstance();
			start.set(Calendar.HOUR_OF_DAY, 0);
			start.set(Calendar.MINUTE, 0);
			start.set(Calendar.SECOND, 0);
			startDateTime = start.getTime();
			Calendar end = Calendar.getInstance();
			end.set(Calendar.HOUR_OF_DAY, 23);
			end.set(Calendar.MINUTE, 59);
			end.set(Calendar.SECOND, 59);
			endDateTime = end.getTime();
		}
		GuardianLogQueryCondition condition = new GuardianLogQueryCondition();
		condition.setPlantId(queryCondition.getPlantId());
		condition.setSecurityGuardSerialNumber(queryCondition.getSecurityGuardSerialNumber());
		condition.setStartDateTime(startDateTime);
		condition.setEndDateTime(endDateTime);
		List<GuardianLogDataStatVO> guardianLogDataStatVOList = iGuardianLogService.selectGuardianLogDataStatByTime(condition);
		Map<String, GuardianLogDataStatVO> guardianLogDataStatMap = guardianLogDataStatVOList.stream().collect(Collectors.toMap(GuardianLogDataStatVO::getDeviceDateTimeForCal, e -> e, (oldValue, newValue) -> newValue));

		LocalDateTime startDateTimeNew = startDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		LocalDateTime endDateTimeNew = endDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		// 当前时间点
		LocalDateTime currentDateTime = startDateTimeNew;

		// 每1分钟打一个点位，判断前面1分钟是否有数据，如16:01-16:02是否有数据?
		// 有数据则将数据则归并数据到时间点16:02
		// 没有数据则造一条数据
		// 注：查询条件的第一个数据，如每天的开始时间00:00，并且该分钟数是5的倍数，此时有一条数据上报，则会直接通过接口返回到前端，不需要走以下遍历
		while (currentDateTime.isBefore(endDateTimeNew)) {
			// 下一个时间点
			LocalDateTime next5MinDateTime = currentDateTime.plusMinutes(1);
			// 如果下一个1分钟是晚上00点00分，则将next5MinDateTime设置为当天23:59，避免把数据点位打到第二天
			if (next5MinDateTime.toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = currentDateTime.withHour(23).withMinute(59);
			}
			LocalDateTime mergerTime = next5MinDateTime;
			// 判断deviceDateTimeForCal字段的数据是否在两个时间点内，在就取出Map<String, InvertStatusReport>的数据，放到集合
			List<GuardianLogDataStatVO> guardianLogDataStatVOS = this.isBetween(currentDateTime, next5MinDateTime, guardianLogDataStatMap);
			// 如果下一个1分钟是晚上00点00分
			if (currentDateTime.plusMinutes(1).toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = next5MinDateTime.plusMinutes(1);
			}
			// 如果有这个时间点（16:01-16:02）的数据，则归并数据到时间点
			if (!guardianLogDataStatVOS.isEmpty()) {
				// 归并数据到下一个时间点
				mergeData(mergerTime, guardianLogDataStatVOS, guardianLogDataStatMap, queryPowerType);
			} else {
				// 造数据,默认都为0
				GuardianLogDataStatVO fakeData = getFakeData(mergerTime, queryPowerType);
				guardianLogDataStatMap.put(mergerTime.format(ymdhmFormatter), fakeData);
			}
			currentDateTime = next5MinDateTime; // 更新当前时间点为下一个时间点
		}

		List<GuardianLogDataStatVO> guardianLogDataStatVOS = new ArrayList<>(guardianLogDataStatMap.values());
		guardianLogDataStatVOS.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
		return guardianLogDataStatVOS;
	}

	@NotNull
	private Map<String, List<Object>> processStatusListDataForExcel(List<GuardianLogDataStatVO> guardianLogDataStatVOS) {
		Map<String, List<Object>> groupedData = new HashMap<>();
		// 获取所有字段名
		Field[] fields = GuardianLogDataStatVO.class.getDeclaredFields();
		// 提前设置访问权限，避免在循环中重复设置
		setFieldsAccessible(fields);
		for (Field field : fields) {
			try {
				encapsulationData(guardianLogDataStatVOS, field, groupedData);
			} catch (Exception e) {
				log.error("Error processing field " + field.getName() + ": " + e.getMessage());
			}
		}
		return groupedData;
	}

	/**
	 * 获取代理商人员信息
	 */
	private void getAgentUserInfo(List<GuardianPlantVO> guardianPlantVOList, Map<Long, AgentUserVo> agentUserVoMap) {
		List<Long> userIds = guardianPlantVOList.stream().map(GuardianPlantVO::getAgentUserId)
				.filter(org.springblade.core.tool.utils.ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (!CollectionUtils.isNullOrEmpty(userList)) {
			userList.parallelStream().forEach(v -> {
				AgentUserVo agentUserVo = new AgentUserVo();
				BeanUtils.copyProperties(v, agentUserVo);
				agentUserVoMap.put(v.getId(), agentUserVo);
			});
		}
	}

	/**
	 * 获取代理商公司信息
	 */
	private void getCompanyInfo(List<GuardianPlantVO> guardianPlantVOList, Map<Long, AgentCompanyVO> companyVoMap) {
		List<Long> companyIds = guardianPlantVOList.stream().map(GuardianPlantVO::getAgentCompanyId).distinct()
				.filter(org.springblade.core.tool.utils.ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<AgentCompanyVO> agentCompanyVOList = agentClient.agentCompanyInfoByIds(companyIds).getData();
		if (!CollectionUtils.isNullOrEmpty(agentCompanyVOList)) {
			agentCompanyVOList.parallelStream().forEach(v -> {
				AgentCompanyVO agentCompanyVO = new AgentCompanyVO();
				BeanUtils.copyProperties(v, agentCompanyVO);
				companyVoMap.put(v.getDeptId(), agentCompanyVO);
			});
		}
	}

	/**
	 * 设置区域信息
	 */
	private void setRegionInfo(List<String> regionCodeList, List<GuardianPlantVO> guardianPlantVOList) {
		if (CollectionUtil.isEmpty(regionCodeList)) {
			return;
		}
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			for (GuardianPlantVO guardianPlantVO : guardianPlantVOList) {
				StringBuilder address = new StringBuilder();
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setCityName(region.getName());
					}
				}
				guardianPlantVO.setAddress(address.append(" ").append(guardianPlantVO.getDetailAddress() == null ? "" : guardianPlantVO.getDetailAddress()).toString());
			}
		}
	}

	// 判断给定的数据时间是否在两个时间点之间
	private List<GuardianLogDataStatVO> isBetween(LocalDateTime start, LocalDateTime end, Map<String, GuardianLogDataStatVO> collect) {
		List<GuardianLogDataStatVO> invertStatusReports = new ArrayList<>();
		LocalDateTime startMin = start.plusMinutes(1);
		// 如果开始时间>结束时间，则跳过循环，返回上一层调用的方法。如00:06>00:05，则跳出循环，此时才能遍历完整从00:01-00:05分的数据，然后归并到00:05时间点上
		while (startMin.isBefore(end) || startMin.isEqual(end)) {
			String formattedDateTime = startMin.format(ymdhmFormatter);
			if (collect.containsKey(formattedDateTime)) {
				invertStatusReports.add(collect.get(formattedDateTime));
			}
			startMin = startMin.plusMinutes(1L);
		}

		return invertStatusReports;
	}

	// 归并数据到指定的时间点
	private void mergeData(LocalDateTime dateTime, List<GuardianLogDataStatVO> guardianLogDataStatVOList, Map<String, GuardianLogDataStatVO> collect, String queryPowerType) {
		// 实现归并数据的逻辑
		GuardianLogDataStatVO sumReport = new GuardianLogDataStatVO();
		if (guardianLogDataStatVOList.size() == 1) {
			GuardianLogDataStatVO guardianLogDataStatVO = guardianLogDataStatVOList.get(0);
			sumReport = guardianLogDataStatVO;
			setGuardianLogDataValue(sumReport, guardianLogDataStatVO, queryPowerType);
			collect.remove(sumReport.getDeviceDateTimeForCal());
		} else if (guardianLogDataStatVOList.size() > 1) {
			//取时间最新那一条
			guardianLogDataStatVOList.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
			GuardianLogDataStatVO guardianLogDataStatVO = guardianLogDataStatVOList.get(guardianLogDataStatVOList.size() - 1);
			setGuardianLogDataValue(sumReport, guardianLogDataStatVO, queryPowerType);
			guardianLogDataStatVOList.forEach(a -> {
				collect.remove(a.getDeviceDateTimeForCal());
			});
		}
		String ymdHmTime = dateTime.format(ymdhmFormatter);
		String hmTime = dateTime.format(hmFormatter);
		String ymd = dateTime.format(ymdFormatter);
		sumReport.setDeviceDateTimeForCal(ymdHmTime);
		sumReport.setDeviceDateTime(hmTime);
		sumReport.setDeviceDateTimeForDay(ymd);
		collect.put(ymdHmTime, sumReport);
	}

	private void setGuardianLogDataValue(GuardianLogDataStatVO sumReport, GuardianLogDataStatVO guardianLogDataStatVO, String queryPowerType) {
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_VOLTAGE.getPowerTypeName())) {
			sumReport.setAPhaseVoltage(formatDecimal(guardianLogDataStatVO.getAPhaseVoltage(), 2));
			sumReport.setBPhaseVoltage(formatDecimal(guardianLogDataStatVO.getBPhaseVoltage(), 2));
			sumReport.setCPhaseVoltage(formatDecimal(guardianLogDataStatVO.getCPhaseVoltage(), 2));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_CURRENT.getPowerTypeName())) {
			sumReport.setAPhaseCurrent(formatDecimal(guardianLogDataStatVO.getAPhaseCurrent(), 2));
			sumReport.setBPhaseCurrent(formatDecimal(guardianLogDataStatVO.getBPhaseCurrent(), 2));
			sumReport.setCPhaseCurrent(formatDecimal(guardianLogDataStatVO.getCPhaseCurrent(), 2));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_TEMPERATURE.getPowerTypeName())) {
			sumReport.setAPhaseTemperature(formatDecimal(guardianLogDataStatVO.getAPhaseTemperature(), 2));
			sumReport.setBPhaseTemperature(formatDecimal(guardianLogDataStatVO.getBPhaseTemperature(), 2));
			sumReport.setCPhaseTemperature(formatDecimal(guardianLogDataStatVO.getCPhaseTemperature(), 2));
			sumReport.setNNeutralLineTemperature(formatDecimal(guardianLogDataStatVO.getNNeutralLineTemperature(), 2));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.LEAKAGE_CURRENT.getPowerTypeName())) {
			sumReport.setLeakageCurrent(formatDecimal(guardianLogDataStatVO.getLeakageCurrent(), 2));
		}
	}

	private BigDecimal formatDecimal(BigDecimal value, int scale) {
		DecimalFormat decimalFormat = new DecimalFormat("#0." + "0".repeat(scale));
		return new BigDecimal(decimalFormat.format(value));
	}

	private boolean includeQueryPowerType(String queryPowerType, String powerType) {
		queryPowerType = CommonConstant.SYMBOL_COMMA + queryPowerType + CommonConstant.SYMBOL_COMMA;
		return queryPowerType.toLowerCase().contains(CommonConstant.SYMBOL_COMMA + powerType.toLowerCase() + CommonConstant.SYMBOL_COMMA);
	}

	@NotNull
	private GuardianLogDataStatVO getFakeData(LocalDateTime mergerTime, String queryPowerType) {
		// 没有数据则造一条数据
		GuardianLogDataStatVO fakeData = new GuardianLogDataStatVO();
		fakeData.setDeviceDateTime(mergerTime.format(hmFormatter));
		fakeData.setDeviceDateTimeForCal(mergerTime.format(ymdhmFormatter));
		fakeData.setDeviceDateTimeForDay(mergerTime.format(ymdFormatter));
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_VOLTAGE.getPowerTypeName())) {
			fakeData.setAPhaseVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setBPhaseVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setCPhaseVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_CURRENT.getPowerTypeName())) {
			fakeData.setAPhaseCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setBPhaseCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setCPhaseCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.PHASE_TEMPERATURE.getPowerTypeName())) {
			fakeData.setAPhaseTemperature(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setBPhaseTemperature(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setCPhaseTemperature(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			fakeData.setNNeutralLineTemperature(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		}
		if (includeQueryPowerType(queryPowerType, QueryPowerTypeEnum.LEAKAGE_CURRENT.getPowerTypeName())) {
			fakeData.setLeakageCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		}
		return fakeData;
	}

	@NotNull
	private Map<String, List<Object>> processStatusListData(List<GuardianLogDataStatVO> guardianLogDataStatVOList, String queryPowerType) {
		Map<String, List<Object>> groupedData = new HashMap<>();
		// 获取所有字段名
		Field[] fields = GuardianLogDataStatVO.class.getDeclaredFields();
		// 提前设置访问权限，避免在循环中重复设置
		setFieldsAccessible(fields);
		String fieldNames = QueryPowerTypeEnum.getFieldName(queryPowerType);
		for (Field field : fields) {
			try {
				String fieldName = field.getName();
				if (fieldNames.contains(CommonConstant.SYMBOL_COMMA + fieldName + CommonConstant.SYMBOL_COMMA) ||
						"deviceDateTime".equalsIgnoreCase(fieldName)
				) {
					encapsulationData(guardianLogDataStatVOList, field, groupedData);
				}
			} catch (Exception e) {
				log.error("Error processing field " + field.getName() + ": " + e.getMessage());
			}
		}
		return groupedData;
	}

	// 设置字段可访问
	private void setFieldsAccessible(Field[] fields) {
		for (Field field : fields) {
			field.setAccessible(true);
		}
	}


	/**
	 * 封装返回数据
	 * */
	private void encapsulationData(List<GuardianLogDataStatVO> guardianLogDataStatVOList, Field field, Map<String, List<Object>> groupedData) {
		String fieldName = field.getName();
		List<Object> fieldValueList = guardianLogDataStatVOList.stream()
				.map(report -> {
					try {
						return field.get(report);
					} catch (IllegalAccessException e) {
						log.error(e.getMessage());
						return "";
					}
				})
				.collect(Collectors.toList());
		groupedData.put(fieldName, fieldValueList);
	}

	// guardianPlantVOList处理
	private void guardianPlantVOListHandle(List<GuardianPlantVO> guardianPlantVOList) {
		// 站点信息list转用户ID集合
		List<Long> userIdList = guardianPlantVOList.stream().map(GuardianPlantVO::getUserId)
				.filter(ObjectUtil::isNotEmpty).distinct().collect(Collectors.toList());
		// 获取用户信息
		R<List<User>> userListR = userSearchClient.listAllByUserIds(userIdList);

		Map<Long, User> userMap = new HashMap<>();
		// 响应成功并且数据不为空
		if (userListR.isSuccess() && CollectionUtil.isNotEmpty(userListR.getData())) {
			userMap = userListR.getData().stream().collect(Collectors.toMap(User::getId, Function.identity()));
		}

		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(guardianPlantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(guardianPlantVOList, companyMap);

		List<String> regionCodeList = new ArrayList<>();
		for (GuardianPlantVO guardianPlantTempVO : guardianPlantVOList) {
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(guardianPlantTempVO.getId());
			regionCodeList.add(guardianPlantTempVO.getCountryCode());
			regionCodeList.add(guardianPlantTempVO.getProvinceCode());
			regionCodeList.add(guardianPlantTempVO.getCityCode());
			regionCodeList.add(guardianPlantTempVO.getCountyCode());

			if (ObjectUtil.isNotEmpty(userMap.get(guardianPlantTempVO.getUserId()))) {
				// 设置用户电话号码
				guardianPlantTempVO.setUserPhone(StringUtil.isBlank(userMap.get(guardianPlantTempVO.getUserId()).getPhone())
						? "" : userMap.get(guardianPlantTempVO.getUserId()).getPhone());
				// 设置用户电话号码区号
				guardianPlantTempVO.setPhoneDiallingCode(StringUtil.isBlank(userMap.get(guardianPlantTempVO.getUserId()).getPhoneDiallingCode())
						? "" : userMap.get(guardianPlantTempVO.getUserId()).getPhoneDiallingCode());
				// 设置用户名
				guardianPlantTempVO.setUserName(StringUtil.isBlank(userMap.get(guardianPlantTempVO.getUserId()).getRealName())
						? "" : userMap.get(guardianPlantTempVO.getUserId()).getRealName());
			} else {
				guardianPlantTempVO.setUserPhone("");
				guardianPlantTempVO.setPhoneDiallingCode("");
				guardianPlantTempVO.setUserName("");
			}

			Long agentUserId = guardianPlantTempVO.getAgentUserId();
			Long agentCompanyId = guardianPlantTempVO.getAgentCompanyId();
			if (ValidationUtil.isNotEmpty(agentUserId)) {
				if (!CollectionUtils.isNullOrEmpty(agentUserVoMap) && agentUserVoMap.containsKey(agentUserId)) {
					guardianPlantTempVO.setAgentUserName(agentUserVoMap.get(agentUserId).getRealName());
				}
			}
			if (ValidationUtil.isNotEmpty(agentCompanyId)) {
				if (!CollectionUtils.isNullOrEmpty(companyMap) && companyMap.containsKey(agentCompanyId)) {
					guardianPlantTempVO.setAgentCompanyName(companyMap.get(agentCompanyId).getCompanyName());
				}
			}
			// 安全卫士状态映射
			guardianPlantTempVO.setSecurityGuardStatusName(SecurityGuardStatusEnum.getDescByCode(guardianPlantTempVO.getSecurityGuardStatus()));
		}

		this.setRegionInfo(regionCodeList, guardianPlantVOList);
	}

}

package org.skyworth.ess.battery.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 储能安装信息
 * <AUTHOR>
 * @description 储能安装信息
 * @since 2023/9/25 09:30:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "储能安装信息对象", description = "储能安装信息对象")
public class BatteryDeviceInstallVO extends BaseEntity {

	@ApiModelProperty(name = "安装日期",notes = "")
	private String installDate;

	@ApiModelProperty(name = "安装人员",notes = "")
	private String installPerson;

	@ApiModelProperty(name = "安装团队",notes = "")
	private String installTeam;

	@ApiModelProperty(name = "安装负责人id",notes = "")
	private Long installUserId;

	@ApiModelProperty(name = "安装团队id",notes = "")
	private Long installTeamId;

	@ApiModelProperty(name = "质保年限",notes = "")
	private String qualityGuaranteeYear;

	@ApiModelProperty(name = "质保开始日期",notes = "")
	private String qualityGuaranteeBeginDate;

	@ApiModelProperty(name = "质保结束日期",notes = "")
	private String qualityGuaranteeEndDate;

	@ApiModelProperty(name = "储能SN",notes = "")
	private String batterySerialNumber;

	@ApiModelProperty(name = "设备SN",notes = "")
	private String deviceSerialNumber;

	@ApiModelProperty(name = "创建人",notes = "")
	private Long createUser;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.exception.mapper.ExceptionLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="exceptionLogResultMap" type="org.skyworth.ess.exception.entity.ExceptionLogEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="exception_type" property="exceptionType"/>
        <result column="serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="exception_message" property="exceptionMessage"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="address_code" property="addressCode"/>
        <result column="time_zone" property="timeZone"/>
    </resultMap>


    <select id="selectExceptionLogPage" resultMap="exceptionLogResultMap">
        select
        log.id,
        log.plant_id,
        log.exception_type,
        log.serial_number,
        log.device_date_time,
        log.exception_message,
        log.create_user_account,
        log.update_user_account,
        log.tenant_id,
        log.create_user,
        log.create_dept,
        log.create_time,
        log.update_user,
        log.update_time,
        log.status,
        log.is_deleted,
        log.address_code,
        log.time_zone
        from exception_log log
        <where>
            log.is_deleted = 0
            <if test="exceptionLog.deviceSerialNumber!=null and exceptionLog.deviceSerialNumber!=''">
                and serial_number = #{exceptionLog.deviceSerialNumber}
            </if>
            <if test="exceptionLog.plantId!=null and exceptionLog.plantId!=''">
                and plant_id = #{exceptionLog.plantId}
            </if>
            <if test="exceptionLog.exceptionType!=null and exceptionLog.exceptionType!=''">
                and exception_type = #{exceptionLog.exceptionType}
            </if>
        </where>
        order by device_date_time desc,id desc
    </select>


    <select id="exportExceptionLog" resultType="org.skyworth.ess.exception.excel.ExceptionLogExcel">
        SELECT
            id,
            plant_id,
            exception_type,
            serial_number,
            device_date_time,
            exception_message,
            create_user_account,
            update_user_account,
            tenant_id,
            create_user,
            create_dept,
            create_time,
            update_user,
            update_time,
            status,
            is_deleted,
            address_code
        FROM exception_log ${ew.customSqlSegment}
    </select>
    <select id="selectExceptionLogPageByBattery" resultMap="exceptionLogResultMap">
        select
        log.id,
        log.plant_id,
        log.exception_type,
        log.serial_number,
        log.device_date_time,
        log.exception_message,
        log.create_user_account,
        log.update_user_account,
        log.tenant_id,
        log.create_user,
        log.create_dept,
        log.create_time,
        log.update_user,
        log.update_time,
        log.status,
        log.is_deleted,
        log.address_code,
        log.time_zone
        from exception_log log
        <where>
            log.is_deleted = 0
            <if test="queryCondition.deviceSerialNumber!=null and queryCondition.deviceSerialNumber!=''">
                and serial_number = #{queryCondition.deviceSerialNumber}
            </if>
            <if test="queryCondition.plantId!=null and queryCondition.plantId!=''">
                and plant_id = #{queryCondition.plantId}
            </if>
            <if test="queryCondition.exceptionType!=null and queryCondition.exceptionType!=''">
                and exception_type = #{queryCondition.exceptionType}
            </if>
        </where>
        order by device_date_time desc,id desc
    </select>

</mapper>

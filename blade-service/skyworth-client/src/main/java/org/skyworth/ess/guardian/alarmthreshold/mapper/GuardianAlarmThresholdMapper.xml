<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.alarmthreshold.mapper.GuardianAlarmThresholdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianAlarmThresholdResultMap" type="org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="undervoltage_alarm_threshold" property="undervoltageAlarmThreshold"/>
        <result column="overvoltage_alarm_threshold" property="overvoltageAlarmThreshold"/>
        <result column="overcurrent_alarm_threshold" property="overcurrentAlarmThreshold"/>
        <result column="leakage_alarm_threshold" property="leakageAlarmThreshold"/>
        <result column="a_temperature_high_alarm_threshold" property="aTemperatureHighAlarmThreshold"/>
        <result column="b_temperature_high_alarm_threshold" property="bTemperatureHighAlarmThreshold"/>
        <result column="c_temperature_high_alarm_threshold" property="cTemperatureHighAlarmThreshold"/>
        <result column="n_temperature_high_alarm_threshold" property="nTemperatureHighAlarmThreshold"/>
        <result column="arc_level_alarm_threshold" property="arcLevelAlarmThreshold"/>
        <result column="alarm_threshold_for_power_restriction_degree" property="alarmThresholdForPowerRestrictionDegree"/>
        <result column="undervoltage_switch" property="undervoltageSwitch"/>
        <result column="overvoltage_switch" property="overvoltageSwitch"/>
        <result column="overcurrent_switch" property="overcurrentSwitch"/>
        <result column="leakage_switch" property="leakageSwitch"/>
        <result column="high_temperature_alarm_switch" property="highTemperatureAlarmSwitch"/>
        <result column="arc_level_switch" property="arcLevelSwitch"/>
        <result column="power_limit_switch" property="powerLimitSwitch"/>
        <result column="phase_loss_protection_switch" property="phaseLossProtectionSwitch"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianAlarmThresholdPage" resultMap="GuardianAlarmThresholdResultMap">
        select * from guardian_alarm_threshold where is_deleted = 0
    </select>


    <select id="exportGuardianAlarmThreshold" resultType="org.skyworth.ess.guardian.alarmthreshold.excel.GuardianAlarmThresholdExcel">
        SELECT * FROM guardian_alarm_threshold ${ew.customSqlSegment}
    </select>

    <select id="getAllAlarmThresholdSetup" resultType="java.util.Map">
        select plant_id as plantId,
               security_guard_serial_number as securityGuardSerialNumber,
               undervoltage_alarm_threshold as undervoltageAlarmThreshold,
               overvoltage_alarm_threshold as overvoltageAlarmThreshold,
               overcurrent_alarm_threshold as overcurrentAlarmThreshold,
               leakage_alarm_threshold as leakageAlarmThreshold,
               a_temperature_high_alarm_threshold as aTemperatureHighAlarmThreshold,
               b_temperature_high_alarm_threshold as bTemperatureHighAlarmThreshold,
               c_temperature_high_alarm_threshold as cTemperatureHighAlarmThreshold,
               n_temperature_high_alarm_threshold as nTemperatureHighAlarmThreshold,
               arc_level_alarm_threshold as arcLevelAlarmThreshold,
               alarm_threshold_for_power_restriction_degree as alarmThresholdForPowerRestrictionDegree,
               undervoltage_switch as undervoltageSwitch,
               overvoltage_switch as overvoltageSwitch,
               overcurrent_switch as overcurrentSwitch,
               leakage_switch as leakageSwitch,
               high_temperature_alarm_switch as highTemperatureAlarmSwitch,
               arc_level_switch as arcLevelSwitch,
               power_limit_switch as powerLimitSwitch,
               phase_loss_protection_switch as phaseLossProtectionSwitch
        from guardian_alarm_threshold
        where security_guard_serial_number = #{serialNumber} and plant_id = #{plantId}
    </select>
</mapper>

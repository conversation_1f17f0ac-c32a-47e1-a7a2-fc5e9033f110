/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.operationlog.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.vo.InverterDevicePageVO;
import org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity;
import org.skyworth.ess.operationlog.vo.AdvancedSettingsOperationLogVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.util.Map;

/**
 * 高级设置操作日志 服务类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface IAdvancedSettingsOperationLogService extends BaseService<AdvancedSettingsOperationLogEntity> {

    IPage<AdvancedSettingsOperationLogVO> getLogList(IPage<AdvancedSettingsOperationLogVO> page, Map<String, Object> map);
}

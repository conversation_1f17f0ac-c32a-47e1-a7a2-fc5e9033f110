/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.controller;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.excel.AddressMapDefinitionColumnEnum;
import org.skyworth.ess.addressmap.excel.AddressMapDefinitionExcel;
import org.skyworth.ess.addressmap.service.IAddressMapDefinitionService;
import org.skyworth.ess.addressmap.vo.AddressMapDefinitionVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.excel.ExcelBuildUtil;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * 物理地址协议映射数据 控制器
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/addressMapDefinition")
@Api(value = "物理地址协议映射数据", tags = "物理地址协议映射数据接口")
@Slf4j
public class AddressMapDefinitionController extends BladeController {

	private final IAddressMapDefinitionService addressMapDefinitionService;
	private final ExcelImportServiceInterface<AddressMapDefinitionExcel> addressMapDefinitionImportImpl;

private final BladeRedis bladeRedis;


	/**
	 * 物理地址映射属性名称 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:detail')")
	public R<AddressMapDefinitionEntity> detail(AddressMapDefinitionEntity addressMapDefinition) {
		AddressMapDefinitionEntity detail = addressMapDefinitionService.getOne(Condition.getQueryWrapper(addressMapDefinition));
		return R.data(detail);
	}




	/**
	 * 物理地址映射属性名称 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:list')")
	public R<IPage<AddressMapDefinitionEntity>> list(@ApiIgnore @RequestParam Map<String, Object> addressMapDefinition, Query query) {
		query.setDescs("create_time");
		IPage<AddressMapDefinitionEntity> pages = addressMapDefinitionService.page(Condition.getPage(query), Condition.getQueryWrapper(addressMapDefinition, AddressMapDefinitionEntity.class));
		return R.data(pages);
	}

	/**
	 * 物理地址映射属性名称 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:list')")
	public R<IPage<AddressMapDefinitionVO>> page(AddressMapDefinitionVO addressMapDefinition, Query query) {
		IPage<AddressMapDefinitionVO> pages = addressMapDefinitionService.selectAddressMapDefinitionPage(Condition.getPage(query), addressMapDefinition);
		return R.data(pages);
	}

	/**
	 * 物理地址映射属性名称 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:add')")
	public R save(@Valid @RequestBody AddressMapDefinitionEntity addressMapDefinition) {
		boolean save = addressMapDefinitionService.saveAddressMapDefinition(addressMapDefinition);
		return R.status(save);
	}



	/**
	 * 物理地址映射属性名称 修改
	 * 修改后更新单个数据缓存
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:update')")
	public R update(@Valid @RequestBody AddressMapDefinitionEntity addressMapDefinition) {
		delCacheKey();
		boolean updated = addressMapDefinitionService.updateAddressMapDefinition(addressMapDefinition);
		return R.status(updated);
	}

	/**
	 * 物理地址映射属性名称 新增或修改
	 * 修改后更新单个数据缓存 && 删除所有数据的缓存
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:update')")
	public R submit(@Valid @RequestBody AddressMapDefinitionEntity addressMapDefinition) {
		delCacheKey();
		boolean saveOrUpdate = addressMapDefinitionService.saveOrUpdateAddressMapDefinition(addressMapDefinition);
		return R.status(saveOrUpdate);
	}

	private void delCacheKey(){
		Set<String> allAddressMapDefinitionByAddress = bladeRedis.keys("allAddressMapDefinitionByAddress*]");
		bladeRedis.del(allAddressMapDefinitionByAddress);
		bladeRedis.del("allAddressMap4Definition");
		bladeRedis.del("getMappingBySetItem");
	}

	/**
	 * 物理地址映射属性名称 删除
	 * 删除单个数据缓存
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:addressMapDefinition:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam("ids") String ids) {
		delCacheKey();
		boolean deleted = addressMapDefinitionService.deleteLogic(Func.toLongList(ids));
		return R.status(deleted);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-addressMapDefinition")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "导出数据", notes = "传入addressMapDefinition")
	@PreAuth("hasPermission('client:addressMapDefinition:export')")
	public void exportAddressMapDefinition(@ApiIgnore @RequestParam Map<String, Object> addressMapDefinition, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<AddressMapDefinitionEntity> queryWrapper = Condition.getQueryWrapper(addressMapDefinition, AddressMapDefinitionEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(AddressMapDefinition::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(AddressMapDefinitionEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<AddressMapDefinitionExcel> list = addressMapDefinitionService.exportAddressMapDefinition(queryWrapper);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		Set<String> column = AddressMapDefinitionColumnEnum.getColumn(currentLanguage);
		List<List<String>> headList = ExcelBuildUtil.buildI18HeadList(column);
		ExcelUtil.export(response, "Physical_address_protocol_mapping_data_" + DateUtil.time(), "sheet1", list, AddressMapDefinitionExcel.class,headList);
	}






	/**
	 * 导出填写模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导出填写模板", notes = "导出填写模板")
	@PreAuth("hasPermission('client:addressMapDefinition:export')")
	public void exportAddTemplate(HttpServletResponse response) {
		List<AddressMapDefinitionExcel> list1 = new ArrayList<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		Set<String> column = AddressMapDefinitionColumnEnum.getColumn(currentLanguage);
		List<List<String>> headList = ExcelBuildUtil.buildI18HeadList(column);
		ExcelUtil.export(response, "Physical_address_protocol_mapping_data_template", "sheet1", list1, AddressMapDefinitionExcel.class,headList);
	}

	/**
	 * 新增导入
	 * @param file
	 * @return
	 */
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "新增导入", notes = "新增导入")
	@PostMapping("/add-import")
	@PreAuth("hasPermission('client:addressMapDefinition:import')")
	public R addImport(MultipartFile file, Integer isCovered, HttpServletRequest request) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		BiFunction<List<AddressMapDefinitionExcel>, Boolean, String> fun = addressMapDefinitionService::addImportExcel;
		String result = addressMapDefinitionImportImpl.imporExcel(file, AddressMapDefinitionColumnEnum.getColumn(currentLanguage),
			AddressMapDefinitionExcel.class, fun);
		if (!BizConstant.IMPORT_SUCCESS.equals(result)){
			return R.fail(result);
		}
		return R.data(result);
	}

	/**
	 * 修改导入
	 * @param file
	 * @return
	 */
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "修改导入", notes = "修改导入")
	@PostMapping("/update-import")
	@PreAuth("hasPermission('client:addressMapDefinition:import')")
	public R updateImport(MultipartFile file, Integer isCovered, HttpServletRequest request) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		BiFunction<List<AddressMapDefinitionExcel>, Boolean, String> fun = addressMapDefinitionService::updateImportExcel;
		String result = addressMapDefinitionImportImpl.imporExcel(file, AddressMapDefinitionColumnEnum.getColumn(currentLanguage),
			AddressMapDefinitionExcel.class, fun);
		if (!BizConstant.IMPORT_SUCCESS.equals(result)){
			return R.fail(result);
		}
		return R.data(result);
	}



	@PostMapping("/getMappingBySetItem")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "getMappingBySetItem", notes = "传入addressMapDefinition")
	public R<List<JSONObject>> getMappingBySetItem(@RequestBody  AddressMapDefinitionVO addressMapDefinition) {
		List<JSONObject> pages = addressMapDefinitionService.getMappingBySetItem(addressMapDefinition);
		return R.data(pages);
	}

}

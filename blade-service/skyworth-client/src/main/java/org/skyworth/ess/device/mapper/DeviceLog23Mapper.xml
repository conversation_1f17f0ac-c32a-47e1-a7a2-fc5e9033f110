<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceLog23Mapper">

    <resultMap id="DeviceLog23ResultMap" type="org.skyworth.ess.device.entity.DeviceLog23">
        <id property="id" column="id"/>
        <result property="hybridWorkMode" column="hybrid_work_mode"/>
        <result property="onceEveryday" column="once_everyday"/>
        <result property="chargeStartTime1" column="charge_start_time1"/>
        <result property="chargeEndTime1" column="charge_end_time1"/>
        <result property="dischargeStartTime1" column="discharge_start_time1"/>
        <result property="dischargeEndTime1" column="discharge_end_time1"/>
        <result property="batteryTypeSelection" column="battery_type_selection"/>
        <result property="commAddress" column="comm_address"/>
        <result property="batteryAh" column="battery_ah"/>
        <result property="stopDischargeVoltage" column="stop_discharge_voltage"/>
        <result property="stopChargeVoltage" column="stop_charge_voltage"/>
        <result property="gridCharge" column="grid_charge"/>
        <result property="maximumGridChargerPower" column="maximum_grid_charger_power"/>
        <result property="capacityOfGridChargerEnd" column="capacity_of_grid_charger_end"/>
        <result property="maximumChargerPower" column="maximum_charger_power"/>
        <result property="capacityOfChargerEnd" column="capacity_of_charger_end"/>
        <result property="maximumDischargerPower" column="maximum_discharger_power"/>
        <result property="capacityOfDischargerEnd" column="capacity_of_discharger_end"/>
        <result property="offGridMode" column="off_grid_mode"/>
        <result property="ratedOutputVoltage" column="rated_output_voltage"/>
        <result property="ratedOutputFrequency" column="rated_output_frequency"/>
        <result property="offGridStartUpBatteryCapacity" column="off_grid_startup_battery_capacity"/>
        <result property="maximumDischargeCurrent" column="maximum_discharge_current"/>
        <result property="maximumChargerCurrent" column="maximum_charger_current"/>
        <result property="plantId" column="plant_id"/>
        <result property="deviceDateTime" column="device_date_time"/>
        <result property="modbusProtocolVersion" column="modbus_protocol_version"/>
        <result property="synchStatus" column="synch_status"/>
        <result property="deviceSerialNumber" column="device_serial_number"/>
        <result property="createUserAccount" column="create_user_account"/>
        <result property="updateUserAccount" column="update_user_account"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="time_based_control_enable" property="timeBasedControlEnable"/>
        <result column="generator_start_soc" property="generatorStartSoc"/>
        <result column="generator_end_soc" property="generatorEndSoc"/>
        <result column="maximum_input_power_from_grid" property="maximumInputPowerFromGrid"/>
        <result column="capacity_of_discharge_end_on_grid" property="capacityOfDischargeEndOnGrid"/>
        <result column="force_charge_start_soc" property="forceChargeStartSoc"/>
        <result column="force_charge_end_soc" property="forceChargeEndSoc"/>
        <result column="backup_minimum_output_voltage" property="backupMinimumOutputVoltage"/>
        <result column="backup_maximum_output_voltage" property="backupMaximumOutputVoltage"/>
        <result column="generator_dry_force_on_or_off" property="generatorDryForceOnOrOff"/>
        <result column="parallel_mode_function" property="parallelModeFunction"/>
        <result column="parallel_system_battery_connect_type" property="parallelSystemBatteryConnectType"/>
    </resultMap>


    <select id="selectDataByLatestTime" resultMap="DeviceLog23ResultMap">
        SELECT t1.*
        FROM device_log23 t1
        JOIN (
        SELECT plant_id, MAX(device_date_time) AS max_date,device_serial_number
        FROM device_log23
        GROUP BY plant_id,device_serial_number
        ) t2 ON t1.plant_id = t2.plant_id AND t1.device_date_time = t2.max_date and t1.device_serial_number=t2.device_serial_number where (t1.synch_status is null or t1.synch_status='' or t1.synch_status='N');

    </select>

</mapper>


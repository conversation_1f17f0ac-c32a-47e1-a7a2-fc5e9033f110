/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.excel.BatteryExitFactoryExcelColumnEnum;
import org.skyworth.ess.battery.excel.BatteryExitFactoryInfoExcel;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.impl.BatteryExitFactoryInfoServiceImpl;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.wrapper.BatteryExitFactoryInfoWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * 储能出厂信息表 控制器
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/batteryExitFactoryInfo")
@Api(value = "储能出厂信息表", tags = "储能出厂信息表接口")
public class BatteryExitFactoryInfoController extends BladeController {

	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final ExcelImportServiceInterface<BatteryExitFactoryInfoExcel> batteryExitFactoryImportImpl;
	private final BladeRedis bladeRedis;

	/**
	 * 储能出厂信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:detail')")
	public R<BatteryExitFactoryInfoVO> detail(BatteryExitFactoryInfoEntity batteryExitFactoryInfo) {
		BatteryExitFactoryInfoEntity detail = batteryExitFactoryInfoService.getOne(Condition.getQueryWrapper(batteryExitFactoryInfo));
		return R.data(BatteryExitFactoryInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 储能出厂信息表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:list')")
	public R<IPage<BatteryExitFactoryInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> batteryExitFactoryInfo, Query query) {
		query.setDescs("create_time");
		IPage<BatteryExitFactoryInfoEntity> pages=new Page<>();
		BladeUser userAuth = AuthUtil.getUser();
		if (userAuth.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) userAuth.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有数据
			if(roleInnerFlag!=null&&roleInnerFlag) {
				pages = batteryExitFactoryInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(batteryExitFactoryInfo, BatteryExitFactoryInfoEntity.class));
				List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntities= pages.getRecords();
				batteryExitFactoryInfoEntities.parallelStream().filter(v -> v.getWarrantyStartDate()!=null && !v.getWarrantyStartDate().isEmpty()) .forEach(v->{
					String warrantyStartDate=v.getWarrantyStartDate();
					String qualityGuaranteeYear=v.getQualityGuaranteeYear();
					String warrantyDeadline= BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),warrantyStartDate);
					v.setWarrantyDeadline(warrantyDeadline);
				});
				pages.setRecords(batteryExitFactoryInfoEntities);
			}
		}
		return R.data(BatteryExitFactoryInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 储能出厂信息表 自定义分页
	 */
	@GetMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:list')")
	public R<IPage<BatteryExitFactoryInfoVO>> page(BatteryExitFactoryInfoVO batteryExitFactoryInfo, Query query) {
		IPage<BatteryExitFactoryInfoVO> pages = batteryExitFactoryInfoService.selectBatteryExitFactoryInfoPage(Condition.getPage(query), batteryExitFactoryInfo);
		List<BatteryExitFactoryInfoVO> batteryExitFactoryInfoVOList= pages.getRecords();
		batteryExitFactoryInfoVOList.parallelStream().filter(v -> v.getWarrantyStartDate()!=null && !v.getWarrantyStartDate().isEmpty()) .forEach(v->{
			String warrantyStartDate=v.getWarrantyStartDate();
			String qualityGuaranteeYear=v.getQualityGuaranteeYear();
			String warrantyDeadline= BatteryExitFactoryInfoServiceImpl.deadline(Integer.parseInt(qualityGuaranteeYear),warrantyStartDate);
			v.setWarrantyDeadline(warrantyDeadline);
		});
		pages.setRecords(batteryExitFactoryInfoVOList);

		return R.data(pages);
	}

	/**
	 * 储能出厂信息表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:add')")
	public R save(@Valid @RequestBody BatteryExitFactoryInfoEntity batteryExitFactoryInfo) {
		delBatchCacheKey("batteryModelProtocol*");
		return R.status(batteryExitFactoryInfoService.addBatteryExitFactoryInfo(batteryExitFactoryInfo));
	}

	/**
	 * 储能出厂信息表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:update')")
	public R update(@Valid @RequestBody BatteryExitFactoryInfoEntity batteryExitFactoryInfo) {
		delBatchCacheKey("batteryModelProtocol*");
		BladeUser user = AuthUtil.getUser();
		Wrapper<BatteryExitFactoryInfoEntity> queryWrapper = Wrappers.<BatteryExitFactoryInfoEntity>lambdaQuery().
			eq(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batteryExitFactoryInfo.getBatterySerialNumber())
			.ne(BatteryExitFactoryInfoEntity::getId, batteryExitFactoryInfo.getId());
		long count = batteryExitFactoryInfoService.count(queryWrapper);
		if (count > BizConstant.NUMBER_ZERO) {
			throw new BusinessException("client.battery.serial.number.exist",batteryExitFactoryInfo.getBatterySerialNumber());
		}
		batteryExitFactoryInfo.setUpdateUserAccount(user.getAccount());
		return R.status(batteryExitFactoryInfoService.updateById(batteryExitFactoryInfo));
	}

	/**
	 * 储能出厂信息表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:update')")
	public R submit(@Valid @RequestBody BatteryExitFactoryInfoEntity batteryExitFactoryInfo) {
		delBatchCacheKey("batteryModelProtocol*");
		return R.status(batteryExitFactoryInfoService.saveOrUpdate(batteryExitFactoryInfo));
	}

	/**
	 * 储能出厂信息表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		delBatchCacheKey("batteryModelProtocol*");
		return R.status(batteryExitFactoryInfoService.deleteLogicBatteryExitFactory(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-BatteryExitFactoryInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入BatteryExitFactoryInfo")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:export')")
	public void exportBatteryExitFactoryInfo(@ApiIgnore @RequestParam Map<String, Object> batteryExitFactoryInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<BatteryExitFactoryInfoEntity> queryWrapper = Condition.getQueryWrapper(batteryExitFactoryInfo, BatteryExitFactoryInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(BatteryExitFactoryInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(BatteryExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<BatteryExitFactoryInfoExcel> list = batteryExitFactoryInfoService.exportBatteryExitFactoryInfo(queryWrapper);
		ExcelUtil.export(response, "BatteryExitFactoryInfo" + DateUtil.time(), "BatteryExitFactoryInfo", list, BatteryExitFactoryInfoExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出模板")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:export')")
	public void exportImportantEventTemplate(HttpServletResponse response) {
		List<BatteryExitFactoryInfoExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "template", "BatteryExitFactoryInfo", list, BatteryExitFactoryInfoExcel.class);
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-add-batteryExitFactory")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "新增导入储能出厂信息", notes = "传入excel")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:import')")
	public R importAddBatteryExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<BatteryExitFactoryInfoExcel>, Boolean, String> fun = batteryExitFactoryInfoService::importAddExcel;
		String result = batteryExitFactoryImportImpl.imporExcel(file, BatteryExitFactoryExcelColumnEnum.getColumn("zh"),
				BatteryExitFactoryInfoExcel.class, fun);
		if(StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-modify-batteryExitFactory")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "修改导入储能出厂信息", notes = "传入excel")
	@PreAuth("hasPermission('client:batteryExitFactoryInfo:import')")
	public R importModifyBatteryExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<BatteryExitFactoryInfoExcel>, Boolean, String> fun = batteryExitFactoryInfoService::importModifyExcel;
		String result = batteryExitFactoryImportImpl.imporExcel(file, BatteryExitFactoryExcelColumnEnum.getColumn("zh"),
				BatteryExitFactoryInfoExcel.class, fun);
		if(StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	private void delBatchCacheKey(String keyPrefix){
		Set<String> allAddressMapDefinitionByAddress = bladeRedis.keys(keyPrefix);
		bladeRedis.del(allAddressMapDefinitionByAddress);
	}
}

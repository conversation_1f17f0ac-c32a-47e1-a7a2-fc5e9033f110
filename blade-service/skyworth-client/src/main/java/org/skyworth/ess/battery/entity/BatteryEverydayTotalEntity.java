/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 储能每日统计 实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@TableName("battery_everyday_total")
@ApiModel(value = "BatteryEverydayTotal对象", description = "储能每日统计")
@EqualsAndHashCode(callSuper = true)
public class BatteryEverydayTotalEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 设备/智能能量变换器SN
	 */
	@ApiModelProperty(value = "设备/智能能量变换器SN")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 统计日期
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd"
	)
	@ApiModelProperty(value = "统计日期")
	private Date totalDate;
	/**
	 * 充电能量
	 */
	@ApiModelProperty(value = "充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 放电能量
	 */
	@ApiModelProperty(value = "放电能量")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 累计充电能量
	 */
	@ApiModelProperty(value = "累计充电能量")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 累计放电能量
	 */
	@ApiModelProperty(value = "累计放电能量")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 吞吐量使用率
	 */
	@ApiModelProperty(value = "吞吐量使用率")
	private BigDecimal throughputUseRate;
	/**
	 * 最高单体储能电压
	 */
	@ApiModelProperty(value = "最高单体储能电压")
	private BigDecimal batteryMaximumCellVoltage;
	/**
	 * 最低单体储能电压
	 */
	@ApiModelProperty(value = "最低单体储能电压")
	private BigDecimal batteryMinimumCellVoltage;
	/**
	 * 最高单体储能温度
	 */
	@ApiModelProperty(value = "最高单体储能温度")
	private BigDecimal batteryMaximumCellTemperature;
	/**
	 * 最低单体储能温度
	 */
	@ApiModelProperty(value = "最低单体储能温度")
	private BigDecimal batteryMinimumCellTemperature;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	@ApiModelProperty(value = "")
	private BigDecimal pvTotalInputPower;

	@ApiModelProperty(value = "")
	private BigDecimal todayEnergy;


	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfLoad;

	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfEps;

	@ApiModelProperty(value = "")
	private BigDecimal todayLoadEnergy;

	@ApiModelProperty(value = "")
	private BigDecimal dailyEnergyToEps;
	// app 饼图字段 begin
	private BigDecimal selfConsumed;

	private BigDecimal fedToGrid;

	private BigDecimal selfSufficiency;

	private BigDecimal fromGrid;
	// app 饼图字段 end


	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "PV当日发电")
	private BigDecimal pvlDailyGeneratingEnergySum;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "储能当日充电")
	private BigDecimal batteryDailyChargeEnergyParallel;

	@ApiModelProperty(value = "每日负载总和")
	private BigDecimal dailyEnergyOfLoadSum;

	@ApiModelProperty(value = "每日支持能量总和")
	private BigDecimal dailySupportEnergySumToBackup;

	@ApiModelProperty(value = "今日输入能量")
	private BigDecimal todayImportEnergy ;
	/**  */
	@ApiModelProperty(value = "今日输出能量")
	private BigDecimal todayExportEnergy ;
	// 13E4
	private BigDecimal batteryDailyDischargeEnergyParallel;

	private BigDecimal generatorTodayEnergySum;

	// 并机 app 饼图字段 begin

	private BigDecimal parallelSelfConsumed;

	private BigDecimal parallelFedToGrid;

	private BigDecimal parallelSelfSufficiency;

	private BigDecimal parallelFromGrid;

	// 并机  app 饼图字段 end

	// 2025.4.15 begin
	// 2200
	private BigDecimal batterySoc2;
	// 2209
	private BigDecimal batteryPower2;
	// 220B
	private BigDecimal batteryTodayChargeEnergy2;
	// 220F
	private BigDecimal batteryTodayDischargeEnergy2;
	// 220D
	private BigDecimal batteryAccumulatedChargeEnergy2;
	// 2211
	private BigDecimal batteryAccumulatedDischargeEnergy2;
	// 2216
	private BigDecimal batteryAverageOfAllModulesSoh2;
	// 2214
	private BigDecimal batteryMaximumCyclesTimes2;
	// 2206
	private BigDecimal batteryVoltage2;
	// 2207
	private BigDecimal batteryCurrent2;
	// 221C
	private BigDecimal batteryMaximumCellTemperature2;
	// 221E
	private BigDecimal batteryMinimumCellTemperature2;
	// 221B
	private BigDecimal batteryAverageCellTemperature2;
	// 2217
	private BigDecimal batteryMaximumCellVoltage2;
	// 2219
	private BigDecimal batteryMinimumCellVoltage2;
	// 2025.4.15 end
}

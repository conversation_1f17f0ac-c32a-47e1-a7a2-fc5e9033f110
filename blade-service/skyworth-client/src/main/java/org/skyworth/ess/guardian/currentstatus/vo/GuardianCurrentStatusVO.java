/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.currentstatus.vo;

import io.swagger.annotations.ApiModelProperty;
import org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity;
import org.springblade.core.tool.node.INode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 安全卫士当前状态 视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GuardianCurrentStatusVO extends GuardianCurrentStatusEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 安全卫士状态(1在线/0离线)
	 */
	@ApiModelProperty(value = "安全卫士状态(1在线/0离线)")
	private Integer securityGuardStatus;

	/**
	 * 闸位状态(0关闭/1打开)
	 */
	@ApiModelProperty(value = "闸位状态(0关闭/1打开)")
	private Integer gatePositionStatus;
	/**
	 * 闸位状态说明(分闸/合闸)
	 */
	@ApiModelProperty(value = "闸位状态说明(分闸/合闸)")
	private String gatePositionStatusName;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;
	/**
	 * 卡号
	 */
	@ApiModelProperty(value = "卡号")
	private String simCardNumber;

}

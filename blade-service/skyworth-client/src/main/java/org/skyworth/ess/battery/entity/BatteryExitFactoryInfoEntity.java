/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;

/**
 * 储能出厂信息表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@TableName("battery_exit_factory_info")
@ApiModel(value = "BatteryExitFactoryInfo对象", description = "储能出厂信息表")
@EqualsAndHashCode(callSuper = true)
public class BatteryExitFactoryInfoEntity extends TenantEntity {

	/**
	 * 储能SN
	 */
	@ApiModelProperty(value = "储能SN")
	@NotNull(message = "batterySerialNumber can not be empty!")
	private String batterySerialNumber;
	/**
	 * 储能型号
	 */
	@ApiModelProperty(value = "储能型号")
	private String batteryType;
	/**
	 * 额定储能电压
	 */
	@ApiModelProperty(value = "额定储能电压")
	private String ratedBatteryVoltage;
	/**
	 * 额定储能容量
	 */
	@ApiModelProperty(value = "额定储能容量")
	private String ratedBatteryCapacity;
	/**
	 * 额定储能能量
	 */
	@ApiModelProperty(value = "额定储能能量")
	private String ratedBatteryEnergy;
	/**
	 * 单体容量
	 */
	@ApiModelProperty(value = "单体容量")
	private String singleCapacity;
	/**
	 * 内含单体并联数
	 */
	@ApiModelProperty(value = "内含单体串并联数")
	private String singleSeriesParallelingNumber;
//	/**
//	 * 内含单体串联数
//	 */
//	@ApiModelProperty(value = "内含单体串联数")
//	private String singleSeriesNumber;
	/**
	 * 质保年限
	 */
	@ApiModelProperty(value = "质保年限")
	@NotNull(message = "qualityGuaranteeYear can not be empty!")
	private String qualityGuaranteeYear;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String company;
	/**
	 * 出厂日期
	 */
	@ApiModelProperty(value = "出厂日期")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@NotNull(message = "exitFactoryDate can not be empty!")
	private Date exitFactoryDate;

	/**
	 * 激活日期
	 */
	@ApiModelProperty(value = "激活日期")
	private String activationDate;

	/**
	 * 质保开始日期
	 */
	@ApiModelProperty(value = "质保开始日期")
	private String warrantyStartDate;

	/**
	 * 质保截止日期
	 */
	@ApiModelProperty(value = "质保截止日期")
	private String warrantyDeadline;

	@ApiModelProperty(value = "modbus版本")
	private String modbusProtocolVersion;
}

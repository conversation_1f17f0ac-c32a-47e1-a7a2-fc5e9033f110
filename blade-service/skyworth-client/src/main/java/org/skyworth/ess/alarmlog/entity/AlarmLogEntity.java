/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmlog.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 异常日志表 实体类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@TableName("alarm_log")
@ApiModel(value = "AlarmLog对象", description = "异常日志表")
@EqualsAndHashCode(callSuper = true)
public class AlarmLogEntity extends TenantEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 异常类型：device智能能量变换器；battery储能
	 */
	@ApiModelProperty(value = "异常类型：inverter智能能量变换器；battery储能；guardian：安全卫士")
	private String exceptionType;
	/**
	 * 告警等级，业务字典：alarm_level:high/middle/low
	 */
	@ApiModelProperty(value = "告警等级，业务字典：alarm_level:high/middle/low")
	private String alarmLevel;
	/**
	 * 设备SN号
	 */
	@ApiModelProperty(value = "设备SN号")
	private String serialNumber;
	/**
	 * 运维安装商部门id，当需要安装商处理的时候写入
	 */
	@ApiModelProperty(value = "运维安装商部门id，当需要安装商处理的时候写入")
	private Long departmentId;
	/**
	 * 用户id，当需要用户处理的时候写入
	 */
	@ApiModelProperty(value = "用户id，当需要用户处理的时候写入")
	private Long userId;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 异常消息
	 */
	@ApiModelProperty(value = "异常消息")
	private String exceptionMessage;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 地址码
	 */
	@ApiModelProperty(value = "地址码")
	private String addressCode;
	/**
	 * 设备上报时间时区
	 */
	@ApiModelProperty(value = "设备上报时间时区")
	private String timeZone;
	/**
	 * 告警序号，对应业务字典device_alarm_mapping_config的dict_key;
	 */
	@ApiModelProperty(value = "告警序号，对应业务字典device_alarm_mapping_config的dict_key;")
	private Integer alarmNumber;

	@TableField(exist = false)
	@ApiModelProperty(value = "用于分组的组合唯一键")
	private String groupKey;

	@TableField(exist = false)
	private JSONObject inverterKindArr;

	@TableField(exist = false)
	private JSONObject inverterDeviceTypeArr;

	@TableField(exist = false)
	private String deviceModel;

	@TableField(exist = false)
	private String inverterModel;
}

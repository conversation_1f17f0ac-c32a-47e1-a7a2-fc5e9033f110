package org.skyworth.ess.guardian.issue;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.feign.FeignConfig;
import org.skyworth.ess.feign.IConsoleIssueClient;
import org.springblade.common.utils.SerialNumberGenerator;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;

/**
 * <AUTHOR> - xue<PERSON>jiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/8/9 16:56:09
 */
@Slf4j
public abstract class GuardianIssueStrategy implements IssueStrategy {
	public BladeRedis bladeRedis = SpringUtil.getBean(BladeRedis.class);
	public IConsoleIssueClient consoleIssueClient = SpringUtil.getBean(IConsoleIssueClient.class);

	//01.获取远程通道
	//02.拼接content内容
	//03.构造下发对象
	//04.调用远程接口进行下发
	//05.处理业务结果
	//06.关闭资源

	/**
	 * 01.获取远程通道
	 *
	 * @param
	 * @return
	 */
	@Override
	public String getRemoteChannel(IssueStrategyEntity issueStrategyEntity) {
		String remoteIp = bladeRedis.get(GuardianInstructConstants.PRE_HEADER + issueStrategyEntity.getDeviceSerialNumber());
		if (ValidationUtil.isEmpty(remoteIp)) {
			log.warn("未找到远程通道");
			throw new BusinessException("client.guardian.not.found.remote.channel");
		}
		return remoteIp;
	}

	/**
	 * 03.构造下发对象
	 *
	 * @param content
	 * @param
	 * @return
	 */
	@Override
	public JSONObject constructIssueObject(String content, IssueStrategyEntity issueStrategyEntity) {
		JSONObject issueObject = new JSONObject();
		issueObject.put("bizType", issueStrategyEntity.getBizType());
		issueObject.put("serialNumber", SerialNumberGenerator.generateSerialNumber());
		issueObject.put("deviceSn", issueStrategyEntity.getDeviceSerialNumber());
		issueObject.put("content", content);
		return issueObject;
	}

	/**
	 * 04.调用远程接口进行下发
	 *
	 * @param issueObject
	 * @param remoteChannelIp
	 * @return
	 */
	@Override
	public R<String> invokeRemoteInterface(JSONObject issueObject, String remoteChannelIp) {
		FeignConfig.setDynamicIp(remoteChannelIp);
		return consoleIssueClient.issueGuard(issueObject);
	}

	/**
	 * 06.关闭资源
	 */
	@Override
	public void closeResource() {
		// 清理ThreadLocal中的值
		FeignConfig.remove();
	}

	public R executeStrategy(IssueStrategyEntity issueStrategyEntity) {
		try {
			String remoteChannelIp = this.getRemoteChannel(issueStrategyEntity);
			String content = assembleContent(issueStrategyEntity);
			JSONObject issueObject = this.constructIssueObject(content, issueStrategyEntity);
			R<String> invokeResult = this.invokeRemoteInterface(issueObject, remoteChannelIp);
			return handleBusinessResult(invokeResult, issueStrategyEntity);
		} catch (Exception e) {
			log.error("下发安全卫士指令失败：{} 类型: {}", e.getMessage(), e.getClass().getName(), e);
			throw new BusinessException("client.guardian.issue.fail");
		} finally {
			try {
				this.closeResource(); // 关闭资源
			} catch (Exception ex) {
				log.error("关闭资源时出现异常:", ex);
			}
		}
	}
}

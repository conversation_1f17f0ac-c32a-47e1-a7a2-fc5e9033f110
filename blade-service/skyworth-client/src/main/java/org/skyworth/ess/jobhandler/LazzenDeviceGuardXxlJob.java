package org.skyworth.ess.jobhandler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.impl.GatewayPlantServiceImpl;
import org.springblade.common.constant.BizConstant;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 良信断路器定时任务
 *
 * <AUTHOR>
 * @date 2024/11/27 14:22
 */
@Component
@Slf4j
@AllArgsConstructor
public class LazzenDeviceGuardXxlJob {

    private final GatewayPlantServiceImpl gatewayPlantService;

    private static final long INVERT_TIME = 70;

    private static final int BATCH_UPDATE_RECORDS_SIZE = 100;

    /**
     * 断路器心跳定时任务
     *
     * @param param 入参
     * @return ReturnT<String>
     * <AUTHOR>
     * @date 2024/11/27 14:22
     */
    @XxlJob("heartBeatGuardAndBackUp")
    public ReturnT<String> heartBeatGuardAndBackUp(String param) {
        // 查出在线设备
        log.info("LazzenDeviceGuardXxlJob ->> heartBeatGuardAndBackUp,Start executing the task.");
        try {
            LambdaQueryWrapper<GatewayPlantEntity> queryWrapper = Wrappers.<GatewayPlantEntity>query().lambda()
                    .eq(GatewayPlantEntity::getStatus, BizConstant.NUMBER_ONE);
            List<GatewayPlantEntity> stickPlantEntityList = gatewayPlantService.list(queryWrapper);
            List<Long> updateList = new ArrayList<>();
            LocalDateTime nowDateTime = LocalDateTime.now();
            // 检查在线设备的心跳时间，将过期设备标记为离线
            if (!stickPlantEntityList.isEmpty()) {
                stickPlantEntityList.forEach(data -> {
                    LocalDateTime heartBeatTime = data.getHeartBeatTime();
                    // 有心跳时间，且时间超过70s，也设置为离线
					if (heartBeatTime != null) {
                        Duration duration = Duration.between(heartBeatTime, nowDateTime);
                        long secondsDifference = duration.getSeconds();
                        // 超过70秒设置为离线
                        if (secondsDifference > INVERT_TIME) {
                            updateList.add(data.getId());
                        }
                    }
					// 没有心跳时间，则设置离线
					else{
						updateList.add(data.getId());
					}
                });
            }
            // 批量更新离线设备状态
            if (!updateList.isEmpty()) {
                batchUpdateRecord(updateList);
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("LazzenDeviceGuardXxlJob ->> heartBeatGuardAndBackUp,Exception occurred during task execution. ", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 批量插入记录到数据库
     * 此方法的目的是通过批处理方式更新数据库中的记录，以提高数据库操作的效率
     * 它每次处理一批记录，直到所有记录都被处理完毕
     *
     * @param updateList 入参
     * <AUTHOR>
     * @date 2024/11/27 13:50
     */
    private void batchUpdateRecord(List<Long> updateList) {
        // 如果更新列表为空，则直接返回，无需执行后续操作
        if (updateList.isEmpty()) {
            return;
        }
        // 获取更新列表的大小，用于后续的批处理
        int listSize = updateList.size();
        // 分批处理更新列表中的记录，每次处理BATCH_UPDATE_RECORDS_SIZE数量的记录
        for (int i = 0; i < listSize; i += BATCH_UPDATE_RECORDS_SIZE) {
            // 确定当前批次处理的结束位置，防止越界
            int end = Math.min(i + BATCH_UPDATE_RECORDS_SIZE, listSize);
            // 获取当前批次需要处理的记录子列表
            List<Long> subList = updateList.subList(i, end);
            // 调用gatewayPlantService的更新方法，设置这些记录的状态为0（假设0代表离线）
            gatewayPlantService.update(Wrappers.<GatewayPlantEntity>lambdaUpdate()
                    .set(GatewayPlantEntity::getStatus, BizConstant.NUMBER_ZERO)
                    .in(GatewayPlantEntity::getId, subList));
            // 记录日志，标识当前批次的记录已成功更新
            log.info("LazzenDeviceGuardXxlJob ->> heartBeatGuardAndBackUp,Successfully updated records in batch: {}",
                    subList);
        }
    }
}

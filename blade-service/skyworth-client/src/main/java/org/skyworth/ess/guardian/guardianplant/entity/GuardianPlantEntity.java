/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 安全卫士对应站点 实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@TableName("guardian_plant")
@ApiModel(value = "GuardianPlant对象", description = "安全卫士对应站点")
@EqualsAndHashCode(callSuper = true)
public class GuardianPlantEntity extends SkyWorthEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ApiModelProperty(value = "安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 安全卫士状态(1在线/0离线)
	 */
	@ApiModelProperty(value = "安全卫士状态(1在线/0离线/2告警/3故障)")
	private Integer securityGuardStatus;
	/**
	 * 闸位状态(0关闭/1打开)
	 */
	@ApiModelProperty(value = "闸位状态(0关闭/1打开)")
	private Integer gatePositionStatus;
	/**
	 * 功率定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ApiModelProperty(value = "功率定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypePower;
	/**
	 * 功率定时类型自定义
	 */
	@ApiModelProperty(value = "功率定时类型自定义")
	private String timingTypePowerCustom;
	/**
	 * 开关闸定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ApiModelProperty(value = "开关闸定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypeGate;
	/**
	 * 开关闸定时类型自定义
	 */
	@ApiModelProperty(value = "开关闸定时类型自定义")
	private String timingTypeGateCustom;
	/**
	 * 心跳时间
	 */
	@ApiModelProperty(value = "心跳时间")
	private Date heartBeatTime;
	/**
	 * 锁死设置（0 解锁 1 锁死）
	 */
	@ApiModelProperty(value = "锁死设置（0 解锁 1 锁死）")
	private Integer lockSet;
	/**
	 * 重合功能设置（0 关闭 1 开启）
	 */
	@ApiModelProperty(value = "重合功能设置（0 关闭 1 开启）")
	private Integer recloseSet;

	/**
	 * 分区字段
	 */
	@ApiModelProperty(value = "分区字段")
	private String partitionDate;

	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;

}

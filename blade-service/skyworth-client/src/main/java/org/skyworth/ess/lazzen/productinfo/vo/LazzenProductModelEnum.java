package org.skyworth.ess.lazzen.productinfo.vo;


import lombok.Getter;

@Getter
public enum LazzenProductModelEnum {
	D_TYPE("A", "1010","D型"),
	C_TYPE("6", "0110","C型"),
	B_TYPE("2", "0010","B型");

	private String decimalNumber;
	private String binaryNumber;
	private String productModel;

	LazzenProductModelEnum(String decimalNumber, String binaryNumber,String productModel) {
		this.decimalNumber = decimalNumber;
		this.binaryNumber = binaryNumber;
		this.productModel = productModel;
	}

	public static String getProductModelByBinaryNumber(String binaryNumber) {
		for (LazzenProductModelEnum lazzenProductModelEnum : LazzenProductModelEnum.values()) {
			if(lazzenProductModelEnum.getBinaryNumber().equals(binaryNumber)) {
				return lazzenProductModelEnum.getProductModel();
			}
		}
		return "";
	}

	public static String getProductModelByDecimalNumber(String decimalNumber) {
		for (LazzenProductModelEnum lazzenProductModelEnum : LazzenProductModelEnum.values()) {
			if(lazzenProductModelEnum.getDecimalNumber().equals(decimalNumber)) {
				return lazzenProductModelEnum.getProductModel();
			}
		}
		return "";
	}
}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.excel.AddressMapDefinitionExcel;
import org.skyworth.ess.addressmap.vo.AddressMapDefinitionVO;

import java.util.List;

/**
 * 物理地址映射属性名称 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface AddressMapDefinitionMapper extends BaseMapper<AddressMapDefinitionEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param addressMapDefinition
	 * @return
	 */
	List<AddressMapDefinitionVO> selectAddressMapDefinitionPage(IPage page, AddressMapDefinitionVO addressMapDefinition);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<AddressMapDefinitionExcel> exportAddressMapDefinition(@Param("ew") Wrapper<AddressMapDefinitionEntity> queryWrapper);


    void updateBatchByCondition(@Param("list")List<AddressMapDefinitionEntity> update);
}

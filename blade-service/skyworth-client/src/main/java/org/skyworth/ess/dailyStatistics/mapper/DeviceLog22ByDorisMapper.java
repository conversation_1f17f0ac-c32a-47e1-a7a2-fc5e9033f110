package org.skyworth.ess.dailyStatistics.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.skyworth.ess.device.vo.InvertStatusReport;

import java.time.LocalDateTime;
import java.util.List;

public interface DeviceLog22ByDorisMapper extends BaseMapper<DeviceLog22> {

	List<DeviceLog22> selectDataByLatestTime();

	List<DeviceLog22> selectDailyData(@Param("beginTime") String beginTime, @Param("endTime") String endTime);

	List<JSONObject> stateCurve(@Param("condition") QueryDeviceLog22Condition queryCondition);

	List<InvertStatusReport> selectStatusReportByTime(@Param("condition") QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appReportEstimate(@Param("queryCondition") QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appReportEstimateV2(@Param("queryCondition") QueryDeviceLog22Condition queryCondition);

	List<DeviceLog22VO> appParallelReportEstimateV2(@Param("queryCondition") QueryDeviceLog22Condition queryCondition);

	DeviceLog22VO appDailyFromPvAndGrid(@Param("queryCondition") QueryDeviceLog22Condition queryCondition);

	JSONObject getSummaryStateCurve(@Param("sldt") LocalDateTime sldt, @Param("eldt") LocalDateTime eldt);
	DeviceLog22 appParallelDailyFromPvAndGrid(@Param("queryCondition")QueryDeviceLog22Condition queryCondition);
	DeviceLog22 queryLatestData(@Param("queryCondition")QueryDeviceLog22Condition queryCondition);
}

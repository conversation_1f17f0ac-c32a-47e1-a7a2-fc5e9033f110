package org.skyworth.ess.messagepushrecord.entity;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 告警类型
 * @author: SDT50545
 * @since: 2024-08-10 10:46
 **/
@Getter
public enum MessageTypeEnum {
	//告警、通知、提醒、系统、其他
	ALARM("alarm", "告警"),
	NOTICE("notice", "通知"),
	REMIND("remind", "提醒"),
	SYSTEM("system", "系统"),
	OTHER("other", "其他");
	private final String code;
	private final String name;

	/**
	 * 构造函数
	 *
	 * @param code 编码
	 * @param name 名称
	 */
	MessageTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	private static final Map<String, MessageTypeEnum> ENUM_MAP = new HashMap<>();

	static {
		for (MessageTypeEnum myEnum : MessageTypeEnum.values()) {
			ENUM_MAP.put(myEnum.code, myEnum);
		}
	}

	public static String getNameByCode(String code) {
		return ENUM_MAP.get(code).getName();
	}
}

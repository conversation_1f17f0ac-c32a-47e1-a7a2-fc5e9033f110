/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.productinfo.excel;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 产品信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceGuardProductInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备网关唯一编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备网关唯一编号")
	private String gatewayUniqueNumber;
	/**
	 * 设备断路器地址64为第一位置
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备断路器地址64为第一位置")
	private String circuitBreakerAddress;
	/**
	 * 产品规格 0x0200
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品规格 0x0200")
	private String productStandard;
	/**
	 * 产品型号 0x0201
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品型号 0x0201")
	private String productModel;
	/**
	 * 壳架电流 0x0202
	 */
	@ColumnWidth(20)
	@ExcelProperty("壳架电流 0x0202")
	private String frameCurrent;
	/**
	 * 额定电流 0x0203
	 */
	@ColumnWidth(20)
	@ExcelProperty("额定电流 0x0203")
	private String ratedCurrent;
	/**
	 * 工作频率(电网频率) 0x0206
	 */
	@ColumnWidth(20)
	@ExcelProperty("工作频率(电网频率) 0x0206")
	private BigDecimal workFrequency;
	/**
	 * 产品极数 0x0209
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品极数 0x0209")
	private String productPoleNumber;
	/**
	 * 生产日期-年 0x020E
	 */
	@ColumnWidth(20)
	@ExcelProperty("生产日期-年 0x020E")
	private String productDateYear;
	/**
	 * 生产日期-月日 0x020F
	 */
	@ColumnWidth(20)
	@ExcelProperty("生产日期-月日 0x020F")
	private String productDateMonthDay;
	/**
	 * 产品序列号 0x0210 - 0x0214
	 */
	@ColumnWidth(20)
	@ExcelProperty("产品序列号 0x0210 - 0x0214")
	private String productSn;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

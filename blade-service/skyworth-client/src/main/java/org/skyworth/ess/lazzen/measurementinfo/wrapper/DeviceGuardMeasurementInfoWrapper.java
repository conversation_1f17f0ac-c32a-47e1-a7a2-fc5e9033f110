/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity;
import org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoVO;
import java.util.Objects;

/**
 * 设备卫士测量信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public class DeviceGuardMeasurementInfoWrapper extends BaseEntityWrapper<DeviceGuardMeasurementInfoEntity, DeviceGuardMeasurementInfoVO>  {

	public static DeviceGuardMeasurementInfoWrapper build() {
		return new DeviceGuardMeasurementInfoWrapper();
 	}

	@Override
	public DeviceGuardMeasurementInfoVO entityVO(DeviceGuardMeasurementInfoEntity deviceGuardMeasurementInfo) {
		DeviceGuardMeasurementInfoVO deviceGuardMeasurementInfoVO = Objects.requireNonNull(BeanUtil.copy(deviceGuardMeasurementInfo, DeviceGuardMeasurementInfoVO.class));

		//User createUser = UserCache.getUser(deviceGuardMeasurementInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceGuardMeasurementInfo.getUpdateUser());
		//deviceGuardMeasurementInfoVO.setCreateUserName(createUser.getName());
		//deviceGuardMeasurementInfoVO.setUpdateUserName(updateUser.getName());

		return deviceGuardMeasurementInfoVO;
	}


}

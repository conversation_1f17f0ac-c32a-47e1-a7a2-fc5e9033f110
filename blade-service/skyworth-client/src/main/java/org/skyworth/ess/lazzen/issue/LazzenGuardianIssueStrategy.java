package org.skyworth.ess.lazzen.issue;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.guardian.issue.IssueStrategyEntity;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2024/8/9 16:56:09
 */
@Slf4j
public abstract class LazzenGuardianIssueStrategy implements LazzenIssueStrategy {
	public BladeRedis bladeRedis = SpringUtil.getBean(BladeRedis.class);
	public IGatewayPlantService gatewayPlantService = SpringUtil.getBean(IGatewayPlantService.class);
	public IDeviceIssueBiz deviceIssue = SpringUtil.getBean(IDeviceIssueBiz.class);

	//01.校验设备在线状态
	//02.拼接content内容
	//03.构造下发对象
	//04.调用远程接口进行下发
	//05.处理业务结果


	@Override
	public boolean checkDeviceOnlineStatus(IssueStrategyEntity issueStrategyEntity) {
		boolean onlineFlag = true;
		// 查询关系表，获取设备状态
		GatewayPlantEntity entity = gatewayPlantService.getOne(Wrappers.lambdaQuery(GatewayPlantEntity.class)
			.eq(GatewayPlantEntity::getGatewayUniqueNumber, issueStrategyEntity.getDeviceSerialNumber()));
		if (ObjectUtils.isEmpty(entity) || ValidationUtil.isEmpty(entity.getStatus()) || 0 == entity.getStatus()) {
			onlineFlag = false;
		}
		if (!onlineFlag) {
			log.warn("设备不在线");
			throw new BusinessException("client.guardian.not.online");
		}
		return onlineFlag;
	}

	/**
	 * 03.构造下发对象
	 *
	 * @param content
	 * @param
	 * @return
	 */
	@Override
	public JSONObject constructIssueObject(String content, IssueStrategyEntity issueStrategyEntity) {
		JSONObject issueObject = new JSONObject();
		issueObject.put("deviceSn", issueStrategyEntity.getDeviceSerialNumber());
		issueObject.put("content", content);
		issueObject.put("topic", Constants.LAZZEN_WRITE_DATA_TOPIC);
		issueObject.put("requestId", issueStrategyEntity.getRequestId());
		issueObject.put("bizType", issueStrategyEntity.getBizType());
		return issueObject;
	}

	/**
	 * 04.调用远程接口进行下发
	 *
	 * @param issueObject
	 * @return
	 */
	@Override
	public Map<String, String> invokeRemoteInterface(JSONObject issueObject) {
		return deviceIssue.settingLazzen(issueObject);
	}


	public R executeStrategy(IssueStrategyEntity issueStrategyEntity) {
		this.checkDeviceOnlineStatus(issueStrategyEntity);
		String content = assembleContent(issueStrategyEntity);
		JSONObject issueObject = this.constructIssueObject(content, issueStrategyEntity);
		Map<String, String> invokeResult = this.invokeRemoteInterface(issueObject);
		return handleBusinessResult(invokeResult, issueStrategyEntity);

	}
}

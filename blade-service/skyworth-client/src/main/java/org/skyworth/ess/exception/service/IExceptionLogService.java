/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exception.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.exception.excel.ExceptionLogExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 异常日志 服务类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public interface IExceptionLogService extends BaseService<ExceptionLogEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param exceptionLog
	 * @return
	 */
	IPage<ExceptionLogVO> selectExceptionLogPage(IPage<ExceptionLogVO> page, ExceptionLogVO exceptionLog);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<ExceptionLogExcel> exportExceptionLog(Wrapper<ExceptionLogEntity> queryWrapper);


	IPage<ExceptionLogEntity> selectExceptionLogPageByCondition(IPage<ExceptionLogEntity> page, QueryCondition queryCondition);

}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 物理地址映射属性名称 实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@TableName("address_map_definition")
@ApiModel(value = "AddressMapDefinition对象", description = "物理地址映射属性名称")
@EqualsAndHashCode(callSuper = true)
public class AddressMapDefinitionEntity extends TenantEntity {

	/**
	 * 厂商
	 */
	@ApiModelProperty(value = "厂商")
	private String company;
	/**
	 * modbus版本
	 */
	@ApiModelProperty(value = "modbus版本")
	private String modbusProtocolVersion;
	/**
	 * 物理地址
	 */
	@ApiModelProperty(value = "物理地址")
	private String address;
	/**
	 * 属性名称
	 */
	@ApiModelProperty(value = "属性名称")
	private String definition;
	/**
	 * 数据类型
	 */
	@ApiModelProperty(value = "数据类型")
	private String dataType;
	/**
	 * 单位
	 */
	@ApiModelProperty(value = "单位")
	private String unit;
	/**
	 * 长度
	 */
	@ApiModelProperty(value = "长度")
	private Integer length;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "十进制物理地址")
	private Integer decimalAddress;

	@ApiModelProperty(value = "系统默认配置")
	private Integer systemRequiredConfiguration;

	@ApiModelProperty(value = "系统默认配置")
	@TableField(exist = false)
	private Integer systemRequiredConfigurationStr;

	private String setItemDefinition;
	// 物理地址说明
	private String addressDescription;
	// 单位说明
	private String unitDescription;
	// 备注
	private String remark;
	// 保留位
	private String reserveFlag;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}

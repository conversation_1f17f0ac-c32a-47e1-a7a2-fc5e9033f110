/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmoperationrecord.mapper;

import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.vo.AlarmLogOperationRecordVO;
import org.skyworth.ess.alarmoperationrecord.excel.AlarmLogOperationRecordExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 告警日志操作记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface AlarmLogOperationRecordMapper extends BaseMapper<AlarmLogOperationRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param alarmLogOperationRecord
	 * @return
	 */
	List<AlarmLogOperationRecordVO> selectAlarmLogOperationRecordPage(IPage page, AlarmLogOperationRecordVO alarmLogOperationRecord);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<AlarmLogOperationRecordExcel> exportAlarmLogOperationRecord(@Param("ew") Wrapper<AlarmLogOperationRecordEntity> queryWrapper);

}

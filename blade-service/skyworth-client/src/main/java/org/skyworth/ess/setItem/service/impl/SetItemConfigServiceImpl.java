/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.entity.SetItemMultiLanguageEntity;
import org.skyworth.ess.setItem.excel.SetItemConfigExcel;
import org.skyworth.ess.setItem.mapper.SetItemConfigMapper;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.skyworth.ess.setItem.service.ISetItemMultiLanguageService;
import org.skyworth.ess.setItem.vo.SetItemConfigVO;
import org.skyworth.ess.util.SnowFlakeGeneratorUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * APP设置项配置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
@Slf4j
public class SetItemConfigServiceImpl extends BaseServiceImpl<SetItemConfigMapper, SetItemConfigEntity> implements ISetItemConfigService {

	@Resource
	private ISetItemMultiLanguageService setItemMultiLanguageService;
	@Resource
	private SetItemConfigMapper setItemConfigMapper;

	@Override
	public IPage<SetItemConfigVO> selectSetItemConfigPage(IPage<SetItemConfigVO> page, SetItemConfigVO setItemConfig) {
		return page.setRecords(baseMapper.selectSetItemConfigPage(page, setItemConfig));
	}


	@Override
	public List<SetItemConfigExcel> exportSetItemConfig(Wrapper<SetItemConfigEntity> queryWrapper) {
		List<SetItemConfigExcel> setItemConfigList = baseMapper.exportSetItemConfig(queryWrapper);
		//setItemConfigList.forEach(setItemConfig -> {
		//	setItemConfig.setTypeName(DictCache.getValue(DictEnum.YES_NO, SetItemConfig.getType()));
		//});
		return setItemConfigList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeSetItem(String ids) {
		List<Long> idList = Func.toLongList(ids);
		// 防止空列表进行后续操作
		if (CollectionUtils.isEmpty(idList)) {
			return true; // 直接返回true，避免不必要的操作
		}
		// 查询受影响的记录数量
		Long count = baseMapper.selectCount(Wrappers.<SetItemConfigEntity>query().lambda().in(SetItemConfigEntity::getParentId, idList));
		if (count > 0L) {
			throw new BusinessException("client.setItem.delete.fail");
		}
		// 获取需要更新的实体列表
		List<SetItemConfigEntity> setItemConfigEntities = baseMapper.selectList(Wrappers.<SetItemConfigEntity>query().lambda().in(SetItemConfigEntity::getId, idList));
		if (CollectionUtils.isNotEmpty(setItemConfigEntities)) {
			// 使用Stream API优化代码，减少重复数据库查询
			List<SetItemConfigEntity> updatedEntities = setItemConfigEntities.stream()
				.map(setItemConfigEntity -> {
					SetItemConfigEntity one = this.getOne(Wrappers.<SetItemConfigEntity>query().lambda().in(SetItemConfigEntity::getId, setItemConfigEntity.getParentId()));
					// 查询父级，如果父级选项不为空，则将父级的默认值设置为空
					if (ObjectUtil.isNotEmpty(one)){
						one.setSetItemDefault(CommonConstant.BLANK);
					}
					return one;
				})
				.filter(Objects::nonNull)
				.collect(Collectors.toList());
			this.saveOrUpdateBatch(updatedEntities);
		}


		setItemMultiLanguageService.deleteLogicByItemId(idList);
		return removeByIds(idList);
	}

	@Override
	public SetItemConfigEntity getSetItemDetail(SetItemConfigVO setItemConfig) {
		SetItemConfigEntity setItemConfigEntity = this.getOne(Condition.getQueryWrapper(setItemConfig));
		List<Long> longList = Func.toLongList(StrUtil.toString(setItemConfigEntity.getId()));
		List<JSONObject> setItemMultiLanguageEntities = setItemMultiLanguageService.selectListByItemId(longList);
		setItemConfigEntity.setMultiLanguage(setItemMultiLanguageEntities);
		return setItemConfigEntity;
	}


	@Cached(name="deviceSetItemConfig-",key = "#appSetRequestVO.deviceSerialNumber + '-' + 'setCategory_' + #appSetRequestVO.setCategory + '-' " +
			"+ 'setItemBigType_' + #appSetRequestVO.setItemBigType + '-' + #appSetRequestVO.language",
		expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	@Override
	public List<SetItemConfigEntity> getSetupConfig(AppSetRequestVO appSetRequestVO) {
		List<SetItemConfigEntity> setupConfig = setItemConfigMapper.getSetupConfig(appSetRequestVO);
		return ForestNodeMerger.merge(setupConfig);
	}

	@Override
	@Transactional
	public boolean saveSetItem(SetItemConfigEntity setItemConfig) {
		long id = SnowFlakeGeneratorUtil.getSnowFlakeId();
		setItemConfig.setId(id);
		boolean saveSetItemConfig = this.save(setItemConfig);
		List<SetItemMultiLanguageEntity> setItemMultiLanguageEntities = new ArrayList<>();
		List<JSONObject> multiLanguage = setItemConfig.getMultiLanguage();
		for (JSONObject jsonObject : multiLanguage) {
			SetItemMultiLanguageEntity setItemMultiLanguageEntity = new SetItemMultiLanguageEntity();
			setItemMultiLanguageEntity.setItemId(id);
			setItemMultiLanguageEntity.setItemLanguageType(jsonObject.getString("itemLanguageType"));
			setItemMultiLanguageEntity.setItemLanguageName(jsonObject.getString("itemLanguageName"));
			setItemMultiLanguageEntities.add(setItemMultiLanguageEntity);
		}

		boolean saveSetItemMultiLanguage = setItemMultiLanguageService.saveBatch(setItemMultiLanguageEntities);
		if (CollectionUtil.isNotEmpty(setItemMultiLanguageEntities)){
			return saveSetItemConfig && saveSetItemMultiLanguage;
		}else {
			return saveSetItemConfig;
		}
	}

	@Override
	@Transactional
	public boolean updateSetItemById(SetItemConfigEntity setItemConfig) {
		boolean updateSetItemConfig = this.updateById(setItemConfig);
		List<SetItemMultiLanguageEntity> setItemMultiLanguageEntities = new ArrayList<>();
		List<JSONObject> multiLanguage = setItemConfig.getMultiLanguage();
		for (JSONObject jsonObject : multiLanguage) {
			SetItemMultiLanguageEntity setItemMultiLanguageEntity = new SetItemMultiLanguageEntity();
			setItemMultiLanguageEntity.setItemId(setItemConfig.getId());
			setItemMultiLanguageEntity.setItemLanguageType(jsonObject.getString("itemLanguageType"));
			setItemMultiLanguageEntity.setItemLanguageName(jsonObject.getString("itemLanguageName"));
			setItemMultiLanguageEntities.add(setItemMultiLanguageEntity);
		}
		// 先删再加语言项
		SetItemMultiLanguageEntity removeEntity = new SetItemMultiLanguageEntity();
		removeEntity.setItemId(setItemConfig.getId());
		setItemMultiLanguageService.remove(Condition.getQueryWrapper(removeEntity));

		boolean saveSetItemMultiLanguage = setItemMultiLanguageService.saveBatch(setItemMultiLanguageEntities);
		if (CollectionUtil.isNotEmpty(setItemMultiLanguageEntities)){
			return updateSetItemConfig && saveSetItemMultiLanguage;
		}else {
			return updateSetItemConfig;
		}
	}


	public void generateAnotherModelSetItem(SetItemConfigEntity setItemConfig){
		BladeUser user = AuthUtil.getUser();
//		SetItemConfigEntity setItemConfigEntity = new SetItemConfigEntity();
//		setItemConfigEntity.setDeviceModel("CVGBST-6KW");
//		setItemConfigEntity.setCreateTime(setItemConfig.getCreateTime());
//		setItemConfigEntity.setSetCategory(1);
		LambdaQueryWrapper<SetItemConfigEntity> queryTemplateData =  Wrappers.<SetItemConfigEntity>query().lambda();
		queryTemplateData.eq(SetItemConfigEntity::getDeviceModel, setItemConfig.getRemark()) // 模板数据的设备类型  比如： CVG3SW-10kW
			.ge(SetItemConfigEntity::getCreateTime, setItemConfig.getCreateTime());
		List<SetItemConfigEntity> setItemConfigEntities = this.list(queryTemplateData);
		// 根据parentId进行分组
		Map<Long, List<SetItemConfigEntity>> groupedEntities = setItemConfigEntities.stream()
			.collect(Collectors.groupingBy(SetItemConfigEntity::getParentId));
		// 对分组后的结果进行排序
		Map<Long, List<SetItemConfigEntity>> sortedGroupedEntities = new LinkedHashMap<>();
		groupedEntities.entrySet().stream()
			.sorted(Map.Entry.comparingByKey()) // 按照ParentId进行排序
			.forEach(entry -> {
				if (entry.getKey() != 0L) {
					sortedGroupedEntities.put(entry.getKey(), entry.getValue());
				}
			});
		sortedGroupedEntities.put(0L, groupedEntities.get(0L)); // 将ParentId为0的项放在最后

		ArrayList<SetItemConfigEntity> resultInsertList = new ArrayList<>();
		Date date = new Date();
		// 遍历分组结果
		for (Map.Entry<Long, List<SetItemConfigEntity>> entry : sortedGroupedEntities.entrySet()) {
			Long parentId = entry.getKey();
			List<SetItemConfigEntity> entities = entry.getValue();
			if (parentId != 0L) {
				// 先处理子配置项,让子配置项有新id和旧的父级id
				for (SetItemConfigEntity entity : entities) {
					entity.setOldId(entity.getId());
					Long snowFlakeId = SnowFlakeGeneratorUtil.getSnowFlakeId();
					entity.setId(snowFlakeId);
					entity.setDeviceModel(setItemConfig.getDeviceModel());
					entity.setCreateTime(date);
					entity.setUpdateTime(date);
					entity.setRemark(setItemConfig.getDeviceModel());
					entity.setCreateUserAccount(setItemConfig.getCreateUserAccount());
					resultInsertList.add(entity);
				}
			}else {
				// 根据parentId进行分组
				Map<Long, List<SetItemConfigEntity>> grouped = resultInsertList.stream()
					.collect(Collectors.groupingBy(SetItemConfigEntity::getParentId));
				// 处理父配置项，让其有新id，
				for (SetItemConfigEntity entity : entities) {
					entity.setOldId(entity.getId());
					Long snowFlakeId = SnowFlakeGeneratorUtil.getSnowFlakeId();
					Long oldPId = entity.getId();
					entity.setId(snowFlakeId);
					entity.setDeviceModel(setItemConfig.getDeviceModel());
					entity.setCreateTime(date);
					entity.setUpdateTime(date);
					entity.setRemark(setItemConfig.getDeviceModel());
					entity.setCreateUserAccount(setItemConfig.getCreateUserAccount());
					resultInsertList.add(entity);

					List<SetItemConfigEntity> groupedSubItems = grouped.get(oldPId);
					if (groupedSubItems == null){
						continue;
					}
					for (SetItemConfigEntity itemConfigEntity : groupedSubItems) {
						itemConfigEntity.setParentId(snowFlakeId);
					}
				}
			}

		}
		log.info("resultInsertList : {}", resultInsertList);
		this.saveBatch(resultInsertList);
		// 保存多语言
		saveSetItemMultiLanguage(setItemConfigEntities,resultInsertList,setItemConfig);
	}


	private void saveSetItemMultiLanguage(List<SetItemConfigEntity> setItemConfigEntities,ArrayList<SetItemConfigEntity> resultInsertList,SetItemConfigEntity setItemConfig) {
		List<Long> idList = setItemConfigEntities.stream().map(SetItemConfigEntity::getOldId).collect(Collectors.toList());
		LambdaQueryWrapper<SetItemMultiLanguageEntity> queryTemplateData = Wrappers.<SetItemMultiLanguageEntity>query().lambda();
		queryTemplateData.in(SetItemMultiLanguageEntity::getItemId, idList);
		List<SetItemMultiLanguageEntity> dbResult = setItemMultiLanguageService.list(queryTemplateData);
		Map<Long, SetItemConfigEntity> oldIdResultList = resultInsertList.stream().collect(Collectors.toMap(SetItemConfigEntity::getOldId,p -> p));
		List<SetItemMultiLanguageEntity> insertMultiLangList = new ArrayList<>();
		for(SetItemMultiLanguageEntity dbItem : dbResult) {
			SetItemMultiLanguageEntity insertEntity = new SetItemMultiLanguageEntity();
			Long snowFlakeId = SnowFlakeGeneratorUtil.getSnowFlakeId();
			insertEntity.setId(snowFlakeId);
			insertEntity.setItemLanguageType(dbItem.getItemLanguageType());
			insertEntity.setItemLanguageName(dbItem.getItemLanguageName());
			SetItemConfigEntity parentEntity = oldIdResultList.get(dbItem.getItemId());
			insertEntity.setItemId(parentEntity.getId());
			insertEntity.setDescription(setItemConfig.getCreateUserAccount());
			insertMultiLangList.add(insertEntity);
		}
		log.info("insertMultiLangList : {}",insertMultiLangList);
		setItemMultiLanguageService.saveBatch(insertMultiLangList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void importExcel(List<SetItemConfigExcel> data, Boolean isCovered) {
		data.forEach(excel -> {
			String fakeId = excel.getFakeId().replace('\'',' ');
			fakeId = fakeId.trim();
			SetItemConfigEntity byId = this.getById(fakeId);
			if (byId != null){
				byId.setAttribute3(excel.getAttribute3());
				byId.setUpdateTime(new Date());
				this.saveOrUpdate(byId);
			}
		});
	}

}

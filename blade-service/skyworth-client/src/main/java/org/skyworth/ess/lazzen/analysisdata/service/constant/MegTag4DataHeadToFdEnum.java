package org.skyworth.ess.lazzen.analysisdata.service.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * 消息类型码定义 : DataHead 为 fd 时 ，megTag 对应的值
 */
@Getter
public enum MegTag4DataHeadToFdEnum {
    code_01("01", "产品信息"),
    code_02("02", "运维参数"),
    code_03("03", "运行状态"),
    code_04("04", "测量数据/数据读取"),
    code_05("05", "保护参数"),
    code_06("06", "故障告警变位信息"),
    code_07("07", "设备参数写入/遥控指令参数"),
    code_08("08", "数据上报频率设置"),
    code_09("09", "点表增减设置/数据读取应答"),
    code_0A("0A", "注册"),
    code_0B("0B", "远程复位"),
    code_0C("0C", "总召"),
    code_0D("0D", "远程升级"),
    code_0E("0E", "数据读取"),
    code_0F("0F", "复费率电能"),
    code_10("10", "电能质量分析"),
    code_11("11", "故障录波"),
    code_12("12", "内部参数配置");

    private final String code;
    private final String comment;

    MegTag4DataHeadToFdEnum(String code, String comment) {
        this.code = code;
        this.comment = comment;
    }

}

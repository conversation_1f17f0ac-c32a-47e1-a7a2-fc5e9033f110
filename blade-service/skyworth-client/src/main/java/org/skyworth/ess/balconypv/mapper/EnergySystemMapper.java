package org.skyworth.ess.balconypv.mapper;

import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.vo.EnergySystemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 能源系统 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface EnergySystemMapper extends BaseMapper<EnergySystemEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param energySystem
	 * @return
	 */
	List<EnergySystemVO> selectEnergySystemPage(IPage page, @Param("param") EnergySystemVO energySystem,
												@Param("userIds") List<Long> userIds);

}

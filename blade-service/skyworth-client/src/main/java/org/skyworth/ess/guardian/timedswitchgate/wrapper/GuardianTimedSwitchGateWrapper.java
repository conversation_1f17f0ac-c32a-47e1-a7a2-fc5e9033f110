/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedswitchgate.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.guardian.timedswitchgate.vo.GuardianTimedSwitchGateVO;
import java.util.Objects;

/**
 * 安全卫士定时设置-开关闸设置 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public class GuardianTimedSwitchGateWrapper extends BaseEntityWrapper<GuardianTimedSwitchGateEntity, GuardianTimedSwitchGateVO>  {

	public static GuardianTimedSwitchGateWrapper build() {
		return new GuardianTimedSwitchGateWrapper();
 	}

	@Override
	public GuardianTimedSwitchGateVO entityVO(GuardianTimedSwitchGateEntity GuardianTimedSwitchGate) {
		GuardianTimedSwitchGateVO GuardianTimedSwitchGateVO = Objects.requireNonNull(BeanUtil.copy(GuardianTimedSwitchGate, GuardianTimedSwitchGateVO.class));

		//User createUser = UserCache.getUser(GuardianTimedSwitchGate.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianTimedSwitchGate.getUpdateUser());
		//GuardianTimedSwitchGateVO.setCreateUserName(createUser.getName());
		//GuardianTimedSwitchGateVO.setUpdateUserName(updateUser.getName());

		return GuardianTimedSwitchGateVO;
	}


}

package org.skyworth.ess.agreement.util;

import org.eclipse.paho.client.mqttv3.MqttMessage;

/**
 * @ClassName AbstractDeviceControlService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/31 17:09
 */
public abstract class AbstractDeviceControlService implements DeviceControlService{
	//protected MqttClient mqttClient;
	protected ProtocolParserFactory parserFactory;

	public AbstractDeviceControlService(/*MqttClient mqttClient, */ProtocolParserFactory parserFactory) {
		/*this.mqttClient = mqttClient;*/
		this.parserFactory = parserFactory;
	}

	/**
	 * 推送到MQTT
	 * @param topic 主题
	 * @param message 消息内容
	 */
	protected void pushToMqtt(String topic, String message) {
		try {
			MqttMessage mqttMessage = new MqttMessage(message.getBytes());
			// 设置QoS级别
			mqttMessage.setQos(1);
			//mqttClient.publish(topic, mqttMessage);
		} catch (Exception e) {
			throw new RuntimeException("MQTT推送失败", e);
		}
	}

	/**
	 * 获取对应公司的协议解析器
	 * @param company 公司标识
	 * @return 协议解析器
	 */
	protected ProtocolParser getParser(String company) {
		return parserFactory.getParser(company);
	}

	/**
	 * 构建MQTT主题
	 * @param company 公司标识
	 * @param deviceId 设备ID
	 * @return 完整的主题路径
	 */
	protected String buildTopic(String company, String deviceId) {
		return String.format("/%s/%s/control", company, deviceId);
	}
}

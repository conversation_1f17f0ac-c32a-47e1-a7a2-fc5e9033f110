/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.mapper;

import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO;
import org.skyworth.ess.guardian.guardianplant.excel.GuardianPlantExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.plant.vo.PlantVO;

import java.util.List;
import java.util.Map;

/**
 * 安全卫士对应站点 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface GuardianPlantMapper extends BaseMapper<GuardianPlantEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianPlant
	 * @return
	 */
	List<GuardianPlantVO> selectGuardianPlantPage(@Param("page") IPage page, @Param("params") GuardianPlantVO GuardianPlant);

	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianPlantExcel> exportGuardianPlant(@Param("ew") Wrapper<GuardianPlantEntity> queryWrapper);

	GuardianPlantVO detail(@Param("plantId") Long plantId, @Param("securityGuardSerialNumber") String securityGuardSerialNumber);

	List<GuardianPlantEntity> queryByGuardianSerialNumberList(@Param("list")List<String> list);

	List<GuardianPlantEntity> queryGuardianSerialNumberList(@Param("list")List<Long> longList);

	int batchDeleteLogicByPlantId(@Param("list")List<Long> longList,@Param("account") String account);

	int updateDataByCondition(@Param("params") GuardianPlantEntity updateGuardianPlantEntity);

	Map<String, Object> getAllGuardianPowerSetup(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);

	Map<String, Object> getAllGuardianSwitchGateSetup(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);

	Map<String, Object> getAllGuardianGatePositionSetup(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);

	List<Map<String,Object>> getPlantInfo(@Param("plantIdList") List<Long> plantIdList);
}

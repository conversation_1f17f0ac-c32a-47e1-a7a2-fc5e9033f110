/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.service.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.service.impl.BatteryExitFactoryInfoServiceImpl;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.vo.GuardianExitFactoryInfoVO;
import org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel;
import org.skyworth.ess.guardian.exitfactory.mapper.GuardianExitFactoryInfoMapper;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 安全卫士储能出厂信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
@AllArgsConstructor
public class GuardianExitFactoryInfoServiceImpl extends BaseServiceImpl<GuardianExitFactoryInfoMapper, GuardianExitFactoryInfoEntity> implements IGuardianExitFactoryInfoService {
	private final IDictBizClient dictBizClient;
	private final IGuardianPlantService guardianPlantService;
	@Override
	public IPage<GuardianExitFactoryInfoVO> selectGuardianExitFactoryInfoPage(IPage<GuardianExitFactoryInfoVO> page, GuardianExitFactoryInfoVO guardianExitFactoryInfo) {
		return page.setRecords(baseMapper.selectGuardianExitFactoryInfoPage(page, guardianExitFactoryInfo));
	}


	@Override
	public List<GuardianExitFactoryInfoExcel> exportGuardianExitFactoryInfo(Wrapper<GuardianExitFactoryInfoEntity> queryWrapper) {
		List<GuardianExitFactoryInfoExcel> guardianExitFactoryInfoList = baseMapper.exportGuardianExitFactoryInfo(queryWrapper);
		Map<String, List<DictBiz>> dictMap = dictBizClient.batchGetList(List.of("device_company",DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode())).getData();
		Map<String, String> companyMap = listDictToMap(dictMap.get("device_company"));
		Map<String, String> guardianTypeMap = listDictToMap(dictMap.get(DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode()));
		for (GuardianExitFactoryInfoExcel excel : guardianExitFactoryInfoList) {
			if (StringUtil.isNotBlank(excel.getCompany()) && companyMap.containsKey(excel.getCompany())) {
				excel.setCompany(companyMap.get(excel.getCompany()));
			}
			if (StringUtil.isNotBlank(excel.getDeviceType()) && guardianTypeMap.containsKey(excel.getDeviceType())) {
				excel.setDeviceType(guardianTypeMap.get(excel.getDeviceType()));
			}
		}
		return guardianExitFactoryInfoList;
	}

	@Override
	public R deleteLogicGuardianExitFactory(List<Long> longList) {
		List<GuardianExitFactoryInfoEntity> exitFactoryInfoEntities = baseMapper.selectBatchIds(longList);
		if (exitFactoryInfoEntities == null || exitFactoryInfoEntities.isEmpty()) {
			throw new BusinessException("client.data.not.exist");
		}
		List<String> serialNumberList = exitFactoryInfoEntities.stream().map(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber).collect(Collectors.toList());
		List<GuardianPlantEntity> guardianPlantEntities = guardianPlantService.queryByGuardianSerialNumberList(serialNumberList);
		// 验证安全卫士是否被使用
		if (guardianPlantEntities != null && !guardianPlantEntities.isEmpty()) {
			throw new BusinessException("client.guardian.sn.exist");
		}
		BladeUser user = AuthUtil.getUser();
		return R.data(baseMapper.deleteLogicGuardianExitFactory(user.getUserId(), user.getAccount(), longList));
	}


	@Override
	public String importAddExcel(List<GuardianExitFactoryInfoExcel> guardianExitFactoryInfoExcels, Boolean aBoolean) {
		BladeUser user = AuthUtil.getUser();
		List<String> serialNumbers = guardianExitFactoryInfoExcels.stream().map(GuardianExitFactoryInfoExcel::getSecurityGuardSerialNumber).collect(Collectors.toList());
		List<GuardianExitFactoryInfoEntity> exitFactoryInfoEntities = baseMapper.queryBySerialNumbers(serialNumbers);
		if (CollectionUtil.isNotEmpty(exitFactoryInfoEntities)) {
			List<String> collect = exitFactoryInfoEntities.stream().map(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber).collect(Collectors.toList());
			String join = Joiner.on(",").skipNulls().join(collect);
			return "SN:" + join + " had exists, please use modify import.";
		}
		List<GuardianExitFactoryInfoEntity> insert = new ArrayList<>();
		guardianExitFactoryInfoExcels.forEach(userExcel -> {
			GuardianExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, GuardianExitFactoryInfoEntity.class));
			entity.setCreateUserAccount(user.getAccount());
			entity.setCreateUser(user.getUserId());
			entity.setExitFactoryDate(userExcel.getExitFactoryDate());
			entity.setStatus(CommonConstant.NOT_SEALED_ID);
			insert.add(entity);
		});
		saveOrUpdateBatch(insert);
		return "";
	}

	@Override
	public String importModifyExcel(List<GuardianExitFactoryInfoExcel> guardianExitFactoryInfoExcels, Boolean aBoolean) {
		BladeUser user = AuthUtil.getUser();
		List<String> serialNumbers = guardianExitFactoryInfoExcels.stream().map(GuardianExitFactoryInfoExcel::getSecurityGuardSerialNumber).collect(Collectors.toList());
		List<GuardianExitFactoryInfoEntity> exitFactoryInfoEntities = baseMapper.queryBySerialNumbers(serialNumbers);
		// 如果入参中个数 和 DB中个数不相等，则表示有新增数据，提示使用新增导入
		if (serialNumbers.size() != exitFactoryInfoEntities.size()) {
			List<String> dbSerialNumbers = exitFactoryInfoEntities.stream().map(GuardianExitFactoryInfoEntity::getSecurityGuardSerialNumber).collect(Collectors.toList());
			StringBuilder join = new StringBuilder();
			serialNumbers.forEach(p -> {
				if (!dbSerialNumbers.contains(p)) {
					join.append(p).append(",");
				}
			});
			return "SN:" + join + " had not exists, please use add import.";
		}
		List<GuardianExitFactoryInfoEntity> update = new ArrayList<>();
		guardianExitFactoryInfoExcels.forEach(userExcel -> {
			GuardianExitFactoryInfoEntity entity = Objects.requireNonNull(BeanUtil.copy(userExcel, GuardianExitFactoryInfoEntity.class));
			entity.setUpdateUserAccount(user.getAccount());
			entity.setUpdateUser(user.getUserId());
			update.add(entity);
		});
		baseMapper.updateBatchBySn(update);
		return "";
	}

	@Override
	public int batchUpdate(List<String> serialNumberList) {
		List<GuardianExitFactoryInfoEntity> update = new ArrayList<>();
		BladeUser user = AuthUtil.getUser();
		for (String serialNumber : serialNumberList) {
			GuardianExitFactoryInfoEntity entity = new GuardianExitFactoryInfoEntity();
			entity.setUpdateUser(user.getUserId());
			entity.setUpdateUserAccount(user.getAccount());
			entity.setSecurityGuardSerialNumber(serialNumber);
			entity.setUpdateTime(new Date());
			entity.setStatus(0);
			update.add(entity);
		}
		return baseMapper.updateBatchBySn(update);
	}

	@Override
	public boolean updateDeadlineInfo(GuardianExitFactoryInfoEntity entity) {
		QueryWrapper<GuardianExitFactoryInfoEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("security_guard_serial_number", entity.getSecurityGuardSerialNumber());
		List<GuardianExitFactoryInfoEntity> list = baseMapper.selectList(queryWrapper);
		if (!list.isEmpty()) {
			GuardianExitFactoryInfoEntity exitFactoryInfo = list.get(0);
			LocalDate startDate = exitFactoryInfo.getWarrantyStartDate();
			Integer qualityGuaranteeYear = exitFactoryInfo.getQualityGuaranteeYear();
			if (ValidationUtil.isNotEmpty(startDate) && ValidationUtil.isNotEmpty(qualityGuaranteeYear)) {
				entity.setWarrantyEndDate(startDate.plusYears(qualityGuaranteeYear));
			}
			baseMapper.updateById(entity);
			return true;
		}
		return false;
	}

	@Cached(name = "GuardianModel.", key = "#guardianExitFactoryInfoEntity.securityGuardSerialNumber", expire = 12, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
	@Override
	public GuardianExitFactoryInfoEntity getModelBySn(GuardianExitFactoryInfoEntity guardianExitFactoryInfoEntity) {
		return this.getOne(Condition.getQueryWrapper(guardianExitFactoryInfoEntity));
	}

	private Map<String, String> listDictToMap(List<DictBiz> dictBizList) {
		return Optional.ofNullable(dictBizList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (a, b) -> a));
	}
}

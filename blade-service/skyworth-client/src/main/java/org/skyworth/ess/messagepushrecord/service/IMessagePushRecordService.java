/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity;
import org.skyworth.ess.messagepushrecord.vo.MessagePushRecordVO;
import org.skyworth.ess.messagepushrecord.excel.MessagePushRecordExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 消息推送日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface IMessagePushRecordService extends BaseService<MessagePushRecordEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messagePushRecord
	 * @return
	 */
	IPage<MessagePushRecordVO> selectMessagePushRecordPage(IPage<MessagePushRecordVO> page, MessagePushRecordVO messagePushRecord);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<MessagePushRecordExcel> exportMessagePushRecord(Wrapper<MessagePushRecordEntity> queryWrapper);

}

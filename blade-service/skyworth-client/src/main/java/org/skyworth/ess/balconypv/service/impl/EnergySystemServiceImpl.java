package org.skyworth.ess.balconypv.service.impl;

import lombok.AllArgsConstructor;
import org.skyworth.ess.balconypv.entity.EnergySystemEntity;
import org.skyworth.ess.balconypv.mapper.EnergySystemMapper;
import org.skyworth.ess.balconypv.service.IEnergySystemService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 能源系统 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
@AllArgsConstructor
public class EnergySystemServiceImpl extends BaseServiceImpl<EnergySystemMapper, EnergySystemEntity> implements IEnergySystemService {

}

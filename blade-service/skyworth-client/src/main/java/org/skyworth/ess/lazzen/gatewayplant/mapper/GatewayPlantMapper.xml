<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.lazzen.gatewayplant.mapper.GatewayPlantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="gatewayPlantResultMap" type="org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantVO">
        <result column="id" property="id"/>
        <result column="gateway_unique_number" property="gatewayUniqueNumber"/>
        <result column="plant_id" property="plantId"/>
        <result column="heart_beat_time" property="heartBeatTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="turn_on_off" property="turnOnOff"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>

    <delete id="batchDeleteLogicByPlantId">
        update lazzen_gateway_plant set is_deleted=1,update_time=now(), update_user_account=#{account} where plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectGatewayPlantPage" resultMap="gatewayPlantResultMap">
        SELECT
        gp.id,
        gp.plant_id,
        gp.gateway_unique_number,
        gp.status,
        gp.device_type,
        gp.turn_on_off,
        p.plant_name,
        p.detail_address,
        p.country_code,
        p.province_code,
        p.city_code,
        p.county_code,
        p.detail_address,
        p.create_user,
        p.operation_user_id,
        p.operation_company_id
        FROM lazzen_gateway_plant gp
        left join plant p on gp.plant_id = p.id and p.is_deleted = 0
        WHERE
        gp.is_deleted = 0
        <if test="params.countryCode!=null and params.countryCode!=''">
            and p.country_code = #{params.countryCode}
        </if>
        <if test="params.provinceCode!=null and params.provinceCode!=''">
            and p.province_code = #{params.provinceCode}
        </if>
        <if test="params.status!=null">
            and gp.status = #{params.status}
        </if>
        <if test="params.userIds!=null and params.userIds.size() > 0 ">
            and p.create_user in
            <foreach item="item" index="index" collection="params.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.agentUserIds!=null and params.agentUserIds.size() > 0 ">
            and p.operation_user_id in
            <foreach item="item" index="index" collection="params.agentUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.agentCompanyIds!=null and params.agentCompanyIds.size() > 0 ">
            and p.operation_company_id in
            <foreach item="item" index="index" collection="params.agentCompanyIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.deviceType!=null and params.deviceType!=''">
            and gp.device_type = #{params.deviceType}
        </if>
        <if test="params.gatewayUniqueNumber!=null and params.gatewayUniqueNumber!=''">
            and gp.gateway_unique_number like CONCAT(#{params.gatewayUniqueNumber}, '%')
        </if>
        ORDER BY gp.update_time DESC, gp.id DESC
    </select>


    <select id="exportGatewayPlant" resultType="org.skyworth.ess.lazzen.gatewayplant.excel.GatewayPlantExcel">
        SELECT * FROM lazzen_gateway_plant ${ew.customSqlSegment}
    </select>
    <select id="getAllGuardianGatePositionSetup" resultType="java.util.Map">
        SELECT
           gp.plant_id as plantId,
           gp.gateway_unique_number as gatewayUniqueNumber,
           gp.device_type as deviceType,
           gp.status as status,
           gp.turn_on_off as turnOnOff
        from lazzen_gateway_plant gp
        where gp.is_deleted = 0
          and gp.gateway_unique_number = #{serialNumber}
          and gp.plant_id = #{plantId}
    </select>

    <select id="detail" resultMap="gatewayPlantResultMap">
        SELECT
        gp.id,
        gp.plant_id,
        gp.gateway_unique_number,
        gp.status,
        gp.device_type,
        p.plant_name,
        p.detail_address,
        p.country_code,
        p.province_code,
        p.city_code,
        p.county_code,
        p.detail_address,
        p.create_user,
        p.operation_user_id,
        p.operation_company_id
        FROM lazzen_gateway_plant gp
        left join plant p on gp.plant_id = p.id and p.is_deleted = 0
        WHERE
        gp.is_deleted = 0
        <if test="plantId!=null and plantId!=''">
            and gp.plant_id = #{plantId}
        </if>
        <if test="serialNumber!=null and serialNumber!=''">
            and gp.gateway_unique_number = #{serialNumber}
        </if>
    </select>

    <update id="batchUpdateHeartTimeByGatewayUniqueNumber">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update lazzen_gateway_plant set
            heart_beat_time=#{item.heartBeatTime},
            update_time=now()
            where gateway_unique_number = #{item.gatewayUniqueNumber}
            and is_deleted = 0
        </foreach>
    </update>

    <update id="batchUpdateStatusByGatewayUniqueNumber">
        <foreach collection="updateList" item="item" index="index" open="" close="" separator=";">
            update lazzen_gateway_plant set
            status=#{item.status},
            update_time=now()
            where gateway_unique_number = #{item.gatewayUniqueNumber}
            and is_deleted = 0
            and (status != 2 or status is null)
        </foreach>
    </update>

    <update id="updateDataByCondition">
        update lazzen_gateway_plant set
        <if test="params.isDeleted!=null ">
            is_deleted = #{params.isDeleted},
        </if>
        <if test="params.updateUser!=null ">
            update_user = #{params.updateUser},
        </if>
        <if test="params.updateUserAccount!=null ">
            update_user_account = #{params.updateUserAccount},
        </if>
        update_time=now()
        where is_deleted = 0
        <if test="params.createUser!=null ">
            and create_user = #{params.createUser}
        </if>
    </update>
</mapper>

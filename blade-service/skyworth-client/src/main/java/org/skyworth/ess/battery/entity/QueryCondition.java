package org.skyworth.ess.battery.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 储能列表查询条件
 *
 * @description
 * @author: SDT50545
 * @since: 2023-09-14 17:58
 **/
@Data
@ApiModel(value = "储能列表查询条件", description = "储能列表查询条件")
public class QueryCondition implements Serializable {
	private static final long serialVersionUID = -1;

	@ApiModelProperty(value = "是否删除 0: 未删除 1: 已删除")
	private String deleteFlag;

	@ApiModelProperty(value = "国家编码")
	private String countryCode;

	@ApiModelProperty(value = "一级行政区域编码")
	private String provinceCode;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点id")
	private Long plantId;

	@ApiModelProperty(value = "站点名称")
	private String plantName;

	@ApiModelProperty(value = "储能SN")
	private String batterySerialNumber;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;

	@ApiModelProperty(value = "智能能量变换器SN")
	private String deviceSerialNumber;

	@ApiModelProperty(value = "状态")
	private Integer status;

	@ApiModelProperty("用户名")
	private String createUserAccount;

	@ApiModelProperty("事件类型(inverter智能能量变换器;battery储能;plant站点)")
	private String eventType;

	@ApiModelProperty("异常类型(inverter智能能量变换器;battery储能)")
	private String exceptionType;

	@ApiModelProperty("运维团队")
	private String operationCompanyName;

	@ApiModelProperty("运维人员")
	private String operationUserName;

	@ApiModelProperty("运维团队Id")
	private List<Long> operationCompanyIds;

	@ApiModelProperty("运维人员Id")
	private List<Long> operationUserIds;

	@ApiModelProperty("区号")
	private String phoneDiallingCode;

	@ApiModelProperty("手机号")
	private String phone;

	@ApiModelProperty("真实名称")
	private String realName;

	@ApiModelProperty("额定容量")
	private String ratedBatteryCapacity;

	@ApiModelProperty("类型 1为日 2为月")
	private String queryDateType;

	@ApiModelProperty("手机号绑定人员")
	private List<Long> userAndPhoneIds;

	@ApiModelProperty("创建人")
	private Long createUser;

	@ApiModelProperty("部门id")
	private String deptId;

	@ApiModelProperty("电池归属1,或者归属2")
	private Integer batteryEnergyStorageNumber = 1;

}

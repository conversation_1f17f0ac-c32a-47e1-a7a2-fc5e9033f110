/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.core.tool.node.INode;
import org.springblade.system.vo.DictBizVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * APP设置项配置 实体类
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
@TableName("device_set_item_config")
@ApiModel(value = "SetItemConfig对象", description = "APP设置项配置")
@EqualsAndHashCode(callSuper = true)
public class SetItemConfigEntity extends TenantEntity implements INode<SetItemConfigEntity> {

	/**
	 * 父级id
	 */
	@ApiModelProperty(value = "父级id")
	private Long parentId;
	/**
	 * 设备类型0智能能量变换器1储能2光伏组件3充电桩
	 */
	@ApiModelProperty(value = "设备类型0智能能量变换器1储能2光伏组件3充电桩")
	private Integer deviceType;
	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String deviceModel;
	/**
	 * 设置类型0高级设置，1开机设置，2分时设置，3设备参数
	 */
	@ApiModelProperty(value = "设置类型0高级设置，1开机设置，2分时设置，3设备参数")
	private Integer setCategory;
	/**
	 * 设置项大类
	 */
	@ApiModelProperty(value = "设置项大类")
	private String setItemBigType;
	/**
	 * 设置项类型0button，1select，2input
	 */
	@ApiModelProperty(value = "设置项类型0button，1select，2input")
	private Integer setItemType;
	/**
	 * 设置项名称
	 */
	@ApiModelProperty(value = "设置项名称")
	private String setItemName;
	/**
	 * 设置项键
	 */
	@ApiModelProperty(value = "设置项键")
	private String setItemKey;
	/**
	 * 设置项值
	 */
	@ApiModelProperty(value = "设置项值")
	private String setItemValue;
	/**
	 * 设置项默认值
	 */
	@ApiModelProperty(value = "设置项默认值")
	private String setItemDefault;
	/**
	 * 设置项排序
	 */
	@ApiModelProperty(value = "设置项排序")
	private Integer setItemSort;
	/**
	 * 设置项对应数据库表
	 */
	@ApiModelProperty(value = "设置项对应数据库表")
	private String setItemTable;
	/**
	 * 设置项对应数据库字段
	 */
	@ApiModelProperty(value = "设置项对应数据库字段")
	private String setItemTableColumn;
	/**
	 * 设置项对应协议协议版本
	 */
	@ApiModelProperty(value = "设置项对应协议协议版本")
	private String setItemProtocolVersion;
	/**
	 * 输入框设置项最小值
	 */
	@ApiModelProperty(value = "输入框设置项最小值")
	private Integer setItemRangeMin;
	/**
	 * 输入框设置项最大值
	 */
	@ApiModelProperty(value = "输入框设置项最大值")
	private Integer setItemRangeMax;
	/**
	 * 设置项对应协议映射地址
	 */
	@ApiModelProperty(value = "设置项对应协议映射地址")
	private String setItemProtocolAddress;
	/**
	 * 是否子设置项0否1是
	 */
	@ApiModelProperty(value = "是否子设置项0否1是")
	private Integer isSubItem;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 保留字段1
	 */
	@ApiModelProperty(value = "保留字段1")
	private String attribute1;
	/**
	 * 保留字段2
	 */
	@ApiModelProperty(value = "保留字段2")
	private String attribute2;
	/**
	 * 保留字段3
	 */
	@ApiModelProperty(value = "保留字段3")
	private String attribute3;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@TableField(exist = false)
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<SetItemConfigEntity> children;

	/**
	 * 设置项多语言
	 */
	@TableField(exist = false)
	private List<JSONObject> multiLanguage;

	@TableField(exist = false)
	private String otherLanguage;
	@TableField(exist = false)
	private Long oldId;

	@Override
	public List<SetItemConfigEntity> getChildren() {
		if (this.children == null){
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	@Override
	public Boolean getHasChildren() {
		return INode.super.getHasChildren();
	}
}

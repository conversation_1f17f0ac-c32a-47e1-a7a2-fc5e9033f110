/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedpower.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.vo.GuardianTimedPowerVO;
import org.skyworth.ess.guardian.timedpower.excel.GuardianTimedPowerExcel;
import org.skyworth.ess.guardian.timedpower.mapper.GuardianTimedPowerMapper;
import org.skyworth.ess.guardian.timedpower.service.IGuardianTimedPowerService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安全卫士定时设置-功率设置 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class GuardianTimedPowerServiceImpl extends BaseServiceImpl<GuardianTimedPowerMapper, GuardianTimedPowerEntity> implements IGuardianTimedPowerService {

	@Override
	public IPage<GuardianTimedPowerVO> selectGuardianTimedPowerPage(IPage<GuardianTimedPowerVO> page, GuardianTimedPowerVO GuardianTimedPower) {
		return page.setRecords(baseMapper.selectGuardianTimedPowerPage(page, GuardianTimedPower));
	}


	@Override
	public List<GuardianTimedPowerExcel> exportGuardianTimedPower(Wrapper<GuardianTimedPowerEntity> queryWrapper) {
		List<GuardianTimedPowerExcel> GuardianTimedPowerList = baseMapper.exportGuardianTimedPower(queryWrapper);
		//GuardianTimedPowerList.forEach(GuardianTimedPower -> {
		//	GuardianTimedPower.setTypeName(DictCache.getValue(DictEnum.YES_NO, GuardianTimedPower.getType()));
		//});
		return GuardianTimedPowerList;
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		LambdaUpdateWrapper<GuardianTimedPowerEntity> update = Wrappers.<GuardianTimedPowerEntity>update().lambda()
			.set(GuardianTimedPowerEntity::getIsDeleted, 1)
			.eq(GuardianTimedPowerEntity::getPlantId, plantId)
			.eq(GuardianTimedPowerEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber);
		return baseMapper.delete(update);
	}

}

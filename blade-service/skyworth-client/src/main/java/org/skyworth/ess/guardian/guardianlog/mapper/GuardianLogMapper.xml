<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.guardianlog.mapper.GuardianLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianLogResultMap" type="org.skyworth.ess.guardian.guardianlog.entity.GuardianLogEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="a_phase_voltage" property="aPhaseVoltage"/>
        <result column="a_phase_current" property="aPhaseCurrent"/>
        <result column="a_phase_temperature" property="aPhaseTemperature"/>
        <result column="b_phase_voltage" property="bPhaseVoltage"/>
        <result column="b_phase_current" property="bPhaseCurrent"/>
        <result column="b_phase_temperature" property="bPhaseTemperature"/>
        <result column="c_phase_voltage" property="cPhaseVoltage"/>
        <result column="c_phase_current" property="cPhaseCurrent"/>
        <result column="c_phase_temperature" property="cPhaseTemperature"/>
        <result column="n_neutral_line_temperature" property="nNeutralLineTemperature"/>
        <result column="active_power" property="activePower"/>
        <result column="reactive_power" property="reactivePower"/>
        <result column="active_power_consumption" property="activePowerConsumption"/>
        <result column="reactive_power_consumption" property="reactivePowerConsumption"/>
        <result column="power_factor" property="powerFactor"/>
        <result column="grid_frequency" property="gridFrequency"/>
        <result column="leakage_current" property="leakageCurrent"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="alarm_status" property="alarmStatus"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianLogPage" resultMap="GuardianLogResultMap">
        select * from security_guard_log where is_deleted = 0
    </select>


    <select id="exportGuardianLog" resultType="org.skyworth.ess.guardian.guardianlog.excel.GuardianLogExcel">
        SELECT * FROM security_guard_log ${ew.customSqlSegment}
    </select>

    <select id="selectGuardianLogDataStatByTime" resultType="org.skyworth.ess.guardian.guardianplant.vo.GuardianLogDataStatVO">
        SELECT
            DATE_FORMAT(device_date_time, '%H:%i') deviceDateTime,
            DATE_FORMAT(device_date_time, '%Y-%m-%d') deviceDateTimeForDay,
            DATE_FORMAT(device_date_time, '%Y-%m-%d %H:%i')  deviceDateTimeForCal,
            IFNULL(a_phase_voltage, 0) AS a_phaseVoltage,
            IFNULL(a_phase_current, 0) AS aPhaseCurrent,
            IFNULL(a_phase_temperature, 0) AS aPhaseTemperature,
            IFNULL(b_phase_voltage, 0) AS bPhaseVoltage,
            IFNULL(b_phase_current, 0) AS bPhaseCurrent,
            IFNULL(b_phase_temperature, 0) AS bPhaseTemperature,
            IFNULL(c_phase_voltage, 0) AS cPhaseVoltage,
            IFNULL(c_phase_current, 0) AS cPhaseCurrent,
            IFNULL(c_phase_temperature, 0) AS cPhaseTemperature,
            IFNULL(n_neutral_line_temperature, 0) AS nNeutralLineTemperature,
            IFNULL(leakage_current, 0) AS leakageCurrent
        FROM security_guard_log
        WHERE device_date_time BETWEEN #{condition.startDateTime} AND #{condition.endDateTime}
        AND security_guard_serial_number = #{condition.securityGuardSerialNumber}
        AND plant_id = #{condition.plantId}
        ORDER BY device_date_time desc,id desc
    </select>


</mapper>

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianlog.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.guardian.guardianlog.entity.GuardianLogEntity;
import org.skyworth.ess.guardian.guardianlog.excel.GuardianLogExcel;
import org.skyworth.ess.guardian.guardianlog.vo.GuardianLogVO;
import org.skyworth.ess.guardian.guardianplant.dto.GuardianLogQueryCondition;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianLogDataStatVO;

import java.util.List;

/**
 * 安全卫士日志 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface GuardianLogMapper extends BaseMapper<GuardianLogEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianLog
	 * @return
	 */
	List<GuardianLogVO> selectGuardianLogPage(IPage page, GuardianLogVO GuardianLog);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianLogExcel> exportGuardianLog(@Param("ew") Wrapper<GuardianLogEntity> queryWrapper);

	List<GuardianLogDataStatVO> selectGuardianLogDataStatByTime(@Param("condition") GuardianLogQueryCondition queryCondition);
}

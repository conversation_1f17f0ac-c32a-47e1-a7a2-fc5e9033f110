<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.setItem.mapper.SetItemConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="setItemConfigResultMap" type="org.skyworth.ess.setItem.entity.SetItemConfigEntity">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="device_type" property="deviceType"/>
        <result column="device_model" property="deviceModel"/>
        <result column="set_category" property="setCategory"/>
        <result column="set_item_big_type" property="setItemBigType"/>
        <result column="set_item_type" property="setItemType"/>
        <result column="set_item_name" property="setItemName"/>
        <result column="set_item_key" property="setItemKey"/>
        <result column="set_item_value" property="setItemValue"/>
        <result column="set_item_default" property="setItemDefault"/>
        <result column="set_item_sort" property="setItemSort"/>
        <result column="set_item_table" property="setItemTable"/>
        <result column="set_item_table_column" property="setItemTableColumn"/>
        <result column="set_item_protocol_version" property="setItemProtocolVersion"/>
        <result column="set_item_range_min" property="setItemRangeMin"/>
        <result column="set_item_range_max" property="setItemRangeMax"/>
        <result column="set_item_protocol_address" property="setItemProtocolAddress"/>
        <result column="is_sub_item" property="isSubItem"/>
        <result column="remark" property="remark"/>
        <result column="attribute1" property="attribute1"/>
        <result column="attribute2" property="attribute2"/>
        <result column="attribute3" property="attribute3"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="otherLanguage" property="otherLanguage"/>
    </resultMap>


    <select id="selectSetItemConfigPage" resultMap="setItemConfigResultMap">
        select id, parent_id, device_type, device_model, set_category, set_item_big_type, set_item_type, set_item_name,
               set_item_key, set_item_value, set_item_default, set_item_sort, set_item_table, set_item_table_column,
               set_item_protocol_version, set_item_range_min, set_item_range_max, set_item_protocol_address,
               is_sub_item,remark, attribute1, attribute2, attribute3, tenant_id, create_user_account,
               update_user_account, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
        from device_set_item_config where is_deleted = 0
    </select>


    <select id="exportSetItemConfig" resultType="org.skyworth.ess.setItem.excel.SetItemConfigExcel">
        SELECT id, parent_id, device_type, device_model, set_category, set_item_big_type, set_item_type, set_item_name,
               set_item_key, set_item_value, set_item_default, set_item_sort, set_item_table, set_item_table_column,
               set_item_protocol_version, set_item_range_min, set_item_range_max, set_item_protocol_address,
               is_sub_item,remark, attribute1, attribute2, attribute3, tenant_id, create_user_account,
               update_user_account, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
        FROM device_set_item_config ${ew.customSqlSegment}
    </select>

    <select id="getSetupConfig" resultMap="setItemConfigResultMap">
        SELECT dsic.id, dsic.parent_id, dsic.device_type, dsic.device_model, dsic.set_category,
        dsic.set_item_big_type, dsic.set_item_type, dsic.set_item_name, dsic.set_item_key,
        dsic.set_item_value, dsic.set_item_default, dsic.set_item_sort, dsic.set_item_table,
        dsic.set_item_table_column, dsic.set_item_protocol_version, dsic.set_item_range_min,
        dsic.set_item_range_max, dsic.set_item_protocol_address, dsic.is_sub_item, dsic.remark,
        dsic.attribute1, dsic.attribute2, dsic.attribute3, dsic.tenant_id, dsic.create_user_account,
        dsic.update_user_account, dsic.create_user, dsic.create_dept, dsic.create_time,
        dsic.update_user, dsic.update_time, dsic.status, dsic.is_deleted
        <if test="appSetRequestVO.language != null and appSetRequestVO.language != ''">
            ,dsiml.item_language_name AS otherLanguage
        </if>
        FROM device_set_item_config dsic
        <if test="appSetRequestVO.language != null and appSetRequestVO.language != ''">
            LEFT JOIN device_set_item_multi_language dsiml ON dsic.id = dsiml.item_id
            AND dsiml.item_language_type = #{appSetRequestVO.language}
        </if>
        <where>
            dsic.is_deleted = 0
            <if test="appSetRequestVO.deviceModel != null and appSetRequestVO.deviceModel != ''">
                AND dsic.device_model = #{appSetRequestVO.deviceModel}
            </if>
            <if test="appSetRequestVO.deviceModel == null and appSetRequestVO.deviceModel != ''">
                <!-- 设备型号为空时，选择默认型号-->
                AND dsic.attribute3 = 'defaultModel'
            </if>
            <if test="appSetRequestVO.deviceType != null ">
                AND dsic.device_type = #{appSetRequestVO.deviceType}
            </if>
            <if test="appSetRequestVO.setCategory != null">
                AND dsic.set_category = #{appSetRequestVO.setCategory}
            </if>
            <if test="appSetRequestVO.setItemBigType != null">
                AND dsic.set_item_big_type = #{appSetRequestVO.setItemBigType}
            </if>
            <if test="appSetRequestVO.setItemProtocolVersion != null and appSetRequestVO.setItemProtocolVersion != ''">
                AND dsic.set_item_protocol_version = #{appSetRequestVO.setItemProtocolVersion}
            </if>
        </where>
    </select>

</mapper>

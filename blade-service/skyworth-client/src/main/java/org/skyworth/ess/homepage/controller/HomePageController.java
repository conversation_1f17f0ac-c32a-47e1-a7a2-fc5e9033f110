package org.skyworth.ess.homepage.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.homepage.service.IHomePageService;
import org.skyworth.ess.homepage.vo.BatteryHomePageVO;
import org.skyworth.ess.homepage.vo.InverterHomePageVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 首页报表统计
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-16 16:10
 **/
@RestController
@RequestMapping("/homePage")
@Api(value = "首页报表统计", tags = "app接口")
@AllArgsConstructor
public class HomePageController extends BladeController {
	private final IHomePageService homePageService;

	/**
	 * 首页智能能量变换器和负载状态曲线
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	@GetMapping("/inverterAndLoadSummary")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "首页智能能量变换器和负载状态曲线", notes = "首页智能能量变换器和负载状态曲线")
	public R<List<JSONObject>> inverterAndLoadSummaryStateCurve() {
		return R.data(homePageService.inverterAndLoadSummaryStateCurve());
	}

	/**
	 * 首页智能能量变换器数据统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	@GetMapping("/inverterPerformance")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "首页智能能量变换器数据统计", notes = "首页智能能量变换器数据统计")
	public R<InverterHomePageVO> inverterPerformance() {
		return R.data(homePageService.inverterPerformance());
	}

	/**
	 * 首页储能数据统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	@GetMapping("/batteryPerformance")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "首页储能数据统计", notes = "首页储能数据统计")
	public R<BatteryHomePageVO> batteryPerformance() {
		return R.data(homePageService.batteryPerformance());
	}

	/**
	 * 首页储能状态统计
	 *
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/10/13 14:13
	 **/
	@GetMapping("/plantStatusSummary")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "首页储能状态统计", notes = "首页储能状态统计")
	public R<JSONObject> plantStatusSummary() {
		return R.data(homePageService.plantStatusSummary());
	}


}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.DeviceEverydayTotalEntity;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.skyworth.ess.device.excel.DeviceEverydayTotalExcel;
import org.skyworth.ess.device.wrapper.DeviceEverydayTotalWrapper;
import org.skyworth.ess.device.service.IDeviceEverydayTotalService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备/智能能量变换器每日统计 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("skyworth-deviceEverydayTotal/deviceEverydayTotal")
@Api(value = "设备/智能能量变换器每日统计", tags = "设备/智能能量变换器每日统计接口")
public class DeviceEverydayTotalController extends BladeController {

	private final IDeviceEverydayTotalService deviceEverydayTotalService;

	/**
	 * 设备/智能能量变换器每日统计 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceEverydayTotal")
	public R<DeviceEverydayTotalVO> detail(DeviceEverydayTotalEntity deviceEverydayTotal) {
		DeviceEverydayTotalEntity detail = deviceEverydayTotalService.getOne(Condition.getQueryWrapper(deviceEverydayTotal));
		return R.data(DeviceEverydayTotalWrapper.build().entityVO(detail));
	}
	/**
	 * 设备/智能能量变换器每日统计 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceEverydayTotal")
	public R<IPage<DeviceEverydayTotalVO>> list(@ApiIgnore @RequestParam Map<String, Object> deviceEverydayTotal, Query query) {
		IPage<DeviceEverydayTotalEntity> pages = deviceEverydayTotalService.page(Condition.getPage(query), Condition.getQueryWrapper(deviceEverydayTotal, DeviceEverydayTotalEntity.class));
		return R.data(DeviceEverydayTotalWrapper.build().pageVO(pages));
	}

	/**
	 * 设备/智能能量变换器每日统计 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceEverydayTotal")
	public R<IPage<DeviceEverydayTotalVO>> page(DeviceEverydayTotalVO deviceEverydayTotal, Query query) {
		IPage<DeviceEverydayTotalVO> pages = deviceEverydayTotalService.selectDeviceEverydayTotalPage(Condition.getPage(query), deviceEverydayTotal);
		return R.data(pages);
	}

	/**
	 * 设备/智能能量变换器每日统计 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceEverydayTotal")
	public R save(@Valid @RequestBody DeviceEverydayTotalEntity deviceEverydayTotal) {
		return R.status(deviceEverydayTotalService.save(deviceEverydayTotal));
	}

	/**
	 * 设备/智能能量变换器每日统计 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入deviceEverydayTotal")
	public R update(@Valid @RequestBody DeviceEverydayTotalEntity deviceEverydayTotal) {
		return R.status(deviceEverydayTotalService.updateById(deviceEverydayTotal));
	}

	/**
	 * 设备/智能能量变换器每日统计 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceEverydayTotal")
	public R submit(@Valid @RequestBody DeviceEverydayTotalEntity deviceEverydayTotal) {
		return R.status(deviceEverydayTotalService.saveOrUpdate(deviceEverydayTotal));
	}

	/**
	 * 设备/智能能量变换器每日统计 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(deviceEverydayTotalService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-deviceEverydayTotal")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入deviceEverydayTotal")
	public void exportDeviceEverydayTotal(@ApiIgnore @RequestParam Map<String, Object> deviceEverydayTotal, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceEverydayTotalEntity> queryWrapper = Condition.getQueryWrapper(deviceEverydayTotal, DeviceEverydayTotalEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(DeviceEverydayTotal::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(DeviceEverydayTotalEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceEverydayTotalExcel> list = deviceEverydayTotalService.exportDeviceEverydayTotal(queryWrapper);
		ExcelUtil.export(response, "设备/智能能量变换器每日统计数据" + DateUtil.time(), "设备/智能能量变换器每日统计数据表", list, DeviceEverydayTotalExcel.class);
	}

}

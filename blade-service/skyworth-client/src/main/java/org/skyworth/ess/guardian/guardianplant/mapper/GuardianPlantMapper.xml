<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.guardianplant.mapper.GuardianPlantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianPlantResultMap" type="org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="security_guard_status" property="securityGuardStatus"/>
        <result column="gate_position_status" property="gatePositionStatus"/>
        <result column="timing_type_power" property="timingTypePower"/>
        <result column="timing_type_power_custom" property="timingTypePowerCustom"/>
        <result column="timing_type_gate" property="timingTypeGate"/>
        <result column="timing_type_gate_custom" property="timingTypeGateCustom"/>
        <result column="heart_beat_time" property="heartBeatTime"/>
        <result column="lock_set" property="lockSet"/>
        <result column="reclose_set" property="recloseSet"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="partition_date" property="partitionDate"/>
        <result column="device_type" property="deviceType"/>
    </resultMap>

    <resultMap id="GuardianPlantVOMap" type="org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="security_guard_status" property="securityGuardStatus"/>
        <result column="plant_name" property="plantName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="create_user" property="userId"/>
        <result column="operation_user_id" property="agentUserId"/>
        <result column="operation_company_id" property="agentCompanyId"/>
    </resultMap>


    <select id="selectGuardianPlantPage" resultMap="GuardianPlantVOMap">
        SELECT
            gp.id,
            gp.plant_id,
            gp.security_guard_serial_number,
            gp.security_guard_status,
            p.plant_name,
            p.detail_address,
            p.country_code,
            p.province_code,
            p.city_code,
            p.county_code,
            p.detail_address,
            p.create_user,
            p.operation_user_id,
            p.operation_company_id
        FROM guardian_plant gp
            left join plant p on gp.plant_id = p.id and p.is_deleted = 0
        WHERE
        gp.is_deleted = 0
        <if test="params.countryCode!=null and params.countryCode!=''">
            and p.country_code = #{params.countryCode}
        </if>
        <if test="params.provinceCode!=null and params.provinceCode!=''">
            and p.province_code = #{params.provinceCode}
        </if>
        <if test="params.securityGuardStatus!=null and params.securityGuardStatus!=''">
            and gp.security_guard_status = #{params.securityGuardStatus}
        </if>
        <if test="params.userIds!=null and params.userIds.size() > 0 ">
            and p.create_user in
            <foreach item="item" index="index" collection="params.userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.agentUserIds!=null and params.agentUserIds.size() > 0 ">
            and p.operation_user_id in
            <foreach item="item" index="index" collection="params.agentUserIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.agentCompanyIds!=null and params.agentCompanyIds.size() > 0 ">
            and p.operation_company_id in
            <foreach item="item" index="index" collection="params.agentCompanyIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY gp.update_time DESC, gp.id DESC
    </select>

    <select id="detail" resultMap="GuardianPlantVOMap">
        SELECT
            gp.id,
            gp.plant_id,
            gp.security_guard_serial_number,
            gp.security_guard_status,
            p.plant_name,
            p.detail_address,
            p.country_code,
            p.province_code,
            p.city_code,
            p.county_code,
            p.detail_address,
            p.create_user,
            p.operation_user_id,
            p.operation_company_id
        FROM guardian_plant gp
            left join plant p on gp.plant_id = p.id and p.is_deleted = 0
        WHERE
        gp.is_deleted = 0
        <if test="plantId!=null and plantId!=''">
            and gp.plant_id = #{plantId}
        </if>
        <if test="securityGuardSerialNumber!=null and securityGuardSerialNumber!=''">
            and gp.security_guard_serial_number = #{securityGuardSerialNumber}
        </if>
    </select>

    <select id="exportGuardianPlant" resultType="org.skyworth.ess.guardian.guardianplant.excel.GuardianPlantExcel">
        SELECT * FROM guardian_plant ${ew.customSqlSegment}
    </select>

    <select id="queryByGuardianSerialNumberList" resultMap="GuardianPlantResultMap">
        select s.plant_id,s.security_guard_serial_number,partition_date  from guardian_plant s where s.is_deleted = 0 and s.security_guard_serial_number in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryGuardianSerialNumberList" resultMap="GuardianPlantResultMap">
        select s.plant_id,s.security_guard_serial_number  from guardian_plant s where s.is_deleted = 0 and s.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="batchDeleteLogicByPlantId">
        update guardian_plant set is_deleted=1,update_time=now(), update_user_account=#{account} where plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateDataByCondition">
        update guardian_plant set
        <if test="params.isDeleted!=null ">
            is_deleted = #{params.isDeleted},
        </if>
        <if test="params.updateUser!=null ">
            update_user = #{params.updateUser},
        </if>
        <if test="params.updateUserAccount!=null ">
            update_user_account = #{params.updateUserAccount},
        </if>
        update_time=now()
        where is_deleted = 0
        <if test="params.createUser!=null ">
            and create_user = #{params.createUser}
        </if>
    </update>

    <select id="getAllGuardianPowerSetup" resultType="java.util.Map">
        SELECT
            gp.plant_id as plantId,
            gp.security_guard_serial_number as securityGuardSerialNumber,
            gp.security_guard_status as securityGuardStatus,
            gp.gate_position_status as gatePositionStatus,
            gp.timing_type_power as timingTypePower,
            gp.timing_type_power_custom as timingTypePowerCustom,
            gp.timing_type_gate as timingTypeGate,
            gp.timing_type_gate_custom as timingTypeGateCustom,
            gp.heart_beat_time as heartBeatTime,
            gp.lock_set as lockSet,
            gp.reclose_set as recloseSet,
            JSON_ARRAYAGG(JSON_OBJECT(
                'startTime', gtp.start_time,
                'endTime', gtp.end_time,
                'minPower', gtp.min_power,
                'maxPower', gtp.max_power,
                'setSort',gtp.set_sort
                )) AS timedPowerData
        FROM
            guardian_plant gp
                LEFT JOIN
            guardian_timed_power gtp ON gp.plant_id = gtp.plant_id and gp.security_guard_serial_number = gtp.security_guard_serial_number
        where gp.is_deleted = 0 and gtp.is_deleted = 0
        and gp.security_guard_serial_number = #{serialNumber} and gp.plant_id = #{plantId}
        GROUP BY
            gp.id, gp.plant_id, gp.security_guard_serial_number, gp.security_guard_status, gp.gate_position_status,
            gp.timing_type_power, gp.timing_type_power_custom, gp.timing_type_gate, gp.timing_type_gate_custom, gp.heart_beat_time
    </select>

    <select id="getAllGuardianSwitchGateSetup" resultType="java.util.Map">
        SELECT
            gp.plant_id as plantId,
            gp.security_guard_serial_number as securityGuardSerialNumber,
            gp.security_guard_status as securityGuardStatus,
            gp.gate_position_status as gatePositionStatus,
            gp.timing_type_power as timingTypePower,
            gp.timing_type_power_custom as timingTypePowerCustom,
            gp.timing_type_gate as timingTypeGate,
            gp.timing_type_gate_custom as timingTypeGateCustom,
            gp.heart_beat_time as heartBeatTime,
            gp.lock_set as lockSet,
            gp.reclose_set as recloseSet,
            JSON_ARRAYAGG(JSON_OBJECT(
                'closingTime', gtsg.closing_time,
                'openingTime', gtsg.opening_time,
                'setSort',gtsg.set_sort
                )) AS timedSwitchGateData
        FROM
            guardian_plant gp
                LEFT JOIN
            guardian_timed_switch_gate gtsg ON gp.plant_id = gtsg.plant_id and gp.security_guard_serial_number = gtsg.security_guard_serial_number
        where gp.is_deleted = 0 and gtsg.is_deleted = 0
          and gp.security_guard_serial_number = #{serialNumber} and gp.plant_id = #{plantId}
        GROUP BY
            gp.id, gp.plant_id, gp.security_guard_serial_number, gp.security_guard_status, gp.gate_position_status,
            gp.timing_type_power, gp.timing_type_power_custom, gp.timing_type_gate, gp.timing_type_gate_custom, gp.heart_beat_time
    </select>

    <select id="getAllGuardianGatePositionSetup" resultType="java.util.Map">
        SELECT
           plant_id as plantId,
           security_guard_serial_number as securityGuardSerialNumber,
           security_guard_status as securityGuardStatus,
           gate_position_status as gatePositionStatus
        from guardian_plant gp
        where gp.is_deleted = 0
          and gp.security_guard_serial_number = #{serialNumber}
          and gp.plant_id = #{plantId}
    </select>
    <select id="getPlantInfo" resultType="java.util.Map">
        SELECT p.operation_company_id as operationCompanyId ,tzd.time_zone as timeZone ,p.create_user as createUser,wsp.plant_id as plantId  FROM wifi_stick_plant wsp
        INNER JOIN guardian_plant gp ON gp.plant_id = wsp.plant_id
        inner join plant p on p.id =wsp .plant_id
        inner join time_zone_device tzd  on tzd .device_serial_number =wsp .device_serial_number
        where wsp.is_deleted =0 and wsp.plant_id in
        <foreach collection="plantIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>
</mapper>

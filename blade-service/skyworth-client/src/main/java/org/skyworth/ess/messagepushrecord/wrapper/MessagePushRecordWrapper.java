/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity;
import org.skyworth.ess.messagepushrecord.vo.MessagePushRecordVO;
import java.util.Objects;

/**
 * 消息推送日志表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public class MessagePushRecordWrapper extends BaseEntityWrapper<MessagePushRecordEntity, MessagePushRecordVO>  {

	public static MessagePushRecordWrapper build() {
		return new MessagePushRecordWrapper();
 	}

	@Override
	public MessagePushRecordVO entityVO(MessagePushRecordEntity messagePushRecord) {
		MessagePushRecordVO messagePushRecordVO = Objects.requireNonNull(BeanUtil.copy(messagePushRecord, MessagePushRecordVO.class));

		//User createUser = UserCache.getUser(messagePushRecord.getCreateUser());
		//User updateUser = UserCache.getUser(messagePushRecord.getUpdateUser());
		//messagePushRecordVO.setCreateUserName(createUser.getName());
		//messagePushRecordVO.setUpdateUserName(updateUser.getName());

		return messagePushRecordVO;
	}


}

package org.skyworth.ess.homepage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页储能汇总数据
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-11-16 16:18
 **/
@Data
@ApiModel(value = "首页储能汇总数据", description = "首页储能汇总数据")
public class BatteryHomePageVO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "当日充电总能量")
	private String chargingEnergyToday;
	@ApiModelProperty(value = "当日放电总能量")
	private String disChargingEnergyToday;
	@ApiModelProperty(value = "储能装机总电量")
	private String totalInstalledPower;
	@ApiModelProperty(value = "累计充电能量")
	private String accumulatedChargingEnergy;
	@ApiModelProperty(value = "累计放电能量")
	private String accumulatedDisChargingEnergy;
}

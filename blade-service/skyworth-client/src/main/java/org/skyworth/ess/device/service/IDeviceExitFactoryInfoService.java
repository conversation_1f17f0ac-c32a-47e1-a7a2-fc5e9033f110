/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.device.excel.DeviceExitFactoryInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.event.excel.ImportantEventExcel;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备/智能能量变换器出厂信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface IDeviceExitFactoryInfoService extends BaseService<DeviceExitFactoryInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param DeviceExitFactoryInfo
	 * @return
	 */
	IPage<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage<DeviceExitFactoryInfoVO> page, DeviceExitFactoryInfoVO DeviceExitFactoryInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<DeviceExitFactoryInfoExcel> exportDeviceExitFactoryInfo(Wrapper<DeviceExitFactoryInfoEntity> queryWrapper);

	List<DeviceExitFactoryInfoVO> queryDeviceExitFactoryByPlant(Long plantId);
	R deleteLogicDeviceExitFactory(List<Long> ids);

	String importAddExcel(List<DeviceExitFactoryInfoExcel> data,boolean isCovered);

	String importModifyExcel(List<DeviceExitFactoryInfoExcel> data,boolean isCovered);
	int batchUpdate(List<String> deviceSerialNumberList);

	BigDecimal queryRatedTotalPowerOfInverter();
	int updateNewQualityQuaranteeYear(DeviceExitFactoryInfoEntity entity);

	boolean updateDeadlineInfo(DeviceExitFactoryInfoEntity entity);

	DeviceExitFactoryInfoEntity getModelAndProtocolBySn(DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity);

    List<DeviceExitFactoryInfoEntity> getListByDeviceSerialNumberCollect(List<String> deviceExitFactoryInfoEntities);
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.vo.Device24VO;
import org.skyworth.ess.device.excel.Device24Excel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Device24 服务类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface IDevice24Service extends BaseService<Device24Entity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Device24
	 * @return
	 */
	IPage<Device24VO> selectDevice24Page(IPage<Device24VO> page, Device24VO Device24);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<Device24Excel> exportDevice24(Wrapper<Device24Entity> queryWrapper);

	R<String> issueAdvancedSetup(AppAdvancedSetup deviceAdvancedSetup);

	HashMap<String, Object> getAllAdvancedSetup(Long plantId, String deviceSerialNumber);

	int updateSetup(Map<String, Object> device24, Long plantId, String deviceSerialNumber);

	List<Device24Entity> getDevice24Info(List<Device24Entity> list);

	void updateInverterControl(Device24Entity device24Entity);

	Map<String, Object> getInverterControl(Long plantId, String deviceSerialNumber);

	Map<String, Object> getInverterControlIsDelete(Long plantId, String deviceSerialNumber);

	Map<String, Object> getInverterModeTimeBase(Long plantId, String deviceSerialNumber);

	int deleteByPlantId(Long plantId, String deviceSerialNumber);
}

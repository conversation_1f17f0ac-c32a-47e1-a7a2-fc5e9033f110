package org.skyworth.ess.battery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.battery.entity.BatteryDeviceInstallVO;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryCapacityVo;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;

import java.util.List;
import java.util.Map;

/**
 * 储能映射设备表(BatteryMapDeviceT)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-13 17:22:49
 */
public interface BatteryMapDeviceMapper extends BaseMapper<BatteryMapDeviceEntity> {

	/**
	 * 通过ID查询单条数据
	 *
	 * @param batteryMapDeviceId 主键
	 * @return 实例对象
	 */
	BatteryMapDeviceVO queryById(Integer batteryMapDeviceId);

	/**
	 * 分页查询
	 *
	 * @param page               分页
	 * @param queryPageCondition 入参
	 * @return List<BatteryPageResult>
	 * <AUTHOR>
	 * @since 2023/9/15 9:32
	 *
	 * if(bcs.battery_status = 4,// 状态为告警
	 *                 if(bmd.battery_energy_storage_number = 1, //储能1
	 *                 if(bcs.exist_user_type_alarm = 1,// 判断储能1是否是用户告警
	 *                 4,// 是用户告警则返回告警
	 *                 if(bcs.battery_power > 0,
	 *                 2,
	 *                 1)),//非用户告警则根据battery_power大于0返回2，否则返回1
	 *                 if(bcs.exist_user_type_alarm2 = 1,// 储能2 判断储能1是否是用户告警
	 *                 4,// 是用户告警则返回告警
	 *                 if(bcs.battery_power2 > 0,
	 *                 2,
	 *                 1))//储能非用户告警则根据battery_power2大于0返回2，否则返回1
	 *                 ) ,
	 *                 bcs.battery_status) as status,
	 **/
	List<BatteryPageResultVO> queryPage(@Param("queryPageCondition") QueryCondition queryPageCondition, @Param("PageVO") IPage page,
										@Param("userType") String userType);

	/**
	 * 查询储能数量，额定能量总和
	 *
	 * @param plantIds 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/15 14:59
	 **/
	Map<String, Object> listSummary(@Param("plantIds") List<Long> plantIds, @Param("deleteFlag") String deleteFlag);

	/**
	 * 安装信息
	 *
	 * @param queryCondition 入参
	 * @return BatteryDeviceInstallVO
	 * <AUTHOR>
	 * @since 2023/10/7 18:21
	 **/
	List<BatteryDeviceInstallVO> installation(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryDeviceInstallVO> installationIsDelete(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 批量删除站点和储能关系
	 *
	 * @param plantIdList
	 * @return
	 */
	int batchDeleteLogicByPlantId(@Param("list") List<Long> plantIdList, @Param("updateUserAccount") String updateUserAccount);

	List<BatteryMapDeviceEntity> queryOwnerData(Long createUser);

	int updateDataByCondition(@Param("params") BatteryMapDeviceEntity updateOwner);

	List<BatteryMapDeviceEntity> queryListByPlantId(@Param("list") List<Long> list);

	List<BatteryMapDeviceVO> queryBatteryDeviceInfo(@Param("params") BatteryMapDeviceEntity batteryMapDeviceEntity);

	List<BatteryCapacityVo> queryBatteryCapacity(List<String> deviceSn);

	List<BatteryMapDeviceEntity> queryListByPlantIdAndSn(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber);
	List<BatteryMapDeviceEntity> queryListByPlantIdAndSnAndBattery(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber,@Param("batteryEnergyStorageNumber") Integer batteryEnergyStorageNumber);

	void batchDeleteLogicByPlantIdAndSn(@Param("plantId") Long plantId,@Param("deviceSerialNumber")  String deviceSerialNumber,@Param("updateUserAccount")  String account);

	List<BatteryCapacityVo> queryBatteryCapacityByBatterySns(List<String> batterySns);
}


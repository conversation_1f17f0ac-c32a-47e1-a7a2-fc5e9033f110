package org.skyworth.ess.lazzen.analysisdata.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.common.MqttTopicProperty;
import org.skyworth.ess.consumer.MqttConsumerService;
import org.skyworth.ess.kafka.producer.KafkaSendMsgService;
import org.skyworth.ess.vo.LazzenConsumerVO;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/testMqtt")
public class LazzenMqttCommonConsumer
		implements MqttConsumerService
		 {
			 @Autowired
	private KafkaSendMsgService<LazzenConsumerVO> kafkaSendMsgService;
	@MqttTopicProperty(topicName = CommonConstant.MQTT_SHARE_KEY + CommonConstant.MQTT_LAZZEN_KEY + CommonConstant.LAZZEN_MQTT_COMMON_CONSUMER_TOPIC, qos=2) //
	public boolean consumer(String topiContent) {
		log.info("mqtt consumer : {} ",topiContent);
//		LazzenConsumerVO lazzenConsumerVO = JSON.parseObject(test, LazzenConsumerVO.class);
		LazzenConsumerVO LazzenConsumerVO1 = JSON.parseObject(topiContent, new TypeReference<LazzenConsumerVO>(){});
		if("fd".equals(LazzenConsumerVO1.getParams().getDataHead()) && "01".equals(LazzenConsumerVO1.getParams().getMegTag())) {
			log.info("mqtt consumer 产品信息:  ");
		}
		LazzenKafkaTopic lazzenKafkaTopic = new LazzenKafkaTopic();
		lazzenKafkaTopic.setTopicName(CommonConstant.LAZZEN_KAFKA_COMMON_CONSUMER_TOPIC);
		lazzenKafkaTopic.setTopicData(LazzenConsumerVO1);
		lazzenKafkaTopic.setTopicTime(LocalDateTime.now());
		lazzenKafkaTopic.setBusinessKey("lazzenBusinessKey");
		kafkaSendMsgService.send(lazzenKafkaTopic,new HashMap<>());
		return true;
	}

	@GetMapping("/detail")
	public void detail(String str) {
		// 产品信息
//		String test = "{'params': {'DataHead': 'fd','DataTag': '01','DataLenth': '00EE','MacAddr': '98D863B915A4000000','GwType': '03','PortAddr': '01','MegTag': '01','PackCount': '0001','PackId': '0001', 'DataValue': '644235454C00000000000000500050000100E6000002002A0007000600500050000100E6003200020001000400020001006496002023100839393338333838330008000000000000000000004E444235452D312D303642303030304D47443032323331313135413033000000654235454C0000000000000050005000010190000002002A000700060050005000010190003200020001000300020001006596002023100939393338323632310008000000000000000000004E444235452D312D303642303030304D47443032323331313135413033000000','DataCRC': '211B','DataRead': '0'},'method': 'thing.event.property.post'}";
		String test = "{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'0082','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'01','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E6000002002A000700060050003F000100E6003200020001000400020001006496002024110237393231383030373838000000000005000000014E444235452D312D303742303230304D47443031323430373038413037303031','DataCRC':'13AD','DataRead':'0'},'method':'thing.event.property.post'}";
		// 网关心跳
//		String test = "{'params':{'DataHead':'fe','Saddr':'0001','MacAddr':'98D863B91366000000','PortAddr':'01','DataRead':'0'},'method':'thing.event.property.post'}";
		// 网关应答
//		String test = "{'params': {'DataHead': 'fc','CmdId': '000000001','CmdAck': '2005','DataRead': '0'},'method': 'thing.event.property.post'}";
		// 运行状态
//		String test = "{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'004A','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'03','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E6000002500E000D00000000000000A0000000000000000000000000000300020000','DataCRC':'084A','DataRead':'0'},'method':'thing.event.property.post'}";
		// 测量数据
//		String test = "{'params':{'DataHead':'fd','DataTag':'01','DataLenth':'016E','MacAddr':'98D863B91366000000','GwType':'03','PortAddr':'01','MegTag':'04','PackCount':'0001','PackId':'0001','DataValue':'644235454C0000000000000050003F000100E600000260A0000000020000000000000001000000000000000000000000000000000000000000000000000000000000000009130941092B0000000000000000000001F401F301F400020000000000000000000000000000000000000000000000000000000000000000001D0027000200160000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000110000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000','DataCRC':'0C4A','DataRead':'0'},'method':'thing.event.property.post'}";
		consumer(test);
	}
}

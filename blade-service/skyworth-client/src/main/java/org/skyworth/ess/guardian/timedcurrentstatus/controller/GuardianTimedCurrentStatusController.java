package org.skyworth.ess.guardian.timedcurrentstatus.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity;
import org.skyworth.ess.guardian.timedcurrentstatus.vo.GuardianTimedCurrentStatusVO;
import org.skyworth.ess.guardian.timedcurrentstatus.excel.GuardianTimedCurrentStatusExcel;
import org.skyworth.ess.guardian.timedcurrentstatus.wrapper.GuardianTimedCurrentStatusWrapper;
import org.skyworth.ess.guardian.timedcurrentstatus.service.IGuardianTimedCurrentStatusService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士-设备上报定时设置当前状态信息 控制器
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianTimedCurrentStatus")
@Api(value = "安全卫士-设备上报定时设置当前状态信息", tags = "安全卫士-设备上报定时设置当前状态信息接口")
public class GuardianTimedCurrentStatusController extends BladeController {

	private final IGuardianTimedCurrentStatusService GuardianTimedCurrentStatusService;

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianTimedCurrentStatus")
	public R<GuardianTimedCurrentStatusVO> detail(GuardianTimedCurrentStatusEntity GuardianTimedCurrentStatus) {
		GuardianTimedCurrentStatusEntity detail = GuardianTimedCurrentStatusService.getOne(Condition.getQueryWrapper(GuardianTimedCurrentStatus));
		return R.data(GuardianTimedCurrentStatusWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士-设备上报定时设置当前状态信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianTimedCurrentStatus")
	public R<IPage<GuardianTimedCurrentStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianTimedCurrentStatus, Query query) {
		IPage<GuardianTimedCurrentStatusEntity> pages = GuardianTimedCurrentStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianTimedCurrentStatus, GuardianTimedCurrentStatusEntity.class));
		return R.data(GuardianTimedCurrentStatusWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianTimedCurrentStatus")
	public R<IPage<GuardianTimedCurrentStatusVO>> page(GuardianTimedCurrentStatusVO GuardianTimedCurrentStatus, Query query) {
		IPage<GuardianTimedCurrentStatusVO> pages = GuardianTimedCurrentStatusService.selectGuardianTimedCurrentStatusPage(Condition.getPage(query), GuardianTimedCurrentStatus);
		return R.data(pages);
	}

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianTimedCurrentStatus")
	public R save(@Valid @RequestBody GuardianTimedCurrentStatusEntity GuardianTimedCurrentStatus) {
		return R.status(GuardianTimedCurrentStatusService.save(GuardianTimedCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianTimedCurrentStatus")
	public R update(@Valid @RequestBody GuardianTimedCurrentStatusEntity GuardianTimedCurrentStatus) {
		return R.status(GuardianTimedCurrentStatusService.updateById(GuardianTimedCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianTimedCurrentStatus")
	public R submit(@Valid @RequestBody GuardianTimedCurrentStatusEntity GuardianTimedCurrentStatus) {
		return R.status(GuardianTimedCurrentStatusService.saveOrUpdate(GuardianTimedCurrentStatus));
	}

	/**
	 * 安全卫士-设备上报定时设置当前状态信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianTimedCurrentStatusService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianTimedCurrentStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianTimedCurrentStatus")
	public void exportGuardianTimedCurrentStatus(@ApiIgnore @RequestParam Map<String, Object> GuardianTimedCurrentStatus, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianTimedCurrentStatusEntity> queryWrapper = Condition.getQueryWrapper(GuardianTimedCurrentStatus, GuardianTimedCurrentStatusEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianTimedCurrentStatus::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianTimedCurrentStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianTimedCurrentStatusExcel> list = GuardianTimedCurrentStatusService.exportGuardianTimedCurrentStatus(queryWrapper);
		ExcelUtil.export(response, "安全卫士-设备上报定时设置当前状态信息数据" + DateUtil.time(), "安全卫士-设备上报定时设置当前状态信息数据表", list, GuardianTimedCurrentStatusExcel.class);
	}

}

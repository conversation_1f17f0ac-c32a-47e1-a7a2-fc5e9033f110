/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianlog.excel;


import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 安全卫士日志 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GuardianLogExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 数据上报时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据上报时间")
	private Date deviceDateTime;
	/**
	 * A相电压，单位V
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相电压，单位V")
	private BigDecimal aPhaseVoltage;
	/**
	 * A相电流，单位A
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相电流，单位A")
	private BigDecimal aPhaseCurrent;
	/**
	 * A相温度，单位°C
	 */
	@ColumnWidth(20)
	@ExcelProperty("A相温度，单位°C")
	private BigDecimal aPhaseTemperature;
	/**
	 * B相电压，单位V
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相电压，单位V")
	private BigDecimal bPhaseVoltage;
	/**
	 * B相电流，单位A
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相电流，单位A")
	private BigDecimal bPhaseCurrent;
	/**
	 * B相温度，单位°C
	 */
	@ColumnWidth(20)
	@ExcelProperty("B相温度，单位°C")
	private BigDecimal bPhaseTemperature;
	/**
	 * C相电压，单位V
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相电压，单位V")
	private BigDecimal cPhaseVoltage;
	/**
	 * C相电流，单位A
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相电流，单位A")
	private BigDecimal cPhaseCurrent;
	/**
	 * C相温度，单位°C
	 */
	@ColumnWidth(20)
	@ExcelProperty("C相温度，单位°C")
	private BigDecimal cPhaseTemperature;
	/**
	 * N零线温度，单位°C
	 */
	@ColumnWidth(20)
	@ExcelProperty("N零线温度，单位°C")
	private BigDecimal nNeutralLineTemperature;
	/**
	 * 有功功率，单位W
	 */
	@ColumnWidth(20)
	@ExcelProperty("有功功率，单位W")
	private BigDecimal activePower;
	/**
	 * 无功功率，单位W
	 */
	@ColumnWidth(20)
	@ExcelProperty("无功功率，单位W")
	private BigDecimal reactivePower;
	/**
	 * 有功电量，单位kWh
	 */
	@ColumnWidth(20)
	@ExcelProperty("有功电量，单位kWh")
	private BigDecimal activePowerConsumption;
	/**
	 * 无功电量，单位kWh
	 */
	@ColumnWidth(20)
	@ExcelProperty("无功电量，单位kWh")
	private BigDecimal reactivePowerConsumption;
	/**
	 * 功率因素，单位L
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率因素，单位L")
	private BigDecimal powerFactor;
	/**
	 * 电网频率，单位Hz
	 */
	@ColumnWidth(20)
	@ExcelProperty("电网频率，单位Hz")
	private BigDecimal gridFrequency;
	/**
	 * 漏电电流，单位ma
	 */
	@ColumnWidth(20)
	@ExcelProperty("漏电电流，单位ma")
	private BigDecimal leakageCurrent;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 
	 */
	@ColumnWidth(20)
	@ExcelProperty("")
	private String alarmStatus;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

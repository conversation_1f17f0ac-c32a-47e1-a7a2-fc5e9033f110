package org.skyworth.ess.constant;

import lombok.Getter;
import org.skyworth.ess.app.service.setup.*;

import java.util.Objects;

/**
 * <AUTHOR> - x<PERSON><PERSON><PERSON>
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/12/1 11:26:09
 */
@Getter
public enum AppSetupTypeEnum {

	INVERTER_ADVANCED_SETUP(0,0, "advancedSetup", new AppSetupTemplateAdvanced()),
	INVERTER_QUICK_SETUP(0,1, "quickSetup", new AppSetupTemplateQuickStartUp()),
	INVERTER_MODE_SETUP(0,2, "inverterModeSetup", new AppSetupTemplateTimeBaseControl()),
	INVERTER_CONTROL_SETUP(0,4, "inverterControlSetup", new AppSetupTemplateInverterModeControl()),
	GUARDIAN_ALARM_SETUP(4,5, "guardianAlarmSetup", new AppSetupTemplateGuardianAlarm()),
	G<PERSON>ARDI<PERSON>_POWER_SETUP(4,6, "guardianPowerSetup", new AppSetupTemplateGuardianTimePower()),
	GUARDIAN_SWITCH_GATE_SETUP(4,7, "guardianSwitchGateSetup", new AppSetupTemplateGuardianTimeSwitchGate()),
	GUARDIAN_GATE_POSITION_SETUP(4,8, "guardianGatePositionSetup", new AppSetupTemplateGuardianGatePosition()),
	LAZZEN_GUARDIAN_GATE_POSITION_SETUP(4,9, "lzGuardianGatePositionSetup", new AppSetupTemplateLazzenGuardianGatePosition());

	private final Integer deviceType;
	private final Integer numType;
	private final String type;
	private final AppSetupTemplate appSetupTemplate;


	private AppSetupTypeEnum(Integer deviceType,Integer numType, String type, AppSetupTemplate appSetupTemplate) {
		this.deviceType = deviceType;
		this.numType = numType;
		this.type = type;
		this.appSetupTemplate = appSetupTemplate;
	}


	public static AppSetupTemplate getAppSetupTemplateByType(String type) {
		AppSetupTemplate result = null;
		for (AppSetupTypeEnum s : values()) {
			if (Objects.equals(s.getType(), type)) {
				result = s.getAppSetupTemplate();
				break;
			}
		}
		return result;
	}

	public static AppSetupTemplate getAppSetupTemplateByNumType(Integer numType) {
		AppSetupTemplate result = null;
		for (AppSetupTypeEnum s : values()) {
			if (Objects.equals(s.getNumType(), numType)) {
				result = s.getAppSetupTemplate();
				break;
			}
		}
		return result;
	}
}

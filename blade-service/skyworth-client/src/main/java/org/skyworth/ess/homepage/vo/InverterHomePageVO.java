package org.skyworth.ess.homepage.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 首页智能能量变换器汇总数据
 * @description:
 * @author: SDT50545
 * @since: 2023-11-16 16:18
 **/
@Data
@ApiModel(value = "首页智能能量变换器汇总数据", description = "首页智能能量变换器汇总数据")
public class InverterHomePageVO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "当前总功率")
	private String currentTotalPower;
	@ApiModelProperty(value = "当日发电量")
	private String dailyPowerGeneration;
	@ApiModelProperty(value = "当日负载能量")
	private String dailyLoadEnergy;
	@ApiModelProperty(value = "累计发电量")
	private String accumulatedPowerGeneration;
	@ApiModelProperty(value = "累计负载能量")
	private String accumulatedLoadEnergy;
	@ApiModelProperty(value = "额定总功率")
	private String ratedTotalPower;
}

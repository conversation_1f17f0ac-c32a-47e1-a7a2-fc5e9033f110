package org.skyworth.ess.device.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.skyworth.ess.app.service.IAppSetupService;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.app.vo.AppSetRequestVO;
import org.skyworth.ess.aspect.AdvancedSettingsOperationLog;
import org.skyworth.ess.constant.AppSetupTypeEnum;
import org.skyworth.ess.constant.SourceEnum;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.annotation.ApiLog;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/10 09:47:18
 */
@RestController
@RequestMapping("/device/setup")
@Api(value = "设备设置", tags = "设备设置")
public class DeviceSetupController extends BladeController {

	@Resource
	private IAppSetupService appSetupService;

	@GetMapping("/commonSetup/queryAll")
	@ApiOperation(value = "查询高级设置", notes = "查询设置")
	@ApiLog("web查询高级设置")
	@PreAuth("hasPermission('client:inverterDevice:querySetup')")
	public R<JSONObject> getAdvancedSetup(@ApiIgnore @RequestParam Map<String, String> setRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(setRequestVO.get("plantId")));
		requestVO.setDeviceSerialNumber(setRequestVO.get("deviceSerialNumber"));
		//默认查询智能能量变换器类型
		requestVO.setDeviceType(setRequestVO.get("deviceType") == null ? 0 : Integer.parseInt(setRequestVO.get("deviceType")));
		//0：高级设置
		requestVO.setSetCategory(0);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/inverter/mode")
	@ApiOperation(value = "查询分时设置", notes = "查询分时设置")
	@ApiLog("web查询分时设置")
	public R<JSONObject> queryInverterMode(@ApiIgnore @RequestParam Map<String, String> setRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(setRequestVO.get("plantId")));
		requestVO.setDeviceSerialNumber(setRequestVO.get("deviceSerialNumber"));
		//默认查询智能能量变换器类型
		requestVO.setDeviceType(setRequestVO.get("deviceType") == null ? 0 : Integer.parseInt(setRequestVO.get("deviceType")));
		//2：分时设置
		requestVO.setSetCategory(2);
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@PostMapping("/commonSetup/issue")
	@ApiOperation(value = "下发高级设置", notes = "下发设置")
	@ApiLog("web下发高级设置")
	@AdvancedSettingsOperationLog(type = SourceEnum.WEB)
	@PreAuth("hasPermission('client:inverterDevice:issueSetup')")
	public R issueAdvancedSetup(@RequestBody @Validated AppAdvancedSetup deviceAdvancedSetup) {
		return appSetupService.issueSetup(deviceAdvancedSetup);
	}


	@GetMapping("/guardian/alarmSetup/queryAll")
	@ApiOperation(value = "查询安全卫士告警设置", notes = "查询安全卫士告警设置")
	@ApiLog("app查询安全卫士告警设置")
	public R<JSONObject> getGuardianAlarmSet(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_ALARM_SETUP.getDeviceType());
		//告警设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_ALARM_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/guardian/powerSetup/queryAll")
	@ApiOperation(value = "查询安全卫士功率设置", notes = "查询安全卫士功率设置")
	@ApiLog("app查询安全卫士功率设置")
	public R<JSONObject> getGuardianPowerSet(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_POWER_SETUP.getDeviceType());
		//功率设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_POWER_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/guardian/switchGateSetup/queryAll")
	@ApiOperation(value = "查询安全卫士定时开关闸设置", notes = "查询安全卫士定时开关闸设置")
	@ApiLog("app查询安全卫士定时开关闸设置")
	public R<JSONObject> getGuardianSwitchGateSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_SWITCH_GATE_SETUP.getDeviceType());
		//开关闸设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_SWITCH_GATE_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/guardian/GatePositionSetup/queryAll")
	@ApiOperation(value = "查询安全卫士闸位状态设置", notes = "查询安全卫士闸位状态设置")
	@ApiLog("app查询安全卫士闸位状态设置")
	public R<JSONObject> getGuardianGatePositionSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.DEVICE_SERIAL_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.GUARDIAN_GATE_POSITION_SETUP.getDeviceType());
		//闸位状态设置
		requestVO.setSetCategory(AppSetupTypeEnum.GUARDIAN_GATE_POSITION_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}

	@GetMapping("/lz/GatePositionSetup/queryAll")
	@ApiOperation(value = "查询安全卫士闸位状态设置", notes = "查询安全卫士闸位状态设置")
	@ApiLog("app查询安全卫士闸位状态设置")
	public R<JSONObject> getLzGuardianGatePositionSetup(@ApiIgnore @RequestParam Map<String, String> appSetRequestVO) {
		AppSetRequestVO requestVO = new AppSetRequestVO();
		requestVO.setPlantId(Long.parseLong(appSetRequestVO.get(EntityFieldConstant.PLANT_ID)));
		requestVO.setDeviceSerialNumber(appSetRequestVO.get(EntityFieldConstant.GATEWAY_UNIQUE_NUMBER));
		//查询安全卫士类型
		requestVO.setDeviceType(AppSetupTypeEnum.LAZZEN_GUARDIAN_GATE_POSITION_SETUP.getDeviceType());
		//闸位状态设置
		requestVO.setSetCategory(AppSetupTypeEnum.LAZZEN_GUARDIAN_GATE_POSITION_SETUP.getNumType());
		String currentLanguage = CommonUtil.getCurrentLanguage();
		requestVO.setLanguage(currentLanguage);
		return R.data(appSetupService.getSetupItem(requestVO));
	}
}

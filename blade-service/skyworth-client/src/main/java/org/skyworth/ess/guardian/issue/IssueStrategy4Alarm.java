package org.skyworth.ess.guardian.issue;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.service.IGuardianThresholdCurrentStatusService;
import org.skyworth.ess.util.HumpConvert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DatabaseFieldConstant;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.resource.entity.Oss;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class IssueStrategy4Alarm extends GuardianIssueStrategy {
	public IGuardianAlarmThresholdService guardianAlarmThresholdService = SpringUtil.getBean(IGuardianAlarmThresholdService.class);

	private final DorisSinkService dorisSinkService = SpringUtil.getBean(DorisSinkService.class);

	private final IGuardianThresholdCurrentStatusService currentStatusService = SpringUtil.getBean(IGuardianThresholdCurrentStatusService.class);

	Integer defaultValue = 1;
	Integer dataLength = 4;
	List<String> allList = List.of("leakageAlarmThreshold", "overcurrentAlarmThreshold", "overvoltageAlarmThreshold", "undervoltageAlarmThreshold",
		"aTemperatureHighAlarmThreshold", "bTemperatureHighAlarmThreshold", "cTemperatureHighAlarmThreshold", "nTemperatureHighAlarmThreshold",
		"arcLevelAlarmThreshold", "alarmThresholdForPowerRestrictionDegree", "undervoltageSwitch", "overvoltageSwitch", "overcurrentSwitch",
		"leakageSwitch", "highTemperatureAlarmSwitch", "arcLevelSwitch", "powerLimitSwitch", "phaseLossProtectionSwitch");

	List<String> thresholdList = List.of("leakageAlarmThreshold", "overcurrentAlarmThreshold", "overvoltageAlarmThreshold", "undervoltageAlarmThreshold",
		"aTemperatureHighAlarmThreshold", "bTemperatureHighAlarmThreshold", "cTemperatureHighAlarmThreshold", "nTemperatureHighAlarmThreshold",
		"arcLevelAlarmThreshold", "alarmThresholdForPowerRestrictionDegree");

	List<String> switchList = List.of("undervoltageSwitch", "overvoltageSwitch", "overcurrentSwitch", "leakageSwitch",
		"highTemperatureAlarmSwitch", "arcLevelSwitch", "powerLimitSwitch", "phaseLossProtectionSwitch");

	/**
	 * 02.拼接content内容
	 *
	 * @param issueStrategyEntity
	 * @return
	 */
	@Override
	public String assembleContent(IssueStrategyEntity issueStrategyEntity) {
		StringBuilder content = new StringBuilder();
		content.append(GuardianInstructConstants.ISSUE_CONTROL_INSTRUCTION)
			.append(GuardianInstructConstants.ISSUE_SETTING_TYPE_ALARM_THRESHOLD_CONTROL);

		List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();

		if (CollectionUtil.isNotEmpty(setupItems)) {
			//数据库查询一份备用数据
			Map<String, Object> typeMap = guardianAlarmThresholdService.getAllAlarmThresholdSetup(
				issueStrategyEntity.getAppAdvancedSetups().getPlantId(),
				issueStrategyEntity.getAppAdvancedSetups().getDeviceSerialNumber());

			Map<String, AppAdvancedSetup.SetupItem> setupItemCollect = setupItems.stream()
				.collect(Collectors.toMap(AppAdvancedSetup.SetupItem::getDefinition, Function.identity()));

			try {
				// 处理 thresholdList
				for (String typeName : thresholdList) {
					appendValue(content, typeName, setupItemCollect, typeMap);
				}

				// 处理 switchList
				StringBuilder switchContent = new StringBuilder();
				for (String switchName : switchList) {
					appendSwitchValue(switchContent, switchName, setupItemCollect, typeMap);
				}
				StringBuilder reverse = switchContent.reverse();
				String binaryToHex = BinaryToHexUtils.binaryToHex(String.valueOf(reverse), 4);
				// 添加 switchContent 到主字符串
				content.append(binaryToHex);
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		} else {
			throw new BusinessException("client.guardian.setitem.cannot.empty");
		}

		return content.toString();
	}

	/**
	 * 处理阈值数据
	 *
	 * @param content
	 * @param key
	 * @param setupItemCollect
	 * @param typeMap
	 */
	private void appendValue(StringBuilder content, String key, Map<String, AppAdvancedSetup.SetupItem> setupItemCollect, Map<String, Object> typeMap) {
		if (setupItemCollect.containsKey(key)) {
			AppAdvancedSetup.SetupItem setupItem = setupItemCollect.get(key);
			Integer value = Convert.toInt(setupItem.getData(), defaultValue);
			//如果是过流阈值将数据乘以10
			if(CommonConstant.KEY_NAME.equals(key)){
				value*=10;
			}
			String hexadecimalNumber = BinaryToHexUtils.toFourDigitHex(value, dataLength);
			content.append(hexadecimalNumber);
		} else {
			Object object = typeMap.get(key);
			Integer value = Convert.toInt(object, defaultValue);
			if(CommonConstant.KEY_NAME.equals(key)){
				value*=10;
			}
			String hexadecimalNumber = BinaryToHexUtils.toFourDigitHex(value, dataLength);
			content.append(hexadecimalNumber);
		}
	}

	/**
	 * ex:01000101 => 转16进制为 69；再补齐4位，即0069 进行拼接
	 * 004c—故障保护开关（bit0 过压，bit1 欠压 bit2 过流，bit3漏电，bit4温度，bit5 故障电弧，bit6限电，bit7缺相  0 保护关闭 1 保护开启）
	 *
	 * @param switchContent
	 * @param key
	 * @param setupItemCollect
	 * @param typeMap
	 */
	private void appendSwitchValue(StringBuilder switchContent, String key, Map<String, AppAdvancedSetup.SetupItem> setupItemCollect, Map<String, Object> typeMap) {
		if (setupItemCollect.containsKey(key)) {
			AppAdvancedSetup.SetupItem setupItem = setupItemCollect.get(key);
			String data = (String) setupItem.getData();
			String replace = data.replace(".0", "");
			switchContent.append(replace);
		} else {
			Object object = typeMap.get(key);
			switchContent.append(object);
		}
	}


	/**
	 * 05.处理业务结果
	 *
	 * @param invokeResult
	 * @return
	 */
	@Override
	public R handleBusinessResult(R<String> invokeResult, IssueStrategyEntity issueStrategyEntity) {
		if (CommonConstant.REST_RESULT_FAIL == invokeResult.getCode()) {
			throw new BusinessException("client.guardian.issue.alarm.fail");
		} else {
			// 业务操作，更新数据库等
			List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
			if (CollectionUtil.isNotEmpty(setupItems)) {
				Map<String, AppAdvancedSetup.SetupItem> setupItemCollect = setupItems.stream()
					.collect(Collectors.toMap(AppAdvancedSetup.SetupItem::getDefinition, Function.identity()));
				// 创建 UpdateWrapper 对象
				UpdateWrapper<GuardianAlarmThresholdEntity> updateWrapper = new UpdateWrapper<>();
				updateWrapper.eq(DatabaseFieldConstant.SECURITY_GUARD_SERIAL_NUMBER, issueStrategyEntity.getDeviceSerialNumber())
					.eq(DatabaseFieldConstant.PLANT_ID, issueStrategyEntity.getAppAdvancedSetups().getPlantId());
				for (String type : allList) {
					if (setupItemCollect.containsKey(type)) {
						updateWrapper.set(CommonUtil.toDBField(type), setupItemCollect.get(type).getData());
					}
				}
				guardianAlarmThresholdService.update(updateWrapper);
				//查询告警阈值doris数据
				LambdaQueryWrapper<GuardianThresholdCurrentStatusEntity> currentAlarmEq = Wrappers.<GuardianThresholdCurrentStatusEntity>query().lambda()
					.eq(GuardianThresholdCurrentStatusEntity::getSecurityGuardSerialNumber, issueStrategyEntity.getDeviceSerialNumber());
				GuardianThresholdCurrentStatusEntity currentStatus =currentStatusService.getOne(currentAlarmEq);
				if(ValidationUtil.isNotEmpty(currentStatus)){
					LambdaQueryWrapper<GuardianAlarmThresholdEntity> alarmEq = Wrappers.<GuardianAlarmThresholdEntity>query().lambda()
						.eq(GuardianAlarmThresholdEntity::getSecurityGuardSerialNumber, issueStrategyEntity.getDeviceSerialNumber())
						.eq(GuardianAlarmThresholdEntity::getIsDeleted, 0);
					GuardianAlarmThresholdEntity alarmThreshold=guardianAlarmThresholdService.getOne(alarmEq);
					if(ValidationUtil.isNotEmpty(alarmThreshold)){
						JSONObject result = (JSONObject) JSON.toJSON(alarmThreshold);
						//将驼峰转化为下划线
						JSONObject newObj = HumpConvert.convertKeysToUnderscore(result);
						newObj.put("device_date_time",currentStatus.getDeviceDateTime());
						newObj.put("partition_date",currentStatus.getPartitionDate());
						dorisSinkService.write(Collections.singletonList(newObj),"security_guard_threshold_current_status");
					}
				}
			}
			return R.success("setup success");
		}
	}
}

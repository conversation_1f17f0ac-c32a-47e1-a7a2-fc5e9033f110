<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.thresholdcurrentstatus.mapper.GuardianThresholdCurrentStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianThresholdCurrentStatusResultMap" type="org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity">
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="id" property="id"/>
        <result column="undervoltage_alarm_threshold" property="undervoltageAlarmThreshold"/>
        <result column="overvoltage_alarm_threshold" property="overvoltageAlarmThreshold"/>
        <result column="overcurrent_alarm_threshold" property="overcurrentAlarmThreshold"/>
        <result column="leakage_alarm_threshold" property="leakageAlarmThreshold"/>
        <result column="a_temperature_high_alarm_threshold" property="aTemperatureHighAlarmThreshold"/>
        <result column="b_temperature_high_alarm_threshold" property="bTemperatureHighAlarmThreshold"/>
        <result column="c_temperature_high_alarm_threshold" property="cTemperatureHighAlarmThreshold"/>
        <result column="n_temperature_high_alarm_threshold" property="nTemperatureHighAlarmThreshold"/>
        <result column="arc_level_alarm_threshold" property="arcLevelAlarmThreshold"/>
        <result column="alarm_threshold_for_power_restriction_degree" property="alarmThresholdForPowerRestrictionDegree"/>
        <result column="undervoltage_switch" property="undervoltageSwitch"/>
        <result column="overvoltage_switch" property="overvoltageSwitch"/>
        <result column="overcurrent_switch" property="overcurrentSwitch"/>
        <result column="leakage_switch" property="leakageSwitch"/>
        <result column="high_temperature_alarm_switch" property="highTemperatureAlarmSwitch"/>
        <result column="arc_level_switch" property="arcLevelSwitch"/>
        <result column="power_limit_switch" property="powerLimitSwitch"/>
        <result column="phase_loss_protection_switch" property="phaseLossProtectionSwitch"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianThresholdCurrentStatusPage" resultMap="GuardianThresholdCurrentStatusResultMap">
        select * from security_guard_threshold_current_status where is_deleted = 0
    </select>


    <select id="exportGuardianThresholdCurrentStatus" resultType="org.skyworth.ess.guardian.thresholdcurrentstatus.excel.GuardianThresholdCurrentStatusExcel">
        SELECT * FROM security_guard_threshold_current_status ${ew.customSqlSegment}
    </select>
    <select id="getThresholdByLast" resultMap="GuardianThresholdCurrentStatusResultMap">
        select * from security_guard_threshold_current_status  where security_guard_serial_number =#{deviceSn}  ORDER BY create_time  DESC LIMIT 1;
    </select>

    <update id="deleteLogicByPlantIdAndSn">
        update security_guard_threshold_current_status set is_deleted=1,update_time=now() where
            plant_id = #{plantId} and security_guard_serial_number=#{securityGuardSerialNumber}
    </update>
</mapper>

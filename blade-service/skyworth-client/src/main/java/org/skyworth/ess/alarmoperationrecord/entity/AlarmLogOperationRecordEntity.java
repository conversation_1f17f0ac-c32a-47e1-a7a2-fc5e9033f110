/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmoperationrecord.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 告警日志操作记录表 实体类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Data
@TableName("alarm_log_operation_record")
@ApiModel(value = "AlarmLogOperationRecord对象", description = "告警日志操作记录表")
@EqualsAndHashCode(callSuper = true)
public class AlarmLogOperationRecordEntity extends TenantEntity {

	/**
	 * 告警日志表ID
	 */
	@ApiModelProperty(value = "告警日志表ID")
	private Long alarmLogId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	public AlarmLogOperationRecordEntity(){

	}

	public AlarmLogOperationRecordEntity(Long alarmLogId, String remark, String createUserAccount, String updateUserAccount) {
		this.alarmLogId = alarmLogId;
		this.remark = remark;
		this.createUserAccount = createUserAccount;
		this.updateUserAccount = updateUserAccount;
	}
}

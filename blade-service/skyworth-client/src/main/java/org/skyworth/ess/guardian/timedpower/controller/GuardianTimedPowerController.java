/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedpower.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.vo.GuardianTimedPowerVO;
import org.skyworth.ess.guardian.timedpower.excel.GuardianTimedPowerExcel;
import org.skyworth.ess.guardian.timedpower.wrapper.GuardianTimedPowerWrapper;
import org.skyworth.ess.guardian.timedpower.service.IGuardianTimedPowerService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士定时设置-功率设置 控制器
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianTimedPower")
@Api(value = "安全卫士定时设置-功率设置", tags = "安全卫士定时设置-功率设置接口")
public class GuardianTimedPowerController extends BladeController {

	private final IGuardianTimedPowerService GuardianTimedPowerService;

	/**
	 * 安全卫士定时设置-功率设置 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianTimedPower")
	public R<GuardianTimedPowerVO> detail(GuardianTimedPowerEntity GuardianTimedPower) {
		GuardianTimedPowerEntity detail = GuardianTimedPowerService.getOne(Condition.getQueryWrapper(GuardianTimedPower));
		return R.data(GuardianTimedPowerWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士定时设置-功率设置 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianTimedPower")
	public R<IPage<GuardianTimedPowerVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianTimedPower, Query query) {
		IPage<GuardianTimedPowerEntity> pages = GuardianTimedPowerService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianTimedPower, GuardianTimedPowerEntity.class));
		return R.data(GuardianTimedPowerWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士定时设置-功率设置 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianTimedPower")
	public R<IPage<GuardianTimedPowerVO>> page(GuardianTimedPowerVO GuardianTimedPower, Query query) {
		IPage<GuardianTimedPowerVO> pages = GuardianTimedPowerService.selectGuardianTimedPowerPage(Condition.getPage(query), GuardianTimedPower);
		return R.data(pages);
	}

	/**
	 * 安全卫士定时设置-功率设置 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianTimedPower")
	public R save(@Valid @RequestBody GuardianTimedPowerEntity GuardianTimedPower) {
		return R.status(GuardianTimedPowerService.save(GuardianTimedPower));
	}

	/**
	 * 安全卫士定时设置-功率设置 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianTimedPower")
	public R update(@Valid @RequestBody GuardianTimedPowerEntity GuardianTimedPower) {
		return R.status(GuardianTimedPowerService.updateById(GuardianTimedPower));
	}

	/**
	 * 安全卫士定时设置-功率设置 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianTimedPower")
	public R submit(@Valid @RequestBody GuardianTimedPowerEntity GuardianTimedPower) {
		return R.status(GuardianTimedPowerService.saveOrUpdate(GuardianTimedPower));
	}

	/**
	 * 安全卫士定时设置-功率设置 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianTimedPowerService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianTimedPower")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianTimedPower")
	public void exportGuardianTimedPower(@ApiIgnore @RequestParam Map<String, Object> GuardianTimedPower, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianTimedPowerEntity> queryWrapper = Condition.getQueryWrapper(GuardianTimedPower, GuardianTimedPowerEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianTimedPower::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianTimedPowerEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianTimedPowerExcel> list = GuardianTimedPowerService.exportGuardianTimedPower(queryWrapper);
		ExcelUtil.export(response, "安全卫士定时设置-功率设置数据" + DateUtil.time(), "安全卫士定时设置-功率设置数据表", list, GuardianTimedPowerExcel.class);
	}

}

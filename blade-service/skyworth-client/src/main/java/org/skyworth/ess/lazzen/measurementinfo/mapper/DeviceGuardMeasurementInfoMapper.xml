<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.lazzen.measurementinfo.mapper.DeviceGuardMeasurementInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceGuardMeasurementInfoResultMap" type="org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity">
        <result column="id" property="id"/>
        <result column="gateway_unique_number" property="gatewayUniqueNumber"/>
        <result column="circuit_breaker_address" property="circuitBreakerAddress"/>
        <result column="phase_a_voltage" property="phaseAVoltage"/>
        <result column="phase_a_current" property="phaseACurrent"/>
        <result column="phase_a_power" property="phaseAPower"/>
        <result column="phase_b_voltage" property="phaseBVoltage"/>
        <result column="phase_b_current" property="phaseBCurrent"/>
        <result column="phase_b_power" property="phaseBPower"/>
        <result column="phase_c_voltage" property="phaseCVoltage"/>
        <result column="phase_c_current" property="phaseCCurrent"/>
        <result column="phase_c_power" property="phaseCPower"/>
        <result column="leakage_current" property="leakageCurrent"/>
        <result column="temperature" property="temperature"/>
        <result column="total_active_electrical_energy" property="totalActiveElectricalEnergy"/>
        <result column="total_active_power" property="totalActivePower"/>
        <result column="total_power_factor" property="totalPowerFactor"/>
        <result column="reactive_power" property="reactivePower"/>
        <result column="reactive_energy" property="reactiveEnergy"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceGuardMeasurementInfoPage" resultMap="deviceGuardMeasurementInfoResultMap">
        select * from lazzen_device_guard_measurement_info where is_deleted = 0
    </select>


    <select id="exportDeviceGuardMeasurementInfo" resultType="org.skyworth.ess.lazzen.measurementinfo.excel.DeviceGuardMeasurementInfoExcel">
        SELECT * FROM lazzen_device_guard_measurement_info ${ew.customSqlSegment}
    </select>

    <select id="selectDataStatByTime"
            resultType="org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoStatVO">
        SELECT
            DATE_FORMAT(device_date_time, '%H:%i') deviceDateTime,
            DATE_FORMAT(device_date_time, '%Y-%m-%d') deviceDateTimeForDay,
            DATE_FORMAT(device_date_time, '%Y-%m-%d %H:%i')  deviceDateTimeForCal,
            IFNULL(phase_a_voltage, 0) AS phaseAVoltage,
            IFNULL(phase_a_current, 0) AS phaseACurrent,
            IFNULL(phase_a_power, 0) AS phaseAPower,
            IFNULL(phase_b_voltage, 0) AS phaseBVoltage,
            IFNULL(phase_b_current, 0) AS phaseBCurrent,
            IFNULL(phase_b_power, 0) AS phaseBPower,
            IFNULL(phase_c_voltage, 0) AS phaseCVoltage,
            IFNULL(phase_c_current, 0) AS phaseCCurrent,
            IFNULL(phase_c_power, 0) AS cPhaseTemperature,
            IFNULL(temperature, 0) AS temperature,
            IFNULL(leakage_current, 0) AS leakageCurrent
        FROM lazzen_device_guard_measurement_info
        WHERE device_date_time BETWEEN #{condition.startDateTime} AND #{condition.endDateTime}
          AND gateway_unique_number = #{condition.gatewayUniqueNumber}
        ORDER BY device_date_time desc,id desc
    </select>

</mapper>

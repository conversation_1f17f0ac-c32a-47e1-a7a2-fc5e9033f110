<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.currentstatus.mapper.GuardianCurrentStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianCurrentStatusResultMap" type="org.skyworth.ess.guardian.currentstatus.entity.GuardianCurrentStatusEntity">
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="id" property="id"/>
        <result column="a_phase_voltage" property="aPhaseVoltage"/>
        <result column="a_phase_current" property="aPhaseCurrent"/>
        <result column="a_phase_temperature" property="aPhaseTemperature"/>
        <result column="b_phase_voltage" property="bPhaseVoltage"/>
        <result column="b_phase_current" property="bPhaseCurrent"/>
        <result column="b_phase_temperature" property="bPhaseTemperature"/>
        <result column="c_phase_voltage" property="cPhaseVoltage"/>
        <result column="c_phase_current" property="cPhaseCurrent"/>
        <result column="c_phase_temperature" property="cPhaseTemperature"/>
        <result column="n_neutral_line_temperature" property="nNeutralLineTemperature"/>
        <result column="active_power" property="activePower"/>
        <result column="reactive_power" property="reactivePower"/>
        <result column="active_power_consumption" property="activePowerConsumption"/>
        <result column="reactive_power_consumption" property="reactivePowerConsumption"/>
        <result column="power_factor" property="powerFactor"/>
        <result column="grid_frequency" property="gridFrequency"/>
        <result column="leakage_current" property="leakageCurrent"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="alarm_status" property="alarmStatus"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianCurrentStatusPage" resultMap="GuardianCurrentStatusResultMap">
        select * from security_guard_current_status where is_deleted = 0
    </select>


    <select id="exportGuardianCurrentStatus" resultType="org.skyworth.ess.guardian.currentstatus.excel.GuardianCurrentStatusExcel">
        SELECT * FROM security_guard_current_status ${ew.customSqlSegment}
    </select>

    <update id="deleteLogicByPlantIdAndSn">
        update security_guard_current_status set is_deleted=1,update_time=now() where
            plant_id = #{plantId} and security_guard_serial_number=#{securityGuardSerialNumber}
    </update>
</mapper>

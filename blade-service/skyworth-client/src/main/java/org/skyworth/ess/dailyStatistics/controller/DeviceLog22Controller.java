/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.dailyStatistics.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.impl.DeviceLog22ByDorisServiceImpl;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备/智能能量变换器日志表，记录22数据 控制器
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("devicelog22")
@Api(value = "设备/智能能量变换器日志表，记录22数据", tags = "设备/智能能量变换器日志表，记录22数据接口")
public class DeviceLog22Controller extends BladeController {

	private final DeviceLog22ByDorisServiceImpl deviceLog22ByDorisService;

	/**
	 * 储能包曲线状态
	 *
	 * @param queryCondition 入参
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/9/21 14:44
	 **/
	@GetMapping("/stateCurve")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceLog22")
	public R<List<JSONObject>> stateCurve(QueryDeviceLog22Condition queryCondition) {
		return R.data(deviceLog22ByDorisService.stateCurve(queryCondition));
	}

	/**
	 * 储能包 状态曲线 导出
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/stateCurve/export")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "详情-状态曲线导出", notes = "传入QueryDeviceLog22Condition")
	public void statusStatExport(QueryDeviceLog22Condition queryCondition, HttpServletResponse response) {
		deviceLog22ByDorisService.selectStatusReportExport(queryCondition, response);
	}


}

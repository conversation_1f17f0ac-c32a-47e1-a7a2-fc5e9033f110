<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.timedpower.mapper.GuardianTimedPowerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianTimedPowerResultMap" type="org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="min_power" property="minPower"/>
        <result column="max_power" property="maxPower"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianTimedPowerPage" resultMap="GuardianTimedPowerResultMap">
        select * from guardian_timed_power where is_deleted = 0
    </select>


    <select id="exportGuardianTimedPower" resultType="org.skyworth.ess.guardian.timedpower.excel.GuardianTimedPowerExcel">
        SELECT * FROM guardian_timed_power ${ew.customSqlSegment}
    </select>

</mapper>

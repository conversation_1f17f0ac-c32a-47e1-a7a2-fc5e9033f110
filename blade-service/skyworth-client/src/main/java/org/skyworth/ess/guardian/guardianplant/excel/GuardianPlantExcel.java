/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 安全卫士对应站点 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GuardianPlantExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 安全卫士状态(1在线/0离线)
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士状态(1在线/0离线)")
	private Integer securityGuardStatus;
	/**
	 * 闸位状态(0关闭/1打开)
	 */
	@ColumnWidth(20)
	@ExcelProperty("闸位状态(0关闭/1打开)")
	private Integer gatePositionStatus;
	/**
	 * 功率定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypePower;
	/**
	 * 功率定时类型自定义
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率定时类型自定义")
	private String timingTypePowerCustom;
	/**
	 * 开关闸定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("开关闸定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypeGate;
	/**
	 * 开关闸定时类型自定义
	 */
	@ColumnWidth(20)
	@ExcelProperty("开关闸定时类型自定义")
	private String timingTypeGateCustom;
	/**
	 * 心跳时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("心跳时间")
	private Date heartBeatTime;
	/**
	 * 锁死设置（0 解锁 1 锁死）
	 */
	@ColumnWidth(20)
	@ExcelProperty("锁死设置（0 解锁 1 锁死）")
	private Integer lockSet;
	/**
	 * 重合功能设置（0 关闭 1 开启）
	 */
	@ColumnWidth(20)
	@ExcelProperty("重合功能设置（0 关闭 1 开启）")
	private Integer recloseSet;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

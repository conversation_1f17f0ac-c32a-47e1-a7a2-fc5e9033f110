/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.addressmap.entity.AddressMapDefinitionEntity;
import org.skyworth.ess.addressmap.excel.AddressMapDefinitionExcel;
import org.skyworth.ess.addressmap.vo.AddressMapDefinitionVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 物理地址映射属性名称 服务类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface IAddressMapDefinitionService extends BaseService<AddressMapDefinitionEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param addressMapDefinition
	 * @return
	 */
	IPage<AddressMapDefinitionVO> selectAddressMapDefinitionPage(IPage<AddressMapDefinitionVO> page, AddressMapDefinitionVO addressMapDefinition);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<AddressMapDefinitionExcel> exportAddressMapDefinition(Wrapper<AddressMapDefinitionEntity> queryWrapper);

	Map<String, Map<String, String>> getAllAddressMapByAddress(Integer beginAddressNum,Integer endAddressNum,String modbusProtocolVersion);

	String addImportExcel(List<AddressMapDefinitionExcel> addressMapDefinitionExcels, Boolean aBoolean);

	String updateImportExcel(List<AddressMapDefinitionExcel> addressMapDefinitionExcels, Boolean aBoolean);

	boolean saveAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition);

	boolean updateAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition);

	boolean saveOrUpdateAddressMapDefinition(AddressMapDefinitionEntity addressMapDefinition);

	Map<String, AddressMapDefinitionEntity> getAllAddressMap4Definition();

	List<JSONObject> getMappingBySetItem(AddressMapDefinitionVO addressMapDefinition);
}

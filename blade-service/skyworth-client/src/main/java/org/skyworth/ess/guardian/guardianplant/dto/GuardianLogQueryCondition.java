package org.skyworth.ess.guardian.guardianplant.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName GuardianLogQueryCondition
 * @Description 安全卫士日志查询条件
 * @Date 2024/8/9 下午3:34
 */
@Data
@ApiModel(value = "安全卫士日志查询条件", description = "安全卫士日志查询条件")
public class GuardianLogQueryCondition {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
    @ApiModelProperty(value = "站点id")
    private Long plantId;

    @ApiModelProperty(value = "安全卫士SN")
    private String securityGuardSerialNumber;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "开始时间")
    private Date startDateTime;

    @ApiModelProperty(value = "结束时间")
    private Date endDateTime;

}

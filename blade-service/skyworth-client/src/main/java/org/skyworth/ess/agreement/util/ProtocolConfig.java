package org.skyworth.ess.agreement.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 协议工厂配置
 *
 * @ClassName ProtocolConfig
 * @Description
 * <AUTHOR>
 * @Date 2025/7/31 17:48
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "protocol")
public class ProtocolConfig {

	private Map<String, String> parsers = new HashMap<>();

	@Bean
	public ProtocolParserFactory protocolParserFactory() throws Exception {
		ProtocolParserFactory factory = new ProtocolParserFactory();
		for (Map.Entry<String, String> entry : parsers.entrySet()) {
			ProtocolParser parser =
				(ProtocolParser) Class.forName(entry.getValue()).getDeclaredConstructor().newInstance();
			factory.registerParser(entry.getKey(), parser);
		}
		return factory;
	}
}

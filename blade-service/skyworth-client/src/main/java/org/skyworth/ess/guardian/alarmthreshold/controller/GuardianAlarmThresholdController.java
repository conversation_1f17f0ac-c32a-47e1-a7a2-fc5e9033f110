/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.vo.GuardianAlarmThresholdVO;
import org.skyworth.ess.guardian.alarmthreshold.excel.GuardianAlarmThresholdExcel;
import org.skyworth.ess.guardian.alarmthreshold.wrapper.GuardianAlarmThresholdWrapper;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全卫士阈值&amp;闸位状态 控制器
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianAlarmThreshold")
@Api(value = "安全卫士阈值&amp;闸位状态", tags = "安全卫士阈值&amp;闸位状态接口")
public class GuardianAlarmThresholdController extends BladeController {

	private final IGuardianAlarmThresholdService GuardianAlarmThresholdService;

	/**
	 * 安全卫士阈值&amp;闸位状态 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入GuardianAlarmThreshold")
	public R<GuardianAlarmThresholdVO> detail(GuardianAlarmThresholdEntity GuardianAlarmThreshold) {
		GuardianAlarmThresholdEntity detail = GuardianAlarmThresholdService.getOne(Condition.getQueryWrapper(GuardianAlarmThreshold));
		return R.data(GuardianAlarmThresholdWrapper.build().entityVO(detail));
	}
	/**
	 * 安全卫士阈值&amp;闸位状态 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入GuardianAlarmThreshold")
	public R<IPage<GuardianAlarmThresholdVO>> list(@ApiIgnore @RequestParam Map<String, Object> GuardianAlarmThreshold, Query query) {
		IPage<GuardianAlarmThresholdEntity> pages = GuardianAlarmThresholdService.page(Condition.getPage(query), Condition.getQueryWrapper(GuardianAlarmThreshold, GuardianAlarmThresholdEntity.class));
		return R.data(GuardianAlarmThresholdWrapper.build().pageVO(pages));
	}

	/**
	 * 安全卫士阈值&amp;闸位状态 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入GuardianAlarmThreshold")
	public R<IPage<GuardianAlarmThresholdVO>> page(GuardianAlarmThresholdVO GuardianAlarmThreshold, Query query) {
		IPage<GuardianAlarmThresholdVO> pages = GuardianAlarmThresholdService.selectGuardianAlarmThresholdPage(Condition.getPage(query), GuardianAlarmThreshold);
		return R.data(pages);
	}

	/**
	 * 安全卫士阈值&amp;闸位状态 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入GuardianAlarmThreshold")
	public R save(@Valid @RequestBody GuardianAlarmThresholdEntity GuardianAlarmThreshold) {
		return R.status(GuardianAlarmThresholdService.save(GuardianAlarmThreshold));
	}

	/**
	 * 安全卫士阈值&amp;闸位状态 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入GuardianAlarmThreshold")
	public R update(@Valid @RequestBody GuardianAlarmThresholdEntity GuardianAlarmThreshold) {
		return R.status(GuardianAlarmThresholdService.updateById(GuardianAlarmThreshold));
	}

	/**
	 * 安全卫士阈值&amp;闸位状态 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入GuardianAlarmThreshold")
	public R submit(@Valid @RequestBody GuardianAlarmThresholdEntity GuardianAlarmThreshold) {
		return R.status(GuardianAlarmThresholdService.saveOrUpdate(GuardianAlarmThreshold));
	}

	/**
	 * 安全卫士阈值&amp;闸位状态 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(GuardianAlarmThresholdService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-GuardianAlarmThreshold")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入GuardianAlarmThreshold")
	public void exportGuardianAlarmThreshold(@ApiIgnore @RequestParam Map<String, Object> GuardianAlarmThreshold, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianAlarmThresholdEntity> queryWrapper = Condition.getQueryWrapper(GuardianAlarmThreshold, GuardianAlarmThresholdEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(GuardianAlarmThreshold::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(GuardianAlarmThresholdEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianAlarmThresholdExcel> list = GuardianAlarmThresholdService.exportGuardianAlarmThreshold(queryWrapper);
		ExcelUtil.export(response, "安全卫士阈值&amp;闸位状态数据" + DateUtil.time(), "安全卫士阈值&amp;闸位状态数据表", list, GuardianAlarmThresholdExcel.class);
	}

}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.vo.GatewayPlantVO;
import java.util.Objects;

/**
 * 良信网关站点关系表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public class GatewayPlantWrapper extends BaseEntityWrapper<GatewayPlantEntity, GatewayPlantVO>  {

	public static GatewayPlantWrapper build() {
		return new GatewayPlantWrapper();
 	}

	@Override
	public GatewayPlantVO entityVO(GatewayPlantEntity gatewayPlant) {
		GatewayPlantVO gatewayPlantVO = Objects.requireNonNull(BeanUtil.copy(gatewayPlant, GatewayPlantVO.class));

		//User createUser = UserCache.getUser(gatewayPlant.getCreateUser());
		//User updateUser = UserCache.getUser(gatewayPlant.getUpdateUser());
		//gatewayPlantVO.setCreateUserName(createUser.getName());
		//gatewayPlantVO.setUpdateUserName(updateUser.getName());

		return gatewayPlantVO;
	}


}

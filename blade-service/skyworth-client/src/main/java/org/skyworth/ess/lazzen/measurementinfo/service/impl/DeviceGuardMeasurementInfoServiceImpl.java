/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.gatewayplant.vo.DeviceGuardMeasurementInfoQueryCondition;
import org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity;
import org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoStatVO;
import org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoVO;
import org.skyworth.ess.lazzen.measurementinfo.excel.DeviceGuardMeasurementInfoExcel;
import org.skyworth.ess.lazzen.measurementinfo.mapper.DeviceGuardMeasurementInfoMapper;
import org.skyworth.ess.lazzen.measurementinfo.service.IDeviceGuardMeasurementInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 设备卫士测量信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
@Slf4j
@DS("slave")
public class DeviceGuardMeasurementInfoServiceImpl extends BaseServiceImpl<DeviceGuardMeasurementInfoMapper, DeviceGuardMeasurementInfoEntity> implements IDeviceGuardMeasurementInfoService {

	@Override
	public IPage<DeviceGuardMeasurementInfoVO> selectDeviceGuardMeasurementInfoPage(IPage<DeviceGuardMeasurementInfoVO> page, DeviceGuardMeasurementInfoVO deviceGuardMeasurementInfo) {
		return page.setRecords(baseMapper.selectDeviceGuardMeasurementInfoPage(page, deviceGuardMeasurementInfo));
	}


	@Override
	public List<DeviceGuardMeasurementInfoExcel> exportDeviceGuardMeasurementInfo(Wrapper<DeviceGuardMeasurementInfoEntity> queryWrapper) {
		List<DeviceGuardMeasurementInfoExcel> deviceGuardMeasurementInfoList = baseMapper.exportDeviceGuardMeasurementInfo(queryWrapper);
		//deviceGuardMeasurementInfoList.forEach(deviceGuardMeasurementInfo -> {
		//	deviceGuardMeasurementInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceGuardMeasurementInfo.getType()));
		//});
		return deviceGuardMeasurementInfoList;
	}

	@Override
	public List<DeviceGuardMeasurementInfoStatVO> selectDataStatByTime(DeviceGuardMeasurementInfoQueryCondition condition) {
		return baseMapper.selectDataStatByTime(condition);
	}

}

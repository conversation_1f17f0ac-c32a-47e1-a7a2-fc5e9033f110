<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.exitfactory.mapper.GuardianExitFactoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="guardianExitFactoryInfoResultMap" type="org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity">
        <result column="id" property="id"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="device_type" property="deviceType"/>
        <result column="sim_card_number" property="simCardNumber"/>
        <result column="company" property="company"/>
        <result column="quality_guarantee_year" property="qualityGuaranteeYear"/>
        <result column="exit_factory_date" property="exitFactoryDate"/>
        <result column="activation_date" property="activationDate"/>
        <result column="warranty_start_date" property="warrantyStartDate"/>
        <result column="warranty_end_date" property="warrantyEndDate"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianExitFactoryInfoPage" resultMap="guardianExitFactoryInfoResultMap">
        select * from guardian_exit_factory_info where is_deleted = 0
    </select>


    <select id="exportGuardianExitFactoryInfo" resultType="org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel">
        SELECT * FROM guardian_exit_factory_info ${ew.customSqlSegment}
    </select>

    <update id="deleteLogicGuardianExitFactory">
        update guardian_exit_factory_info set is_deleted = 1, update_user = #{updateUser},
        update_user_account = #{updateUserAccount},update_time=now() where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateBatchBySn" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update guardian_exit_factory_info set
            <if test="item.deviceType!=null and item.deviceType!=''">
                device_type=#{item.deviceType},
            </if>
            <if test="item.company!=null and item.company!=''">
                company=#{item.company},
            </if>
            <if test="item.simCardNumber!=null and item.simCardNumber!=''">
                sim_card_number=#{item.simCardNumber},
            </if>
            <if test="item.qualityGuaranteeYear!=null and item.qualityGuaranteeYear!=''">
                quality_guarantee_year=#{item.qualityGuaranteeYear},
            </if>
            <if test="item.exitFactoryDate!=null ">
                exit_factory_date=#{item.exitFactoryDate},
            </if>
            <if test="item.updateUserAccount!=null and item.updateUserAccount!=''">
                update_user_account=#{item.updateUserAccount},
            </if>
            <if test="item.updateUser!=null and item.updateUser!=''">
                update_user=#{item.updateUser},
            </if>
            <if test="item.status!=null ">
                status=#{item.status},
            </if>
            <if test="item.activationDate!=null">
                activation_date =#{item.activationDate},
            </if>
            <if test="item.warrantyStartDate!=null">
                warranty_start_date =#{item.warrantyStartDate},
            </if>
            <if test="item.warrantyEndDate!=null">
                warranty_end_date =#{item.warrantyEndDate},
            </if>
            update_time=now()
            where security_guard_serial_number = #{item.securityGuardSerialNumber}
        </foreach>
    </update>

    <select id="queryBySerialNumbers" resultMap="guardianExitFactoryInfoResultMap">
        SELECT id, security_guard_serial_number, device_type, sim_card_number, company, quality_guarantee_year, exit_factory_date,
        activation_date, warranty_start_date, warranty_end_date, create_user_account, update_user_account, tenant_id, create_user,
        create_dept, create_time, update_user, update_time, status, is_deleted
        FROM guardian_exit_factory_info where is_deleted = 0
        and security_guard_serial_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

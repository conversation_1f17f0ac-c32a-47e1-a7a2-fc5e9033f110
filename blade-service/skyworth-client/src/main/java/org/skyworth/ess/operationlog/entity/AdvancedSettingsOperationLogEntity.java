/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.operationlog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 高级设置操作日志 实体类
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@TableName("advanced_settings_operation_log")
@ApiModel(value = "AdvancedSettingsOperationLog对象", description = "高级设置操作日志")
@EqualsAndHashCode(callSuper = true)
public class AdvancedSettingsOperationLogEntity extends SkyWorthEntity {

	/**
	 * 设备/智能能量变换器SN
	 */
	@ApiModelProperty(value = "设备/智能能量变换器SN")
	private String deviceSerialNumber;
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;
	/**
	 * 来源
	 */
	@ApiModelProperty(value = "来源")
	private String source;
	/**
	 * 修改内容:json对象
	 */
	@ApiModelProperty(value = "修改内容:json对象")
	private String requestBody;
	/**
	 * 响应
	 */
	@ApiModelProperty(value = "响应")
	private String responseBody;

	private String timeZone;

}

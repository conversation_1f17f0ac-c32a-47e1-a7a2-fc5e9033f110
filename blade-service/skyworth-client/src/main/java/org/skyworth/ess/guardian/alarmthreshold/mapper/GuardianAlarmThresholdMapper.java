/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.mapper;

import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.vo.GuardianAlarmThresholdVO;
import org.skyworth.ess.guardian.alarmthreshold.excel.GuardianAlarmThresholdExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * 安全卫士阈值&amp;闸位状态 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface GuardianAlarmThresholdMapper extends BaseMapper<GuardianAlarmThresholdEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianAlarmThreshold
	 * @return
	 */
	List<GuardianAlarmThresholdVO> selectGuardianAlarmThresholdPage(IPage page, GuardianAlarmThresholdVO GuardianAlarmThreshold);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianAlarmThresholdExcel> exportGuardianAlarmThreshold(@Param("ew") Wrapper<GuardianAlarmThresholdEntity> queryWrapper);

	Map<String, Object> getAllAlarmThresholdSetup(@Param("plantId") Long plantId,@Param("serialNumber") String serialNumber);
}

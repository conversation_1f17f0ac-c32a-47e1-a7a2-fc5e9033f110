/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.addressmap.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.springblade.common.excel.ExcelBusinessUniqueValidate;
import org.springblade.common.excel.ExcelNotNullValidate;

import java.io.Serializable;


/**
 * 物理地址映射属性名称 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class AddressMapDefinitionExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 厂商
	 */
	@ColumnWidth(20)
	@ExcelProperty("Company")
	@ExcelNotNullValidate(message = "company")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
    private String company;
	/**
	 * modbus版本
	 */
	@ColumnWidth(30)
	@ExcelProperty("ModbusProtocolVersion")
	@ExcelNotNullValidate(message = "ModbusProtocolVersion")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
    private String modbusProtocolVersion;
	/**
	 * 物理地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("Address")
	@ExcelNotNullValidate(message = "Address")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
    private String address;
	/**
	 * 数据类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("Type")
	@ExcelNotNullValidate(message = "Type")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
    private String dataType;
	/**
	 * 属性名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("Definition")
	private String definition;
	/**
	 * 单位
	 */
	@ColumnWidth(20)
	@ExcelProperty("Unit")
	private String unit;
	/**
	 * 长度
	 */
	@ColumnWidth(30)
	@ExcelProperty("RegisterNumber")
	private Integer length;
	/**
	 * 创建人账号
	 */
//	@ColumnWidth(20)
//	@ExcelProperty("创建人账号")
//	private String createUserAccount;
	/**
	 * 更新人账号
	 */
//	@ColumnWidth(20)
//	@ExcelProperty("更新人账号")
//	private String updateUserAccount;
	/**
	 * 租户ID
	 */
//	@ColumnWidth(20)
//	@ExcelProperty("租户ID")
//	private String tenantId;
	/**
	 * 逻辑删除
	 */
//	@ColumnWidth(20)
//	@ExcelProperty("逻辑删除")
//	private Integer isDeleted;

	/**
	 * 十进制物理地址
	 */
//	@ColumnWidth(30)
//	@ExcelProperty("DecimalAddress")
//	private Integer decimalAddress;

}

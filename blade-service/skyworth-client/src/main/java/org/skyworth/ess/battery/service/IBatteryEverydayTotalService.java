/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.battery.excel.BatteryEverydayTotalExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 储能每日统计 服务类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface IBatteryEverydayTotalService extends BaseService<BatteryEverydayTotalEntity> {
	/**
	 * 储能每日统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<JSONObject> dailyEstimate(QueryCondition queryCondition);

	/**
	 * 储能总量统计
	 *
	 * @param queryCondition 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/16 15:52
	 **/
	Map<String, Object> totalStatistics(QueryCondition queryCondition);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param batteryEverydayTotal
	 * @return
	 */
	IPage<BatteryEverydayTotalVO> selectBatteryEverydayTotalPage(IPage<BatteryEverydayTotalVO> page, BatteryEverydayTotalVO batteryEverydayTotal);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<BatteryEverydayTotalExcel> exportBatteryEverydayTotal(Wrapper<BatteryEverydayTotalEntity> queryWrapper);

	/**
	 * 月报表
	 *
	 * @param queryCondition
	 * @return
	 */
	List<BatteryEverydayTotalVO> monthEstimate(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> monthEstimateV2(QueryCondition queryCondition);

	/**
	 * 月报表
	 *
	 * @param queryCondition
	 * @return
	 */
	List<BatteryEverydayTotalVO> annualEstimate(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> annualEstimateV2(QueryCondition queryCondition);

	List<DeviceLog22VO> appReportEstimate(QueryDeviceLog22Condition queryCondition);

	List<BatteryEverydayTotalVO> weekEstimate(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> weekEstimateV2(QueryCondition queryCondition);

	void dailyEstimateExport(QueryCondition queryCondition, HttpServletResponse response);

	BatteryEverydayTotalEntity pieReport(QueryCondition queryCondition);

	BatteryEverydayTotalEntity parallelPieReport(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelWeekEstimateV2(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelMonthEstimateV2(QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelAnnualEstimateV2(QueryCondition queryCondition);
}

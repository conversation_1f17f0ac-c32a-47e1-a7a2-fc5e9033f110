package org.skyworth.ess.addressmap.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum AddressMapDefinitionColumnEnum {
	company("Company","厂家"),
	modbusProtocolVersion("ModbusProtocolVersion","modbus版本"),
	address("Address","物理地址"),
	type("Type","数据类型"),
	definition("Definition","属性名称"),
	unit("Unit","单位"),
	registerNumber("RegisterNumber","长度");
//	systemRequiredConfiguration("SystemRequiredConfiguration","系统默认配置");
//	decimalAddress("DecimalAddress","十进制物理地址");

    private String columnEn;
    private String columnCn;
    AddressMapDefinitionColumnEnum(String columnEn, String columnCn) {
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(AddressMapDefinitionColumnEnum item : AddressMapDefinitionColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }
}

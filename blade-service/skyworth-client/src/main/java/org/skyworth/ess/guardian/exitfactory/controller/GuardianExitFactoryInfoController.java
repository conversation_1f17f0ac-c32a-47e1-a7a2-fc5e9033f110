/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.excel.GuardianExitFactoryExcelColumnEnum;
import org.skyworth.ess.device.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.guardian.exitfactory.entity.GuardianExitFactoryInfoEntity;
import org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel;
import org.skyworth.ess.guardian.exitfactory.service.IGuardianExitFactoryInfoService;
import org.skyworth.ess.guardian.exitfactory.vo.GuardianExitFactoryInfoVO;
import org.skyworth.ess.guardian.exitfactory.wrapper.GuardianExitFactoryInfoWrapper;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.excel.ExcelImportServiceInterface;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;

/**
 * 安全卫士储能出厂信息 控制器
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/guardianExitFactoryInfo")
@Api(value = "安全卫士储能出厂信息", tags = "安全卫士储能出厂信息接口")
public class GuardianExitFactoryInfoController extends BladeController {

	private final IGuardianExitFactoryInfoService guardianExitFactoryInfoService;
	private final BladeRedis bladeRedis;
	private final ExcelImportServiceInterface<GuardianExitFactoryInfoExcel> exitFactoryImportImpl;

	/**
	 * 安全卫士储能出厂信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:detail')")
	public R<GuardianExitFactoryInfoVO> detail(GuardianExitFactoryInfoEntity guardianExitFactoryInfo) {
		GuardianExitFactoryInfoEntity detail = guardianExitFactoryInfoService.getOne(Condition.getQueryWrapper(guardianExitFactoryInfo));
		return R.data(GuardianExitFactoryInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 安全卫士储能出厂信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:list')")
	public R<IPage<GuardianExitFactoryInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> guardianExitFactoryInfo, Query query) {
		query.setDescs("id");
		BladeUser userAuth = AuthUtil.getUser();
		IPage<GuardianExitFactoryInfoEntity> pages = new Page<>();
		if (userAuth.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) userAuth.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有数据
			if (roleInnerFlag != null && roleInnerFlag) {
				pages = guardianExitFactoryInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(guardianExitFactoryInfo, GuardianExitFactoryInfoEntity.class));
				List<GuardianExitFactoryInfoEntity> exitFactoryInfoVOList = pages.getRecords();
				exitFactoryInfoVOList.stream().filter(v -> ValidationUtil.isNotEmpty(v.getWarrantyStartDate())).forEach(v -> {
					LocalDate warrantyStartDate = v.getWarrantyStartDate();
					Integer qualityGuaranteeYear = v.getQualityGuaranteeYear();
					LocalDate warrantyDeadline = warrantyStartDate.plusYears(qualityGuaranteeYear);
					v.setWarrantyEndDate(warrantyDeadline);
				});
				pages.setRecords(exitFactoryInfoVOList);
			}
		}
		return R.data(GuardianExitFactoryInfoWrapper.build().pageVO(pages));
	}


	/**
	 * 安全卫士储能出厂信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:add')")
	public R save(@Valid @RequestBody GuardianExitFactoryInfoEntity guardianExitFactoryInfo) {
		delBatchCacheKey("guardianExitFactory*");
		BladeUser user = AuthUtil.getUser();
		guardianExitFactoryInfo.setCreateUserAccount(user.getAccount());
		GuardianExitFactoryInfoEntity entity = new GuardianExitFactoryInfoEntity();
		entity.setSecurityGuardSerialNumber(guardianExitFactoryInfo.getSecurityGuardSerialNumber());
		long count = guardianExitFactoryInfoService.count(Condition.getQueryWrapper(entity));
		if (count > BizConstant.NUMBER_ZERO) {
			throw new ServiceException("the guardian number : " + guardianExitFactoryInfo.getSecurityGuardSerialNumber() + " is exist.");
		}
		guardianExitFactoryInfoService.save(guardianExitFactoryInfo);
		return R.data(guardianExitFactoryInfo.getId());
	}

	/**
	 * 安全卫士储能出厂信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:update')")
	public R update(@Valid @RequestBody GuardianExitFactoryInfoEntity guardianExitFactoryInfo) {
		delBatchCacheKey("guardianExitFactory*");
		BladeUser user = AuthUtil.getUser();
		guardianExitFactoryInfo.setUpdateUserAccount(user.getAccount());
		return R.status(guardianExitFactoryInfoService.updateById(guardianExitFactoryInfo));
	}

	/**
	 * 安全卫士储能出厂信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:update')")
	public R submit(@Valid @RequestBody GuardianExitFactoryInfoEntity guardianExitFactoryInfo) {
		delBatchCacheKey("guardianExitFactory*");
		return R.status(guardianExitFactoryInfoService.saveOrUpdate(guardianExitFactoryInfo));
	}

	/**
	 * 安全卫士储能出厂信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:remove')")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestBody GuardianExitFactoryInfoVO guardianExitFactoryInfoVO) {
		delBatchCacheKey("guardianExitFactory*");
		return guardianExitFactoryInfoService.deleteLogicGuardianExitFactory(Func.toLongList(guardianExitFactoryInfoVO.getIds()));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-guardianExitFactoryInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入guardianExitFactoryInfo")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:export')")
	public void exportGuardianExitFactoryInfo(@ApiIgnore @RequestParam Map<String, Object> guardianExitFactoryInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<GuardianExitFactoryInfoEntity> queryWrapper = Condition.getQueryWrapper(guardianExitFactoryInfo, GuardianExitFactoryInfoEntity.class);
		queryWrapper.lambda().eq(GuardianExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<GuardianExitFactoryInfoExcel> list = guardianExitFactoryInfoService.exportGuardianExitFactoryInfo(queryWrapper);
		ExcelUtil.export(response, "安全卫士储能出厂信息数据" + DateUtil.time(), "安全卫士储能出厂信息数据表", list, GuardianExitFactoryInfoExcel.class);
	}

	/**
	 * 导出模板
	 */
	@GetMapping("/export-template")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出模板")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:export')")
	public void exportImportantEventTemplate(HttpServletResponse response) {
		List<GuardianExitFactoryInfoExcel> list = new ArrayList<>();
		ExcelUtil.export(response, "template", "安全卫士储能出厂信息数据表", list, GuardianExitFactoryInfoExcel.class);
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-add-guardianExitFactory")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "新增导入出厂信息", notes = "传入excel")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:import')")
	public R importAddExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<GuardianExitFactoryInfoExcel>, Boolean, String> fun = guardianExitFactoryInfoService::importAddExcel;
		String result = exitFactoryImportImpl.imporExcel(file, GuardianExitFactoryExcelColumnEnum.getColumn(CommonConstant.CURRENT_LANGUAGE_ZH),
			GuardianExitFactoryInfoExcel.class, fun);
		if (StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}

	/**
	 * 导入
	 */
	@PostMapping("/import-modify-guardianExitFactory")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "修改导入出厂信息", notes = "传入excel")
//	@PreAuth("hasPermission('client:guardianExitFactoryInfo:import')")
	public R importModifyExitFactory(MultipartFile file, Integer isCovered) {
		BiFunction<List<GuardianExitFactoryInfoExcel>, Boolean, String> fun = guardianExitFactoryInfoService::importModifyExcel;
		String result = exitFactoryImportImpl.imporExcel(file, GuardianExitFactoryExcelColumnEnum.getColumn(CommonConstant.CURRENT_LANGUAGE_ZH),
			GuardianExitFactoryInfoExcel.class, fun);
		if (StringUtil.isNotBlank(result)) {
			return R.fail(result);
		} else {
			return R.data(result);
		}
	}


	private void delBatchCacheKey(String keyPrefix) {
		Set<String> allAddressMapDefinitionByAddress = bladeRedis.keys(keyPrefix);
		bladeRedis.del(allAddressMapDefinitionByAddress);
	}
}

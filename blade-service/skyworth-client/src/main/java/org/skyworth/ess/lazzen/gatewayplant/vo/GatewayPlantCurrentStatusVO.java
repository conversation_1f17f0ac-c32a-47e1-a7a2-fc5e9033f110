package org.skyworth.ess.lazzen.gatewayplant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity;
import org.skyworth.ess.util.BigDecimalSerializer2Scale;

import java.math.BigDecimal;
import java.time.LocalDate;


@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayPlantCurrentStatusVO  extends DeviceGuardMeasurementInfoEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	private Long plantId;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;

//	/**
//	 * 开合闸(闸位状态) 0x041F
//	 */
//	@ApiModelProperty(value = "开合闸(闸位状态) 0x041F")
//	private BigDecimal turnOnOff;

	/**
	 * 产品规格 0x0200
	 */
	@ApiModelProperty(value = "产品规格 0x0200")
	private String productStandard;


	/**
	 * 设备子类型（壳架电流+产品型号+额定电流+/产品极数）：0x0202 0x0201 0x0203 /0x0209
	 */
	private String deviceSubType;

	/**
	 * 生产日期：0x020E+0x020F
	 */
	@ApiModelProperty(value = "生产日期")
	private LocalDate productDate;

	/**
	 * 工作频率(电网频率) 0x0206
	 */
	@ApiModelProperty(value = "工作频率(电网频率) 0x0206")
	@JsonSerialize(using = BigDecimalSerializer2Scale.class)
	private BigDecimal workFrequency;

	/**
	 * 产品序列号 0x0210 - 0x0214
	 */
	@ApiModelProperty(value = "产品序列号 0x0210 - 0x0214")
	private String productSn;

	/**
	 * 产品设备状态
	 */
	@ApiModelProperty(value = "产品状态 在线/告警/离线")
	private Integer productStatus;
}

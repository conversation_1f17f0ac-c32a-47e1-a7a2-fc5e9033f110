/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.wrapper;

import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.vo.Device21VO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 设备/智能能量变换器表，记录2.1数据 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public class Device21Wrapper extends BaseEntityWrapper<Device21Entity, Device21VO>  {

	public static Device21Wrapper build() {
		return new Device21Wrapper();
 	}

	@Override
	public Device21VO entityVO(Device21Entity Device21) {
		if (Device21 == null) {
			return null;
		}
		Device21VO Device21VO = Objects.requireNonNull(BeanUtil.copy(Device21, Device21VO.class));

		//User createUser = UserCache.getUser(Device21.getCreateUser());
		//User updateUser = UserCache.getUser(Device21.getUpdateUser());
		//Device21VO.setCreateUserName(createUser.getName());
		//Device21VO.setUpdateUserName(updateUser.getName());

		return Device21VO;
	}


}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianplant.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.vo.GuardianPlantVO;
import java.util.Objects;

/**
 * 安全卫士对应站点 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public class GuardianPlantWrapper extends BaseEntityWrapper<GuardianPlantEntity, GuardianPlantVO>  {

	public static GuardianPlantWrapper build() {
		return new GuardianPlantWrapper();
 	}

	@Override
	public GuardianPlantVO entityVO(GuardianPlantEntity GuardianPlant) {
		GuardianPlantVO GuardianPlantVO = Objects.requireNonNull(BeanUtil.copy(GuardianPlant, GuardianPlantVO.class));

		//User createUser = UserCache.getUser(GuardianPlant.getCreateUser());
		//User updateUser = UserCache.getUser(GuardianPlant.getUpdateUser());
		//GuardianPlantVO.setCreateUserName(createUser.getName());
		//GuardianPlantVO.setUpdateUserName(updateUser.getName());

		return GuardianPlantVO;
	}


}

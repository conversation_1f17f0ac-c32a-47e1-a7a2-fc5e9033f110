package org.skyworth.ess.balconypv.analysisdata.consumer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aecc.convert.Convert;
import org.aecc.pojo.TcpData;
import org.aecc.utils.DataConversionOutils;
import org.aecc.utils.SectionDataParsing;
import org.skyworth.ess.common.MqttTopicProperty;
import org.skyworth.ess.consumer.MqttConsumerService;
import org.springblade.common.constant.CommonConstant;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Service
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/testTentech")
public class TentechMqttConsumer implements MqttConsumerService {

	@MqttTopicProperty(topicName = CommonConstant.MQTT_SHARE_KEY + CommonConstant.MQTT_TENTECH_KEY + CommonConstant.TENTECH_MQTT_COMMON_CONSUMER_TOPIC, qos=1) //
	public boolean consumer(String topiContent) {
		log.info("tentechMqttConsumer : {} ",topiContent);
		return true;
	}

	public static void main(String[] args) {
		// 采集器上传的03报文，因为是模拟操作，需要把16进制字符串先转换为byte数组
		byte[] bytes = DataConversionOutils.hexStringToBytes("00 00 00 08 02 ab 00 03 32 3d 27 2a 41 43 1d 28 58 37 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 51 55 53 52 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 78 62 60 72 42 5a c0 6c 6c 44 61 54 63 37 39 44 1d 5b 5a 33 51 56 22 53 43 44 1c 49 4f 54 41 45 43 43 53 1c 5a 36 06 1a 17 3a 51 53 43 44 5a 36 5e 5a 51 4b 53 43 53 54 0d 1a 18 2b 08 0b 15 3c 41 44 1d 59 18 2b 50 4b 53 4d 43 54 0d 49 4f 74 61 65 60 63 73 74 2d 69 6f 74 61 65 63 63 83 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 44 63 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 70 68 77 70 4d 64 63 73 74 2d 69 6f 74 61 65 62 63 73 74 2d 69 6f 74 61 64 97 62 87 75 d9 68 d7 75 95 64 97 62 d7 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 72 63 72 74 2d 69 7e 74 69 65 63 63 4d 74 d1 69 6f 74 5e 65 9e 63 73 74 2d 69 6f 74 61 65 6e 63 73 74 1f 69 6f 74 61 65 63 63 70 74 c5 69 6c 74 89 9a b7 63 73 7c 74 69 3a b1 ec 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 72 74 2c 69 95 74 9b 65 63 63 72 74 d7 6b 7c 74 61 65 63 63 73 74 2d 69 68 74 61 65 63 63 73 74 2d 69 6e 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 65 63 63 94 74 2c 96 90 74 61 65 63 63 73 74 2d 5e 6f 43 41 65 62 63 73 74 3c 69 6e 74 61 65 72 63 7a 74 2d 69 51 74 9d 65 63 63 4c 74 d0 69 6f 74 61 65 63 63 73 74 23 69 6f 74 53 65 63 63 73 74 2d 69 6c 74 89 65 60 63 9b 8b f9 69 6f 7d 77 65 37 98 4b 74 2d 51 6f 4c 68 65 63 63 73 74 2d 69 6f 74 61 65 63 63 73 74 2d 69 6f 74 61 53 25");
		// 调用解析之前需要先初始化，并填入我司提供的私钥
		Convert convert = Convert.init("aeccst-iot");  // 此密钥仅为测试
		// 解析
		TcpData tcpData = convert.toTcpData(bytes);
		System.out.println("功能码：" + tcpData.getFunction());
		System.out.println("采集器序列号：" + tcpData.getDatalogSn());
		System.out.println("设备序列号：" + tcpData.getDeviceSn());
		System.out.println("设备编码：" + tcpData.getDtc());
		System.out.println("时间：" + tcpData.getTimeCalendar());

		/**
		 * 根据不同设备类型（deviceTypeCode）可以对不同设备的数据分别进行处理和保存，这里省略该步骤
		 */

		// 区段数据解析
		SectionDataParsing sdp = tcpData.getSectionDataParsing();
		// 设备示例数据：根据地址取数据 "";//
		String controllerVersion = sdp.getRegisterAsciiData(0x3000, 11).trim();  // 控制器版本  地址从0X000C开始并占用6个寄存器
		Integer dischargeSocProtectValue = sdp.getRegisterData(0x3043);  // 电池放电SOC保护值
		Integer inv1FeedRatedPower = sdp.getRegisterData(0x3023);  // 逆变器1馈电额定功率
		System.out.println("控制器版本：" + controllerVersion + ";  " + "电池放电SOC保护值:" + dischargeSocProtectValue + "%  "
			+ "逆变器1馈电额定功率:" + inv1FeedRatedPower + "W");

	}
}

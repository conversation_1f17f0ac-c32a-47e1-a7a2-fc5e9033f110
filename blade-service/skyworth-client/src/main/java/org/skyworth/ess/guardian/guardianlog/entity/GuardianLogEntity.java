/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.guardianlog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 安全卫士日志 实体类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@TableName("security_guard_log")
@ApiModel(value = "GuardianLog对象", description = "安全卫士日志")
@EqualsAndHashCode(callSuper = true)
public class GuardianLogEntity extends SkyWorthEntity {

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ApiModelProperty(value = "安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 数据上报时间
	 */
	@ApiModelProperty(value = "数据上报时间")
	private Date deviceDateTime;
	/**
	 * A相电压，单位V
	 */
	@ApiModelProperty(value = "A相电压，单位V")
	private BigDecimal aPhaseVoltage;
	/**
	 * A相电流，单位A
	 */
	@ApiModelProperty(value = "A相电流，单位A")
	private BigDecimal aPhaseCurrent;
	/**
	 * A相温度，单位°C
	 */
	@ApiModelProperty(value = "A相温度，单位°C")
	private BigDecimal aPhaseTemperature;
	/**
	 * B相电压，单位V
	 */
	@ApiModelProperty(value = "B相电压，单位V")
	private BigDecimal bPhaseVoltage;
	/**
	 * B相电流，单位A
	 */
	@ApiModelProperty(value = "B相电流，单位A")
	private BigDecimal bPhaseCurrent;
	/**
	 * B相温度，单位°C
	 */
	@ApiModelProperty(value = "B相温度，单位°C")
	private BigDecimal bPhaseTemperature;
	/**
	 * C相电压，单位V
	 */
	@ApiModelProperty(value = "C相电压，单位V")
	private BigDecimal cPhaseVoltage;
	/**
	 * C相电流，单位A
	 */
	@ApiModelProperty(value = "C相电流，单位A")
	private BigDecimal cPhaseCurrent;
	/**
	 * C相温度，单位°C
	 */
	@ApiModelProperty(value = "C相温度，单位°C")
	private BigDecimal cPhaseTemperature;
	/**
	 * N零线温度，单位°C
	 */
	@ApiModelProperty(value = "N零线温度，单位°C")
	private BigDecimal nNeutralLineTemperature;
	/**
	 * 有功功率，单位W
	 */
	@ApiModelProperty(value = "有功功率，单位W")
	private BigDecimal activePower;
	/**
	 * 无功功率，单位W
	 */
	@ApiModelProperty(value = "无功功率，单位W")
	private BigDecimal reactivePower;
	/**
	 * 有功电量，单位kWh
	 */
	@ApiModelProperty(value = "有功电量，单位kWh")
	private BigDecimal activePowerConsumption;
	/**
	 * 无功电量，单位kWh
	 */
	@ApiModelProperty(value = "无功电量，单位kWh")
	private BigDecimal reactivePowerConsumption;
	/**
	 * 功率因素，单位L
	 */
	@ApiModelProperty(value = "功率因素，单位L")
	private BigDecimal powerFactor;
	/**
	 * 电网频率，单位Hz
	 */
	@ApiModelProperty(value = "电网频率，单位Hz")
	private BigDecimal gridFrequency;
	/**
	 * 漏电电流，单位ma
	 */
	@ApiModelProperty(value = "漏电电流，单位ma")
	private BigDecimal leakageCurrent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String alarmStatus;

	/**
	 * 分区字段
	 */
	@ApiModelProperty(value = "分区字段")
	private String partitionDate;

}

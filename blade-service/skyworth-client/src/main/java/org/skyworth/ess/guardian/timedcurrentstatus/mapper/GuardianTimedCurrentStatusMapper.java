/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedcurrentstatus.mapper;

import org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity;
import org.skyworth.ess.guardian.timedcurrentstatus.vo.GuardianTimedCurrentStatusVO;
import org.skyworth.ess.guardian.timedcurrentstatus.excel.GuardianTimedCurrentStatusExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 安全卫士-设备上报定时设置当前状态信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface GuardianTimedCurrentStatusMapper extends BaseMapper<GuardianTimedCurrentStatusEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianTimedCurrentStatus
	 * @return
	 */
	List<GuardianTimedCurrentStatusVO> selectGuardianTimedCurrentStatusPage(IPage page, GuardianTimedCurrentStatusVO GuardianTimedCurrentStatus);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianTimedCurrentStatusExcel> exportGuardianTimedCurrentStatus(@Param("ew") Wrapper<GuardianTimedCurrentStatusEntity> queryWrapper);

	int deleteLogicByPlantIdAndSn(@Param("plantId") Long plantId,@Param("securityGuardSerialNumber") String securityGuardSerialNumber);

	GuardianTimedCurrentStatusEntity  getTimeByLast(String deviceSn);
}

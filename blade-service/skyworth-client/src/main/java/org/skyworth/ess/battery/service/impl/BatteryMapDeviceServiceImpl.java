package org.skyworth.ess.battery.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.battery.entity.BatteryDeviceInstallVO;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.mapper.BatteryMapDeviceMapper;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.battery.vo.BatteryCapacityVo;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.exception.entity.ExceptionLogEntity;
import org.skyworth.ess.exception.service.IExceptionLogService;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 储能映射设备表(BatteryMapDeviceT)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-13 17:22:57
 */
@Service
@AllArgsConstructor
public class BatteryMapDeviceServiceImpl extends BaseServiceImpl<BatteryMapDeviceMapper, BatteryMapDeviceEntity> implements IBatteryMapDeviceService {

	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IImportantEventService importantEventService;
	private final IExceptionLogService exceptionLogService;
	private final IAgentClient agentClient;
	private final IUserSearchClient userSearchClient;
	private final IUserClient userClient;

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public BatteryMapDeviceVO queryById(Integer id) {
		return baseMapper.queryById(id);
	}

	/**
	 * 分页查询储能列表信息
	 *
	 * @param queryPageCondition 查询条件
	 * @param page               入参
	 * @return IPage<BatteryPageResult>
	 * <AUTHOR>
	 * @since 2023/9/15 9:25
	 **/
	@Override
	public IPage<BatteryPageResultVO> queryPage(QueryCondition queryPageCondition, IPage<BatteryPageResultVO> page) {
		String companyName=queryPageCondition.getOperationCompanyName();
		List<AgentCompanyVO> agentCompanyVOList=new ArrayList<>();

		//搜索运维团队
		if(ValidationUtil.isNotEmpty(companyName)){
			agentCompanyVOList=agentClient.agentCompany(companyName).getData();
			if(ValidationUtil.isNotEmpty(agentCompanyVOList)&&!agentCompanyVOList.isEmpty()){
				String companyIds=agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setOperationCompanyIds(Func.toLongList(companyIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索运维人员
		List<User> userList=new ArrayList<>();
		String userName=queryPageCondition.getOperationUserName();
		if(ValidationUtil.isNotEmpty(userName)){
			userList=userSearchClient.listByRealName(userName).getData();
			if(ValidationUtil.isNotEmpty(userList)&&!userList.isEmpty()){
				String userIds=userList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setOperationUserIds(Func.toLongList(userIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索手机号
		List<User> phoneList = new ArrayList<>();
		String phone=queryPageCondition.getPhone();
		if(ValidationUtil.isNotEmpty(phone)){
			phoneList=userSearchClient.listByPhone(phone).getData();
			if(ValidationUtil.isNotEmpty(phoneList)&&!phoneList.isEmpty()){
				String userIds=phoneList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setUserAndPhoneIds(Func.toLongList(userIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}

		BladeUser userAuth = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(userAuth);
		queryPageCondition.setCreateUser(userAuth.getUserId());
		queryPageCondition.setDeptId(deptId);
		// 获取用户类型，展示站点状态
		String userType = StatusDisplayUtil.getRoleType(userAuth.getRoleName(),userAuth.getDeptId());
		// queryPageCondition新增删除标识
		List<BatteryPageResultVO> batteryPageResultVOList = baseMapper.queryPage(queryPageCondition, page, userType);
		//储能额定总容量
		List<String> batterySns=batteryPageResultVOList.stream().map(BatteryPageResultVO::getBatterySerialNumber).collect(Collectors.toList());
		Map<String,BatteryCapacityVo> batterySnMap;
		if(!CollectionUtils.isNullOrEmpty(batterySns)){
			//List<BatteryCapacityVo> batterysnList=baseMapper.queryBatteryCapacity(batterySns);
			List<BatteryCapacityVo> batterysnList=baseMapper.queryBatteryCapacityByBatterySns(batterySns);
			batterySnMap = batterysnList.stream().collect(Collectors.toMap(BatteryCapacityVo::getBatterySn, Function.identity(), (a, b) -> a));
		} else {
			batterySnMap = new HashMap<>();
		}

		//用户信息
		Map<Long,User> createUserResultMap;
		if(org.springblade.common.utils.CollectionUtils.isNullOrEmpty(phoneList)){
			List<Long> createUserIds=batteryPageResultVOList.stream().map(BatteryPageResultVO::getCreateUser).collect(Collectors.toList());
			List<User> createUserRes=userSearchClient.listByUserIds(createUserIds).getData();
			if(!CollectionUtils.isNullOrEmpty(createUserRes)){
				createUserResultMap = createUserRes.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			} else {
				createUserResultMap = new HashMap<>();
			}
		}else {
			createUserResultMap = phoneList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		}

		//运维团队信息
		Map<Long, AgentCompanyVO> agentCompanyMap = new HashMap<>();
		if(org.springblade.common.utils.CollectionUtils.isNullOrEmpty(agentCompanyVOList)){
			String companyIds=batteryPageResultVOList.stream().map(companyVO -> Func.toStr(companyVO.getOperationCompanyId())).distinct().collect(Collectors.joining(","));
			List<AgentCompanyVO> agentCompanyList=agentClient.agentCompanyInfoByIds(Func.toLongList(companyIds)).getData();
			if (!CollectionUtils.isNullOrEmpty(agentCompanyList)){
				agentCompanyMap = agentCompanyList.stream().collect(Collectors.toMap(AgentCompanyVO::getDeptId, Function.identity(), (a, b) -> a));
			}
		} else {
			agentCompanyMap = agentCompanyVOList.stream().collect(Collectors.toMap(AgentCompanyVO::getDeptId, Function.identity(), (a, b) -> a));
		}

		//运维人员信息
		Map<Long, User> userMap = new HashMap<>();
		if(org.springblade.common.utils.CollectionUtils.isNullOrEmpty(userList)){
			String userIds=batteryPageResultVOList.stream().map(UserVo -> Func.toStr(UserVo.getOperationUserId())).distinct().collect(Collectors.joining(","));
			List<User> userResult=userSearchClient.listByUserIds(Func.toLongList(userIds)).getData();
			if (!CollectionUtils.isNullOrEmpty(userResult)){
				userMap = userResult.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			}
		} else {
			userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		}
		Map<Long, User> finalUserMap = userMap;
		Map<Long, AgentCompanyVO> finalAgentCompanyMap = agentCompanyMap;
		batteryPageResultVOList.parallelStream().forEach(v->{
			if(finalAgentCompanyMap.containsKey(v.getOperationCompanyId())){
				v.setOperationCompanyName(finalAgentCompanyMap.get(v.getOperationCompanyId()).getCompanyName());
			}
			if(finalUserMap.containsKey(v.getOperationUserId())){
				User user= finalUserMap.get(v.getOperationUserId());
				if(ValidationUtil.isNotEmpty(user)){
					v.setOperationUserName(user.getRealName());
				}
			}
			User user=createUserResultMap.get(v.getCreateUser());
			if(ValidationUtil.isNotEmpty(user)){
				v.setPhone(user.getPhone());
				v.setPhoneDiallingCode(user.getPhoneDiallingCode());
				v.setRealName(user.getRealName());
			}

			BatteryCapacityVo batteryCapacityVo=batterySnMap.get(v.getBatterySerialNumber());
			if(ValidationUtil.isNotEmpty(batteryCapacityVo)){
				v.setRatedBatteryCapacity(ValidationUtil.isEmpty(batteryCapacityVo.getCapacity())?"":batteryCapacityVo.getCapacity()+"");
			}else {
				v.setRatedBatteryCapacity("");
			}
			// 判断电池归属第几簇电池
			Integer batteryEnergyStorageNumber = v.getBatteryEnergyStorageNumber();
			// 展示不同角色的视角下，第1簇电池状态
			if(BizConstant.NUMBER_ONE.equals(batteryEnergyStorageNumber)){
				v.setStatus(StatusDisplayUtil.batteryStatusConvert(v.getStatus(), userType, v.getExistUserTypeAlarm(),
					v.getExistAgentTypeAlarm(),v.getBatteryPower()));
			}
			// 展示不同角色的视角下，第2簇电池状态
			else if(BizConstant.NUMBER_TWO.equals(batteryEnergyStorageNumber)){
				v.setStatus(StatusDisplayUtil.batteryStatusConvert(v.getStatus(), userType, v.getExistUserTypeAlarm(),
					v.getExistAgentTypeAlarm(),v.getBatteryPower2()));
			}
		});

		if (ObjectUtil.isNotNull(queryPageCondition.getRealName())){
			batteryPageResultVOList = batteryPageResultVOList.stream()
				.filter(x -> ValidationUtil.isNotEmpty(x.getRealName()))
				.filter(x -> x.getRealName().contains(queryPageCondition.getRealName())).collect(Collectors.toList());
		}
		return page.setRecords(batteryPageResultVOList);
	}

	/**
	 * 查询储能储能列表查询汇总项
	 *
	 * @param queryPageCondition 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/15 14:30
	 **/
	@Override
	public Map<String, Object> listSummary(QueryCondition queryPageCondition) {
		//查询储能数量以及额定总能量
		Query query = new Query();
		query.setSize(100000);
		List<BatteryPageResultVO> records = this.queryPage(queryPageCondition,Condition.getPage(query)).getRecords();
		List<Long> collect = records.stream().map(BatteryPageResultVO::getPlantId).collect(Collectors.toList());
		if (!collect.isEmpty()){
			return baseMapper.listSummary(collect, queryPageCondition.getDeleteFlag());
		}else {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("totalCount",0);
			jsonObject.put("totalBatteryEnergy",0);
			return jsonObject;
		}
	}

	/**
	 * 安装信息
	 *
	 * @param queryPageCondition 入参
	 * @return BatteryDeviceInstallVO
	 * <AUTHOR>
	 * @since 2023/10/7 18:18
	 **/
	@Override
	public List<BatteryDeviceInstallVO> installation(QueryCondition queryPageCondition) {
		if (queryPageCondition == null) {
			throw new BusinessException("client.parameter.error.empty");
		}
		List<BatteryDeviceInstallVO> batteryDeviceInstallVOList=baseMapper.installation(queryPageCondition);
		if (CollectionUtils.isNullOrEmpty(batteryDeviceInstallVOList)) {
			batteryDeviceInstallVOList = baseMapper.installationIsDelete(queryPageCondition);
		}
		for (BatteryDeviceInstallVO batteryDeviceInstallVO : batteryDeviceInstallVOList) {
			if (ValidationUtil.isNotEmpty(batteryDeviceInstallVO.getInstallTeamId())) {
				List<AgentCompanyVO> agentCompanyList = agentClient.agentCompanyInfoByIds(Collections.singletonList(batteryDeviceInstallVO.getInstallTeamId())).getData();
				if (!CollectionUtils.isNullOrEmpty(agentCompanyList)) {
					batteryDeviceInstallVO.setInstallTeam(agentCompanyList.get(0).getCompanyName());
				}
			}
			if (ObjectUtil.isNotEmpty(batteryDeviceInstallVO.getInstallUserId())) {
				Long installUserId = batteryDeviceInstallVO.getInstallUserId();
				User user = userClient.userInfoById(installUserId).getData();
				if (ObjectUtil.isNotNull(user)){
					batteryDeviceInstallVO.setInstallPerson(user.getRealName());
				}
			}
		}
		return batteryDeviceInstallVOList;
	}

	@Override
	public int batchDeleteLogicByPlantId(List<Long> plantIdList, String updateUserAccount) {
		return baseMapper.batchDeleteLogicByPlantId(plantIdList, updateUserAccount);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryOwnerData(Long createUser) {
		return baseMapper.queryOwnerData(createUser);
	}

	@Override
	public int updateDataByCondition(BatteryMapDeviceEntity updateOwner) {
		return baseMapper.updateDataByCondition(updateOwner);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantId(List<Long> list) {
		return baseMapper.queryListByPlantId(list);
	}

	@Override
	public List<BatteryMapDeviceVO> queryBatteryDeviceInfo(BatteryMapDeviceEntity batteryMapDeviceEntity) {
		return baseMapper.queryBatteryDeviceInfo(batteryMapDeviceEntity);
	}

	@Override
	public List<BatteryExitFactoryInfoVO> deviceInformation(QueryCondition queryPageCondition) {
		// 通过智能能量变换器SN和站点id获取储能列表
		List<BatteryExitFactoryInfoVO> batteryMapDeviceEntityList = batteryExitFactoryInfoService.deviceInformation(queryPageCondition);
		if (CollectionUtils.isNullOrEmpty(batteryMapDeviceEntityList)) {
			batteryMapDeviceEntityList = batteryExitFactoryInfoService.deviceInformationIsDelete(queryPageCondition);
		}
		Optional.ofNullable(batteryMapDeviceEntityList).orElse(new ArrayList<>()).forEach(a -> {
			a.setRunningDay(DateUtil.getDayBetweenTwoDate(a.getCreateTime(), new Date()));
		});
		return batteryMapDeviceEntityList;
	}

	@Override
	public IPage<ImportantEventEntity> importantEvents(QueryCondition queryCondition, IPage<ImportantEventEntity> page) {
		Wrapper<ImportantEventEntity> queryWrapper = Wrappers.<ImportantEventEntity>lambdaQuery().eq(ImportantEventEntity::getEventType, queryCondition.getEventType()).eq(ImportantEventEntity::getPlantId, queryCondition.getPlantId()).orderByDesc(ImportantEventEntity::getCreateTime);
		return importantEventService.page(page, queryWrapper);
	}

	@Override
	public IPage<ExceptionLogEntity> exceptionLog(QueryCondition queryCondition, IPage<ExceptionLogEntity> page) {
        return exceptionLogService.selectExceptionLogPageByCondition(page, queryCondition);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantIdAndSn(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryListByPlantIdAndSn(plantId,deviceSerialNumber);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantIdAndSnAndBattery(Long plantId, String deviceSerialNumber,Integer batteryEnergyStorageNumber) {
		return baseMapper.queryListByPlantIdAndSnAndBattery(plantId,deviceSerialNumber,batteryEnergyStorageNumber);
	}

	@Override
	public void batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account) {
		baseMapper.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber,account);
	}

}

package org.skyworth.ess.guardian.issue;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class IssueStrategy4GatePosition extends GuardianIssueStrategy {


	public IGuardianPlantService guardianPlantService = SpringUtil.getBean(IGuardianPlantService.class);


	/**
	 * 02.拼接content内容
	 *
	 * @param issueStrategyEntity
	 * @return
	 */
	@Override
	public String assembleContent(IssueStrategyEntity issueStrategyEntity) {
		StringBuilder content = new StringBuilder();
		content.append(GuardianInstructConstants.ISSUE_CONTROL_INSTRUCTION);
		List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
		if (CollectionUtil.isNotEmpty(setupItems)) {
			AppAdvancedSetup.SetupItem setupItem = setupItems.get(0);
			// 前端传过来的1为合闸，0为分闸。两种设置的控制命令不一样
			if (setupItem.getData().equals("1")){
				content.append(GuardianInstructConstants.ISSUE_SETTING_TYPE_GATE_SWITCH);
			}else{
				content.append(GuardianInstructConstants.ISSUE_SETTING_TYPE_OUTAGE);
			}
			String hexadecimalNumber = BinaryToHexUtils.toFourDigitHex(Convert.toInt(setupItem.getData(), 0), 2);
			content.append(hexadecimalNumber);
		} else {
			throw new BusinessException("client.guardian.setitem.cannot.empty");
		}
		return content.toString();
	}


	/**
	 * 05.处理业务结果
	 *
	 * @param invokeResult
	 * @return
	 */
	@Override
	public R handleBusinessResult(R<String> invokeResult,IssueStrategyEntity issueStrategyEntity) {
		if (CommonConstant.REST_RESULT_FAIL == invokeResult.getCode()) {
			throw new BusinessException("client.guardian.issue.gateposition.fail");
		} else {
			// 业务操作，更新数据库等
			List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
			if (!CollectionUtils.isEmpty(setupItems)){
				String deviceSerialNumber = issueStrategyEntity.getDeviceSerialNumber();
				Long plantId = issueStrategyEntity.getAppAdvancedSetups().getPlantId();
				for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
					if ("gatePositionStatus".equals(setupItem.getDefinition())){
						Object data = setupItem.getData();
						guardianPlantService.update(Wrappers.<GuardianPlantEntity>update().lambda().set(GuardianPlantEntity::getGatePositionStatus, data)
							.eq(GuardianPlantEntity::getSecurityGuardSerialNumber, deviceSerialNumber)
							.eq(GuardianPlantEntity::getPlantId,plantId));
					}
				}
			}
			return R.success("setup success");
		}
	}
}

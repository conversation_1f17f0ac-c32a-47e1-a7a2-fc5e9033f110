package org.skyworth.ess.guardian.exitfactory.service.impl;

import lombok.AllArgsConstructor;
import org.skyworth.ess.guardian.exitfactory.excel.GuardianExitFactoryInfoExcel;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class GuardianExitFactoryImportImpl extends ExcelImportServiceAbstract<GuardianExitFactoryInfoExcel> {
	private final IDictBizClient dictBizClient;

	@Override
	public String validateDataEffective(List<GuardianExitFactoryInfoExcel> dataList) {
		Map<String, List<DictBiz>> dictMap = dictBizClient.batchGetList(List.of("device_company",DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode())).getData();
		List<DictBiz> dictData = dictMap.get("device_company");
		List<DictBiz> clientGuardianTypeData = dictMap.get(DictBizCodeEnum.DEVICE_CLIENT_GUARDIAN_TYPE.getDictCode());
		boolean allListsNotEmpty = Stream.of(dictData,clientGuardianTypeData).noneMatch(List::isEmpty);
		if (!allListsNotEmpty) {
			return "the company or GuardianType not setting in system,please setting in business menu,the code is : device_company";
		}
		StringBuilder stringBuilder = this.validateCompany(dataList, dictData);
		stringBuilder.append(this.validateGuardianType(dataList, clientGuardianTypeData));
		return stringBuilder.toString();
	}

	private StringBuilder validateCompany(List<GuardianExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (GuardianExitFactoryInfoExcel vo : dataList) {
			String company = vo.getCompany();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(company)) {
					vo.setCompany(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(company).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these company is not setting in business menu,the code is : device_company");
		}
		return sb;
	}

	private StringBuilder validateGuardianType(List<GuardianExitFactoryInfoExcel> dataList, List<DictBiz> dictData) {
		StringBuilder sb = new StringBuilder();
		for (GuardianExitFactoryInfoExcel vo : dataList) {
			String deviceType = vo.getDeviceType();
			boolean flag = false;
			for (DictBiz biz : dictData) {
				if (biz.getDictValue().equalsIgnoreCase(deviceType)) {
					vo.setDeviceType(biz.getDictKey());
					flag = true;
					break;
				}
			}
			if (!flag) {
				sb.append(deviceType).append(",");
			}
		}
		if (StringUtils.isNotEmpty(sb)) {
			sb.append(" these deviceType is not setting in business menu,the code is : client_guardian_type");
		}
		return sb;
	}






}

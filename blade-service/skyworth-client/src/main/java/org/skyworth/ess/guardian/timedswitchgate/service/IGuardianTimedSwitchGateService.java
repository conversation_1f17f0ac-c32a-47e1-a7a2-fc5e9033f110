/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedswitchgate.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.guardian.timedswitchgate.entity.GuardianTimedSwitchGateEntity;
import org.skyworth.ess.guardian.timedswitchgate.vo.GuardianTimedSwitchGateVO;
import org.skyworth.ess.guardian.timedswitchgate.excel.GuardianTimedSwitchGateExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 安全卫士定时设置-开关闸设置 服务类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface IGuardianTimedSwitchGateService extends BaseService<GuardianTimedSwitchGateEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param GuardianTimedSwitchGate
	 * @return
	 */
	IPage<GuardianTimedSwitchGateVO> selectGuardianTimedSwitchGatePage(IPage<GuardianTimedSwitchGateVO> page, GuardianTimedSwitchGateVO GuardianTimedSwitchGate);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<GuardianTimedSwitchGateExcel> exportGuardianTimedSwitchGate(Wrapper<GuardianTimedSwitchGateEntity> queryWrapper);

	int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber);
}

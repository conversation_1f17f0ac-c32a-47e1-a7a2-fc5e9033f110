/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.measurementinfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 设备卫士测量信息 视图实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
public class DeviceGuardMeasurementInfoStatVO {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "A相电压，单位V 0x0276")
	private BigDecimal phaseAVoltage;
	/**
	 * A相电流，单位A 0x0260 ~0x0261
	 */
	@ApiModelProperty(value = "A相电流，单位A 0x0260 ~0x0261")
	private BigDecimal phaseACurrent;
	/**
	 * A相有功功率 0x0282
	 */
	@ApiModelProperty(value = "A相有功功率 0x0282")
	private BigDecimal phaseAPower;
	/**
	 * B相电压，单位V 0x0277
	 */
	@ApiModelProperty(value = "B相电压，单位V 0x0277")
	private BigDecimal phaseBVoltage;
	/**
	 * B相电流，单位A 0x0262 ~0x0263
	 */
	@ApiModelProperty(value = "B相电流，单位A 0x0262 ~0x0263")
	private BigDecimal phaseBCurrent;
	/**
	 * B相有功功率 0x0282
	 */
	@ApiModelProperty(value = "B相有功功率 0x0282")
	private BigDecimal phaseBPower;
	/**
	 * C相电压，单位V 0x0278
	 */
	@ApiModelProperty(value = "C相电压，单位V 0x0278")
	private BigDecimal phaseCVoltage;
	/**
	 * C相电流，单位A 0x0264 ~0x0265
	 */
	@ApiModelProperty(value = "C相电流，单位A 0x0264 ~0x0265")
	private BigDecimal phaseCCurrent;
	/**
	 * C相有功功率 0x0284
	 */
	@ApiModelProperty(value = "C相有功功率 0x0284")
	private BigDecimal phaseCPower;

	/**
	 * 漏电电流 0x02AF
	 */
	@ApiModelProperty(value = "漏电电流 0x02AF")
	private BigDecimal leakageCurrent;
	/**
	 * 实时温度 0x02AE
	 */
	@ApiModelProperty(value = "实时温度 0x02AE")
	private BigDecimal temperature;
	/**
	 * 数据上报时间2
	 */
	private String deviceDateTimeForCal;

	/**
	 * 数据上报时间3
	 */
	private String deviceDateTimeForDay;


	@ApiModelProperty(value = "设备时间")
	private String deviceDateTime;


}

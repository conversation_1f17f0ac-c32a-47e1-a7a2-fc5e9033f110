package org.skyworth.ess.lazzen.gatewayplant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time
 */
@Data
@ApiModel(value = "查询条件", description = "查询条件")
public class GatewayPlantReportQueryVO implements Serializable {
	private static final long serialVersionUID = -1;

	@ApiModelProperty(value = "站点id")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId;

	@ApiModelProperty(value = "安全卫士SN")
	private String gatewayUniqueNumber;

	@ApiModelProperty(value = "开始时间")
	private Date startDateTime;

	@ApiModelProperty(value = "结束时间")
	private Date endDateTime;

	@ApiModelProperty(value = "日期")
	private String date;

	@ApiModelProperty(value = "每日统计查询类型(1:天报表,2:月报表，不传则默认1)")
	private Integer everyDayStatQueryType;

	@ApiModelProperty(value = "状态曲线查询模块，voltage：电压,current：电流,temperature：温度,leakCurrent：漏电电流")
	private String queryPowerType;

}


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.lazzen.remotecontrolinfo.mapper.DeviceGuardRemoteControlInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceGuardRemoteControlInfoResultMap" type="org.skyworth.ess.lazzen.remotecontrolinfo.entity.DeviceGuardRemoteControlInfoEntity">
        <result column="gateway_unique_number" property="gatewayUniqueNumber"/>
        <result column="circuit_breaker_address" property="circuitBreakerAddress"/>
        <result column="id" property="id"/>
        <result column="turn_on_off" property="turnOnOff"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceGuardRemoteControlInfoPage" resultMap="deviceGuardRemoteControlInfoResultMap">
        select * from lazzen_device_guard_remote_control_info where is_deleted = 0
    </select>


    <select id="exportDeviceGuardRemoteControlInfo" resultType="org.skyworth.ess.lazzen.remotecontrolinfo.excel.DeviceGuardRemoteControlInfoExcel">
        SELECT * FROM lazzen_device_guard_remote_control_info ${ew.customSqlSegment}
    </select>

</mapper>

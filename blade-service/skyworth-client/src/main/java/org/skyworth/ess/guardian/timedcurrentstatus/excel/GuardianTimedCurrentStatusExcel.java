/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.timedcurrentstatus.excel;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 安全卫士-设备上报定时设置当前状态信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GuardianTimedCurrentStatusExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	/**
	 * 站点ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点ID")
	private Long plantId;
	/**
	 * 安全卫士SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 数据上报时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("数据上报时间")
	private Date deviceDateTime;
	/**
	 * 功率定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypePower;
	/**
	 * 开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("开始时间")
	private String startTime1;
	/**
	 * 结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束时间")
	private String endTime1;
	/**
	 * 最小功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最小功率")
	private BigDecimal minPower1;
	/**
	 * 最大功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大功率")
	private BigDecimal maxPower1;
	/**
	 * 开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("开始时间")
	private String startTime2;
	/**
	 * 结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束时间")
	private String endTime2;
	/**
	 * 最小功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最小功率")
	private BigDecimal minPower2;
	/**
	 * 最大功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大功率")
	private BigDecimal maxPower2;
	/**
	 * 开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("开始时间")
	private String startTime3;
	/**
	 * 结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束时间")
	private String endTime3;
	/**
	 * 最小功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最小功率")
	private BigDecimal minPower3;
	/**
	 * 最大功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大功率")
	private BigDecimal maxPower3;
	/**
	 * 开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("开始时间")
	private String startTime4;
	/**
	 * 结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("结束时间")
	private String endTime4;
	/**
	 * 最小功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最小功率")
	private BigDecimal minPower4;
	/**
	 * 最大功率
	 */
	@ColumnWidth(20)
	@ExcelProperty("最大功率")
	private BigDecimal maxPower4;
	/**
	 * 功率定时类型，来源业务字典:client_guardian_timing_type
	 */
	@ColumnWidth(20)
	@ExcelProperty("功率定时类型，来源业务字典:client_guardian_timing_type")
	private String timingTypeGate;
	/**
	 * 合闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("合闸时间")
	private String closingTime1;
	/**
	 * 分闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("分闸时间")
	private String openingTime1;
	/**
	 * 合闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("合闸时间")
	private String closingTime2;
	/**
	 * 分闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("分闸时间")
	private String openingTime2;
	/**
	 * 合闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("合闸时间")
	private String closingTime3;
	/**
	 * 分闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("分闸时间")
	private String openingTime3;
	/**
	 * 合闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("合闸时间")
	private String closingTime4;
	/**
	 * 分闸时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("分闸时间")
	private String openingTime4;
	/**
	 * 锁死设置（0 解锁 1 锁死）
	 */
	@ColumnWidth(20)
	@ExcelProperty("锁死设置（0 解锁 1 锁死）")
	private Integer lockSet;
	/**
	 * 重合功能设置（0 关闭 1 开启）
	 */
	@ColumnWidth(20)
	@ExcelProperty("重合功能设置（0 关闭 1 开启）")
	private Integer recloseSet;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}

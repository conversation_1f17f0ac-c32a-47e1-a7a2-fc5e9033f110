/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import org.skyworth.ess.device.entity.DeviceEverydayTotalEntity;
import org.skyworth.ess.device.vo.DeviceEverydayTotalVO;
import org.skyworth.ess.device.excel.DeviceEverydayTotalExcel;
import org.skyworth.ess.device.mapper.DeviceEverydayTotalMapper;
import org.skyworth.ess.device.service.IDeviceEverydayTotalService;
import org.skyworth.ess.device.vo.InverterReportQueryVO;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;

import java.util.List;

/**
 * 设备/智能能量变换器每日统计 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@Service
public class DeviceEverydayTotalServiceImpl extends BaseServiceImpl<DeviceEverydayTotalMapper, DeviceEverydayTotalEntity> implements IDeviceEverydayTotalService {

	@Override
	public IPage<DeviceEverydayTotalVO> selectDeviceEverydayTotalPage(IPage<DeviceEverydayTotalVO> page, DeviceEverydayTotalVO deviceEverydayTotal) {
		return page.setRecords(baseMapper.selectDeviceEverydayTotalPage(page, deviceEverydayTotal));
	}


	@Override
	public List<DeviceEverydayTotalExcel> exportDeviceEverydayTotal(Wrapper<DeviceEverydayTotalEntity> queryWrapper) {
		List<DeviceEverydayTotalExcel> deviceEverydayTotalList = baseMapper.exportDeviceEverydayTotal(queryWrapper);
		//deviceEverydayTotalList.forEach(deviceEverydayTotal -> {
		//	deviceEverydayTotal.setTypeName(DictCache.getValue(DictEnum.YES_NO, DeviceEverydayTotal.getType()));
		//});
		return deviceEverydayTotalList;
	}

	@Override
	public DeviceEverydayTotalVO appFromPvAndGrid(DeviceEverydayTotalVO queryCondition) {
		return baseMapper.appFromPvAndGrid(queryCondition);
	}

	@Override
	public List<DeviceEverydayTotalEntity> queryDailyStatisticalDataOfInverters(InverterReportQueryVO queryCondition) {
		return baseMapper.queryDailyStatisticalDataOfInverters(queryCondition);
	}

	@Override
	public List<DeviceEverydayTotalEntity> queryMonthlyStatisticalDataOfInverters(InverterReportQueryVO queryCondition) {
		return baseMapper.queryMonthlyStatisticalDataOfInverters(queryCondition);
	}
}

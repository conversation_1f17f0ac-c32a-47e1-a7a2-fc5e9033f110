/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.productinfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.lazzen.productinfo.entity.DeviceGuardProductInfoEntity;
import org.skyworth.ess.lazzen.productinfo.vo.DeviceGuardProductInfoVO;
import java.util.Objects;

/**
 * 产品信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public class DeviceGuardProductInfoWrapper extends BaseEntityWrapper<DeviceGuardProductInfoEntity, DeviceGuardProductInfoVO>  {

	public static DeviceGuardProductInfoWrapper build() {
		return new DeviceGuardProductInfoWrapper();
 	}

	@Override
	public DeviceGuardProductInfoVO entityVO(DeviceGuardProductInfoEntity deviceGuardProductInfo) {
		DeviceGuardProductInfoVO deviceGuardProductInfoVO = Objects.requireNonNull(BeanUtil.copy(deviceGuardProductInfo, DeviceGuardProductInfoVO.class));

		//User createUser = UserCache.getUser(deviceGuardProductInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceGuardProductInfo.getUpdateUser());
		//deviceGuardProductInfoVO.setCreateUserName(createUser.getName());
		//deviceGuardProductInfoVO.setUpdateUserName(updateUser.getName());

		return deviceGuardProductInfoVO;
	}


}

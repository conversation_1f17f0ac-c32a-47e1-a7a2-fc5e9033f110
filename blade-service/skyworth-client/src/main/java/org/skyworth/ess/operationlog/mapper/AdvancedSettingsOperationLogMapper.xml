<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.operationlog.mapper.AdvancedSettingsOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="advancedSettingsOperationLogResultMap" type="org.skyworth.ess.operationlog.entity.AdvancedSettingsOperationLogEntity">
        <result column="id" property="id"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="plant_id" property="plantId"/>
        <result column="source" property="source"/>
        <result column="request_body" property="requestBody"/>
        <result column="response_body" property="responseBody"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="time_zone" property="timeZone"/>
    </resultMap>


    <select id="selectAdvancedSettingsOperationLogPage" resultMap="advancedSettingsOperationLogResultMap">
        select * from advanced_settings_operation_log where is_deleted = 0
    </select>

    <select id="getLogList" resultMap="advancedSettingsOperationLogResultMap">
        select log.id, log.device_serial_number, log.plant_id, log.source, log.request_body, log.response_body,
        log.create_user_account, log.update_user_account, log.tenant_id, log.create_user, log.create_dept,
        log.create_time, log.update_user, log.update_time, log.status, log.is_deleted ,
        log.time_zone
        from advanced_settings_operation_log log
        <where>
            log.is_deleted = 0 and log.source != 'AI_MODE'
            <if test="map.plantId!=null and map.plantId!=''">
                AND log.plant_id = #{map.plantId}
            </if>
            <if test="map.deviceSerialNumber!=null and map.deviceSerialNumber!=''">
                AND log.device_serial_number = #{map.deviceSerialNumber}
            </if>
            <if test="map.status!=null and map.status!=''">
                AND log.status = #{map.status}
            </if>
            <if test="map.startTime!=null">
                AND log.create_time &gt;= #{map.startTime}
            </if>
            <if test="map.endTime!=null">
                AND log.create_time &lt;= #{map.endTime}
            </if>
            and DATEDIFF(log.create_time,CURDATE()) &lt; 365
        </where>
        order by log.id desc
    </select>
</mapper>

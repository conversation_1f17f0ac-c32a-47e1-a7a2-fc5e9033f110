<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.guardian.timedcurrentstatus.mapper.GuardianTimedCurrentStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="GuardianTimedCurrentStatusResultMap" type="org.skyworth.ess.guardian.timedcurrentstatus.entity.GuardianTimedCurrentStatusEntity">
        <result column="plant_id" property="plantId"/>
        <result column="security_guard_serial_number" property="securityGuardSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="id" property="id"/>
        <result column="timing_type_power" property="timingTypePower"/>
        <result column="start_time_1" property="startTime1"/>
        <result column="end_time_1" property="endTime1"/>
        <result column="min_power_1" property="minPower1"/>
        <result column="max_power_1" property="maxPower1"/>
        <result column="start_time_2" property="startTime2"/>
        <result column="end_time_2" property="endTime2"/>
        <result column="min_power_2" property="minPower2"/>
        <result column="max_power_2" property="maxPower2"/>
        <result column="start_time_3" property="startTime3"/>
        <result column="end_time_3" property="endTime3"/>
        <result column="min_power_3" property="minPower3"/>
        <result column="max_power_3" property="maxPower3"/>
        <result column="start_time_4" property="startTime4"/>
        <result column="end_time_4" property="endTime4"/>
        <result column="min_power_4" property="minPower4"/>
        <result column="max_power_4" property="maxPower4"/>
        <result column="timing_type_gate" property="timingTypeGate"/>
        <result column="closing_time_1" property="closingTime1"/>
        <result column="opening_time_1" property="openingTime1"/>
        <result column="closing_time_2" property="closingTime2"/>
        <result column="opening_time_2" property="openingTime2"/>
        <result column="closing_time_3" property="closingTime3"/>
        <result column="opening_time_3" property="openingTime3"/>
        <result column="closing_time_4" property="closingTime4"/>
        <result column="opening_time_4" property="openingTime4"/>
        <result column="lock_set" property="lockSet"/>
        <result column="reclose_set" property="recloseSet"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="selectGuardianTimedCurrentStatusPage" resultMap="GuardianTimedCurrentStatusResultMap">
        select * from security_guard_timed_current_status where is_deleted = 0
    </select>


    <select id="exportGuardianTimedCurrentStatus" resultType="org.skyworth.ess.guardian.timedcurrentstatus.excel.GuardianTimedCurrentStatusExcel">
        SELECT * FROM security_guard_timed_current_status ${ew.customSqlSegment}
    </select>
    <select id="getTimeByLast"
           resultMap="GuardianTimedCurrentStatusResultMap">
        select * from security_guard_timed_current_status   where security_guard_serial_number =#{deviceSn}  ORDER BY create_time  DESC  LIMIT 1

    </select>

    <update id="deleteLogicByPlantIdAndSn">
        update security_guard_timed_current_status set is_deleted=1,update_time=now() where
            plant_id = #{plantId} and security_guard_serial_number=#{securityGuardSerialNumber}
    </update>
</mapper>

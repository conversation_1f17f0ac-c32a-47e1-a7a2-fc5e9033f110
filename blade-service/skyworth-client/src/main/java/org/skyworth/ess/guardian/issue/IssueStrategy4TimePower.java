package org.skyworth.ess.guardian.issue;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.guardianplant.service.IGuardianPlantService;
import org.skyworth.ess.guardian.timedpower.entity.GuardianTimedPowerEntity;
import org.skyworth.ess.guardian.timedpower.service.IGuardianTimedPowerService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DatabaseFieldConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class IssueStrategy4TimePower extends GuardianIssueStrategy {
	public IGuardianPlantService guardianPlantService = SpringUtil.getBean(IGuardianPlantService.class);
	public IGuardianTimedPowerService guardianTimedPowerService = SpringUtil.getBean(IGuardianTimedPowerService.class);
	Integer defaultValue = 0;
	Integer dataLength = 4;
	List<String> allList = List.of("startTime", "endTime", "minPower", "maxPower");


	/**
	 * 02.拼接content内容
	 *
	 * @param issueStrategyEntity
	 * @return
	 */
	@Override
	public String assembleContent(IssueStrategyEntity issueStrategyEntity) {
		StringBuilder content = new StringBuilder();
		content.append(GuardianInstructConstants.ISSUE_CONTROL_INSTRUCTION)
			.append(GuardianInstructConstants.ISSUE_SETTING_TYPE_TIMING_POWER_PLAN_CONTROL);

		List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
		Map<String, AppAdvancedSetup.SetupItem> collect = setupItems.stream().collect(Collectors.toMap(AppAdvancedSetup.SetupItem::getDefinition, Function.identity()));

		AppAdvancedSetup.SetupItem timingTypePowerSetupItem = collect.get("timingTypePower");
		AppAdvancedSetup.SetupItem timingTypePowerCustomSetupItem = collect.get("timingTypePowerCustom");
		String timingTypePower = (String) timingTypePowerSetupItem.getData();

		// 00 bit0 星期一 bit1 星期二 bit2 星期三 bit3 星期四 bit4 星期五 bit5 星期六 bit6 星期日 bit7 每天
		if (BizConstant.CLIENT_GUARDIAN_TIMING_TYPE_CLOSE.equals(timingTypePower)) {
			content.append(BizConstant.CHAR_ZERO + BizConstant.CHAR_ZERO);
		} else if (BizConstant.CLIENT_GUARDIAN_TIMING_TYPE_CUSTOM.equals(timingTypePower)) {
			if (ObjectUtil.isNotNull(timingTypePowerCustomSetupItem)) {
				// 1,0,1,1,0,1,1 => 1011011 。拼接0=>10110110。转16进制=>B6
				String timingTypePowerCustom = (String) timingTypePowerCustomSetupItem.getData();
				// 前端传递过来的格式转换成二进制格式
				String generalSetToBinarySet = BinaryToHexUtils.generalSetToBinarySet(timingTypePowerCustom);
				timingTypePowerCustomSetupItem.setData(generalSetToBinarySet);
				String time = generalSetToBinarySet.replace(",", "") + BizConstant.CHAR_ZERO;
				//二进制转16进制
				StringBuilder stringBuilder = new StringBuilder(time);
				StringBuilder reverse = stringBuilder.reverse();
				String binaryToHex = BinaryToHexUtils.binaryToHex(String.valueOf(reverse), 2);
				content.append(binaryToHex);
			}
		} else if (BizConstant.CLIENT_GUARDIAN_TIMING_TYPE_EVERYDAY.equals(timingTypePower)) {
			content.append(BizConstant.CHAR_ZERO + BizConstant.CHAR_ONE);
		}


		// 共4组设置
		for (int i = 1; i < 5; i++) {
			// 遍历设置类型
			for (String type : allList) {
				String wholeType = type + i;
				// 如果前端传的设置项不包含这个设置项，说明前端没有这组设置项了，该组设置项则直接用默认值替代
				if (wholeType.contains("Time")) {
					if (collect.containsKey(wholeType)) {
						AppAdvancedSetup.SetupItem setupItem = collect.get(wholeType);
						Object data = setupItem.getData();
						String dataStr = Convert.toStr(data);
						String replace = dataStr.replace(":", "");
						content.append(replace);
					} else {
						content.append("000000");
					}

				}
				if (wholeType.contains("Power")) {
					if (collect.containsKey(wholeType)) {
						AppAdvancedSetup.SetupItem setupItem = collect.get(wholeType);
						Object data = setupItem.getData();
						String dataStr = Convert.toStr(data);
						String hexadecimalNumber = BinaryToHexUtils.toFourDigitHex(Convert.toInt(dataStr, defaultValue), dataLength);
						content.append(hexadecimalNumber);
					} else {
						content.append("0000");
					}
				}
			}
		}
		return content.toString();
	}


	/**
	 * 05.处理业务结果
	 *
	 * @param invokeResult
	 * @return
	 */
	@Override
	public R handleBusinessResult(R<String> invokeResult, IssueStrategyEntity issueStrategyEntity) {
		if (CommonConstant.REST_RESULT_FAIL == invokeResult.getCode()) {
			throw new BusinessException("client.guardian.issue.timepower.fail");
		} else {
			// 业务操作，更新数据库等
			List<AppAdvancedSetup.SetupItem> setupItems = issueStrategyEntity.getAppAdvancedSetups().getSetupItems();
			Map<String, AppAdvancedSetup.SetupItem> setupItemCollect = setupItems.stream()
				.collect(Collectors.toMap(AppAdvancedSetup.SetupItem::getDefinition, Function.identity()));

			for (int i = 1; i < 5; i++) {
				// 创建 UpdateWrapper 对象
				UpdateWrapper<GuardianTimedPowerEntity> updateWrapper = new UpdateWrapper<>();
				updateWrapper.eq(DatabaseFieldConstant.SECURITY_GUARD_SERIAL_NUMBER, issueStrategyEntity.getDeviceSerialNumber())
					.eq(DatabaseFieldConstant.PLANT_ID, issueStrategyEntity.getAppAdvancedSetups().getPlantId());
				for (String type : allList) {
					String wholeType = type + i;
					// 如果前端传的设置项不包含这个设置项，说明前端没有这组设置项了，该组设置项则直接用默认值替代
					if (wholeType.contains("Time")) {
						if (setupItemCollect.containsKey(wholeType)) {
							AppAdvancedSetup.SetupItem setupItem = setupItemCollect.get(wholeType);
							Object data = setupItem.getData();
							String dataStr = Convert.toStr(data);
							updateWrapper.set(CommonUtil.toDBField(type), dataStr);
						} else {
							updateWrapper.set(CommonUtil.toDBField(type), "00:00:00");
						}
					}
					if (wholeType.contains("Power")) {
						if (setupItemCollect.containsKey(wholeType)) {
							AppAdvancedSetup.SetupItem setupItem = setupItemCollect.get(wholeType);
							Object data = setupItem.getData();
							String dataStr = Convert.toStr(data);
							updateWrapper.set(CommonUtil.toDBField(type), dataStr);
						} else {
							updateWrapper.set(CommonUtil.toDBField(type), "0");
						}
					}
				}
				//同时以setSort字段作为更新条件，因为站点id+SN无法确定某一条设置项数据
				updateWrapper.eq("set_sort", i);
				guardianTimedPowerService.update(updateWrapper);
			}
			// 更新对应站点信息
			UpdateWrapper<GuardianPlantEntity> guardianPlantEntityUpdateWrapper = new UpdateWrapper<>();
			guardianPlantEntityUpdateWrapper.eq(DatabaseFieldConstant.SECURITY_GUARD_SERIAL_NUMBER, issueStrategyEntity.getDeviceSerialNumber())
				.eq(DatabaseFieldConstant.PLANT_ID, issueStrategyEntity.getAppAdvancedSetups().getPlantId());
			AppAdvancedSetup.SetupItem timingTypePowerSetupItem = setupItemCollect.get("timingTypePower");
			Object timingTypePower = timingTypePowerSetupItem.getData();
			AppAdvancedSetup.SetupItem timingTypePowerCustomSetupItem = setupItemCollect.get("timingTypePowerCustom");
			if (ObjectUtil.isNotNull(timingTypePowerCustomSetupItem)) {
				Object timingTypePowerCustom = timingTypePowerCustomSetupItem.getData();
				guardianPlantEntityUpdateWrapper.set("timing_type_power_custom", timingTypePowerCustom);
			}
			guardianPlantEntityUpdateWrapper.set("timing_type_power", timingTypePower);
			guardianPlantService.update(guardianPlantEntityUpdateWrapper);

			return R.success("setup success");
		}
	}
}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.battery.entity.BatteryStatusExtendEntity;
import org.skyworth.ess.battery.vo.BatteryStatusExtendVO;
import java.util.Objects;

/**
 * 电池状态扩展表，每个储能一行 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public class BatteryStatusExtendWrapper extends BaseEntityWrapper<BatteryStatusExtendEntity, BatteryStatusExtendVO>  {

	public static BatteryStatusExtendWrapper build() {
		return new BatteryStatusExtendWrapper();
 	}

	@Override
	public BatteryStatusExtendVO entityVO(BatteryStatusExtendEntity statusExtend) {
		BatteryStatusExtendVO statusExtendVO = Objects.requireNonNull(BeanUtil.copy(statusExtend, BatteryStatusExtendVO.class));

		//User createUser = UserCache.getUser(statusExtend.getCreateUser());
		//User updateUser = UserCache.getUser(statusExtend.getUpdateUser());
		//statusExtendVO.setCreateUserName(createUser.getName());
		//statusExtendVO.setUpdateUserName(updateUser.getName());

		return statusExtendVO;
	}


}

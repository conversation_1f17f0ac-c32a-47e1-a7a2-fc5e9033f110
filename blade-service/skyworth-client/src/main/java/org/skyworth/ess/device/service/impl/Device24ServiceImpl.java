/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.app.vo.AppAdvancedSetup;
import org.skyworth.ess.device.client.IDeviceIssueBiz;
import org.skyworth.ess.device.entity.Constants;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.Device24Entity;
import org.skyworth.ess.device.excel.Device24Excel;
import org.skyworth.ess.device.mapper.Device24Mapper;
import org.skyworth.ess.device.service.IDevice24Service;
import org.skyworth.ess.device.vo.Device24VO;
import org.skyworth.ess.plant.entity.WifiStickPlantEntity;
import org.skyworth.ess.plant.service.IWifiStickPlantService;
import org.springblade.common.constant.I18nMsgCode;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Device24 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Service
public class Device24ServiceImpl extends BaseServiceImpl<Device24Mapper, Device24Entity> implements IDevice24Service {

	private static final int num=20;

	@Resource
	IDeviceIssueBiz deviceIssueBiz;

	@Resource
	private IWifiStickPlantService wifiStickPlantService;

	@Resource
	Device24Mapper device24Mapper;

	private BladeRedis redis;

	@Override
	public IPage<Device24VO> selectDevice24Page(IPage<Device24VO> page, Device24VO Device24) {
		return page.setRecords(baseMapper.selectDevice24Page(page, Device24));
	}


	@Override
	public List<Device24Excel> exportDevice24(Wrapper<Device24Entity> queryWrapper) {
		List<Device24Excel> Device24List = baseMapper.exportDevice24(queryWrapper);
		//Device24List.forEach(Device24 -> {
		//	Device24.setTypeName(DictCache.getValue(DictEnum.YES_NO, Device24.getType()));
		//});
		return Device24List;
	}

	@Override
	public R<String> issueAdvancedSetup(AppAdvancedSetup deviceAdvancedSetup) {
		String currentLanguage = CommonUtil.getCurrentLanguage();
		JSONObject issueObj = new JSONObject();
		LambdaQueryWrapper<WifiStickPlantEntity> wifiEq = Wrappers.<WifiStickPlantEntity>query().lambda()
			.eq(WifiStickPlantEntity::getPlantId, deviceAdvancedSetup.getPlantId())
			.eq(WifiStickPlantEntity::getDeviceSerialNumber,deviceAdvancedSetup.getDeviceSerialNumber());  //查出在线设备
		List<WifiStickPlantEntity> stickPlantEntityList=wifiStickPlantService.list(wifiEq);

		if(!stickPlantEntityList.isEmpty()) {
			String requestId= TimeUtils.generateRequestId();
			issueObj.put("deviceSn",deviceAdvancedSetup.getDeviceSerialNumber());
			issueObj.put("requestId",requestId);
			issueObj.put("topic", Constants.SETTING_ISSUE);
			JSONArray objects = new JSONArray();
			List<AppAdvancedSetup.SetupItem> setupItems = deviceAdvancedSetup.getSetupItems();
			for (AppAdvancedSetup.SetupItem setupItem : setupItems) {
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("address",setupItem.getAddress());
				jsonObject.put("data",setupItem.getData());
				jsonObject.put("len",setupItem.getLen());
				objects.add(jsonObject);
			}
            issueObj.put("msg",objects);


			Map<String,String> res=deviceIssueBiz.setting(issueObj);

			if(res!=null){
				R<String> r = new R<>();
				if(res.containsKey("200")) { //下发成功
					return R.success(I18nMsgCode.SKYWORTH_CLIENT_DEVICE_100123.autoGetMessage(currentLanguage));
				}else if (res.containsKey("401")){
					String errorAddress = res.get("401");
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100027.getCode());
					r.setMsg(errorAddress);
					return r;
				} else if (res.containsKey("402")){
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100024.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100024.autoGetMessage(currentLanguage));
					return r;
				}else if (res.containsKey("400")){
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100025.getCode());
					res.forEach((key, value) -> r.setMsg(value));
					return r;
				}else {
					r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100023.getCode());
					r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100023.autoGetMessage(currentLanguage));
					return r;
				}
			}
		}
		R<String> r = new R<>();
		r.setCode(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100024.getCode());
		r.setMsg(I18nMsgCode.SKYWORTH_CLIENT_INVERTER_100024.autoGetMessage(currentLanguage));
		return r;
	}

	@Override
	public HashMap<String, Object> getAllAdvancedSetup(Long plantId, String deviceSerialNumber) {
		HashMap<String, Object> allAdvancedSetup = device24Mapper.getAllAdvancedSetup(plantId, deviceSerialNumber);
		if (ObjectUtil.isEmpty(allAdvancedSetup)) {
			allAdvancedSetup = device24Mapper.getAllAdvancedSetupIsDelete(plantId,deviceSerialNumber);
		}
		return allAdvancedSetup;
	}

	@Override
	public int updateSetup(Map<String, Object> device24, Long plantId, String deviceSerialNumber) {
		return device24Mapper.updateSetup(device24,plantId,deviceSerialNumber);
	}
	@Override
	public List<Device24Entity> getDevice24Info(List<Device24Entity> list) {
		return device24Mapper.getDevice24Info(list);
	}

	@Override
	public void updateInverterControl(Device24Entity device24Entity) {
		device24Mapper.updateInverterControl(device24Entity);
	}

	@Override
	public Map<String, Object> getInverterControl(Long plantId, String deviceSerialNumber) {
		return device24Mapper.getInverterControl(plantId,deviceSerialNumber);
	}

	@Override
	public Map<String, Object> getInverterControlIsDelete(Long plantId, String deviceSerialNumber) {
		return device24Mapper.getInverterControlIsDelete(plantId,deviceSerialNumber);
	}

	@Override
	public Map<String, Object> getInverterModeTimeBase(Long plantId, String deviceSerialNumber) {
		Map<String, Object> inverterModeTimeBase = device24Mapper.getInverterModeTimeBase(plantId, deviceSerialNumber);
		if (ObjectUtil.isEmpty(inverterModeTimeBase)) {
			inverterModeTimeBase = device24Mapper.getInverterModeTimeBaseIsDelete(plantId,deviceSerialNumber);
		}
		return inverterModeTimeBase;
	}

	@Override
	public int deleteByPlantId(Long plantId, String deviceSerialNumber) {
		LambdaUpdateWrapper<Device24Entity> update = Wrappers.<Device24Entity>update().lambda()
			.set(Device24Entity::getIsDeleted, 1)
			.eq(Device24Entity::getPlantId, plantId)
			.eq(Device24Entity::getDeviceSerialNumber, deviceSerialNumber);
		return device24Mapper.delete(update);
	}
}

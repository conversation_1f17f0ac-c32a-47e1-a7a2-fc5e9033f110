package org.skyworth.ess.lazzen.analysisdata.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.lazzen.productinfo.service.IDeviceGuardProductInfoService;
import org.skyworth.ess.util.HumpConvert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.tool.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 产品信息
 */
@Service("lazzenProductInfoServiceImpl" )
@Slf4j
public class LazzenProductInfoServiceImpl extends LazzenAbstractService {
    @Autowired
    DorisSinkService dorisSinkService;
    @Override
    public void saveData(List<JSONObject> listMap) {
        log.info("lazzenProductInfoServiceImpl save data : {}", listMap);
        List<JSONObject> tableFieldsJSONObject = new ArrayList<>();
        for(JSONObject jsonObject:listMap) {
            JSONObject newObj = HumpConvert.convertKeysToUnderscore(jsonObject);
            IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
            Number number = identifierGenerator.nextId(new Object());
            newObj.put("id", number.longValue());
            String currentTime= TimeUtils.getCurrentTime();
            newObj.put("create_time", currentTime);
            newObj.put("update_time", currentTime);
			newObj.put("tenant_id", CommonConstant.CLIENT_TENANT_ID);
			newObj.put("status", 0);
			newObj.put("is_deleted", 0);
            tableFieldsJSONObject.add(newObj);
        }

        dorisSinkService.write(tableFieldsJSONObject,"lazzen_device_guard_product_info");
        log.info("save lazzen_device_guard_product_info end ");
    }
}

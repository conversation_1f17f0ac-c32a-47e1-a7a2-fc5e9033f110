/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.exitfactory.excel;


import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 安全卫士储能出厂信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class GuardianExitFactoryInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 安全卫士SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全卫士SN")
	private String securityGuardSerialNumber;
	/**
	 * 设备型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备型号")
	private String deviceType;
	/**
	 * 卡号
	 */
	@ColumnWidth(20)
	@ExcelProperty("卡号")
	private String simCardNumber;
	/**
	 * 厂家
	 */
	@ColumnWidth(20)
	@ExcelProperty("厂家")
	private String company;
	/**
	 * 质保年限
	 */
	@ColumnWidth(20)
	@ExcelProperty("质保年限")
	private Integer qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("出厂日期")
	private LocalDate exitFactoryDate;
}

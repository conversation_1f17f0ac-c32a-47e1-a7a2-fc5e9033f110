package org.skyworth.ess.agreement.balconyPv;

import org.skyworth.ess.agreement.util.AbstractDeviceControlService;
import org.skyworth.ess.agreement.util.ProtocolParser;
import org.skyworth.ess.agreement.util.ProtocolParserFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName BalconyPvControlServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/7/31 17:14
 */
@Service
public class BalconyPvControlServiceImpl  extends AbstractDeviceControlService {

	public BalconyPvControlServiceImpl(ProtocolParserFactory parserFactory) {
		super(parserFactory);
	}

	@Override
	public boolean sendControlCommand(String company, String deviceId, String command, Map<String, Object> params) {
		// 1. 获取协议解析器
		ProtocolParser parser = getParser(company);

		// 2. 验证设备ID
		if (!parser.validateDeviceId(deviceId)) {
			throw new IllegalArgumentException("无效的设备ID: " + deviceId);
		}
		// 3. 解析命令为特定格式
		String huaweiCommand = parser.parseCommand(command, params);

		// 4. 构建MQTT主题并推送
		String topic = buildTopic(company, deviceId);
		pushToMqtt(topic, huaweiCommand);
		return true;
	}


	@Override
	public int batchSendControlCommand(String company, List<String> deviceIds, String command, Map<String, Object> params) {
		int successCount = 0;
		ProtocolParser parser = getParser(company);
		String parsedCommand = parser.parseCommand(command, params);

		for (String deviceId : deviceIds) {
			try {
				if (parser.validateDeviceId(deviceId)) {
					String topic = buildTopic(company, deviceId);
					pushToMqtt(topic, parsedCommand);
					successCount++;
				}
			} catch (Exception e) {
				// 记录日志
				System.err.println("设备控制下发失败: " + deviceId + ", 错误: " + e.getMessage());
			}
		}

		return successCount;
	}

	@Override
	public List<String> getSupportedProtocols() {
		return List.of("huawei", "huawei-v2");
	}
}

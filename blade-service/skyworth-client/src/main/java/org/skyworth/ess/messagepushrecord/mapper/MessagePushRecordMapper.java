/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.messagepushrecord.mapper;

import org.skyworth.ess.messagepushrecord.entity.MessagePushRecordEntity;
import org.skyworth.ess.messagepushrecord.vo.MessagePushRecordVO;
import org.skyworth.ess.messagepushrecord.excel.MessagePushRecordExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 消息推送日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
public interface MessagePushRecordMapper extends BaseMapper<MessagePushRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messagePushRecord
	 * @return
	 */
	List<MessagePushRecordVO> selectMessagePushRecordPage(IPage page, MessagePushRecordVO messagePushRecord);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<MessagePushRecordExcel> exportMessagePushRecord(@Param("ew") Wrapper<MessagePushRecordEntity> queryWrapper);

}

package org.skyworth.ess.guardian.issue;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;

@Slf4j
public class IssueStrategy4HostIp extends GuardianIssueStrategy {


	/**
	 * 02.拼接content内容
	 *
	 * @param issueStrategyEntity
	 * @return
	 */
	@Override
	public String assembleContent(IssueStrategyEntity issueStrategyEntity) {
		JSONObject dataObject = issueStrategyEntity.getDataObject();
		String ip = dataObject.getString("ip");
		String port = dataObject.getString("port");
		StringBuilder content = new StringBuilder();
		content.append(GuardianInstructConstants.ISSUE_CONTROL_INSTRUCTION)
			.append(GuardianInstructConstants.ISSUE_SETTING_TYPE_HOST_IP);
		content.append("00");
		String[] ipAddress = ip.split("\\.");
		content.append(BinaryToHexUtils.toTwoDigitHex(Integer.parseInt(ipAddress[0])));
		content.append("00");
		content.append(BinaryToHexUtils.toTwoDigitHex(Integer.parseInt(ipAddress[1])));
		content.append("00");
		content.append(BinaryToHexUtils.toTwoDigitHex(Integer.parseInt(ipAddress[2])));
		content.append("00");
		content.append(BinaryToHexUtils.toTwoDigitHex(Integer.parseInt(ipAddress[3])));
		content.append(port);

		return content.toString();
	}


	/**
	 * 05.处理业务结果
	 *
	 * @param invokeResult
	 * @return
	 */
	@Override
	public R handleBusinessResult(R<String> invokeResult,IssueStrategyEntity issueStrategyEntity) {
		if (CommonConstant.REST_RESULT_FAIL == invokeResult.getCode()) {
			throw new BusinessException("client.guardian.issue.ip.fail");
		} else {
			return R.success("");
		}
	}
}

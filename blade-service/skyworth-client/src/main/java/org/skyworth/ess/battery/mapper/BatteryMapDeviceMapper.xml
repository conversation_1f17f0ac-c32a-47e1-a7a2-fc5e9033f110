<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.battery.mapper.BatteryMapDeviceMapper">

    <resultMap id="BatteryMapDeviceResultMap" type="org.skyworth.ess.battery.vo.BatteryMapDeviceVO">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="battery_serial_number" property="batterySerialNumber"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="battery_type" property="batteryType"/>
        <result column="rated_battery_voltage" property="ratedBatteryVoltage"/>
        <result column="rated_battery_capacity" property="ratedBatteryCapacity"/>
        <result column="rated_battery_energy" property="ratedBatteryEnergy"/>
        <result column="single_capacity" property="singleCapacity"/>
        <result column="single_series_paralleling_number" property="singleSeriesParallelingNumber"/>
        <result column="device_type" property="deviceType"/>
        <result column="battery_match_device_flag" property="batteryMatchDeviceFlag"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BatteryMapDeviceResultMap">
        select bmd.id,
        bmd.plant_id,
        bmd.device_serial_number,
        bmd.battery_serial_number,
        bmd.create_user_account,
        bmd.update_user_account,
        bmd.create_user,
        bmd.create_time,
        bmd.update_user,
        bmd.update_time,
        bmd.status,
        befi.battery_type,
        befi.rated_battery_voltage,
        befi.rated_battery_capacity,
        befi.rated_battery_energy,
        befi.single_capacity,
        befi.single_series_paralleling_number
        from battery_map_device bmd
        left join battery_exit_factory_info befi on
        bmd.battery_serial_number = befi.battery_serial_number
        where bmd.is_deleted = 0
        and befi.is_deleted = 0
        and bmd.id = #{batteryMapDeviceId}
    </select>

    <select id="queryPage" resultType="org.skyworth.ess.battery.vo.BatteryPageResultVO">
        select
        pt.id as plantId,
        wsp.device_serial_number as deviceSerialNumber,
        pt.plant_name as plantName,
        bse.battery_status as status,
        bmd.battery_energy_storage_number as batteryEnergyStorageNumber,
        bcs.battery_power as batteryPower,
        bcs.battery_power2 as batteryPower2,
        pt.create_user_account as createUserAccount,
        pt.battery_number as batteryNumber,
        pt.operation_company_id as operationCompanyId,
        pt.operation_user_id as operationUserId,
        pt.create_user as createUser,
        bmd.battery_serial_number as batterySerialNumber,
        bse.exist_user_type_alarm as existUserTypeAlarm,
        bse.exist_agent_type_alarm as existAgentTypeAlarm
        from  plant pt
        left join wifi_stick_plant wsp on wsp.plant_id = pt.id
        left join battery_current_status bcs on pt.id = bcs.plant_id
        left join battery_map_device bmd on bmd.plant_id = pt.id
        left join battery_status_extend bse on bse.plant_id =pt.id and bse.battery_energy_storage_number =bmd.battery_energy_storage_number
        where
        <choose>
            <when test='queryPageCondition.deleteFlag!=null and queryPageCondition.deleteFlag!="" and queryPageCondition.deleteFlag=="1" '>
                wsp.device_serial_number in
                ( select device_serial_number from battery_map_device bmd where bmd.plant_id = pt.id and bmd.is_deleted = 1)
                and pt.is_deleted = 1
                and wsp.is_deleted = 1
                and bmd.is_deleted = 1
            </when>
            <otherwise>
                wsp.device_serial_number in
                ( select device_serial_number from battery_map_device bmd where bmd.plant_id = pt.id and bmd.is_deleted = 0)
                and pt.is_deleted = 0
                and wsp.is_deleted = 0
                and bmd.is_deleted = 0
            </otherwise>
        </choose>
        <if test="queryPageCondition.countryCode!=null and queryPageCondition.countryCode!=''">
            and pt.country_code = #{queryPageCondition.countryCode}
        </if>
        <if test="queryPageCondition.provinceCode!=null and queryPageCondition.provinceCode!=''">
            and pt.province_code = #{queryPageCondition.provinceCode}
        </if>
        <if test="queryPageCondition.plantName!=null and queryPageCondition.plantName!=''">
            and pt.plant_name like CONCAT(#{queryPageCondition.plantName}, '%')
        </if>
        <if test="queryPageCondition.deviceSerialNumber!=null and queryPageCondition.deviceSerialNumber!=''">
            and (
            wsp.device_serial_number like CONCAT(#{queryPageCondition.deviceSerialNumber}, '%')
            or bmd.battery_serial_number like CONCAT(#{queryPageCondition.deviceSerialNumber}, '%')
            )
        </if>
        <if test="queryPageCondition.status != null">
            <if test="queryPageCondition.status == 1">
                and (bse.battery_status = 1
                <choose>
                    <when test="userType=='user'">
                        or (bse.battery_status = 4 and bse.exist_user_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0))
                        <![CDATA[ < ]]> 0 )
                    </when>
                    <when test="userType=='agent'">
                        or (bse.battery_status = 4 and bse.exist_agent_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0))
                        <![CDATA[ < ]]> 0 )
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
                )
            </if>

            <if test="queryPageCondition.status == 2">
                and (bse.battery_status = 2
                <choose>
                    <when test="userType=='user'">
                        or (bse.battery_status = 4 and bse.exist_user_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0)) <![CDATA[ > ]]> 0 )
                    </when>
                    <when test="userType=='agent'">
                        or (bse.battery_status = 4 and bse.exist_agent_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0)) <![CDATA[ > ]]> 0 )
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
                )
            </if>

            <if test="queryPageCondition.status == 3">
                and (bse.battery_status = 3
                <choose>
                    <when test="userType=='user'">
                        or (bse.battery_status = 4 and bse.exist_user_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0)) <![CDATA[ = ]]> 0 )
                    </when>
                    <when test="userType=='agent'">
                        or (bse.battery_status = 4 and bse.exist_agent_type_alarm = 0 and
                        if(bse.exist_agent_type_alarm = 1, ifnull(bcs.battery_power,0), ifnull(bcs.battery_power2,0)) <![CDATA[ = ]]> 0 )
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
                )
            </if>
            <if test="queryPageCondition.status == 4">
                and bse.battery_status = 4
                <choose>
                    <when test="userType=='user'">
                        and bse.exist_user_type_alarm = 1
                    </when>
                    <when test="userType=='agent'">
                        and bse.exist_agent_type_alarm = 1
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
            </if>
            <if  test="queryPageCondition.status == 5">
                and bse.battery_status = 5
            </if>
        </if>

        <if test="queryPageCondition.createUserAccount!=null and queryPageCondition.createUserAccount!=''">
            and pt.create_user_account like CONCAT(#{queryPageCondition.createUserAccount},'%')
        </if>

        <if test="queryPageCondition.deptId!=null and queryPageCondition.deptId!= ''">
            and (
            find_in_set(pt.operation_company_id ,#{queryPageCondition.deptId}) != 0
            or ( pt.create_user =#{queryPageCondition.createUser} and pt.operation_company_id is null )
            or ( pt.create_user =#{queryPageCondition.createUser} and find_in_set(pt.operation_company_id ,#{queryPageCondition.deptId}) = 0 )
            )
            and pt.id not in (select plant_id  from plant_agent_unauthorized_user where unauthorized_user_id=#{queryPageCondition.createUser} and is_deleted =0)
        </if>

        <if test="queryPageCondition.operationCompanyIds!=null and queryPageCondition.operationCompanyIds.size >0">
            and pt.operation_company_id in
            <foreach collection="queryPageCondition.operationCompanyIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryPageCondition.operationUserIds!=null and queryPageCondition.operationUserIds.size >0" >
            and pt.operation_user_id in
            <foreach collection="queryPageCondition.operationUserIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryPageCondition.userAndPhoneIds!=null and queryPageCondition.userAndPhoneIds.size >0" >
            and pt.create_user in
            <foreach collection="queryPageCondition.userAndPhoneIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by pt.update_time desc,pt.id desc
    </select>

    <sql id="QUERY_BATTERY_PAGE_SQL">
        from
        plant pt
        left join battery_map_device bmdt on pt.id = bmdt.plant_id
        left join wifi_stick_plant wsp on wsp.plant_id = pt.id
        left join battery_exit_factory_info bi on bmdt.battery_serial_number = bi.battery_serial_number
        where
        <choose>
            <when test='deleteFlag!=null and deleteFlag!="" and deleteFlag=="1" '>
                wsp.device_serial_number in
                ( select device_serial_number from battery_map_device bmd where bmd.plant_id = pt.id and bmd.is_deleted = 1)
                and bmdt.is_deleted = 1
                and pt.is_deleted = 1
            </when>
            <otherwise>
                wsp.device_serial_number in
                ( select device_serial_number from battery_map_device bmd where bmd.plant_id = pt.id and bmd.is_deleted = 0)
                and bmdt.is_deleted = 0
                and pt.is_deleted = 0
            </otherwise>
        </choose>
        <if test="plantIds!=null and plantIds.size >0">
            and pt.id in
            <foreach collection="plantIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="listSummary" resultType="java.util.Map">
        select
        count(1) as totalCount,
        ROUND(IFNULL(SUM(CAST(IFNULL(bi.rated_battery_energy, '0') AS DECIMAL(10, 2))), 0), 1) as totalBatteryEnergy
        <include refid="QUERY_BATTERY_PAGE_SQL"/>
    </select>

    <select id="installation" resultType="org.skyworth.ess.battery.entity.BatteryDeviceInstallVO">
        select
        pt.install_user_id as installUserId,
        pt.install_team_id as installTeamId,
        date (bmdt.create_time) as installDate,
        bi.quality_guarantee_year as qualityGuaranteeYear,
        bi.warranty_start_date as qualityGuaranteeBeginDate,
        DATE_ADD(date (bi.warranty_start_date),interval bi.quality_guarantee_year year) as qualityGuaranteeEndDate,
        bmdt.battery_serial_number as
        batterySerialNumber,bmdt.device_serial_number as deviceSerialNumber, pt.create_user as createUser
        from
        plant pt
        left join battery_map_device bmdt
        on
        pt.id = bmdt.plant_id
        left join battery_exit_factory_info bi on
        bmdt.battery_serial_number = bi.battery_serial_number and bi.is_deleted = 0
        where
        pt.is_deleted = 0
        and bmdt.is_deleted = 0
        and pt.id = #{queryCondition.plantId}
        and bmdt.device_serial_number = #{queryCondition.deviceSerialNumber}
        and bmdt.battery_energy_storage_number = #{queryCondition.batteryEnergyStorageNumber}
    </select>

    <select id="installationIsDelete" resultType="org.skyworth.ess.battery.entity.BatteryDeviceInstallVO">
        select pt.install_team as installTeam,
        date (bmdt.create_time) as installDate,
        bi.quality_guarantee_year as qualityGuaranteeYear,
        bi.warranty_start_date as qualityGuaranteeBeginDate,
        DATE_ADD(date (bi.warranty_start_date),interval bi.quality_guarantee_year year) as qualityGuaranteeEndDate,
        bmdt.battery_serial_number as
        batterySerialNumber,bmdt.device_serial_number as deviceSerialNumber, pt.create_user as createUser
        from
        plant pt
        left join battery_map_device bmdt
        on
        pt.id = bmdt.plant_id
        left join battery_exit_factory_info bi on
        bmdt.battery_serial_number = bi.battery_serial_number and bi.is_deleted = 0
        where
        pt.is_deleted = 1
        and bmdt.is_deleted = 1
        and pt.id = #{queryCondition.plantId}
        and bmdt.device_serial_number = #{queryCondition.deviceSerialNumber}
        and bmdt.battery_energy_storage_number = #{queryCondition.batteryEnergyStorageNumber}
    </select>

    <update id="batchDeleteLogicByPlantId">
        update battery_map_device set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now() where
        plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryOwnerData" resultMap="BatteryMapDeviceResultMap">
        select s.plant_id, s.device_serial_number, s.battery_serial_number
        from battery_map_device s
        where s.is_deleted = 0
        and s.create_user = #{createUser}
    </select>

    <update id="updateDataByCondition">
        update battery_map_device set
        <if test="params.isDeleted!=null ">
            is_deleted = #{params.isDeleted},
        </if>
        <if test="params.updateUser!=null ">
            update_user = #{params.updateUser},
        </if>
        <if test="params.updateUserAccount!=null ">
            update_user_account = #{params.updateUserAccount},
        </if>
        update_time=now()
        where is_deleted = 0
        <if test="params.createUser!=null ">
            and create_user = #{params.createUser}
        </if>
    </update>
    <select id="queryListByPlantId" resultMap="BatteryMapDeviceResultMap">
        select s.plant_id,s.device_serial_number,s.battery_serial_number from battery_map_device s where s.is_deleted =
        0 and s.plant_id in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryBatteryDeviceInfo" resultMap="BatteryMapDeviceResultMap">
        select b.battery_type ,d.device_type,m.plant_id ,m.device_serial_number ,m.battery_serial_number,
        m.battery_match_device_flag
        from battery_map_device m left join battery_exit_factory_info b on m.battery_serial_number
        =b.battery_serial_number and m.is_deleted =0
        left join device_exit_factory_info d on m.device_serial_number =d.device_serial_number and d.is_deleted =0
        where m.is_deleted =0
        <if test="params.id!=null ">
            and m.id = #{params.id}
        </if>
        <if test="params.deviceSerialNumber!=null ">
            and m.device_serial_number = #{params.deviceSerialNumber}
        </if>
    </select>
    <select id="queryBatteryCapacity" resultType="org.skyworth.ess.battery.vo.BatteryCapacityVo">
        select
        ROUND(sum(ifnull(befi.rated_battery_energy,0)),2) as capacity ,   bmd.device_serial_number as deviceSn
        from battery_map_device bmd
        left join battery_exit_factory_info befi on
        bmd.battery_serial_number = befi.battery_serial_number
        and befi.is_deleted = 0
        where bmd.is_deleted = 0
        <if test="deviceSn!=null and deviceSn.size >0">
            and bmd.device_serial_number in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by bmd.device_serial_number

    </select>

    <select id="queryListByPlantIdAndSn" resultType="org.skyworth.ess.battery.entity.BatteryMapDeviceEntity">
        select s.plant_id,s.device_serial_number,s.battery_serial_number from battery_map_device s where s.is_deleted =
        0 and s.plant_id = #{plantId} and s.device_serial_number = #{deviceSerialNumber}
    </select>

    <select id="queryListByPlantIdAndSnAndBattery" resultType="org.skyworth.ess.battery.entity.BatteryMapDeviceEntity">
        select s.plant_id,s.device_serial_number,s.battery_serial_number,s.battery_energy_storage_number
        from battery_map_device s inner join battery_exit_factory_info befi on s.battery_serial_number = befi.battery_serial_number and befi.is_deleted = 0
        where s.is_deleted = 0 and s.plant_id = #{plantId} and s.device_serial_number = #{deviceSerialNumber}
        and s.battery_energy_storage_number = #{batteryEnergyStorageNumber}
    </select>

    <select id="batchDeleteLogicByPlantIdAndSn">
        update battery_map_device set is_deleted=1,update_user_account=#{updateUserAccount},update_time=now()
        where plant_id = #{plantId} and device_serial_number = #{deviceSerialNumber}
    </select>

    <select id="queryBatteryCapacityByBatterySns" resultType="org.skyworth.ess.battery.vo.BatteryCapacityVo">
        select
        ifnull(befi.rated_battery_energy,0) as capacity ,befi.battery_serial_number as batterySn
        from battery_exit_factory_info befi
        where befi.is_deleted = 0
        <if test="batterySns!=null and batterySns.size >0">
            and befi.battery_serial_number in
            <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>


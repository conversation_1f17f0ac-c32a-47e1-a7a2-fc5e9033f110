/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.setItem.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;

import javax.validation.Valid;

import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.setItem.entity.SetItemConfigEntity;
import org.skyworth.ess.setItem.vo.SetItemConfigVO;
import org.skyworth.ess.setItem.excel.SetItemConfigExcel;
import org.skyworth.ess.setItem.wrapper.SetItemConfigWrapper;
import org.skyworth.ess.setItem.service.ISetItemConfigService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;
import java.util.List;
import java.util.Set;
import javax.servlet.http.HttpServletResponse;

/**
 * APP设置项配置 控制器
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/setItemConfig")
@Api(value = "APP设置项配置", tags = "APP设置项配置接口")
public class SetItemConfigController extends BladeController {

	private final ISetItemConfigService setItemConfigService;

	private final BladeRedis bladeRedis;

	/**
	 * APP设置项配置 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入setItemConfig")
	public R<SetItemConfigVO> detail(SetItemConfigEntity setItemConfig) {
		SetItemConfigEntity detail = setItemConfigService.getOne(Condition.getQueryWrapper(setItemConfig));
		return R.data(SetItemConfigWrapper.build().entityVO(detail));
	}

	/**
	 * APP设置项配置 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入setItemConfig")
	public R<IPage<SetItemConfigVO>> list(@ApiIgnore @RequestParam Map<String, Object> setItemConfig, Query query) {
		LambdaQueryWrapper<SetItemConfigEntity> lambda = Condition.getQueryWrapper(setItemConfig, SetItemConfigEntity.class).lambda();
		lambda.orderByAsc(SetItemConfigEntity::getSetItemSort).orderByDesc(SetItemConfigEntity::getUpdateTime);
		IPage<SetItemConfigEntity> pages = setItemConfigService.page(Condition.getPage(query),lambda);
		return R.data(SetItemConfigWrapper.build().pageVO(pages));
	}

	/**
	 * APP设置项配置 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入setItemConfig")
	public R<IPage<SetItemConfigVO>> page(SetItemConfigVO setItemConfig, Query query) {
		IPage<SetItemConfigVO> pages = setItemConfigService.selectSetItemConfigPage(Condition.getPage(query), setItemConfig);
		return R.data(pages);
	}

	@GetMapping("/getSetItemDetail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "获取设置项详情", notes = "传入setItemConfig")
	public R<SetItemConfigEntity> page(SetItemConfigVO setItemConfig) {
		SetItemConfigEntity one = setItemConfigService.getSetItemDetail(setItemConfig);
		return R.data(one);
	}

	/**
	 * APP设置项配置 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入setItemConfig")
	public R save(@Valid @RequestBody SetItemConfigEntity setItemConfig) {
		delBatchCacheKey();
		return R.status(setItemConfigService.saveSetItem(setItemConfig));
	}

	/**
	 * APP设置项配置 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入setItemConfig")
	public R update(@Valid @RequestBody SetItemConfigEntity setItemConfig) {
		delBatchCacheKey();
		return R.status(setItemConfigService.updateSetItemById(setItemConfig));
	}

	/**
	 * 根据设备型号，新增设置项
	 * @param setItemConfig
	 */
	@PostMapping("/generateAnotherModelSetItem")
	public void generateAnotherModelSetItem(@RequestBody SetItemConfigEntity setItemConfig) {
		setItemConfigService.generateAnotherModelSetItem(setItemConfig);
	}

	/**
	 * APP设置项配置 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入setItemConfig")
	public R submit(@Valid @RequestBody SetItemConfigEntity setItemConfig) {
		delBatchCacheKey();
		return R.status(setItemConfigService.saveOrUpdate(setItemConfig));
	}

	/**
	 * APP设置项配置 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		delBatchCacheKey();
		return R.status(setItemConfigService.removeSetItem(ids));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-setItemConfig")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入setItemConfig")
	public void exportSetItemConfig(@ApiIgnore @RequestParam Map<String, Object> setItemConfig, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SetItemConfigEntity> queryWrapper = Condition.getQueryWrapper(setItemConfig, SetItemConfigEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SetItemConfig::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SetItemConfigEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SetItemConfigExcel> list = setItemConfigService.exportSetItemConfig(queryWrapper);
		ExcelUtil.export(response, "APP设置项配置数据" + DateUtil.time(), "APP设置项配置数据表", list, SetItemConfigExcel.class);
	}


	/**
	 * 导入用户
	 */
	@PostMapping("/import")
	public R importUser(MultipartFile file, Integer isCovered) {
		SetItemConfigServiceImporter importer = new SetItemConfigServiceImporter(setItemConfigService, isCovered == 1);
		ExcelUtil.save(file, importer, SetItemConfigExcel.class);
		return R.success("操作成功");
	}

	private void delBatchCacheKey(){
		Set<String> allAddressMapDefinitionByAddress = bladeRedis.keys("deviceSetItemConfig*");
		bladeRedis.del(allAddressMapDefinitionByAddress);
	}
}

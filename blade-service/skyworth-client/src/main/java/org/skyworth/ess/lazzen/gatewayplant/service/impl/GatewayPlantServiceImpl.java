/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.gatewayplant.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.constant.SecurityGuardStatusEnum;
import org.skyworth.ess.device.vo.QueryPowerTypeEnum;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.entity.GatewayPlantEntity;
import org.skyworth.ess.lazzen.gatewayplant.excel.GatewayPlantExcel;
import org.skyworth.ess.lazzen.gatewayplant.mapper.GatewayPlantMapper;
import org.skyworth.ess.lazzen.gatewayplant.service.IGatewayPlantService;
import org.skyworth.ess.lazzen.gatewayplant.vo.*;
import org.skyworth.ess.lazzen.measurementinfo.entity.DeviceGuardMeasurementInfoEntity;
import org.skyworth.ess.lazzen.measurementinfo.service.IDeviceGuardMeasurementInfoService;
import org.skyworth.ess.lazzen.measurementinfo.vo.DeviceGuardMeasurementInfoStatVO;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 良信网关站点关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
@Slf4j
public class GatewayPlantServiceImpl extends BaseServiceImpl<GatewayPlantMapper, GatewayPlantEntity> implements IGatewayPlantService {
	public static final DateTimeFormatter ymdhmFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
	public static final DateTimeFormatter ymdFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public static final DateTimeFormatter hmFormatter = DateTimeFormatter.ofPattern("HH:mm");
	@Resource
	private IUserSearchClient userSearchClient;
	@Resource
	private IAgentClient agentClient;
	@Resource
	private ISysClient sysClient;
	@Resource
	private IDeviceGuardMeasurementInfoService deviceGuardMeasurementInfoService;

	@Override
	public IPage<GatewayPlantVO> selectGatewayPlantPage(IPage<GatewayPlantVO> page, GatewayPlantVO gatewayPlant) {
		return page.setRecords(baseMapper.selectGatewayPlantPage(page, gatewayPlant));
	}


	@Override
	public List<GatewayPlantExcel> exportGatewayPlant(Wrapper<GatewayPlantEntity> queryWrapper) {
		List<GatewayPlantExcel> gatewayPlantList = baseMapper.exportGatewayPlant(queryWrapper);
		//gatewayPlantList.forEach(gatewayPlant -> {
		//	gatewayPlant.setTypeName(DictCache.getValue(DictEnum.YES_NO, GatewayPlant.getType()));
		//});
		return gatewayPlantList;
	}
	@Override
	public boolean batchUpdateHeartTimeByGatewayUniqueNumber(List<GatewayPlantEntity> updateList) {
		return  baseMapper.batchUpdateHeartTimeByGatewayUniqueNumber(updateList);
	}
	@Override
	public boolean batchUpdateStatusByGatewayUniqueNumber(List<GatewayPlantEntity> updateList) {
		return  baseMapper.batchUpdateStatusByGatewayUniqueNumber(updateList);
	}
	@Override
	public Map<String, Object> getAllGuardianGatePositionSetup(Long plantId, String serialNumber) {
			return baseMapper.getAllGuardianGatePositionSetup(plantId,serialNumber);
	}

	@Override
	public GatewayPlantOverviewVO getOverview(GatewayPlantVO gatewayPlantVO) {
		GatewayPlantOverviewVO gatewayPlantOverviewVO = new GatewayPlantOverviewVO();
		IPage<GatewayPlantVO> page = Condition.getPage(new Query());
		baseMapper.selectGatewayPlantPage(page, gatewayPlantVO);
		gatewayPlantOverviewVO.setLazzenDeviceTotal(Func.toInt(page.getTotal()));
		return gatewayPlantOverviewVO;
	}

	@Override
	public IPage<GatewayPlantVO> page(Query query, GatewayPlantVO gatewayPlantVO) {
		IPage<GatewayPlantVO> page = Condition.getPage(query);

		Set<Long> userIdSet = new HashSet<>();
		//搜索用户名
		String userName = gatewayPlantVO.getUserName();
		if (ValidationUtil.isNotEmpty(userName)) {
			List<User> userNameList = userSearchClient.listByRealName(userName).getData();
			if (ValidationUtil.isNotEmpty(userNameList) && !userNameList.isEmpty()) {
				String userIds = userNameList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索用户手机号
		String userPhone = gatewayPlantVO.getUserPhone();
		if (ValidationUtil.isNotEmpty(userPhone)) {
			List<User> userPhoneList = userSearchClient.listByPhone(userPhone).getData();
			if (ValidationUtil.isNotEmpty(userPhoneList) && !userPhoneList.isEmpty()) {
				String userIds = userPhoneList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				userIdSet.addAll(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}
		gatewayPlantVO.setUserIds(List.copyOf(userIdSet));

		//搜索运维人员
		List<User> agentUserList;
		String agentUserName = gatewayPlantVO.getAgentUserName();
		if (ValidationUtil.isNotEmpty(agentUserName)) {
			agentUserList = userSearchClient.listByRealName(agentUserName).getData();
			if (ValidationUtil.isNotEmpty(agentUserList) && !agentUserList.isEmpty()) {
				String userIds = agentUserList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				gatewayPlantVO.setAgentUserIds(Func.toLongList(userIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		//搜索运维团队
		String companyName = gatewayPlantVO.getAgentCompanyName();
		List<AgentCompanyVO> agentCompanyVOList;
		if (ValidationUtil.isNotEmpty(companyName)) {
			agentCompanyVOList = agentClient.agentCompany(companyName).getData();
			if (ValidationUtil.isNotEmpty(agentCompanyVOList) && !agentCompanyVOList.isEmpty()) {
				String companyIds = agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				gatewayPlantVO.setAgentCompanyIds(Func.toLongList(companyIds));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}

		List<GatewayPlantVO> gatewayPlantVOList = baseMapper.selectGatewayPlantPage(page, gatewayPlantVO);
		gatewayPlantVOListHandle(gatewayPlantVOList);

		return page.setRecords(gatewayPlantVOList);
	}

	@Override
	public GatewayPlantVO detail(GatewayPlantEntity gatewayPlant) {
		GatewayPlantVO gatewayPlantVO = new GatewayPlantVO();
		if (ObjectUtil.isEmpty(gatewayPlant.getPlantId()) || StringUtil.isBlank(gatewayPlant.getGatewayUniqueNumber())) {
			log.warn("参数异常，无法查询详情");
			return gatewayPlantVO;
		}
		GatewayPlantEntity guardianPlantEntityResp = baseMapper.detail(gatewayPlant.getPlantId(), gatewayPlant.getGatewayUniqueNumber());
		BeanUtil.copy(guardianPlantEntityResp, gatewayPlantVO);
		List<GatewayPlantVO> guardianPlantVOList = Collections.singletonList(gatewayPlantVO);
		gatewayPlantVOListHandle(guardianPlantVOList);
		return gatewayPlantVO;
	}

	@Override
	public GatewayPlantCurrentStatusVO detailCurrentStatus(GatewayPlantEntity gatewayPlantEntity) {
		Long plantId = gatewayPlantEntity.getPlantId();
		String gatewayUniqueNumber = gatewayPlantEntity.getGatewayUniqueNumber();
		GatewayPlantCurrentStatusVO gatewayPlantCurrentStatus = new GatewayPlantCurrentStatusVO();
		gatewayPlantCurrentStatus.setDeviceType(gatewayPlantEntity.getDeviceType());
		gatewayPlantCurrentStatus.setPlantId(plantId);
		gatewayPlantCurrentStatus.setGatewayUniqueNumber(gatewayUniqueNumber);
		// 查询状态信息表
		DeviceGuardMeasurementInfoEntity measurementInfoServiceOne = deviceGuardMeasurementInfoService.getOne(Wrappers.lambdaQuery(DeviceGuardMeasurementInfoEntity.class)
			.eq(DeviceGuardMeasurementInfoEntity::getGatewayUniqueNumber, gatewayUniqueNumber)
			.orderByDesc(DeviceGuardMeasurementInfoEntity::getDeviceDateTime)
			.last("LIMIT 1"));
		if (ObjectUtil.isNotEmpty(measurementInfoServiceOne)){
			BeanUtil.copy(measurementInfoServiceOne, gatewayPlantCurrentStatus);
		}
		return gatewayPlantCurrentStatus;
	}

	private void gatewayPlantVOListHandle(List<GatewayPlantVO> gatewayPlantVOList) {
		// 站点信息list转用户ID集合
		List<Long> userIdList = gatewayPlantVOList.stream().map(GatewayPlantVO::getCreateUser)
			.filter(ObjectUtil::isNotEmpty).distinct().collect(Collectors.toList());
		// 获取用户信息
		R<List<User>> userListR = userSearchClient.listAllByUserIds(userIdList);

		Map<Long, User> userMap = new HashMap<>();
		// 响应成功并且数据不为空
		if (userListR.isSuccess() && CollectionUtil.isNotEmpty(userListR.getData())) {
			userMap = userListR.getData().stream().collect(Collectors.toMap(User::getId, Function.identity()));
		}

		//获取代理商人员信息
		Map<Long, AgentUserVo> agentUserVoMap = new HashMap<>();
		getAgentUserInfo(gatewayPlantVOList, agentUserVoMap);

		//获取代理商信息
		Map<Long, AgentCompanyVO> companyMap = new HashMap<>();
		getCompanyInfo(gatewayPlantVOList, companyMap);

		List<String> regionCodeList = new ArrayList<>();
		for (GatewayPlantVO gatewayPlantVO : gatewayPlantVOList) {
			QueryCondition queryCondition = new QueryCondition();
			queryCondition.setPlantId(gatewayPlantVO.getId());
			regionCodeList.add(gatewayPlantVO.getCountryCode());
			regionCodeList.add(gatewayPlantVO.getProvinceCode());
			regionCodeList.add(gatewayPlantVO.getCityCode());
			regionCodeList.add(gatewayPlantVO.getCountyCode());

			if (ObjectUtil.isNotEmpty(userMap.get(gatewayPlantVO.getCreateUser()))) {
				// 设置用户电话号码
				gatewayPlantVO.setUserPhone(StringUtil.isBlank(userMap.get(gatewayPlantVO.getCreateUser()).getPhone())
					? "" : userMap.get(gatewayPlantVO.getCreateUser()).getPhone());
				// 设置用户电话号码区号
				gatewayPlantVO.setPhoneDiallingCode(StringUtil.isBlank(userMap.get(gatewayPlantVO.getCreateUser()).getPhoneDiallingCode())
					? "" : userMap.get(gatewayPlantVO.getCreateUser()).getPhoneDiallingCode());
				// 设置用户名
				gatewayPlantVO.setUserName(StringUtil.isBlank(userMap.get(gatewayPlantVO.getCreateUser()).getRealName())
					? "" : userMap.get(gatewayPlantVO.getCreateUser()).getRealName());
			} else {
				gatewayPlantVO.setUserPhone("");
				gatewayPlantVO.setPhoneDiallingCode("");
				gatewayPlantVO.setUserName("");
			}

			Long agentUserId = gatewayPlantVO.getAgentUserId();
			Long agentCompanyId = gatewayPlantVO.getAgentCompanyId();
			if (ValidationUtil.isNotEmpty(agentUserId)) {
				if (!CollectionUtils.isNullOrEmpty(agentUserVoMap) && agentUserVoMap.containsKey(agentUserId)) {
					gatewayPlantVO.setAgentUserName(agentUserVoMap.get(agentUserId).getRealName());
				}
			}
			if (ValidationUtil.isNotEmpty(agentCompanyId)) {
				if (!CollectionUtils.isNullOrEmpty(companyMap) && companyMap.containsKey(agentCompanyId)) {
					gatewayPlantVO.setAgentCompanyName(companyMap.get(agentCompanyId).getCompanyName());
				}
			}
			// 安全卫士状态映射
			gatewayPlantVO.setStatusName(SecurityGuardStatusEnum.getDescByCode(gatewayPlantVO.getStatus()));
		}

		this.setRegionInfo(regionCodeList, gatewayPlantVOList);
	}

	/**
	 * 设置区域信息
	 */
	private void setRegionInfo(List<String> regionCodeList, List<GatewayPlantVO> guardianPlantVOList) {
		if (CollectionUtil.isEmpty(regionCodeList)) {
			return;
		}
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			R<List<Region>> regionResult = sysClient.getRegionList(regionCodeNotNullList);
			List<Region> regionList = regionResult.getData();
			for (GatewayPlantVO guardianPlantVO : guardianPlantVOList) {
				StringBuilder address = new StringBuilder();
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getCountryCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(guardianPlantVO.getCityCode())) {
						address.append(region.getName()).append(" ");
						guardianPlantVO.setCityName(region.getName());
					}
				}
				guardianPlantVO.setAddress(address.append(" ").append(guardianPlantVO.getDetailAddress() == null ? "" : guardianPlantVO.getDetailAddress()).toString());
			}
		}
	}

	/**
	 * 获取代理商人员信息
	 */
	private void getAgentUserInfo(List<GatewayPlantVO> gatewayPlantVOList, Map<Long, AgentUserVo> agentUserVoMap) {
		List<Long> userIds = gatewayPlantVOList.stream().map(GatewayPlantVO::getAgentUserId)
			.filter(org.springblade.core.tool.utils.ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (!CollectionUtils.isNullOrEmpty(userList)) {
			userList.parallelStream().forEach(v -> {
				AgentUserVo agentUserVo = new AgentUserVo();
				BeanUtils.copyProperties(v, agentUserVo);
				agentUserVoMap.put(v.getId(), agentUserVo);
			});
		}
	}

	/**
	 * 获取代理商公司信息
	 */
	private void getCompanyInfo(List<GatewayPlantVO> gatewayPlantVOList, Map<Long, AgentCompanyVO> companyVoMap) {
		List<Long> companyIds = gatewayPlantVOList.stream().map(GatewayPlantVO::getAgentCompanyId).distinct()
			.filter(org.springblade.core.tool.utils.ObjectUtil::isNotEmpty).collect(Collectors.toList());
		List<AgentCompanyVO> agentCompanyVOList = agentClient.agentCompanyInfoByIds(companyIds).getData();
		if (!CollectionUtils.isNullOrEmpty(agentCompanyVOList)) {
			agentCompanyVOList.parallelStream().forEach(v -> {
				AgentCompanyVO agentCompanyVO = new AgentCompanyVO();
				BeanUtils.copyProperties(v, agentCompanyVO);
				companyVoMap.put(v.getDeptId(), agentCompanyVO);
			});
		}
	}



	@Override
	public Map<String, List<Object>> selectStatusReport(GatewayPlantReportQueryVO queryCondition) {
		List<DeviceGuardMeasurementInfoStatVO> statVOList = getGatewayPlantData(queryCondition);
		return processStatusListData(statVOList, queryCondition.getQueryPowerType());
	}

	@Override
	public void selectStatusReportExport(GatewayPlantReportQueryVO queryCondition, HttpServletResponse response) {
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("statistical_data", StandardCharsets.UTF_8) + ".xlsx");
			queryCondition.setQueryPowerType("lazzen_voltage,lazzen_current,lazzen_temperature,lazzen_leakage_current");
			List<DeviceGuardMeasurementInfoStatVO> gatewayPlantData = this.getGatewayPlantData(queryCondition);
			Map<String, List<Object>> statusListDataMap = this.processDataForExcel(gatewayPlantData);
			List<Object> dateTimeForDay = statusListDataMap.get("deviceDateTimeForDay");
			List<Object> minuteTime = statusListDataMap.get("deviceDateTime");
			// 分钟去重
			List<String> distinctMinuteTime = minuteTime.stream()
				.distinct()
				.map(Object::toString)
				.collect(Collectors.toList());
			// 日期去重
			List<String> firstHeadList = dateTimeForDay.stream()
				.distinct() // 去重
				.map(obj -> LocalDate.parse((String) obj, ymdFormatter)) // 转换为LocalDateTime类型
				.sorted(Comparator.naturalOrder()) // 按照时间顺序排序
				.map(dateTime -> dateTime.format(ymdFormatter)) // 格式化为字符串
				.collect(Collectors.toList()); // 转换为列表

			List<String> secondHeadVoltage = Arrays.asList("A PHASE VOLTAGE(V)", "B PHASE VOLTAGE(V)", "C PHASE VOLTAGE(V)");
			List<String> secondHeadCurrent = Arrays.asList("A PHASE CURRENT(A)", "B PHASE CURRENT(A)", "C PHASE CURRENT(A)");
			List<String> secondHeadTemperature = Arrays.asList("TEMPERATURE(°C)");
			List<String> secondHeadLeakageCurrent = List.of("LEAKAGE CURRENT(mA)");
			List<List<String>> voltageDataList = new ArrayList<>();
			List<List<String>> currentDataList = new ArrayList<>();
			List<List<String>> temperatureDataList = new ArrayList<>();
			List<List<String>> leakageCurrentDataList = new ArrayList<>();

			// 根据时间分组、再根据日期分组
			Map<String, Map<String, DeviceGuardMeasurementInfoStatVO>> groupedMap = gatewayPlantData.stream()
				.collect(Collectors.groupingBy(DeviceGuardMeasurementInfoStatVO::getDeviceDateTime,
					Collectors.toMap(DeviceGuardMeasurementInfoStatVO::getDeviceDateTimeForDay, report -> report)));

			// 循环外层：按每1分钟的行数据
			for (String mintStr : distinctMinuteTime) {
				List<String> rowVoltage = Lists.newArrayList(mintStr);
				List<String> rowCurrent = Lists.newArrayList(mintStr);
				List<String> rowTemperature = Lists.newArrayList(mintStr);
				List<String> rowLeakageCurrent = Lists.newArrayList(mintStr);
				if (groupedMap.containsKey(mintStr)) {
					Map<String, DeviceGuardMeasurementInfoStatVO> stringListMap = groupedMap.get(mintStr);
					// 循环内层：按日的列数据
					// 限制流的大小为总元素数减1(去除最后一天冗余日期)
					for (String dayData : firstHeadList) {
						if (stringListMap.containsKey(dayData)) {
							DeviceGuardMeasurementInfoStatVO infoStatVO = stringListMap.get(dayData);
							rowVoltage.add(StrUtil.toString(infoStatVO.getPhaseAVoltage()));
							rowVoltage.add(StrUtil.toString(infoStatVO.getPhaseBVoltage()));
							rowVoltage.add(StrUtil.toString(infoStatVO.getPhaseCVoltage()));
							rowCurrent.add(StrUtil.toString(infoStatVO.getPhaseACurrent()));
							rowCurrent.add(StrUtil.toString(infoStatVO.getPhaseBCurrent()));
							rowCurrent.add(StrUtil.toString(infoStatVO.getPhaseCCurrent()));
							rowTemperature.add(StrUtil.toString(infoStatVO.getTemperature()));
							rowLeakageCurrent.add(StrUtil.toString(infoStatVO.getLeakageCurrent()));
						}
					}
				}
				voltageDataList.add(rowVoltage);
				currentDataList.add(rowCurrent);
				temperatureDataList.add(rowTemperature);
				leakageCurrentDataList.add(rowLeakageCurrent);
			}

			ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
			// 构建sheet对象&写数据
			WriteSheet writeSheet1 = EasyExcelFactory.writerSheet("Voltage").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadVoltage, "Time")).build();
			excelWriter.write(voltageDataList, writeSheet1);
			WriteSheet writeSheet2 = EasyExcelFactory.writerSheet("Current").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadCurrent, "Time")).build();
			excelWriter.write(currentDataList, writeSheet2);
			WriteSheet writeSheet3 = EasyExcelFactory.writerSheet("Temperature").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadTemperature, "Time")).build();
			excelWriter.write(temperatureDataList, writeSheet3);
			WriteSheet writeSheet4 = EasyExcelFactory.writerSheet("LeakageCurrent").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList, secondHeadLeakageCurrent, "Time")).build();
			excelWriter.write(leakageCurrentDataList, writeSheet4);
			excelWriter.finish();
		} catch (Exception var6) {
			log.error("export guardian data error: {}", var6.getMessage(), var6);
		}
	}

	@Override
	public void batchDeleteLogicByPlantId(List<Long> longList, String account) {
		baseMapper.batchDeleteLogicByPlantId(longList,account);
	}

	@Override
	public List<GatewayPlantEntity> queryOwnerData(Long userId) {
		return baseMapper.selectList(Wrappers.<GatewayPlantEntity>query().lambda()
			.eq(GatewayPlantEntity::getCreateUser,userId)
			.eq(GatewayPlantEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
	}

	@Override
	public int updateDataByCondition(GatewayPlantEntity updateGuardianPlantEntity) {
		return baseMapper.updateDataByCondition(updateGuardianPlantEntity);
	}

	@NotNull
	private Map<String, List<Object>> processDataForExcel(List<DeviceGuardMeasurementInfoStatVO> dataList) {
		Map<String, List<Object>> groupedData = new HashMap<>();
		// 获取所有字段名
		Field[] fields = DeviceGuardMeasurementInfoStatVO.class.getDeclaredFields();
		// 提前设置访问权限，避免在循环中重复设置
		setFieldsAccessible(fields);
		for (Field field : fields) {
			try {
				encapsulationData(dataList, field, groupedData);
			} catch (Exception e) {
				log.error("Error processing field " + field.getName() + ": " + e.getMessage());
			}
		}
		return groupedData;
	}

	private List<List<String>> getHeadList(List<String> firstHeadList, List<String> secondHeadList, String leftmostRow) {
		List<List<String>> headList = new ArrayList<>();
		headList.add(Lists.newArrayList(leftmostRow));
		for (String first : firstHeadList) {
			for (String second : secondHeadList) {
				List<String> secondList = new ArrayList<>();
				secondList.add(first);
				secondList.add(second);
				headList.add(secondList);
			}
		}
		return headList;
	}


	private @NotNull List<DeviceGuardMeasurementInfoStatVO> getGatewayPlantData(GatewayPlantReportQueryVO queryCondition) {
		Date startDateTime = queryCondition.getStartDateTime();
		Date endDateTime = queryCondition.getEndDateTime();
		// 查询模块状态曲线查询模块：枚举-QueryPowerTypeEnum
		String queryPowerType = queryCondition.getQueryPowerType();
		// 设置默认时间
		if (startDateTime == null || endDateTime == null) {
			Calendar start = Calendar.getInstance();
			start.set(Calendar.HOUR_OF_DAY, 0);
			start.set(Calendar.MINUTE, 0);
			start.set(Calendar.SECOND, 0);
			startDateTime = start.getTime();
			Calendar end = Calendar.getInstance();
			end.set(Calendar.HOUR_OF_DAY, 23);
			end.set(Calendar.MINUTE, 59);
			end.set(Calendar.SECOND, 59);
			endDateTime = end.getTime();
		}
		DeviceGuardMeasurementInfoQueryCondition condition = new DeviceGuardMeasurementInfoQueryCondition();
		condition.setGatewayUniqueNumber(queryCondition.getGatewayUniqueNumber());
		condition.setStartDateTime(startDateTime);
		condition.setEndDateTime(endDateTime);
		List<DeviceGuardMeasurementInfoStatVO> deviceInfoList = deviceGuardMeasurementInfoService.selectDataStatByTime(condition);
		Map<String, DeviceGuardMeasurementInfoStatVO> deviceGuardMeasurementInfoStatVoMap = deviceInfoList.stream()
			.collect(Collectors.toMap(DeviceGuardMeasurementInfoStatVO::getDeviceDateTimeForCal, e -> e, (oldValue, newValue) -> newValue));

		LocalDateTime startDateTimeNew = startDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		LocalDateTime endDateTimeNew = endDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		// 当前时间点
		LocalDateTime currentDateTime = startDateTimeNew;

		// 每1分钟打一个点位，判断前面1分钟是否有数据，如16:01-16:02是否有数据?
		// 有数据则将数据则归并数据到时间点16:02
		// 没有数据则造一条数据
		// 注：查询条件的第一个数据，如每天的开始时间00:00，并且该分钟数是5的倍数，此时有一条数据上报，则会直接通过接口返回到前端，不需要走以下遍历
		while (currentDateTime.isBefore(endDateTimeNew)) {
			// 下一个时间点
			LocalDateTime next5MinDateTime = currentDateTime.plusMinutes(1);
			// 如果下一个1分钟是晚上00点00分，则将next5MinDateTime设置为当天23:59，避免把数据点位打到第二天
			if (next5MinDateTime.toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = currentDateTime.withHour(23).withMinute(59);
			}
			LocalDateTime mergerTime = next5MinDateTime;
			// 判断deviceDateTimeForCal字段的数据是否在两个时间点内，在就取出Map<String, InvertStatusReport>的数据，放到集合
			List<DeviceGuardMeasurementInfoStatVO> infoStatVOS = this.isBetween(currentDateTime, next5MinDateTime, deviceGuardMeasurementInfoStatVoMap);
			// 如果下一个1分钟是晚上00点00分
			if (currentDateTime.plusMinutes(1).toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = next5MinDateTime.plusMinutes(1);
			}
			// 如果有这个时间点（16:01-16:02）的数据，则归并数据到时间点
			if (!infoStatVOS.isEmpty()) {
				// 归并数据到下一个时间点
				mergeData(mergerTime, infoStatVOS, deviceGuardMeasurementInfoStatVoMap, queryPowerType);
			} else {
				// 造数据,默认都为0
				DeviceGuardMeasurementInfoStatVO fakeData = getFakeData(mergerTime, queryPowerType);
				deviceGuardMeasurementInfoStatVoMap.put(mergerTime.format(ymdhmFormatter), fakeData);
			}
			currentDateTime = next5MinDateTime; // 更新当前时间点为下一个时间点
		}

		List<DeviceGuardMeasurementInfoStatVO> infoStatVOS = new ArrayList<>(deviceGuardMeasurementInfoStatVoMap.values());
		infoStatVOS.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
		return infoStatVOS;
	}

	@NotNull
	private Map<String, List<Object>> processStatusListData(List<DeviceGuardMeasurementInfoStatVO> infoStatVOS, String queryPowerType) {
		Map<String, List<Object>> groupedData = new HashMap<>();
		// 获取所有字段名
		Field[] fields = DeviceGuardMeasurementInfoStatVO.class.getDeclaredFields();
		// 提前设置访问权限，避免在循环中重复设置
		setFieldsAccessible(fields);
		String fieldNames = QueryPowerTypeEnum.getFieldName(queryPowerType);
		for (Field field : fields) {
			try {
				String fieldName = field.getName();
				if (fieldNames.contains(CommonConstant.SYMBOL_COMMA + fieldName + CommonConstant.SYMBOL_COMMA) ||
					"deviceDateTime".equalsIgnoreCase(fieldName)
				) {
					encapsulationData(infoStatVOS, field, groupedData);
				}
			} catch (Exception e) {
				log.error("Error processing field " + field.getName() + ": " + e.getMessage());
			}
		}
		return groupedData;
	}

	/**
	 * 封装返回数据
	 * */
	private void encapsulationData(List<DeviceGuardMeasurementInfoStatVO> infoStatVOS, Field field, Map<String, List<Object>> groupedData) {
		String fieldName = field.getName();
		List<Object> fieldValueList = infoStatVOS.stream()
			.map(report -> {
				try {
					return field.get(report);
				} catch (IllegalAccessException e) {
					log.error(e.getMessage());
					return "";
				}
			})
			.collect(Collectors.toList());
		groupedData.put(fieldName, fieldValueList);
	}


	// 设置字段可访问
	private void setFieldsAccessible(Field[] fields) {
		for (Field field : fields) {
			field.setAccessible(true);
		}
	}

	// 判断给定的数据时间是否在两个时间点之间
	private List<DeviceGuardMeasurementInfoStatVO> isBetween(LocalDateTime start, LocalDateTime end, Map<String, DeviceGuardMeasurementInfoStatVO> collect) {
		List<DeviceGuardMeasurementInfoStatVO> invertStatusReports = new ArrayList<>();
		LocalDateTime startMin = start.plusMinutes(1);
		// 如果开始时间>结束时间，则跳过循环，返回上一层调用的方法。如00:06>00:05，则跳出循环，此时才能遍历完整从00:01-00:05分的数据，然后归并到00:05时间点上
		while (startMin.isBefore(end) || startMin.isEqual(end)) {
			String formattedDateTime = startMin.format(ymdhmFormatter);
			if (collect.containsKey(formattedDateTime)) {
				invertStatusReports.add(collect.get(formattedDateTime));
			}
			startMin = startMin.plusMinutes(1L);
		}

		return invertStatusReports;
	}

	// 归并数据到指定的时间点
	private void mergeData(LocalDateTime dateTime, List<DeviceGuardMeasurementInfoStatVO> infoStatVOS, Map<String, DeviceGuardMeasurementInfoStatVO> collect, String queryPowerType) {
		// 实现归并数据的逻辑
		DeviceGuardMeasurementInfoStatVO sumReport = new DeviceGuardMeasurementInfoStatVO();
		if (infoStatVOS.size() == 1) {
			DeviceGuardMeasurementInfoStatVO guardianLogDataStatVO = infoStatVOS.get(0);
			sumReport = guardianLogDataStatVO;
			setGuardianLogDataValue(sumReport, guardianLogDataStatVO, queryPowerType);
			collect.remove(sumReport.getDeviceDateTimeForCal());
		} else if (infoStatVOS.size() > 1) {
			//取时间最新那一条
			infoStatVOS.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
			DeviceGuardMeasurementInfoStatVO guardianLogDataStatVO = infoStatVOS.get(infoStatVOS.size() - 1);
			setGuardianLogDataValue(sumReport, guardianLogDataStatVO, queryPowerType);
			infoStatVOS.forEach(a -> collect.remove(a.getDeviceDateTimeForCal()));
		}
		String ymdHmTime = dateTime.format(ymdhmFormatter);
		String hmTime = dateTime.format(hmFormatter);
		String ymd = dateTime.format(ymdFormatter);
		sumReport.setDeviceDateTimeForCal(ymdHmTime);
		sumReport.setDeviceDateTime(hmTime);
		sumReport.setDeviceDateTimeForDay(ymd);
		collect.put(ymdHmTime, sumReport);
	}

	private BigDecimal formatDecimal(BigDecimal value, int scale) {
		DecimalFormat decimalFormat = new DecimalFormat("#0." + "0".repeat(scale));
		return new BigDecimal(decimalFormat.format(value));
	}

	private boolean includeQueryPowerType(String queryPowerType, String powerType) {
		queryPowerType = CommonConstant.SYMBOL_COMMA + queryPowerType + CommonConstant.SYMBOL_COMMA;
		return queryPowerType.toLowerCase().contains(CommonConstant.SYMBOL_COMMA + powerType.toLowerCase() + CommonConstant.SYMBOL_COMMA);
	}
	private void setGuardianLogDataValue(DeviceGuardMeasurementInfoStatVO sumReport, DeviceGuardMeasurementInfoStatVO guardianLogDataStatVO, String queryPowerType) {
			sumReport.setPhaseAVoltage(formatDecimal(guardianLogDataStatVO.getPhaseAVoltage(), 2));
			sumReport.setPhaseACurrent(formatDecimal(guardianLogDataStatVO.getPhaseACurrent(), 2));
			sumReport.setPhaseBVoltage(formatDecimal(guardianLogDataStatVO.getPhaseBVoltage(), 2));
			sumReport.setPhaseBCurrent(formatDecimal(guardianLogDataStatVO.getPhaseBCurrent(), 2));
			sumReport.setPhaseCVoltage(formatDecimal(guardianLogDataStatVO.getPhaseCVoltage(), 2));
			sumReport.setPhaseCCurrent(formatDecimal(guardianLogDataStatVO.getPhaseCCurrent(), 2));
			sumReport.setTemperature(formatDecimal(guardianLogDataStatVO.getTemperature(), 2));
			sumReport.setLeakageCurrent(formatDecimal(guardianLogDataStatVO.getLeakageCurrent(), 2));
	}

	@NotNull
	private DeviceGuardMeasurementInfoStatVO getFakeData(LocalDateTime mergerTime, String queryPowerType) {
		// 没有数据则造一条数据
		DeviceGuardMeasurementInfoStatVO fakeData = new DeviceGuardMeasurementInfoStatVO();
		fakeData.setDeviceDateTime(mergerTime.format(hmFormatter));
		fakeData.setDeviceDateTimeForCal(mergerTime.format(ymdhmFormatter));
		fakeData.setDeviceDateTimeForDay(mergerTime.format(ymdFormatter));
		fakeData.setPhaseAVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseACurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseBVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseBCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseCVoltage(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseCCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setTemperature(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setLeakageCurrent(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		return fakeData;
	}
}

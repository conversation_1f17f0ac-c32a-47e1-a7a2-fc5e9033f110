package org.skyworth.ess.guardian.realTimeHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.skyworth.ess.guardian.guardianplant.entity.GuardianPlantEntity;
import org.skyworth.ess.guardian.thresholdcurrentstatus.entity.GuardianThresholdCurrentStatusEntity;
import org.skyworth.ess.util.HumpConvert;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.GuardianInstructConstants;
import org.springblade.common.sink.DorisSinkService;
import org.springblade.common.utils.BinaryToHexUtils;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * 探测器发送报警阀值数据
 */
@Component(CommonConstant.TERMINAL_WARDS_KEY +"_"+ CommonConstant.BIZ_ALARM_THRESHOLD)
@Slf4j
@AllArgsConstructor
public class AlarmThresholdServiceImpl implements DataHandler {

	private DorisSinkService dorisSinkService;

	private IGuardianAlarmThresholdService guardianAlarmThresholdService;

	private static final SimpleDateFormat INPUT_FORMAT = new SimpleDateFormat("yyMMddHHmmss");
	private static final ThreadLocal<DateFormat> OUTPUT_FORMAT = ThreadLocal.withInitial(
			() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()));
	@Override
	public void handler(List<JSONObject> dataList,Map<String, Long> resultMap, Map<String,String> partitionMap) {
		List<JSONObject> thresholdCurrentStatusList=new ArrayList<>();
		List<GuardianAlarmThresholdEntity> thresholdEntities=new ArrayList<>();
		dataList.parallelStream().forEach(data-> {
			try{
				log.info("探测器发送报警阀值数据:{}", data.toJSONString());
				String content=data.getString("content");
				String deviceSn = data.getString("deviceSn");
				String alarmThreshold = content.substring(0,40);
				//基础数据
				List<Integer> res=RealTimeServiceImpl.getCurrentData(alarmThreshold,1,Integer.class);
				//故障开关
				String faultSwitch= BinaryToHexUtils.binaryComplement(content.substring(40,44),8);
				String faultSwitchReverse= new StringBuilder(faultSwitch).reverse().toString();
				Long plantId=resultMap.get(deviceSn);
				String partitionDate= LocalDate.now().toString();
				if(ValidationUtil.isNotEmpty(plantId)){
					partitionDate=partitionMap.get(deviceSn);
				}
				String time=content.substring(44);
				String deviceDateTime = TimeUtils.getCurrentTime();
				try {
					if(!GuardianInstructConstants.TIME_IS_NULL.equals(time)){
						Date date = INPUT_FORMAT.parse(time);
						deviceDateTime =  OUTPUT_FORMAT.get().format(date);
					}
				} catch (Exception e) {
					log.error("时间格式错误:{} " , e.getMessage());
				}
				//数据入库  security_guard_threshold_current_status    guardian_alarm_threshold
				handlerData(res,faultSwitchReverse,plantId,partitionDate,deviceSn,thresholdCurrentStatusList,thresholdEntities,deviceDateTime);

			}catch (Exception e){
				log.error("探测器发送报警阀值解析数据异常:{}", e.getMessage());
			}

			if(!thresholdCurrentStatusList.isEmpty()){
				dorisSinkService.write(thresholdCurrentStatusList,"security_guard_threshold_current_status");
			}
			if(!thresholdEntities.isEmpty()){
				insertOrUpdateAlarm(thresholdEntities);
			}

		});
	}

	public void insertOrUpdateAlarm(List<GuardianAlarmThresholdEntity> thresholdEntities) {
		thresholdEntities.parallelStream().forEach(v->{
			LambdaQueryWrapper<GuardianAlarmThresholdEntity> eq = Wrappers.<GuardianAlarmThresholdEntity>query().lambda()
					.eq(GuardianAlarmThresholdEntity::getSecurityGuardSerialNumber,v.getSecurityGuardSerialNumber())
					.eq(GuardianAlarmThresholdEntity::getPlantId,v.getPlantId())
					.eq(GuardianAlarmThresholdEntity::getIsDeleted,0);
			GuardianAlarmThresholdEntity guardianAlarmThreshold=guardianAlarmThresholdService.getOne(eq);
			if(ValidationUtil.isNotEmpty(guardianAlarmThreshold)){
				Long id=guardianAlarmThreshold.getId();
				BeanUtils.copyProperties(v,guardianAlarmThreshold);
				guardianAlarmThreshold.setId(id);
				guardianAlarmThresholdService.updateById(guardianAlarmThreshold);
			}else {
				guardianAlarmThresholdService.save(v);
			}
		});
	}

	/**
	 * 处理数据入库
	 * */
	public void handlerData(List<Integer> res,String faultSwitchReverse,Long plantId,String partitionDate,String deviceSn
			,List<JSONObject> thresholdCurrentStatusList,List<GuardianAlarmThresholdEntity> thresholdEntities,String deviceDateTime) {
		GuardianThresholdCurrentStatusEntity thresholdCurrentStatus=new GuardianThresholdCurrentStatusEntity();
		thresholdCurrentStatus.setSecurityGuardSerialNumber(deviceSn);
		thresholdCurrentStatus.setPartitionDate(partitionDate);
		thresholdCurrentStatus.setPlantId(plantId);
		thresholdCurrentStatus.setOvercurrentAlarmThreshold(BigDecimal.valueOf(res.get(0)*0.1));
		thresholdCurrentStatus.setLeakageAlarmThreshold(BigDecimal.valueOf(res.get(1)));
		thresholdCurrentStatus.setOvervoltageAlarmThreshold(BigDecimal.valueOf(res.get(2)));
		thresholdCurrentStatus.setUndervoltageAlarmThreshold(BigDecimal.valueOf(res.get(3)));
		thresholdCurrentStatus.setATemperatureHighAlarmThreshold(BigDecimal.valueOf(res.get(4)));
		thresholdCurrentStatus.setBTemperatureHighAlarmThreshold(BigDecimal.valueOf(res.get(5)));
		thresholdCurrentStatus.setCTemperatureHighAlarmThreshold(BigDecimal.valueOf(res.get(6)));
		thresholdCurrentStatus.setNTemperatureHighAlarmThreshold(BigDecimal.valueOf(res.get(7)));
		thresholdCurrentStatus.setArcLevelAlarmThreshold(BigDecimal.valueOf(res.get(8)));
		thresholdCurrentStatus.setAlarmThresholdForPowerRestrictionDegree(BigDecimal.valueOf(res.get(9)));
        thresholdCurrentStatus.setOvervoltageSwitch(faultSwitchReverse.charAt(0)=='1'?1:0);
		thresholdCurrentStatus.setUndervoltageSwitch(faultSwitchReverse.charAt(1)=='1'?1:0);
		thresholdCurrentStatus.setOvercurrentSwitch(faultSwitchReverse.charAt(2)=='1'?1:0);
		thresholdCurrentStatus.setLeakageSwitch(faultSwitchReverse.charAt(3)=='1'?1:0);
		thresholdCurrentStatus.setHighTemperatureAlarmSwitch(faultSwitchReverse.charAt(4)=='1'?1:0);
		thresholdCurrentStatus.setArcLevelSwitch(faultSwitchReverse.charAt(5)=='1'?1:0);
		thresholdCurrentStatus.setPowerLimitSwitch(faultSwitchReverse.charAt(6)=='1'?1:0);
		thresholdCurrentStatus.setPhaseLossProtectionSwitch(faultSwitchReverse.charAt(7)=='1'?1:0);
		thresholdCurrentStatus.setTenantId(CommonConstant.CLIENT_TENANT_ID);

		JSONObject result = (JSONObject) JSON.toJSON(thresholdCurrentStatus);
		//将驼峰转化为下划线
		JSONObject newObj = HumpConvert.convertKeysToUnderscore(result);
		IdentifierGenerator identifierGenerator = new DefaultIdentifierGenerator();
		Number number = identifierGenerator.nextId(new Object());
		newObj.put("id", number.longValue());
		String currentTime=TimeUtils.getCurrentTime();
		newObj.put("create_time", currentTime);
		newObj.put("update_time", currentTime);
		newObj.put("device_date_time",deviceDateTime);
		thresholdCurrentStatusList.add(newObj);
		if(ValidationUtil.isNotEmpty(plantId)){
			GuardianAlarmThresholdEntity thresholdEntity= new GuardianAlarmThresholdEntity();
			BeanUtils.copyProperties(thresholdCurrentStatus, thresholdEntity);
			thresholdEntity.setId(null);
			thresholdEntities.add(thresholdEntity);
		}

	}



}

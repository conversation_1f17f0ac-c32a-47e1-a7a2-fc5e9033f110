/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.lazzen.remotecontrolinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 遥感信息 实体类
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@TableName("lazzen_device_guard_remote_control_info")
@ApiModel(value = "DeviceGuardRemoteControlInfo对象", description = "遥感信息")
@EqualsAndHashCode(callSuper = true)
public class DeviceGuardRemoteControlInfoEntity extends TenantEntity {

	/**
	 * 设备网关唯一编号
	 */
	@ApiModelProperty(value = "设备网关唯一编号")
	private String gatewayUniqueNumber;
	/**
	 * 设备断路器地址64为第一位置
	 */
	@ApiModelProperty(value = "设备断路器地址64为第一位置")
	private String circuitBreakerAddress;
	/**
	 * 开合闸(闸位状态) 0x041F
	 */
	@ApiModelProperty(value = "开合闸(闸位状态) 0x041F")
	private BigDecimal turnOnOff;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

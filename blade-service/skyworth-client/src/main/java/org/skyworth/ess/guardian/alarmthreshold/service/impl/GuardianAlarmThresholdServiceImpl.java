/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.guardian.alarmthreshold.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.guardian.alarmthreshold.entity.GuardianAlarmThresholdEntity;
import org.skyworth.ess.guardian.alarmthreshold.vo.GuardianAlarmThresholdVO;
import org.skyworth.ess.guardian.alarmthreshold.excel.GuardianAlarmThresholdExcel;
import org.skyworth.ess.guardian.alarmthreshold.mapper.GuardianAlarmThresholdMapper;
import org.skyworth.ess.guardian.alarmthreshold.service.IGuardianAlarmThresholdService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;
import java.util.Map;

/**
 * 安全卫士阈值&amp;闸位状态 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class GuardianAlarmThresholdServiceImpl extends BaseServiceImpl<GuardianAlarmThresholdMapper, GuardianAlarmThresholdEntity> implements IGuardianAlarmThresholdService {

	@Override
	public IPage<GuardianAlarmThresholdVO> selectGuardianAlarmThresholdPage(IPage<GuardianAlarmThresholdVO> page, GuardianAlarmThresholdVO GuardianAlarmThreshold) {
		return page.setRecords(baseMapper.selectGuardianAlarmThresholdPage(page, GuardianAlarmThreshold));
	}


	@Override
	public List<GuardianAlarmThresholdExcel> exportGuardianAlarmThreshold(Wrapper<GuardianAlarmThresholdEntity> queryWrapper) {
        return baseMapper.exportGuardianAlarmThreshold(queryWrapper);
	}

	@Override
	public int deleteLogicByPlantIdAndSn(Long plantId, String securityGuardSerialNumber) {
		LambdaUpdateWrapper<GuardianAlarmThresholdEntity> update = Wrappers.<GuardianAlarmThresholdEntity>update().lambda()
			.set(GuardianAlarmThresholdEntity::getIsDeleted, 1)
			.eq(GuardianAlarmThresholdEntity::getPlantId, plantId)
			.eq(GuardianAlarmThresholdEntity::getSecurityGuardSerialNumber, securityGuardSerialNumber);
		return baseMapper.delete(update);
	}

	@Override
	public Map<String, Object> getAllAlarmThresholdSetup(Long plantId, String serialNumber) {
		return baseMapper.getAllAlarmThresholdSetup(plantId, serialNumber);
	}

}

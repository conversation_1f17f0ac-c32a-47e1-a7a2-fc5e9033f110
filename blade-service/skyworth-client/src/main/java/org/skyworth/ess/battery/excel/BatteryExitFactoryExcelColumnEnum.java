package org.skyworth.ess.battery.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum BatteryExitFactoryExcelColumnEnum {
	batterySerialNumber("Battery Serial Number", "储能SN"),
	batteryType("Battery Type", "储能型号"),
	ratedBatteryVoltage("Battery Voltage", "额定储能电压"),
	ratedBatteryCapacity("Battery Capacity", "额定储能容量"),
	ratedBatteryEnergy("Battery Energy", "额定储能能量"),
	singleCapacity("Cell Capacity", "单体容量"),
	singleSeriesParallelingNumber("Cell Series or Paralleling Number", "内含单体串并联数"),
	company("company", "公司"),
	exitFactoryDate("exit Factory Date", "出厂日期"),
	qualityGuaranteeYear("quality Guarantee Year", "质保年限");

	private String columnEn;
	private String columnCn;

	BatteryExitFactoryExcelColumnEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new HashSet<>();
		for (BatteryExitFactoryExcelColumnEnum item : BatteryExitFactoryExcelColumnEnum.values()) {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}

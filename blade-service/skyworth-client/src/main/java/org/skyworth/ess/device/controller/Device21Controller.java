/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.vo.Device21VO;
import org.skyworth.ess.device.excel.Device21Excel;
import org.skyworth.ess.device.wrapper.Device21Wrapper;
import org.skyworth.ess.device.service.IDevice21Service;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备/智能能量变换器表，记录2.1数据 控制器
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-Device21/Device21")
@Api(value = "设备/智能能量变换器表，记录2.1数据", tags = "设备/智能能量变换器表，记录2.1数据接口")
public class Device21Controller extends BladeController {

	private final IDevice21Service Device21Service;

	/**
	 * 设备/智能能量变换器表，记录2.1数据 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入Device21")
	public R<Device21VO> detail(Device21Entity Device21) {
		Device21Entity detail = Device21Service.getOne(Condition.getQueryWrapper(Device21));
		return R.data(Device21Wrapper.build().entityVO(detail));
	}
	/**
	 * 设备/智能能量变换器表，记录2.1数据 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入Device21")
	public R<IPage<Device21VO>> list(@ApiIgnore @RequestParam Map<String, Object> Device21, Query query) {
		IPage<Device21Entity> pages = Device21Service.page(Condition.getPage(query), Condition.getQueryWrapper(Device21, Device21Entity.class));
		return R.data(Device21Wrapper.build().pageVO(pages));
	}

	/**
	 * 设备/智能能量变换器表，记录2.1数据 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入Device21")
	public R<IPage<Device21VO>> page(Device21VO Device21, Query query) {
		IPage<Device21VO> pages = Device21Service.selectDevice21Page(Condition.getPage(query), Device21);
		return R.data(pages);
	}

	/**
	 * 设备/智能能量变换器表，记录2.1数据 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入Device21")
	public R save(@Valid @RequestBody Device21Entity Device21) {
		return R.status(Device21Service.save(Device21));
	}

	/**
	 * 设备/智能能量变换器表，记录2.1数据 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入Device21")
	public R update(@Valid @RequestBody Device21Entity Device21) {
		return R.status(Device21Service.updateById(Device21));
	}

	/**
	 * 设备/智能能量变换器表，记录2.1数据 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入Device21")
	public R submit(@Valid @RequestBody Device21Entity Device21) {
		return R.status(Device21Service.saveOrUpdate(Device21));
	}

	/**
	 * 设备/智能能量变换器表，记录2.1数据 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(Device21Service.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-Device21")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入Device21")
	public void exportDevice21(@ApiIgnore @RequestParam Map<String, Object> Device21, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<Device21Entity> queryWrapper = Condition.getQueryWrapper(Device21, Device21Entity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Device21::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(Device21Entity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<Device21Excel> list = Device21Service.exportDevice21(queryWrapper);
		ExcelUtil.export(response, "设备/智能能量变换器表，记录2.1数据数据" + DateUtil.time(), "设备/智能能量变换器表，记录2.1数据数据表", list, Device21Excel.class);
	}

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.alarmoperationrecord.service.impl;

import org.skyworth.ess.alarmoperationrecord.entity.AlarmLogOperationRecordEntity;
import org.skyworth.ess.alarmoperationrecord.vo.AlarmLogOperationRecordVO;
import org.skyworth.ess.alarmoperationrecord.excel.AlarmLogOperationRecordExcel;
import org.skyworth.ess.alarmoperationrecord.mapper.AlarmLogOperationRecordMapper;
import org.skyworth.ess.alarmoperationrecord.service.IAlarmLogOperationRecordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 告警日志操作记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
public class AlarmLogOperationRecordServiceImpl extends BaseServiceImpl<AlarmLogOperationRecordMapper, AlarmLogOperationRecordEntity> implements IAlarmLogOperationRecordService {

	@Override
	public IPage<AlarmLogOperationRecordVO> selectAlarmLogOperationRecordPage(IPage<AlarmLogOperationRecordVO> page, AlarmLogOperationRecordVO alarmLogOperationRecord) {
		return page.setRecords(baseMapper.selectAlarmLogOperationRecordPage(page, alarmLogOperationRecord));
	}


	@Override
	public List<AlarmLogOperationRecordExcel> exportAlarmLogOperationRecord(Wrapper<AlarmLogOperationRecordEntity> queryWrapper) {
		List<AlarmLogOperationRecordExcel> alarmLogOperationRecordList = baseMapper.exportAlarmLogOperationRecord(queryWrapper);
		//alarmLogOperationRecordList.forEach(alarmLogOperationRecord -> {
		//	alarmLogOperationRecord.setTypeName(DictCache.getValue(DictEnum.YES_NO, AlarmLogOperationRecord.getType()));
		//});
		return alarmLogOperationRecordList;
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.balconypv.mapper.EnergySystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="energySystemResultMap" type="org.skyworth.ess.balconypv.entity.EnergySystemEntity">
        <result column="id" property="id"/>
        <result column="energy_system_name" property="energySystemName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectEnergySystemPage" resultType="org.skyworth.ess.balconypv.vo.EnergySystemVO">
        select id, energy_system_name, country_code, province_code, city_code, county_code, detail_address,create_user
        from
        energy_system where is_deleted = 0
        <if test="param.energySystemName != null and param.energySystemName != ''">
            and (energy_system_name like concat(#{param.energySystemName},'%')
            <if test="userIds != null and userIds.size > 0">
                or create_user in
                <foreach item="item" collection="userIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>

        <if test="param.createUser != null">
            and create_user = #{param.createUser}
        </if>
        order by id desc
    </select>
</mapper>

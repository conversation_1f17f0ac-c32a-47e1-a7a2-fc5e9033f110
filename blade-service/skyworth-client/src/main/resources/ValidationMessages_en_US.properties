client.battery.query.connot.empty=The query criteria cannot be empty.
client.setItem.delete.fail=Delete the sub-configuration item node first!
client.system.error=System Error
client.email.address.error=The email address is incorrect
client.parameter.error.empty=Parameter exception: Parameter cannot be empty
client.parameter.error.record.duplicate=There are duplicate records, please check.
client.parameter.error.record.empty=Please select a record.
client.invert.serial.number.exist=the inverter serial number : %s is exist.
client.plant.name.exist=the plant name : [%s] is exist
client.ota.record.count.error=The total number of selected records does not match the total number of servers
client.ota.record.upgrade.one=The same SN can only update one type of firmware at a time, and the SN with exception check is as follows:%s
client.inverter.max.parallel.limit=The number of parallel inverters for this model is limited to 2.
client.inverter.bind.same.parallel=only the same model of inverter can be bound to one station.
client.email.code.exist=Email verification code has been sent, please check your inbox, if you do not receive please look in the trash or check if it is blocked.
client.please.register.account.first=Please register account.
client.user.email.is.exits=The email address has been registered
client.inverter.sn.exist=The inverter SN is in use and cannot be deleted
client.weather.plant.name.is.empty=cannot get plant name by code
client.weather.geo.id.empty=cannot get geo id
client.weather.geo.error=get weather geo error\uFF1A%s
client.weather.detail.error=get weather detail error\uFF1A%s
client.guardian.sn.exist=The guardian SN is in use and cannot be deleted
client.data.not.exist=The data does not exist.
client.guardian.not.found.remote.channel=No remote communication channel found!
client.guardian.issue.fail=Failed to issue the guardian instruction.
client.guardian.issue.ip.fail=Failed to set the guardian IP address.
client.guardian.setitem.cannot.empty=The setting item cannot be empty
client.guardian.issue.gateposition.fail=Failed to set the guardian gate position.
client.guardian.issue.alarm.fail=Failed to set the guardian alarm parameters
client.guardian.issue.timepower.fail=Failed to set the guardian timing power
client.guardian.issue.timeswitchgate.fail=Failed to set the guardian timing switch
client.guardian.important.event.add.plant=Establish the energy station
client.guardian.important.event.add.inverter=Inverter is bound to energy station
client.guardian.important.event.add.inverter.factory=Inverter factory information
client.guardian.important.event.add.battery=Battery is bound to inverter
client.guardian.important.event.add.battery.factory=Battery factory information
client.guardian.important.event.unbound.battery=Battery is unbound
client.guardian.important.event.unbound.battery.factory=Battery is unbound factory information
client.guardian.important.event.guardian.bound=Guardian is bound to energy station
client.guardian.important.event.guardian.bound.factory=Guardian factory information
client.guardian.important.event.unbound.inverter=Inverter is unbound
client.guardian.important.event.delete.plant=The energy station is deleted
client.battery.exit.add.sn.exist=The battery SN has been added in the factory delivery information
client.battery.serial.number.exist=the battery serial number : %s is exist.
client.guardian.important.event.backupbox.bound=device is bound to energy station
client.guardian.device.not.exist=device does not exist
client.guardian.issue.timeout=Set timeout. Check the device status
client.guardian.not.online=Issue Setting failed. The device is offline
client.params.time.format.error=The format of the time parameter is incorrect
client.params.time.size.error=The start time must be earlier than the end time
client.ota.record.upgrading=The following devices have firmware being upgraded, please check:%s
client.ota.record.already.latest=The firmware selected for upgrade is already the latest version. Please check or refresh the page:%s
client.energysystem.delete.nopermission=The current user does not have permission to delete this energy system
client.energysystem.not.delete=There is only one energy system under the username, and this energy system does not support deletion

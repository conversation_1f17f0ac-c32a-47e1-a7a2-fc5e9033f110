client.battery.query.connot.empty=\u67E5\u8BE2\u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
client.setItem.delete.fail=\u5148\u5220\u9664\u5B50\u914D\u7F6E\u9879\u8282\u70B9!
client.system.error=\u7CFB\u7EDF\u6B63\u5728\u5F00\u5C0F\u5DEE,\u8BF7\u7A0D\u540E\u518D\u8BD5
client.email.address.error=\u90AE\u7BB1\u5730\u5740\u4E0D\u6B63\u786E
client.parameter.error.empty=\u53C2\u6570\u5F02\u5E38:\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
client.parameter.error.record.duplicate=\u6709\u91CD\u590D\u7684\u8BB0\u5F55,\u8BF7\u786E\u8BA4
client.parameter.error.record.empty=\u8BF7\u9009\u62E9\u4E00\u6761\u8BB0\u5F55
client.invert.serial.number.exist=\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u5E8F\u5217\u53F7 : %s \u5DF2\u88AB\u4F7F\u7528
client.plant.name.exist=\u7AD9\u70B9\u540D\u79F0:[%s]\u5DF2\u5B58\u5728
client.ota.record.count.error=\u6240\u9009\u8BB0\u5F55\u603B\u6570\u548C\u670D\u52A1\u5668\u603B\u6570\u4E0D\u4E00\u81F4
client.ota.record.upgrade.one=\u540C\u4E00\u4E2Asn\u4E00\u6B21\u53EA\u80FD\u66F4\u65B0\u4E00\u79CD\u7C7B\u578B\u7684\u56FA\u4EF6\uFF0C\u5F02\u5E38\u52FE\u9009\u7684sn\u5982\u4E0B:%s
client.inverter.max.parallel.limit=\u8BE5\u578B\u53F7\u7684\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u5E76\u673A\u6570\u91CF\u9650\u5236\u4E3A2\u4E2A\u3002
client.inverter.bind.same.parallel=\u53EA\u6709\u540C\u4E00\u578B\u53F7\u7684\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u53EF\u4EE5\u8FDE\u63A5\u5230\u540C\u4E00\u4E2A\u7535\u7AD9\u3002
client.email.code.exist=\u90AE\u7BB1\u9A8C\u8BC1\u7801\u5DF2\u53D1\u9001\uFF0C\u8BF7\u68C0\u67E5\u60A8\u7684\u6536\u4EF6\u7BB1\uFF0C\u5982\u679C\u6CA1\u6709\u6536\u5230\u8BF7\u67E5\u770B\u5783\u573E\u7BB1\u6216\u8005\u68C0\u67E5\u662F\u5426\u88AB\u62E6\u622A
client.please.register.account.first=\u8BF7\u5148\u6CE8\u518C\u8D26\u53F7\u3002
client.user.email.is.exits=\u8BE5\u90AE\u7BB1\u5DF2\u7ECF\u88AB\u6CE8\u518C
client.inverter.sn.exist=\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668SN\u88AB\u4F7F\u7528\uFF0C\u4E0D\u80FD\u5220\u9664
client.weather.plant.name.is.empty=\u6839\u636E\u7AD9\u70B9\u5730\u5740\u83B7\u53D6\u540D\u79F0\u4E3A\u7A7A
client.weather.geo.id.empty=\u672A\u83B7\u53D6\u5230\u5730\u7406\u4FE1\u606FID
client.weather.geo.error=\u83B7\u53D6\u5730\u7406\u4FE1\u606F\u5931\u8D25\uFF1A%s
client.weather.detail.error=\u83B7\u53D6\u5929\u6C14\u8BE6\u60C5\u5931\u8D25\uFF1A%s
client.guardian.sn.exist=\u5B89\u5168\u536B\u58EBSN\u88AB\u4F7F\u7528\uFF0C\u4E0D\u80FD\u5220\u9664
client.data.not.exist=\u6570\u636E\u4E0D\u5B58\u5728
client.guardian.not.found.remote.channel=\u672A\u627E\u5230\u8FDC\u7A0B\u901A\u9053
client.guardian.issue.fail=\u4E0B\u53D1\u5B89\u5168\u536B\u58EB\u6307\u4EE4\u5931\u8D25
client.guardian.issue.ip.fail=\u8BBE\u7F6E\u5B89\u5168\u536B\u58EBIP\u5730\u5740\u5931\u8D25
client.guardian.setitem.cannot.empty=\u8BBE\u7F6E\u9879\u4E0D\u80FD\u4E3A\u7A7A
client.guardian.issue.gateposition.fail=\u8BBE\u7F6E\u8BBE\u5907\u95F8\u4F4D\u72B6\u6001\u5931\u8D25
client.guardian.issue.alarm.fail=\u8BBE\u7F6E\u5B89\u5168\u536B\u58EB\u544A\u8B66\u9608\u503C\u53C2\u6570\u5931\u8D25
client.guardian.issue.timepower.fail=\u8BBE\u7F6E\u5B89\u5168\u536B\u58EB\u5B9A\u65F6\u529F\u7387\u5931\u8D25
client.guardian.issue.timeswitchgate.fail=\u8BBE\u7F6E\u5B89\u5168\u536B\u58EB\u5B9A\u65F6\u5F00\u5173\u95F8\u5931\u8D25
client.guardian.important.event.add.plant=\u521B\u5EFA\u7535\u7AD9
client.guardian.important.event.add.inverter=\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u7ED1\u5B9A\u7535\u7AD9
client.guardian.important.event.add.inverter.factory=\u51FA\u5382\u4FE1\u606F
client.guardian.important.event.add.battery=\u50A8\u80FD\u7ED1\u5B9A\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668
client.guardian.important.event.add.battery.factory=\u50A8\u80FD\u51FA\u5382\u4FE1\u606F
client.guardian.important.event.unbound.battery=\u50A8\u80FD\u89E3\u7ED1
client.guardian.important.event.unbound.battery.factory=\u50A8\u80FD\u89E3\u7ED1\u51FA\u5382\u4FE1\u606F
client.guardian.important.event.guardian.bound=\u5B89\u5168\u536B\u58EB\u7ED1\u5B9A
client.guardian.important.event.guardian.bound.factory=\u5B89\u5168\u536B\u58EB\u51FA\u5382\u4FE1\u606F
client.guardian.important.event.unbound.inverter=\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u89E3\u7ED1
client.guardian.important.event.delete.plant=\u5220\u9664\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668
client.battery.exit.add.sn.exist=\u8BE5\u50A8\u80FDSN\u5728\u51FA\u5382\u4FE1\u606F\u4E2D\u5DF2\u5B58\u5728
client.battery.serial.number.exist=\u50A8\u80FDSN: %s \u5DF2\u5B58\u5728.
client.guardian.important.event.backupbox.bound=\u5168\u5C4B\u5907\u7535\u7BB1\u7ED1\u5B9A
client.guardian.device.not.exist=\u8BBE\u5907\u4E0D\u5B58\u5728
client.guardian.issue.timeout=\u8BBE\u7F6E\u8D85\u65F6\uFF0C\u8BF7\u68C0\u67E5\u8BBE\u5907\u72B6\u6001
client.guardian.not.online=\u8BBE\u7F6E\u5931\u8D25\uFF0C\u8BBE\u5907\u5DF2\u79BB\u7EBF
client.params.time.format.error=\u65F6\u95F4\u53C2\u6570\u683C\u5F0F\u4E0D\u6B63\u786E
client.params.time.size.error=\u5F00\u59CB\u65F6\u95F4\u5FC5\u987B\u8981\u65E9\u4E8E\u7ED3\u675F\u65F6\u95F4
client.ota.record.upgrading=\u5982\u4E0B\u8BBE\u5907\u5B58\u5728\u5347\u7EA7\u4E2D\u7684\u56FA\u4EF6\uFF0C\u8BF7\u68C0\u67E5:%s
client.ota.record.already.latest=\u9009\u62E9\u5347\u7EA7\u7684\u56FA\u4EF6\u5DF2\u7ECF\u662F\u6700\u65B0\u7248\u672C\uFF0C\u8BF7\u68C0\u67E5\u6216\u5237\u65B0\u9875\u9762:%s
client.energysystem.delete.nopermission=\u5F53\u524D\u7528\u6237\u65E0\u6743\u9650\u5220\u9664\u8BE5\u80FD\u6E90\u7CFB\u7EDF
client.energysystem.not.delete=\u7528\u6237\u540D\u4E0B\u4EC5\u67091\u4E2A\u80FD\u6E90\u7CFB\u7EDF\uFF0C\u8BE5\u80FD\u6E90\u7CFB\u7EDF\u4E0D\u652F\u6301\u5220\u9664

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.inverterinstallation.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.qcsubmission.inverterinstallation.entity.InverterInstallationEntity;
import org.skyworth.ess.qcsubmission.inverterinstallation.vo.InverterInstallationVO;
import java.util.Objects;

/**
 * 施工-智能能量变换器信息; 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public class InverterInstallationWrapper extends BaseEntityWrapper<InverterInstallationEntity, InverterInstallationVO>  {

	public static InverterInstallationWrapper build() {
		return new InverterInstallationWrapper();
 	}

	@Override
	public InverterInstallationVO entityVO(InverterInstallationEntity inverterinstallation) {
		InverterInstallationVO inverterinstallationVO = Objects.requireNonNull(BeanUtil.copy(inverterinstallation, InverterInstallationVO.class));

		//User createUser = UserCache.getUser(inverterinstallation.getCreateUser());
		//User updateUser = UserCache.getUser(inverterinstallation.getUpdateUser());
		//inverterinstallationVO.setCreateUserName(createUser.getName());
		//inverterinstallationVO.setUpdateUserName(updateUser.getName());

		return inverterinstallationVO;
	}


}

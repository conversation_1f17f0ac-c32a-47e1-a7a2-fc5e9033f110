/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.vo.OrderNodeSubStatusVO;
import java.util.Objects;

/**
 * 订单节点子状态 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public class OrderNodeSubStatusWrapper extends BaseEntityWrapper<OrderNodeSubStatusEntity, OrderNodeSubStatusVO>  {

	public static OrderNodeSubStatusWrapper build() {
		return new OrderNodeSubStatusWrapper();
 	}

	@Override
	public OrderNodeSubStatusVO entityVO(OrderNodeSubStatusEntity orderNodeSubStatus) {
		OrderNodeSubStatusVO orderNodeSubStatusVO = Objects.requireNonNull(BeanUtil.copy(orderNodeSubStatus, OrderNodeSubStatusVO.class));

		//User createUser = UserCache.getUser(orderNodeSubStatus.getCreateUser());
		//User updateUser = UserCache.getUser(orderNodeSubStatus.getUpdateUser());
		//orderNodeSubStatusVO.setCreateUserName(createUser.getName());
		//orderNodeSubStatusVO.setUpdateUserName(updateUser.getName());

		return orderNodeSubStatusVO;
	}


}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.mapper;

import org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * coc和维护信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface CocMaintenanceInfoMapper extends BaseMapper<CocMaintenanceInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cocMaintenanceInfo
	 * @return
	 */
	List<CocMaintenanceInfoVO> selectCocMaintenanceInfoPage(IPage page, CocMaintenanceInfoVO cocMaintenanceInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<CocMaintenanceInfoExcel> exportCocMaintenanceInfo(@Param("ew") Wrapper<CocMaintenanceInfoEntity> queryWrapper);

	/**
	 * 更新coc信息
	 * @param data
	 * @param orderId
	 * @return
	 */
	int updateCocMaintenanceByOrderId(@Param("data") Map<String,Object> data,@Param("orderId")long orderId);

	int insertCocMaintenance(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);

	List<BasicOrderDTO> selectBasicOrderInfo(long orderId);

}

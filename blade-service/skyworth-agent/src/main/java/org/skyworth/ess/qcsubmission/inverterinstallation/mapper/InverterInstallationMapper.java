/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.inverterinstallation.mapper;

import org.skyworth.ess.qcsubmission.inverterinstallation.entity.InverterInstallationEntity;
import org.skyworth.ess.qcsubmission.inverterinstallation.vo.InverterInstallationVO;
import org.skyworth.ess.qcsubmission.inverterinstallation.excel.InverterInstallationExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 施工-智能能量变换器信息; Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface InverterInstallationMapper extends BaseMapper<InverterInstallationEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inverterinstallation
	 * @return
	 */
	List<InverterInstallationVO> selectInverterInstallationPage(IPage page, InverterInstallationVO inverterinstallation);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<InverterInstallationExcel> exportInverterInstallation(@Param("ew") Wrapper<InverterInstallationEntity> queryWrapper);

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.installrelatedInfo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.vo.InstallRelatedInfoVO;
import org.skyworth.ess.qcsubmission.installrelatedInfo.excel.InstallRelatedInfoExcel;
import org.skyworth.ess.qcsubmission.installrelatedInfo.wrapper.InstallRelatedInfoWrapper;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 安装相关信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-relatedInfo/relatedInfo")
@Api(value = "安装相关信息", tags = "安装相关信息接口")
public class InstallRelatedInfoController extends BladeController {

	private final IInstallRelatedInfoService relatedInfoService;

	/**
	 * 安装相关信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入relatedInfo")
	public R<InstallRelatedInfoVO> detail(InstallRelatedInfoEntity relatedInfo) {
		InstallRelatedInfoEntity detail = relatedInfoService.getOne(Condition.getQueryWrapper(relatedInfo));
		return R.data(InstallRelatedInfoWrapper.build().entityVO(detail));
	}
	/**
	 * 安装相关信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入relatedInfo")
	public R<IPage<InstallRelatedInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> relatedInfo, Query query) {
		IPage<InstallRelatedInfoEntity> pages = relatedInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(relatedInfo, InstallRelatedInfoEntity.class));
		return R.data(InstallRelatedInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 安装相关信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入relatedInfo")
	public R<IPage<InstallRelatedInfoVO>> page(InstallRelatedInfoVO relatedInfo, Query query) {
		IPage<InstallRelatedInfoVO> pages = relatedInfoService.selectInstallRelatedInfoPage(Condition.getPage(query), relatedInfo);
		return R.data(pages);
	}

	/**
	 * 安装相关信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入relatedInfo")
	public R save(@Valid @RequestBody InstallRelatedInfoEntity relatedInfo) {
		return R.status(relatedInfoService.save(relatedInfo));
	}

	/**
	 * 安装相关信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入relatedInfo")
	public R update(@Valid @RequestBody InstallRelatedInfoEntity relatedInfo) {
		return R.status(relatedInfoService.updateById(relatedInfo));
	}

	/**
	 * 安装相关信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入relatedInfo")
	public R submit(@Valid @RequestBody InstallRelatedInfoEntity relatedInfo) {
		return R.status(relatedInfoService.saveOrUpdate(relatedInfo));
	}

	/**
	 * 安装相关信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(relatedInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-relatedInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入relatedInfo")
	public void exportInstallRelatedInfo(@ApiIgnore @RequestParam Map<String, Object> relatedInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<InstallRelatedInfoEntity> queryWrapper = Condition.getQueryWrapper(relatedInfo, InstallRelatedInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(InstallRelatedInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(InstallRelatedInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<InstallRelatedInfoExcel> list = relatedInfoService.exportInstallRelatedInfo(queryWrapper);
		ExcelUtil.export(response, "安装相关信息数据" + DateUtil.time(), "安装相关信息数据表", list, InstallRelatedInfoExcel.class);
	}

}

package org.skyworth.ess.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum SkuStockLogTypeEnum {
    ADD("add","库存新增"),
    CANCEL("cancel","库存取消"),
    CONSUME("consume","订单消耗"),
    CANCEL_CONSUME("cancel_consume","取消订单"),
	REJECT_CONSUME("reject_consume","拒绝订单")
    ;
    final String logTypeCode;
    final String remark;
    SkuStockLogTypeEnum(String logTypeCode, String remark) {
        this.logTypeCode = logTypeCode;
        this.remark = remark;
    }


}

package org.skyworth.ess.revieworder.orderworkflow.service.impl;

import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.skyworth.ess.utils.ImgBusinessUtils;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 图片处理 服务类
 *
 * <AUTHOR>
 * @since 2023-12-4
 */
@Service
public class AttachmentInfoServiceImpl implements IAttachmentInfoService {
	@Autowired
	private IAttachmentInfoClient attachmentInfoService;

	@Autowired
	private IAdditionalInfoService additionalInfoService;


	public AttachmentDescVO getAttachmentInfo(Object object) {
		try {
			if (object == null) {
				return null;
			}
			AttachmentDescVO attachmentDescVO = new AttachmentDescVO();
			// 图片处理
			Map<String, Object> map = ImgBusinessUtils.getImgBizKey(object);
			if (map != null) {
				List<Long> businessIds = (List<Long>) map.get("businessIds");
				Map<String, Long> properties = (Map<String, Long>) map.get("properties");
				Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoService.findByBusinessIds(businessIds).getData();
				if (attachmentInfoMap != null) {
					Map<String, List<AttachmentInfoEntity>> attachmentInfoDesc = ImgBusinessUtils.getAttachmentInfoDesc(attachmentInfoMap, properties);
					attachmentDescVO.setAttachmentInfoDesc(attachmentInfoDesc);
				}

				Map<Long, String> attachmentInfoViewMap = additionalInfoService.selectAdditionalMapByBusinessIds(businessIds);
				if (attachmentInfoViewMap != null) {
					Map<String, String> attachmentInfoViewDesc = ImgBusinessUtils.getAttachmentInfoDescView(attachmentInfoViewMap, properties);
					attachmentDescVO.setAttachmentInfoDescView(attachmentInfoViewDesc);
				}
			}
			return attachmentDescVO;
		} catch (Exception e) {
			throw new BusinessException("load img failure");
		}
	}

	@Override
	public void saveAttachmentInfo(Object object) {
		Map<String, Object> properties = ImgBusinessUtils.getBatchVODescOperationList(object);

		// 保存附件信息
		if (Objects.nonNull(properties.get("batchVO"))) {
			BatchVO<AttachmentInfoEntity> batchVO = (BatchVO<AttachmentInfoEntity>) properties.get("batchVO");
			attachmentInfoService.saveAndUpdate(batchVO);
		}
		// 保存图片备注信息
		if (Objects.nonNull(properties.get("imgDescOperationList"))) {
			List<AdditionalInfoEntity> imgDescOperationList = (List<AdditionalInfoEntity>) properties.get("imgDescOperationList");
			additionalInfoService.saveAdditionalInfoEntityList(imgDescOperationList);
		}

	}

}

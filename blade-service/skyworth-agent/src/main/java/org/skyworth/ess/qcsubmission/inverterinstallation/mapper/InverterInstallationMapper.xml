<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.inverterinstallation.mapper.InverterInstallationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inverterinstallationResultMap" type="org.skyworth.ess.qcsubmission.inverterinstallation.entity.InverterInstallationEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="inverter_type" property="inverterType"/>
        <result column="inverter_type_other_remark" property="inverterTypeOtherRemark"/>
<!--        <result column="number_of_batteries" property="numberOfBatteries"/>-->
<!--        <result column="number_of_batteries_other_remark" property="numberOfBatteriesOtherRemark"/>-->
        <result column="verification_of_specification_model_img_biz_key" property="verificationOfSpecificationModelImgBizKey"/>
<!--        <result column="inverter_serial_number_img_biz_key" property="inverterSerialNumberImgBizKey"/>-->
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectInverterInstallationPage" resultMap="inverterinstallationResultMap">
        select * from qc_inverter_installation where is_deleted = 0
    </select>


    <select id="exportInverterInstallation" resultType="org.skyworth.ess.qcsubmission.inverterinstallation.excel.InverterInstallationExcel">
        SELECT * FROM qc_inverter_installation ${ew.customSqlSegment}
    </select>

</mapper>

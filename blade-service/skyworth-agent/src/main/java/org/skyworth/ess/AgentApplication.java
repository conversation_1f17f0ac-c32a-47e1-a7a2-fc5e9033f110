/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess;


import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.cloud.client.BladeCloudApplication;
import org.springblade.core.cloud.feign.EnableBladeFeign;
import org.springblade.core.launch.BladeApplication;
import org.springblade.core.launch.utils.PropsUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.lang.annotation.*;
import java.util.Properties;

/**
 * 系统模块启动器
 *
 * <AUTHOR>
 */
@EnableMethodCache(basePackages = "org.skyworth.ess")
@EnableCreateCacheAnnotation
@EnableDiscoveryClient
@EnableFeignClients({"org.springblade", "org.skyworth"})
@SpringBootApplication
@EnableTransactionManagement
@ImportResource("applicationContext.xml")
//@ComponentScan(basePackages = "org.skyworth.ess")
public class AgentApplication implements CommandLineRunner {
	@Autowired
	private ConfigurableEnvironment springEnv;

	public static void main(String[] args) {
		BladeApplication.run(CommonConstant.APPLICATION_AGENT_NAME, AgentApplication.class, args);
	}

	/**
	 * 打印配置文件
	 *
	 * @Description
	 * @Param args
	 * @Return void
	 * <AUTHOR>
	 * @Date 2023/8/22 13:43
	 **/
	@Override
	public void run(String... args) throws Exception {
		CommonUtil.printConfigurationParameters(springEnv);
	}
}


/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.color.PDDeviceGray;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationWidget;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.apache.pdfbox.pdmodel.interactive.form.PDSignatureField;
import org.jetbrains.annotations.Nullable;
import org.skyworth.ess.aspect.FileSave;
import org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel;
import org.skyworth.ess.cocmaintenanceinfo.mapper.CocMaintenanceInfoMapper;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocFacFileVo;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IMaintOrderInstallRelatedService;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.MaintRelatedVO;
import org.skyworth.ess.device.client.IPlantClient;
import org.skyworth.ess.device.entity.PlantInfoVO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.skyworth.ess.qcsubmission.qcotherInformation.service.IQcOtherInformationService;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.vo.EmailVO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.impl.OrderRelatedUserServiceImpl;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.*;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * coc和维护信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */

@Slf4j
@Service
public class CocMaintenanceInfoServiceImpl extends BaseServiceImpl<CocMaintenanceInfoMapper, CocMaintenanceInfoEntity> implements ICocMaintenanceInfoService {
	@Autowired
	private IOrderWorkFlowService orderWorkFlowService;
	@Autowired
	private IQcOtherInformationService otherInformationService;
	@Autowired
	private IOrderNodeSubStatusService orderNodeSubStatusService;
	@Autowired
	private IAttachmentInfoClient attachmentInfoClient;
	@Autowired
	private IOrderService orderService;
	@Autowired
	private IReviewOrderService reviewOrderService;
	@Autowired
	private IOrderRelatedUserService orderRelatedUser;
	@Autowired
	private IDictBizClient dictBizClient;

	@Autowired
	private IPlantClient plantClient;

	@Autowired
	private IInstallRelatedInfoService installRelatedInfoService;

	@Autowired
	private IMaintOrderInstallRelatedService maintOrderInstallRelatedService;
	@Autowired
	private OrderRelatedUserServiceImpl orderRelatedUserServiceImpl;

	@Override
	public IPage<CocMaintenanceInfoVO> selectCocMaintenanceInfoPage(IPage<CocMaintenanceInfoVO> page, CocMaintenanceInfoVO cocMaintenanceInfo) {
		return page.setRecords(baseMapper.selectCocMaintenanceInfoPage(page, cocMaintenanceInfo));
	}


	@Override
	public List<CocMaintenanceInfoExcel> exportCocMaintenanceInfo(Wrapper<CocMaintenanceInfoEntity> queryWrapper) {
		List<CocMaintenanceInfoExcel> cocMaintenanceInfoList = baseMapper.exportCocMaintenanceInfo(queryWrapper);
		//cocMaintenanceInfoList.forEach(cocMaintenanceInfo -> {
		//	cocMaintenanceInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, cocMaintenanceInfo.getType()));
		//});
		return cocMaintenanceInfoList;
	}


	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean qcVerifyAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO, Integer processType) {
		List<OrderNodeSubStatusEntity> contentVoList = cocMaintenanceInfoDTO.getContent();
		String nodeType = "qcVerification";
		Long orderId = cocMaintenanceInfoDTO.getOrderId();
		contentVoList.stream().parallel().forEach(v -> {
			v.setBusinessType("approve");
			v.setOrderId(orderId);
			v.setNodeName(nodeType);
		});
		// 设置处理人:施工队长
		getUserInfo(orderId, cocMaintenanceInfoDTO.getOrderFlowDTO().getVariables());
		//删除子节点旧数据
		orderNodeSubStatusService.deleteByOrderId(orderId, nodeType, "approve");
		orderNodeSubStatusService.saveOrUpdateBatch(contentVoList);
		reviewOrderService.examineApprove(cocMaintenanceInfoDTO.getOrderFlowDTO());
		return true;
	}

	@Override
	public boolean qcVerifyDeliveryManagerAudit(OrderFlowDTO orderFlowDTO) {
		String businessId = orderFlowDTO.getBusinessId();
		String taskId = orderFlowDTO.getTaskId();
		String examineApproveType = orderFlowDTO.getVariables().get("examineApproveType").toString();
		if (StringUtil.isAnyBlank(businessId, taskId, examineApproveType)) {
			throw new BusinessException("agent.parameter.notEmpty");
		}
		// 设置处理人:施工队长
		getUserInfo(Long.parseLong(businessId), orderFlowDTO.getVariables());
		try {
			reviewOrderService.examineApprove(orderFlowDTO);
		} catch (BusinessException e) {
			// 如果是业务异常，直接抛出
			throw e;
		} catch (Exception e) {
			// 记录详细的错误信息
			log.error("CocMaintenanceInfoServiceImpl->qcVerifyDeliveryManagerAudit, " +
					"businessId={}, taskId={}, examineApproveType={}, error:",
				businessId, taskId, examineApproveType, e);
			throw new BusinessException("system.error");
		}
		return true;
	}

	/**
	 * 根据订单ID获取用户信息
	 * 此方法旨在通过指定的订单ID获取与该订单相关的用户信息，特别是现场工程师的信息
	 * 它查询数据库以获取与订单相关联的用户实体，并将施工人员的用户ID存储到变量映射中
	 *
	 * @param orderId   订单ID，用于定位特定的订单
	 * @param variables 变量映射，用于存放查询到的用户信息
	 */
	private void getUserInfo(Long orderId, Map<String, Object> variables) {
		// 创建查询包装器，用于构建查询条件
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		// 添加条件：匹配特定的订单ID
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderId);
		// 添加条件：匹配特定的节点类型（安装工作单分配）
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode());
		// 添加条件：匹配施工人员的用户类型
		orderRelatedUserEntityLambdaQueryWrapper.in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		// 执行查询并获取结果列表
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = orderRelatedUser.list(orderRelatedUserEntityLambdaQueryWrapper);
		// 如果查询结果为空，则直接返回
		if (CollectionUtils.isEmpty(orderRelatedUserEntityList)) {
			return;
		}
		// 从查询结果中筛选出现场工程师领导的记录，并取第一个
		Optional<OrderRelatedUserEntity> surveyUserOptional = orderRelatedUserEntityList.stream()
			.filter(re -> UserTypeEnum.SITE_TECHNICIAN_LEADER.getValue().equals(re.getUserType()))
			.findFirst();
		// 如果存在施工人员的信息，则将其存入变量映射中
		surveyUserOptional.ifPresent(surveyUserList -> variables.put("siteEngineer", surveyUserList.getUserId()));
	}

	@Override
	public List<OrderNodeSubStatusEntity> qcVerifyDetail(Long orderId) {
		return orderNodeSubStatusService.getNodeByOrderId(orderId, "approve");
	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean temporaryCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		int res;
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (cocMaintenanceInfoEntityList != null && !cocMaintenanceInfoEntityList.isEmpty()) {
//			data.put("temporary_coc_img_biz_key", cocMaintenanceInfoDTO.getTemporaryCocImgBizKey());
//			data.put("temporary_coc_start_date", cocMaintenanceInfoDTO.getTemporaryCocStartDate());
			res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		} else {
			res = baseMapper.insert(cocMaintenanceInfoDTO);
		}
		Map<String, Object> variables = new HashMap<>();
		//getUserInfo(cocMaintenanceInfoDTO, variables);
		variables.put("wfCurrentType", "role");
		//更新工作流状态
		//updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO temporaryCocDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
//			List<Long> businessIds = new ArrayList<>();
//			Long key = cocMaintenanceInfo.getTemporaryCocImgBizKey();
//			if (ValidationUtil.isNotEmpty(key)) {
//				businessIds.add(key);
//				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
//				cocMaintenanceInfoDTO.setCocImg(attachments.get(key));
//			}

			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean facAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		OrderFlowDTO orderFlowDTO = cocMaintenanceInfoDTO.getOrderFlowDTO();
		Map<String, Object> variables = orderFlowDTO.getVariables();
		Object examineApproveType = variables.get("examineApproveType");
		if (examineApproveType == null) {
			// 处理 examineApproveType 为 null 的情况
			throw new BusinessException("agent.examineApproveType.cannot.be.null");
		}
		String bizType = cocMaintenanceInfoDTO.getBizType();
		// 驳回操作
		if (StringUtil.isBlank(bizType) && BizConstant.CHAR_TWO.equals(examineApproveType)) {
			// 更新工作流状态
			reviewOrderService.examineApprove(cocMaintenanceInfoDTO.getOrderFlowDTO());
		} else {
			// 保存或通过操作
			saveOrPassFacAudit(cocMaintenanceInfoDTO, bizType);
		}
		return true;
	}

	/**
	 * 保存或通过操作
	 *
	 * @param cocMaintenanceInfoDTO coc对象
	 * @param bizType               入参
	 * <AUTHOR>
	 * @since 2024/9/2 15:08
	 **/
	private void saveOrPassFacAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO, String bizType) {
		// 保存+审批操作
		CocMaintenanceInfoEntity cocMaintenanceInfoEntity = new CocMaintenanceInfoEntity();
		cocMaintenanceInfoEntity.setFacDeclaration(cocMaintenanceInfoDTO.getFacDeclaration());
		cocMaintenanceInfoEntity.setSignerRelationship(cocMaintenanceInfoDTO.getSignerRelationship());
		cocMaintenanceInfoEntity.setFacPlantId(cocMaintenanceInfoDTO.getFacPlantId());
		cocMaintenanceInfoEntity.setFacTechnicianSign(cocMaintenanceInfoDTO.getFacTechnicianSign());
		cocMaintenanceInfoEntity.setFacPlantName(cocMaintenanceInfoDTO.getFacPlantName());
		cocMaintenanceInfoEntity.setOrderId(cocMaintenanceInfoDTO.getOrderId());
		cocMaintenanceInfoEntity.setFacLandlordSign(cocMaintenanceInfoDTO.getFacLandlordSign());
		// 提交审核
		if (BizConstant.CHAR_TWO.equals(bizType)) {
			// 判断电站是否被其他安全订单绑定过，一个电站只能被一个安装订单关联，如果被绑定过，则不进行下一步操作
			if (ValidationUtil.isNotEmpty(cocMaintenanceInfoDTO.getFacPlantId())) {
				LambdaQueryWrapper<CocMaintenanceInfoEntity> lambdaQueryWrapper = Wrappers.<CocMaintenanceInfoEntity>query().lambda().eq(CocMaintenanceInfoEntity::getFacPlantId, cocMaintenanceInfoDTO.getFacPlantId()).notIn(CocMaintenanceInfoEntity::getOrderId, cocMaintenanceInfoDTO.getOrderId());
				List<CocMaintenanceInfoEntity> maintenanceInfoEntityList = this.list(lambdaQueryWrapper);
				if (ValidationUtil.isNotEmpty(maintenanceInfoEntityList)) {
					throw new BusinessException("agent.station.selectedBy.another.order");
				}
			}
			// 获取当前时间
			LocalDateTime now = LocalDateTime.now();
			// 加一个月
			LocalDateTime oneMonthLater = now.plusMonths(1);
			// 加一年
			LocalDateTime oneYearLater = now.plusYears(1);
			// 将时间转换为字符串
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			cocMaintenanceInfoEntity.setFreeMaintenanceServiceDate(oneMonthLater.format(formatter));
			cocMaintenanceInfoEntity.setWarrantyDate(oneYearLater.format(formatter));
			//更新工作流状态
			reviewOrderService.examineApprove(cocMaintenanceInfoDTO.getOrderFlowDTO());
			// fac确认好站点后，根据站点id跨服务调接口把安装信息同步到站点表
			syncInstallInfoToPlant(cocMaintenanceInfoDTO);
		}
		// 更新信息
		baseMapper.insert(cocMaintenanceInfoEntity);
	}

	/**
	 * fac审批通过
	 *
	 * @param cocMaintenanceInfoDTO 入参
	 * <AUTHOR>
	 * @since 2024/9/2 14:11
	 **/
	private void facAuditPass(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {

	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean maintFacAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
//		data.put("fac_landlord_sign", cocMaintenanceInfoDTO.getFacLandlordSign());
		data.put("fac_technician_sign", cocMaintenanceInfoDTO.getFacTechnicianSign());
//		data.put("fac_declaration", cocMaintenanceInfoDTO.getFacDeclaration());
//		data.put("customers_acceptance", cocMaintenanceInfoDTO.getCustomersAcceptance());
//		data.put("signer_relationship", cocMaintenanceInfoDTO.getSignerRelationship());
		data.put("fac_plant_id", cocMaintenanceInfoDTO.getFacPlantId());
		data.put("fac_plant_name", cocMaintenanceInfoDTO.getFacPlantName());
		data.put("update_time", TimeUtils.getCurrentTime());
		cocMaintenanceInfoDTO.setUpdateTime(new Date());
		//提交审核
		if ("2".equals(cocMaintenanceInfoDTO.getBizType())) {
			// 获取当前时间
			LocalDateTime now = LocalDateTime.now();
			// 加一个月
			LocalDateTime oneMonthLater = now.plusMonths(1);
			// 加一年
			LocalDateTime oneYearLater = now.plusYears(1);
			// 将时间转换为字符串
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			data.put("free_maintenance_service_date", oneMonthLater.format(formatter));
			data.put("warranty_date", oneYearLater.format(formatter));
			cocMaintenanceInfoDTO.setFreeMaintenanceServiceDate(oneMonthLater.format(formatter));
			cocMaintenanceInfoDTO.setWarrantyDate(oneYearLater.format(formatter));
			Map<String, Object> variables = new HashMap<>();
			variables.put("wfCurrentType", "role");
			//更新工作流状态
			updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		}
		// fac确认好站点后，根据站点id跨服务调接口把安装信息同步到站点表
		syncInstallInfoToPlant(cocMaintenanceInfoDTO);
		int res;
		// 根据orderId查询cocMaintenanceInfo
		LambdaQueryWrapper<CocMaintenanceInfoEntity> lambdaQueryWrapper = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(lambdaQueryWrapper);
		if (ValidationUtil.isNotEmpty(cocMaintenanceInfoEntityList)) {
			res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		} else {
			// 如果跳过了coc，则需要插入coc信息
			res = baseMapper.insert(cocMaintenanceInfoDTO);
		}
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO facDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);

			/*MaintRelatedVO maintRelatedVO = maintOrderInstallRelatedService.queryInstallOrderByMaintId(orderId);
			if (ObjectUtil.isNotEmpty(maintRelatedVO) && ObjectUtil.isNotEmpty(maintRelatedVO.getOrderInstallId())) {
				LambdaQueryWrapper<CocMaintenanceInfoEntity> lambdaQueryWrapper = Wrappers.<CocMaintenanceInfoEntity>query().lambda().eq(CocMaintenanceInfoEntity::getOrderId, maintRelatedVO.getOrderInstallId());
				CocMaintenanceInfoEntity maintenanceInfoEntity = this.getOne(lambdaQueryWrapper);
				if (ObjectUtil.isNotEmpty(maintenanceInfoEntity)) {
					cocMaintenanceInfoDTO.setFacPlantId(maintenanceInfoEntity.getFacPlantId());
					cocMaintenanceInfoDTO.setFacPlantName(maintenanceInfoEntity.getFacPlantName());
				}
			}*/

			List<Long> businessIds = new ArrayList<>();
			Long facLandlordSign = cocMaintenanceInfo.getFacLandlordSign();
			Long facTechnicianSign = cocMaintenanceInfo.getFacTechnicianSign();
//			Long customersAcceptance = cocMaintenanceInfo.getCustomersAcceptance();
			if (ValidationUtil.isNotEmpty(facLandlordSign)) {
				businessIds.add(facLandlordSign);
			}
			if (ValidationUtil.isNotEmpty(facTechnicianSign)) {
				businessIds.add(facTechnicianSign);
			}
//			if (ValidationUtil.isNotEmpty(customersAcceptance)) {
//				businessIds.add(customersAcceptance);
//			}
			if (!businessIds.isEmpty()) {
				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
				if (ValidationUtil.isNotEmpty(facLandlordSign)) {
					cocMaintenanceInfoDTO.setFacLandlordSignImg(attachments.get(facLandlordSign));
				}
				if (ValidationUtil.isNotEmpty(facTechnicianSign)) {
					cocMaintenanceInfoDTO.setTechnicianSignImg(attachments.get(facTechnicianSign));
				}
//				if (ValidationUtil.isNotEmpty(customersAcceptance)) {
//					cocMaintenanceInfoDTO.setCustomersAcceptanceFile(attachments.get(customersAcceptance));
//				}
			}

			return cocMaintenanceInfoDTO;
		}
		CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
		cocMaintenanceInfoDTO.setOrderId(orderId);
		// 同步安装信息
		MaintRelatedVO maintRelatedVO = maintOrderInstallRelatedService.queryInstallOrderByMaintId(orderId);
		if (ObjectUtil.isNotEmpty(maintRelatedVO) && ObjectUtil.isNotEmpty(maintRelatedVO.getOrderInstallId())) {
			LambdaQueryWrapper<CocMaintenanceInfoEntity> lambdaQueryWrapper = Wrappers.<CocMaintenanceInfoEntity>query().lambda().eq(CocMaintenanceInfoEntity::getOrderId, maintRelatedVO.getOrderInstallId());
			CocMaintenanceInfoEntity maintenanceInfoEntity = this.getOne(lambdaQueryWrapper);
			if (ObjectUtil.isNotEmpty(maintenanceInfoEntity)) {
				cocMaintenanceInfoDTO.setFacPlantId(maintenanceInfoEntity.getFacPlantId());
				cocMaintenanceInfoDTO.setFacPlantName(maintenanceInfoEntity.getFacPlantName());
			}
		}
		return cocMaintenanceInfoDTO;

	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean balancePayAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>(2);
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		data.put("balance_payment_confirm_doc_biz_key", cocMaintenanceInfoDTO.getBalancePaymentConfirmDocBizKey());
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		// 更新工作流状态
		reviewOrderService.examineApprove(cocMaintenanceInfoDTO.getOrderFlowDTO());
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO balancePayDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (cocMaintenanceInfoEntityList != null && !cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			finalPaymentEmailDetail(cocMaintenanceInfo);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			// 查询订单工作流当前节点状态, 如果当前节点状态为paymentConfirmation，则取默认邮箱
			LambdaQueryWrapper<OrderWorkFlowEntity> queryWrapper = Wrappers.<OrderWorkFlowEntity>query().lambda();
			queryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderId);
			OrderWorkFlowEntity orderWorkFlowEntity = orderWorkFlowService.getOne(queryWrapper);
			if (ObjectUtil.isNotEmpty(orderWorkFlowEntity) && StringUtil.isNotBlank(orderWorkFlowEntity.getWfCurrentStatus()) && orderWorkFlowEntity.getWfCurrentStatus().equals("balancePayment")) {
				// 如果客服默认邮箱为空，则取默认邮箱
				if (StringUtil.isBlank(cocMaintenanceInfoDTO.getPaymentConfirmMailBcc())) {
					// 查询客服默认邮箱
					R<List<DictBiz>> dictBizList = dictBizClient.getList(DictBizCodeEnum.FINANCE_SD_DEFAULT_EMAIL.getDictCode());
					if (dictBizList.isSuccess() && !CollectionUtils.isEmpty(dictBizList.getData())) {
						List<DictBiz> dictBizListData = dictBizList.getData();
						dictBizListData.forEach(dictBiz -> {
							if (dictBiz.getDictKey().equals(DictBizCodeEnum.AGENT_SD_DEFAULT_EMAIL.getDictCode())) {
								cocMaintenanceInfoDTO.setPaymentConfirmMailBcc(dictBiz.getDictValue());
							}
						});
					}
				}
			}
			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@Override
	@FileSave
	public boolean finalCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
//		Integer type = cocMaintenanceInfoDTO.getFinalCocType();
//		data.put("final_coc_img_biz_key", cocMaintenanceInfoDTO.getFinalCocImgBizKey());
//		data.put("final_coc_type", type != null ? type : 0);
//		data.put("final_coc_start_date", cocMaintenanceInfoDTO.getFinalCocStartDate());
//		data.put("client_confirmation_biz_key", cocMaintenanceInfoDTO.getClientConfirmationBizKey());
//		data.put("delivery", cocMaintenanceInfoDTO.getDelivery());
//		String deliveryNumber = cocMaintenanceInfoDTO.getDeliveryNumber();
//		if (ValidationUtil.isNotEmpty(deliveryNumber)) {
//			data.put("delivery_number", deliveryNumber);
//		}
		//未选择永久
//		if (type != null && type == 0) {
//			data.put("final_coc_end_date", cocMaintenanceInfoDTO.getFinalCocEndDate());
//		}
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		Map<String, Object> variables = new HashMap<>();
		//更新工作流状态
		updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO finalCocDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			List<Long> businessIds = new ArrayList<>();
//			Long cocKey = cocMaintenanceInfo.getFinalCocImgBizKey();
//			Long clientKey = cocMaintenanceInfo.getClientConfirmationBizKey();
//			if (ValidationUtil.isNotEmpty(cocKey)) {
//				businessIds.add(cocKey);
//			}
//			if (ValidationUtil.isNotEmpty(clientKey)) {
//				businessIds.add(clientKey);
//			}
//			if (!businessIds.isEmpty()) {
//				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
//				if (ValidationUtil.isNotEmpty(cocKey)) {
//					cocMaintenanceInfoDTO.setFinalCocImg(attachments.get(cocKey));
//				}
//				if (ValidationUtil.isNotEmpty(clientKey)) {
//					cocMaintenanceInfoDTO.setClientConfirmationImg(attachments.get(clientKey));
//				}
//			}
			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@Override
	public boolean maintenanceAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>(3);
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		if (StringUtil.isNotBlank(cocMaintenanceInfoDTO.getFreeMaintenanceServiceDate())) {
			data.put("free_maintenance_service_date", cocMaintenanceInfoDTO.getFreeMaintenanceServiceDate());
		}
		data.put("fi_required_free_service", cocMaintenanceInfoDTO.getFiRequiredFreeService());
		data.put("warranty_date", cocMaintenanceInfoDTO.getWarrantyDate());
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		//更新工作流状态
		reviewOrderService.examineApprove(cocMaintenanceInfoDTO.getOrderFlowDTO());
		return res > 0;
	}

	@Override
	public BasicOrderDTO maintenanceDetail(long orderId) {
		List<BasicOrderDTO> basicOrderDTOList = baseMapper.selectBasicOrderInfo(orderId);
		if (ValidationUtil.isNotEmpty(basicOrderDTOList) && !basicOrderDTOList.isEmpty()) {
			BasicOrderDTO basicOrderDTO = basicOrderDTOList.get(0);
			OrderEntity orderEntity = new OrderEntity();
			orderEntity.setSiteCountryCode(basicOrderDTO.getSiteCountryCode());
			orderEntity.setSiteProvinceCode(basicOrderDTO.getSiteProvinceCode());
			orderEntity.setSiteCityCode(basicOrderDTO.getSiteCityCode());
			orderEntity.setSiteCountyCode(basicOrderDTO.getSiteCountyCode());
			log.info("maintenanceDetail get region name");
			OrderEntity orderEntityNew = orderService.findRegionInfoByCode(orderEntity);
			if (ValidationUtil.isNotEmpty(orderEntityNew)) {
				basicOrderDTO.setSiteCountryCode(orderEntityNew.getSiteCountryName());
				basicOrderDTO.setSiteProvinceCode(orderEntityNew.getSiteProvinceName());
				basicOrderDTO.setSiteCityCode(orderEntityNew.getSiteCityName());
				basicOrderDTO.setSiteCountyCode(orderEntityNew.getSiteCountyName());
			}
			return basicOrderDTO;
		}
		return null;
	}

	@Override
	public void exportFacTemplate(HttpServletResponse response, CocFacFileVo cocFacFileVo) {
		this.ConstructFacTemplate(response, cocFacFileVo);
	}

	private void ConstructFacTemplate(HttpServletResponse response, CocFacFileVo cocFacFileVo) {
		String pathPrefix = "";

		// 获取当前项目路径
		String projectDir = pathPrefix + "/opt/data/facfile";
		// 模板文件路径
		String templatePath = pathPrefix + "/opt/data/facfile/SKYWORTH_FAC_WORD_template.docx";

		File existfile = new File(templatePath);
		if (!existfile.exists()) {
			try {
				Resource resource = new ClassPathResource("facfile/SKYWORTH_FAC_WORD_template.docx");

				InputStream in = resource.getInputStream();
				File file = new File(templatePath);
				FileUtils.copyToFile(in, file);
			} catch (IOException e) {
				log.error("facfile template build fail!");
			}
		}

		// 设置响应头信息
		String fileName = "SKYWORTH_FAC_WORD_";

		// 将导出的文件写入响应流
		try {
			Map<String, Object> renderDataMap = getRenderDataMap(cocFacFileVo);
			String fileType = cocFacFileVo.getFileType();
			Long orderId = cocFacFileVo.getOrderId();

			XWPFTemplate template = XWPFTemplate.compile(templatePath).render(renderDataMap);
			if ("word".equals(fileType)) {
				// 设置响应类型为Word文档
				response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
				response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + orderId + ".docx");

				// 将Word文档写入响应输出流
				ServletOutputStream outputStream = response.getOutputStream();
				BufferedOutputStream bos = new BufferedOutputStream(outputStream);
				template.write(bos);
				bos.flush();
				outputStream.flush();
				bos.close();
				outputStream.close();
				template.close();
			} else if ("pdf".equals(fileType)) {
				long outPdfPathId = orderId + 1L;

				// 先将Word文档保存到临时文件
				String tempWordPath = projectDir + "/temp.docx";
				FileOutputStream os = new FileOutputStream(tempWordPath);
				template.write(os);
				os.close();
				template.close();

				// pdf文件目录
				String outPdfPath = projectDir + "/SKYWORTH_FAC_WORD_" + outPdfPathId + ".pdf";
				String signPdfPath = projectDir + "/SKYWORTH_FAC_WORD_" + orderId + ".pdf";


				// 使用Aspose.Words将Word转换为PDF
				InputStream tempWordPathIs = new FileInputStream(tempWordPath);
				Document doc = new Document(tempWordPathIs);
				FileOutputStream targetFileOs = new FileOutputStream(outPdfPath);
				doc.save(targetFileOs, SaveFormat.PDF);

				//给pdf添加签名区域
				this.createPdfSignature(outPdfPath, signPdfPath);
				tempWordPathIs.close();
				targetFileOs.close();

				// 设置响应类型为PDF文档
				response.setContentType("application/pdf");
				response.setHeader("Content-Type", "application/pdf");
				response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("SKYWORTH_FAC_WORD_", StandardCharsets.UTF_8) + orderId + ".pdf");

				// 将PDF文档写入响应输出流
				// 创建输入流并将文件内容写入响应输出流
				FileInputStream fileInputStream = new FileInputStream(signPdfPath);
				OutputStream outputStream = response.getOutputStream();
				byte[] buffer = new byte[4096];
				int bytesRead;
				while ((bytesRead = fileInputStream.read(buffer)) != -1) {
					outputStream.write(buffer, 0, bytesRead);
				}
				fileInputStream.close();
				outputStream.close();

				// 清理临时文件
				new File(outPdfPath).delete();
				new File(signPdfPath).delete();
				new File(tempWordPath).delete();
			}
		} catch (Exception e) {
			log.error("export fac template :", e);
		}
	}


	private Map<String, Object> getRenderDataMap(CocFacFileVo cocFacFileVo) {
		Map<String, Object> renderDataMap = new HashMap<>();
		Long orderId = cocFacFileVo.getOrderId();
		OrderEntity orderEntity = orderService.getById(orderId);
		renderDataMap.put("customerName", orderEntity.getCustomerName());
		renderDataMap.put("installationLocation", orderEntity.getSiteAddress());
		renderDataMap.put("customerWoNo", orderEntity.getOrderNumber());
		renderDataMap.put("name", orderEntity.getCustomerName());
		String capacity = "";
		if (ValidationUtil.isNotEmpty(cocFacFileVo.getCapacity())) {
			capacity = cocFacFileVo.getCapacity();
		}
		renderDataMap.put("capacity", capacity);
		renderDataMap.put("date", LocalDate.now().toString());
		return renderDataMap;
	}


	private void createPdfSignature(String sourceFile, String endFile) {
		try {
			// 加载现有的PDF文档
			PDDocument document = PDDocument.load(new File(sourceFile));

			// 获取文档的第一个页面，或者根据需要创建新页面
			PDPage page = document.getPage(0);

			// 创建一个交互式表单
			PDAcroForm acroForm = new PDAcroForm(document);
			document.getDocumentCatalog().setAcroForm(acroForm);

			// 创建签名字段
			PDField signatureField = new PDSignatureField(acroForm);
			signatureField.setPartialName("Signature");
			signatureField.setAlternateFieldName("Signature");

			// 创建一个注释作为签名字段的容器
			PDAnnotationWidget widget = new PDAnnotationWidget();
			PDRectangle pdRectangle = new PDRectangle(100, 735, 150, 37);
			widget.setRectangle(pdRectangle); // 设置签名区域的位置和大小
			widget.setPage(page);
			widget.setAnnotationName("Signature");
			float gray = 0.5f;
			PDColor color = new PDColor(new float[]{gray}, PDDeviceGray.INSTANCE);
			widget.setColor(color);

			// 将注释添加到页面
			page.getAnnotations().add(widget);

			// 将字段添加到表单
			acroForm.getFields().add(signatureField);

			// 保存修改后的PDF文档
			document.save(endFile);
			document.close();
		} catch (IOException e) {
			log.error("createPdfSignature occur error:", e);
		}
	}


	/**
	 * 更新工作流状态
	 */
	private void updateFlowWork(long orderId, String remark, Map<String, Object> variables) {
		variables.put("examineApproveType", "1");
		variables.put("comment", remark != null ? remark : "");
		OrderFlowDTO orderFlowDTO = new OrderFlowDTO();
		orderFlowDTO.setBusinessId(orderId + "");
		orderFlowDTO.setVariables(variables);
		reviewOrderService.examineApprove(orderFlowDTO);
	}

	private void finalPaymentEmailDetail(CocMaintenanceInfoEntity cocMaintenanceInfoEntity) {
		EmailVO emailVO = getEmailContent(cocMaintenanceInfoEntity, "mail_content_balance_payment");
//		cocMaintenanceInfoEntity.setPaymentConfirmMailBcc(emailVO.getBccEmail());
//		cocMaintenanceInfoEntity.setPaymentConfirmMailTo(emailVO.getToEmail());
//		cocMaintenanceInfoEntity.setPaymentConfirmSendMail(emailVO.getSendEmail());
//		cocMaintenanceInfoEntity.setPaymentConfirmMailContent(emailVO.getContent());
		cocMaintenanceInfoEntity.setDocFile(emailVO.getAttachmentInfo());
	}

	private EmailVO getEmailContent(CocMaintenanceInfoEntity cocMaintenanceInfoEntity, String dicMailTemplate) {
		EmailVO emailVO = new EmailVO();
		List<Long> businessIds = new ArrayList<>();
		Long orderId = cocMaintenanceInfoEntity.getOrderId();
		Long balancePaymentConfirmDocBizKey = cocMaintenanceInfoEntity.getBalancePaymentConfirmDocBizKey();

		if (balancePaymentConfirmDocBizKey == null) {
			emailVO.setContent(getContent(orderId, null, dicMailTemplate));
			return emailVO;
		} else {
			businessIds.add(balancePaymentConfirmDocBizKey);
			emailVO.setId(cocMaintenanceInfoEntity.getId());
//			emailVO.setContent(getContent(orderId, cocMaintenanceInfoEntity.getPaymentConfirmMailContent(), dicMailTemplate));
//			emailVO.setSendEmail(cocMaintenanceInfoEntity.getPaymentConfirmSendMail());
//			emailVO.setToEmail(cocMaintenanceInfoEntity.getPaymentConfirmMailTo());
//			emailVO.setBccEmail(cocMaintenanceInfoEntity.getPaymentConfirmMailBcc());
			Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
			if (!attachmentInfoMap.isEmpty()) {
				emailVO.setAttachmentInfo(attachmentInfoMap.get(businessIds.get(0)));
			}
			return emailVO;
		}
	}

	@Nullable
	private String getContent(Long orderId, String content, String dicMailTemplate) {
		if (ValidationUtil.isEmpty(content)) {
			OrderEntity orderEntity = orderService.getById(orderId);
			String name = orderEntity.getCustomerName();
			List<DictBiz> dictBizList = dictBizClient.getList(dicMailTemplate).getData();
			if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(dictBizList)) {
				DictBiz dictBiz = dictBizList.get(0);
				content = dictBiz.getAttribute1().replace("{name}", name);
			}
		}
		return content;
	}

	@SneakyThrows
	private void resolveEntity(CocMaintenanceInfoEntity entity) {
		BladeUser user = AuthUtil.getUser();
		Date now = DateUtil.now();
		if (entity.getId() == null) {
			// 处理新增逻辑
			if (user != null) {
				entity.setCreateUser(user.getUserId());
				entity.setCreateDept(Func.firstLong(user.getDeptId()));
				entity.setUpdateUser(user.getUserId());
			}
			if (entity.getStatus() == null) {
				entity.setStatus(BladeConstant.DB_STATUS_NORMAL);
			}
			if (entity.getCreateTime() == null) {
				entity.setCreateTime(now);
			}
		} else if (user != null) {
			// 处理修改逻辑
			entity.setUpdateUser(user.getUserId());
		}
		entity.setUpdateTime(now);
		entity.setIsDeleted(BladeConstant.DB_NOT_DELETED);
		// 处理多租户逻辑，若字段值为空，则不进行操作
		Field field = ReflectUtil.getField(entity.getClass(), BladeConstant.DB_TENANT_KEY);
		if (ObjectUtil.isNotEmpty(field)) {
			Method getTenantId = ClassUtil.getMethod(entity.getClass(), BladeConstant.DB_TENANT_KEY_GET_METHOD);
			String tenantId = String.valueOf(getTenantId.invoke(entity));
			if (ObjectUtil.isEmpty(tenantId)) {
				Method setTenantId = ClassUtil.getMethod(entity.getClass(), BladeConstant.DB_TENANT_KEY_SET_METHOD, String.class);
				setTenantId.invoke(entity, (Object) null);
			}
		}
	}

	// 历史订单安装信息同步到站点表
	@Override
	@SneakyThrows
	public void historyOrderInstallInfoSyncToPlant(List<Long> orderIds) {
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoDTOList = List.of();
		if (CollectionUtil.isEmpty(orderIds)) {
			// 查询所有历史订单
			cocMaintenanceInfoDTOList = this.list();
		} else {
			LambdaQueryWrapper<CocMaintenanceInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.in(CocMaintenanceInfoEntity::getOrderId, orderIds);
			cocMaintenanceInfoDTOList = this.list(queryWrapper);
		}
		if (CollectionUtil.isNotEmpty(cocMaintenanceInfoDTOList)) {
			for (CocMaintenanceInfoEntity cocMaintenanceInfoEntity : cocMaintenanceInfoDTOList) {
				// cocMaintenanceInfoEntity转cocMaintenanceInfoDTO
				CocMaintenanceInfoDTO cocMaintenanceInfoDTO = BeanUtil.copy(cocMaintenanceInfoEntity, CocMaintenanceInfoDTO.class);
				if (ObjectUtil.isNotEmpty(cocMaintenanceInfoDTO)) {
					syncInstallInfoToPlant(cocMaintenanceInfoDTO);
				}
			}
		}
	}

	// fac确认好站点后，根据站点id跨服务调接口把安装信息同步到站点表
	private void syncInstallInfoToPlant(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		if (ObjectUtil.isNotEmpty(cocMaintenanceInfoDTO.getFacPlantId())) {
			plantUpdateHandle(cocMaintenanceInfoDTO);
		}
	}

	// 更新站点信息
	private void plantUpdateHandle(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try {
			R<PlantInfoVO> plantInfoVo = plantClient.queryByPlantId(cocMaintenanceInfoDTO.getFacPlantId());
			if (plantInfoVo.isSuccess() && ObjectUtil.isNotEmpty(plantInfoVo.getData())) {

				JSONObject jsonObject = new JSONObject();
				jsonObject.put("plantId", cocMaintenanceInfoDTO.getFacPlantId());

				PlantInfoVO plantInfoVO = plantInfoVo.getData();
				if (ObjectUtil.isEmpty(plantInfoVO.getInstallUserId())) {
					// 安装负责人，设置成施工队长
					//查询订单关系人表查询人员信息
					LambdaQueryWrapper<OrderRelatedUserEntity> queryWrapper = new LambdaQueryWrapper<>();
					queryWrapper.eq(OrderRelatedUserEntity::getOrderId, cocMaintenanceInfoDTO.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
					List<OrderRelatedUserEntity> orderRelatedUserEntityList = orderRelatedUserServiceImpl.list(queryWrapper);
					if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
						// 设置施工队长
						setInstallUserName(orderRelatedUserEntityList, jsonObject);
					}
				}
				if (ObjectUtil.isEmpty(plantInfoVO.getInstallTeamId())) {
					// 根据订单id查询订单信息
					OrderEntity orderEntity = orderService.getById(cocMaintenanceInfoDTO.getOrderId());
					if (ObjectUtil.isNotEmpty(orderEntity) && ObjectUtil.isNotEmpty(orderEntity.getBelongDeptId())) {
						// 代理商所属部门id
						jsonObject.put("distributorId", orderEntity.getBelongDeptId());
					}
				}
				if (ObjectUtil.isEmpty(plantInfoVO.getInstallDate())) {
					// 根据订单id查询安装信息
					InstallRelatedInfoEntity installRelatedInfoEntity = installRelatedInfoService.getOne(Wrappers.<InstallRelatedInfoEntity>query().lambda().eq(InstallRelatedInfoEntity::getOrderId, cocMaintenanceInfoDTO.getOrderId()));
					if (ObjectUtil.isNotEmpty(installRelatedInfoEntity)) {
						// 安装时间
						jsonObject.put("installDate", installRelatedInfoEntity.getConstructionDate());
					}
				}
				plantClient.updatePlant(jsonObject);
			} else {
				log.info("syncInstallInfoToPlant occur error: {}", plantInfoVo.getMsg());
			}
		} catch (Exception ex) {
			log.error("syncInstallInfoToPlant occur error:", ex);
		}
	}

	/**
	 * @Description: 设置施工队长和电气工程师名称
	 * @Param: [orderRelatedUserEntityList, orderRelatedUserDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/5 17:45
	 **/
	private void setInstallUserName(List<OrderRelatedUserEntity> orderRelatedUserEntityList, JSONObject jsonObject) {
		//根据人员类型分组，每组取第一个
		Map<String, OrderRelatedUserEntity> orderRelatedUserEntityMap = orderRelatedUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getUserType())).collect(Collectors.groupingBy(OrderRelatedUserEntity::getUserType, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
		orderRelatedUserEntityMap.forEach((key, value) -> {
			//施工队长
			if (UserTypeEnum.SITE_TECHNICIAN_LEADER.getName().equals(key)) {
				jsonObject.put("installUserId", value.getUserId());
			}
		});
	}


}

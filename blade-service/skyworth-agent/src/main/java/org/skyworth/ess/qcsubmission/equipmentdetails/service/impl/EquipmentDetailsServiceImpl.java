/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.equipmentdetails.service.impl;

import org.skyworth.ess.qcsubmission.equipmentdetails.entity.EquipmentDetailsEntity;
import org.skyworth.ess.qcsubmission.equipmentdetails.mapper.EquipmentDetailsMapper;
import org.skyworth.ess.qcsubmission.equipmentdetails.service.IEquipmentDetailsService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 施工-设备明细信息表; 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Service
public class EquipmentDetailsServiceImpl extends BaseServiceImpl<EquipmentDetailsMapper, EquipmentDetailsEntity> implements IEquipmentDetailsService {
}

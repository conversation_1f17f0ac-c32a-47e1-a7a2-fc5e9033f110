/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.installrelatedInfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.vo.InstallRelatedInfoVO;
import java.util.Objects;

/**
 * 安装相关信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public class InstallRelatedInfoWrapper extends BaseEntityWrapper<InstallRelatedInfoEntity, InstallRelatedInfoVO>  {

	public static InstallRelatedInfoWrapper build() {
		return new InstallRelatedInfoWrapper();
 	}

	@Override
	public InstallRelatedInfoVO entityVO(InstallRelatedInfoEntity relatedInfo) {
		InstallRelatedInfoVO relatedInfoVO = Objects.requireNonNull(BeanUtil.copy(relatedInfo, InstallRelatedInfoVO.class));

		//User createUser = UserCache.getUser(relatedInfo.getCreateUser());
		//User updateUser = UserCache.getUser(relatedInfo.getUpdateUser());
		//relatedInfoVO.setCreateUserName(createUser.getName());
		//relatedInfoVO.setUpdateUserName(updateUser.getName());

		return relatedInfoVO;
	}


}

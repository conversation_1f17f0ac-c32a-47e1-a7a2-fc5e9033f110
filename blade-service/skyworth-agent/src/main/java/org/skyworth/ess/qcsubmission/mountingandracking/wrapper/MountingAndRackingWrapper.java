/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.mountingandracking.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.qcsubmission.mountingandracking.entity.MountingAndRackingEntity;
import org.skyworth.ess.qcsubmission.mountingandracking.vo.MountingAndRackingVO;
import java.util.Objects;

/**
 * 施工-支架信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public class MountingAndRackingWrapper extends BaseEntityWrapper<MountingAndRackingEntity, MountingAndRackingVO>  {

	public static MountingAndRackingWrapper build() {
		return new MountingAndRackingWrapper();
 	}

	@Override
	public MountingAndRackingVO entityVO(MountingAndRackingEntity mountingandracking) {
		MountingAndRackingVO mountingandrackingVO = Objects.requireNonNull(BeanUtil.copy(mountingandracking, MountingAndRackingVO.class));

		//User createUser = UserCache.getUser(mountingandracking.getCreateUser());
		//User updateUser = UserCache.getUser(mountingandracking.getUpdateUser());
		//mountingandrackingVO.setCreateUserName(createUser.getName());
		//mountingandrackingVO.setUpdateUserName(updateUser.getName());

		return mountingandrackingVO;
	}


}

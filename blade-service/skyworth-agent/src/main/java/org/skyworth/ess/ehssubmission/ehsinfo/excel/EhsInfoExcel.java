/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.excel;


import lombok.Data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * ehs信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EhsInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 医疗紧急联系电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("医疗紧急联系电话")
	private String medicalEmergencyNumber;
	/**
	 * 火警联系
	 */
	@ColumnWidth(20)
	@ExcelProperty("火警联系")
	private String fireDepartment;
	/**
	 * 警察
	 */
	@ColumnWidth(20)
	@ExcelProperty("警察")
	private String police;
	/**
	 * 拖车服务
	 */
	@ColumnWidth(20)
	@ExcelProperty("拖车服务")
	private String carTowingService;
	/**
	 * 公司主管
	 */
	@ColumnWidth(20)
	@ExcelProperty("公司主管")
	private String companySupervisor;
	/**
	 * 安全检查（业务字典agent_member_safety_checklist，多个逗号拼接）
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全检查（业务字典agent_member_safety_checklist，多个逗号拼接）")
	private String memberSafetyChecklist;
	/**
	 * 安全检查图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("安全检查图片key")
	private Long memberSafetyChecklistImgBizKey;
	/**
	 * 急救箱图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("急救箱图片key")
	private Long firstAidKitImgBizKey;
	/**
	 * 救援装备图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("救援装备图片key")
	private Long rescueKitImgBizKey;
	/**
	 * 灭火器图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("灭火器图片key")
	private Long fireExtinguisherImgBizKey;
	/**
	 * 通知书图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("通知书图片key")
	private Long acceptanceLetterImgBizKey;
	/**
	 * 风险图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("风险图片key")
	private Long riskAssessmentImgBizKey;
	/**
	 * 风险列表
	 */
	@ColumnWidth(20)
	@ExcelProperty("风险列表")
	private String riskList;
	/**
	 * 风险解决措施
	 */
	@ColumnWidth(20)
	@ExcelProperty("风险解决措施")
	private String riskSolution;
	/**
	 * 声明（1勾选）
	 */
	@ColumnWidth(20)
	@ExcelProperty("声明（1勾选）")
	private Integer declaration;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.qcotherInformation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 施工-其他信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("qc_other_information")
@ApiModel(value = "QcOtherInformation对象", description = "施工-其他信息")
@EqualsAndHashCode(callSuper = true)
public class QcOtherInformationEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 接地设备图片业务主键
	 */
	@ApiModelProperty(value = "接地设备图片业务主键")
	private Long geGroundingCableImgBizKey;
	/**
	 * 避雷针图片业务主键
	 */
	@ApiModelProperty(value = "避雷针图片业务主键")
	private Long geLightningRodImgBizKey;
	/**
	 * 接地系统图片主键
	 */
	@ApiModelProperty(value = "接地系统图片主键")
	private Long geGroundSystemConnetImgBizKey;
	/**
	 * db接地值主键
	 */
	@ApiModelProperty(value = "db接地值主键")
	private Long geGroundingValueDbImgBizKey;
	/**
	 * 面板接地值主键
	 */
	@ApiModelProperty(value = "面板接地值主键")
	private Long geGroundingValuePanelsImgBizKey;
	/**
	 * 警告标识图片业务主键
	 */
	@ApiModelProperty(value = "警告标识图片业务主键")
	private Long scWarningSignsImgBizKey;
	/**
	 * 智能能量变换器状态图片业务主键
	 */
	@ApiModelProperty(value = "智能能量变换器状态图片业务主键")
	private Long stConfirmInverterStatusImgBizKey;
	/**
	 * 门外安装完成图片业务主键
	 */
	@ApiModelProperty(value = "门外安装完成图片业务主键")
	private Long fiCompletedInstallOutImgBizKey;
	/**
	 * 门内安装完成图片业务主键
	 */
	@ApiModelProperty(value = "门内安装完成图片业务主键")
	private Long fiCompletedInstallInImgBizKey;
	/**
	 * 环境清洁图片业务主键
	 */
	@ApiModelProperty(value = "环境清洁图片业务主键")
	private Long fiEnvironCleanImgBizKey;
	/**
	 * 建议
	 */
//	@ApiModelProperty(value = "建议")
//	private String fiRecommendations;
	/**
	 * QVWI电视图片业务主键
	 */
	@ApiModelProperty(value = "QVWI电视图片业务主键")
	private Long fiQvwiTvImgBizKey;
	/**
	 * 是否需要COC Creation步骤
	 */
//	@ApiModelProperty(value = "是否需要COC Creation步骤, 0:否, 1:是")
//	private Integer fiRequiredCocCreation;
	/**
	 * 是否需要COC Creation步骤的备注
	 */
//	@ApiModelProperty(value = "是否需要COC Creation步骤的备注")
//	private String fiRequiredCocCreationRemark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

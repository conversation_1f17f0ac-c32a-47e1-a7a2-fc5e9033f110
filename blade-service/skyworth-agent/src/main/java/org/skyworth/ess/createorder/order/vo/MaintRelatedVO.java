package org.skyworth.ess.createorder.order.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MaintRelatedVO implements Serializable {
    // 运维订单id或安装订单id，主键
    private Long id;
    private String orderNumber;
    private String orderType;
    private String orderTypeName;
    private Date orderCreateTime;
    private String customerName;
    private String projectType;
    // 维保到期日期
    private Date warrantyExpirationDate;
    // coc创建时间
    private Date cocCreationDate;
    // coc常见时间文档key
    private Long cocCreationDateImgBizKey;
    // 运维单对应的安装单id
    private Long orderInstallId;
    private String wfCurrentStatus;
    private String wfCurrentStatusName;
    private String installReason;
    private String installBudgets;
    private String secondContactName;
    private String secondContactPhone;
}

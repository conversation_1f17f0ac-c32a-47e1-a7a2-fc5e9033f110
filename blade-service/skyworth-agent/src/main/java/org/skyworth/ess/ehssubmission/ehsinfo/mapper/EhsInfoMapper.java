/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.mapper;

import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.excel.EhsInfoExcel;
import org.skyworth.ess.ehssubmission.ehsinfo.vo.EhsInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * ehs信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
public interface EhsInfoMapper extends BaseMapper<EhsInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param info
	 * @return
	 */
	List<EhsInfoVO> selectInfoPage(IPage page, EhsInfoVO info);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<EhsInfoExcel> exportInfo(@Param("ew") Wrapper<EhsInfoEntity> queryWrapper);

}

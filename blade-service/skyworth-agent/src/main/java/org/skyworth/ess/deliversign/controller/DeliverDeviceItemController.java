package org.skyworth.ess.deliversign.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.deliversign.service.IDeliverDeviceItemService;
import org.skyworth.ess.deliversign.vo.DeliverSignItemVO;
import org.skyworth.ess.deliversign.vo.DeliverSignVO;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemStatusVO;
import org.skyworth.ess.design.vo.ProductVO;
import org.springblade.common.valid.ValidGroups;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/deviceItem")
@Api(value = "物料状态", tags = "物料状态")
public class DeliverDeviceItemController {

    private final IDeliverDeviceItemService deliverDeviceItemService;

    @GetMapping("/deliver/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "设备发货详情", notes = "传入orderId")
    public R<DeviceItemStatusVO> queryDeliverDeviceItemList(@RequestParam("orderId") Long orderId) {
        return R.data(deliverDeviceItemService.queryDeliverDeviceItemList(orderId));
    }

    @GetMapping("/sign/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "签收详情", notes = "传入orderId")
    public R<DeviceItemStatusVO> querySignDeviceItemList(@RequestParam("orderId") Long orderId) {
        return R.data(deliverDeviceItemService.querySignDeviceItemList(orderId));
    }

    @PostMapping("/deliver/audit")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "发货审批", notes = "传入DeliverSignVO")
    public R<?> deliverAudit(@RequestBody DeliverSignVO deliverSignVO) {
        return deliverDeviceItemService.deliverAudit(deliverSignVO);
    }

    @PostMapping("/sign/audit")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "签收审批", notes = "传入DeliverSignVO")
    public R<?> signAudit(@RequestBody DeliverSignVO deliverSignVO) {
        return deliverDeviceItemService.signAudit(deliverSignVO);
    }

    @PostMapping("/sign/loss")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "差损", notes = "传入DeliverSignItemVO")
    public R<?> signLoss(@Validated(value = ValidGroups.AddGroup.class) @RequestBody DeliverSignItemVO deliverSignItemVO) {
        return deliverDeviceItemService.signLoss(deliverSignItemVO);
    }

    @PostMapping("/sign/submit")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "签收单个提交", notes = "传入DeliverSignItemVO")
    public R<?> signSubmit(@Validated(value = ValidGroups.EditGroup.class) @RequestBody DeliverSignItemVO deliverSignItemVO) {
        return deliverDeviceItemService.signSubmit(deliverSignItemVO);
    }

    @GetMapping("/sign/history")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "签收历史", notes = "传入 orderId")
    public R<Map<String, List<ProductVO>>> signHistory(@RequestParam("orderId") Long orderId) {
        return R.data(deliverDeviceItemService.signHistory(orderId));
    }
}

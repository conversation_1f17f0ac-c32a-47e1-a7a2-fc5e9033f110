package org.skyworth.ess.surveyreview.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springblade.flow.core.dto.OrderFlowDTO;

import java.io.Serializable;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-18 15:43
 **/
@Data
public class MaintainSurveyReviewEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long orderId;
	/**
	 * 审批流参数
	 */
	private OrderFlowDTO orderFlowDTO;
}

package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.Valid;

/**
 * 订单表列表 视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.dto
 *
 * <AUTHOR>
 * @since 2023/12/25 - 12 - 25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SubmitQcInfoRequestDTO {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单审批流程入参
	 */
	@ApiModelProperty(value = "订单审批流程入参")
	private OrderFlowDTO orderFlow;
	/**
	 * 质检信息
	 */
	@ApiModelProperty(value = "质检信息")
	@Valid
	private QcInfoDTOs qcInfoDtos;
	/**
	 * 请求来源标识,"app"或"web"
	 */
	@ApiModelProperty(value = "请求来源标识,app或web")
	private String clientType;

}

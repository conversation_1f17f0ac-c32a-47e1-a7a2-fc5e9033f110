package org.skyworth.ess.revieworder.orderworkflow.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReviewOrderVO extends TenantEntity {

	/**
	 * 订单编号
	 */
	private String orderNumber;

	/**
	 * 经销商id
	 */
	@NotNull(message = "{agent.createOrder.distributorId.notNull}")
	private Long distributorId;

	/**
	 * 推广经理
	 */
	@NotNull(message = "{agent.createOrder.rolloutManagerId.notNull}")
	private Long rolloutManagerId;

	/**
	 * 客户名称
	 */
	@NotBlank(message = "{agent.createOrder.customerName.notNull}")
	private String customerName;

	/**
	 * 客户电话
	 */
	@NotBlank(message = "{agent.createOrder.customerPhone.notNull}")
	private String customerPhone;

	/**
	 * 客户邮箱
	 */
	@NotBlank(message = "{agent.createOrder.customerEmail.notNull}")
	private String customerEmail;

	/**
	 * 客户公司名称
	 */
	private String customerCompanyName;

	/**
	 * 安装站点国家编码
	 */
	@NotBlank(message = "{agent.reviewOrder.siteCountryCode.notNull}")
	private String siteCountryCode;

	/**
	 * 安装站点省份编码
	 */
	@NotBlank(message = "{agent.reviewOrder.siteProvinceCode.notNull}")
	private String siteProvinceCode;

	/**
	 * 安装站点城市编码
	 */
	@NotBlank(message = "{agent.reviewOrder.siteCityCode.notNull}")
	private String siteCityCode;

	/**
	 * 地址
	 */
	@NotBlank(message = "{agent.reviewOrder.siteAddress.notNull}")
	private String siteAddress;

	/**
	 * 经度
	 */
	@NotBlank(message = "{agent.reviewOrder.longitude.notNull}")
	private String longitude;

	/**
	 * 纬度
	 */
	@NotBlank(message = "{agent.reviewOrder.latitude.notNull}")
	private String latitude;

	/**
	 * 安装原因
	 */
	@NotBlank(message = "{agent.reviewOrder.installReason.notNull}")
	private String installReason;

	/**
	 * 安装预算
	 */
	@NotBlank(message = "{agent.reviewOrder.installBudgets.notNull}")
	private String installBudgets;

	/**
	 * 预计安装开始日期
	 */
	@NotNull(message = "{agent.reviewOrder.tentativeInstallStartDate.notNull}")
	private Date tentativeInstallStartDate;

	/**
	 * 预计安装结束日期
	 */
	@NotNull(message = "{agent.reviewOrder.tentativeInstallEndDate.notNull}")
	private Date tentativeInstallEndDate;

	/**
	 * 踏勘时间
	 */
	@NotNull(message = "{agent.reviewOrder.surveyDate.notNull}")
	private Date surveyDate;

	/**
	 * 附加文档key
	 */
	private Long additionalDocBizKey;

	/**
	 * 第二联系人名称
	 */
	private String secondContactName;

	/**
	 * 第二联系人电话
	 */
	private String secondContactPhone;

	/**
	 * 项目类型（业务字典agent_project_type）
	 */
	@NotBlank(message = "{agent.createOrder.projectType.notNull}")
	private String projectType;

	/**
	 * 项目类型中其他类型说明
	 */
	private String projectTypeOtherRemark;
	/**
	 * 创建人账号
	 */
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	private String updateUserAccount;

	/**
	 * 踏勘时间确认
	 */
	private Date surveyDateConfirm;


	/**
	 * 踏勘日期更改原因
	 */
	private String surveyDateConfirmRemark;


	/**
	 * 代理商公司名称
	 */
	private String distributorName;
	/**
	 * 推广经理姓名
	 */
	private String rolloutManagerName;


	/**
	 * 安装站点国家名称
	 */
	private String siteCountryName;

	/**
	 * 安装站点省份名称
	 */
	private String siteProvinceName;

	/**
	 * 安装站点城市名称
	 */
	private String siteCityName;


	/**
	 * 项目类型名称
	 */
	private String projectTypeName;

	/**
	 * 建站地址，拼接全地址
	 */
	private String siteDetailAddress;

	/**
	 * 距离限制标识
	 * */
	private String distanceRestriction;


}

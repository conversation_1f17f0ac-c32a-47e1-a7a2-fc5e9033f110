package org.skyworth.ess.revieworder.orderworkflow.constant;

/**
 * 模块分类枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum OutCome {
	PASS(1, "pass"),
	REJECT(2, "reject"),
	SKIP(3, "skip"),
	CANCEL(4, "cancel"),
	;

	private Integer type;
	private String dec;

	OutCome(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(Integer type) {
		for (OutCome houseModuleType : OutCome.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}

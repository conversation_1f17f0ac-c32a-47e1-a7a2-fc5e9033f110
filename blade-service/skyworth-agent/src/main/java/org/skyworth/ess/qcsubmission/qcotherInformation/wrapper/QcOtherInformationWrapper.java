/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.qcotherInformation.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.qcsubmission.qcotherInformation.entity.QcOtherInformationEntity;
import org.skyworth.ess.qcsubmission.qcotherInformation.vo.QcOtherInformationVO;
import java.util.Objects;

/**
 * 施工-其他信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public class QcOtherInformationWrapper extends BaseEntityWrapper<QcOtherInformationEntity, QcOtherInformationVO>  {

	public static QcOtherInformationWrapper build() {
		return new QcOtherInformationWrapper();
 	}

	@Override
	public QcOtherInformationVO entityVO(QcOtherInformationEntity otherInformation) {
		QcOtherInformationVO otherInformationVO = Objects.requireNonNull(BeanUtil.copy(otherInformation, QcOtherInformationVO.class));

		//User createUser = UserCache.getUser(otherInformation.getCreateUser());
		//User updateUser = UserCache.getUser(otherInformation.getUpdateUser());
		//otherInformationVO.setCreateUserName(createUser.getName());
		//otherInformationVO.setUpdateUserName(updateUser.getName());

		return otherInformationVO;
	}


}

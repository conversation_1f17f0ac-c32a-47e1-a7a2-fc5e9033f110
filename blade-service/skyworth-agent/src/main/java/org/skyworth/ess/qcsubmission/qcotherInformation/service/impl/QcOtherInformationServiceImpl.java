/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.qcotherInformation.service.impl;

import org.skyworth.ess.qcsubmission.qcotherInformation.entity.QcOtherInformationEntity;
import org.skyworth.ess.qcsubmission.qcotherInformation.vo.QcOtherInformationVO;
import org.skyworth.ess.qcsubmission.qcotherInformation.excel.QcOtherInformationExcel;
import org.skyworth.ess.qcsubmission.qcotherInformation.mapper.QcOtherInformationMapper;
import org.skyworth.ess.qcsubmission.qcotherInformation.service.IQcOtherInformationService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 施工-其他信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class QcOtherInformationServiceImpl extends BaseServiceImpl<QcOtherInformationMapper, QcOtherInformationEntity> implements IQcOtherInformationService {

	@Override
	public IPage<QcOtherInformationVO> selectQcOtherInformationPage(IPage<QcOtherInformationVO> page, QcOtherInformationVO otherInformation) {
		return page.setRecords(baseMapper.selectQcOtherInformationPage(page, otherInformation));
	}


	@Override
	public List<QcOtherInformationExcel> exportQcOtherInformation(Wrapper<QcOtherInformationEntity> queryWrapper) {
		List<QcOtherInformationExcel> otherInformationList = baseMapper.exportQcOtherInformation(queryWrapper);
		//otherInformationList.forEach(otherInformation -> {
		//	otherInformation.setTypeName(DictCache.getValue(DictEnum.YES_NO, QcOtherInformation.getType()));
		//});
		return otherInformationList;
	}

}

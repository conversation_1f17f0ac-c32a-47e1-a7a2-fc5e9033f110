package org.skyworth.ess.qcsubmission.maintenance.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.springblade.common.constant.BizConstant;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MaintenanceQcVerificationController
 * @Description 运维QC审核控制器
 * @Date 2024/4/17 10:49
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("maintenanceQc/verification")
@Api(value = "20-运维QC审核", tags = "运维管理流程相关接口")
public class MaintenanceQcVerificationController extends BladeController {

    private final ICocMaintenanceInfoService cocMaintenanceInfoService;

    @PostMapping("/qcVerify/audit")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "20-运维QC审核-QC审核", notes = "CocMaintenanceInfoEntity")
    public R qcVerifyAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
        try {
            return R.status(cocMaintenanceInfoService.qcVerifyAudit(cocMaintenanceInfoDTO, BizConstant.NUMBER_ONE));
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.status(false);
        }
    }

}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import java.util.Date;
import java.util.List;

/**
 * 订单表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderVO extends SkyWorthFileEntity {
	private static final long serialVersionUID = 1L;

	private OrderEntity orderEntity;

	//订单进度
	private String orderProgress;

	//订单持续天数
	private String orderAccumulationTime;

	//代理商部门信息
	private List<AgentCompanyInfoEntity> agentCompanyInfoEntityList;

	//是否存在代理商公司
	private boolean isAgentStatus;

	/**
	 * 工作流当前状态/订单进度
	 */
	private String wfCurrentStatus;
	/**
	 * 工作流当前状态/订单进度名称
	 */
	private String wfCurrentStatusName;
	/**
	 * 角色类型
	 */
	private String roleType;
	/**
	 * 代理商人员对应的代理商部门id
	 */
	private String userDeptId;
	/**
	 * 安装商人员id
	 */
	private Long installUserId;
	/**
	 * 订单编号
	 */
	private String queryOrderNumber;
	/**
	 * 工作流实例id
	 */
	private String wfInstanceId;
	/**
	 * 任务id
	 */
	private String taskId;
	/**
	 * 上一步审批状态（1:通过；2：不通过；3：待审批）
	 */
	private Integer auditStatus;
	/**
	 * 当前处理节点类型
	 */
	private String wfCurrentType;
	/**
	 * 当前处理节点角色或人
	 */
	private String wfCurrentRole;
	/**
	 * 当前处理节点角色或人名称
	 */
	private String wfCurrentRoleName;
	// 订单创建人
	private Long orderCreateUser;
	// 订单创建人
	private String orderCreateUserName;
	// 订单创建时间
	private Date orderCreateTime;
	// 订单创建人电话
	private String orderCreateUserPhone;
	// 节点持续时间
	private String statusLastingTime;
	// 工作流最新操作时间
	private Date wfUpdateTime;
	// 代理商名称
	private String agentCompanyName;
	// 代理商或者安装商类型
	private String agentOrInstallType;
	// 查询国家编码
	private String querySiteCountryCode;
	// 查询省份编码
	private String querySiteProvinceCode;
	// 查询城市编码
	private String querySiteCityCode;
	// 维保到期日期
//	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date warrantyExpirationDate;
	// coc创建时间
//	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date cocCreationDate;
	// coc常见时间文档key
	private Long cocCreationDateImgBizKey;
}

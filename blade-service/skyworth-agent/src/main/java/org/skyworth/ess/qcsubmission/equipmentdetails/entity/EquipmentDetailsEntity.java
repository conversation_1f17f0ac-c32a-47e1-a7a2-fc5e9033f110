/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.equipmentdetails.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.system.entity.AttachmentInfoEntity;

/**
 * 施工-设备明细信息表; 实体类
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@TableName("qc_equipment_details")
@ApiModel(value = "EquipmentDetails对象", description = "施工-设备明细信息表;")
@EqualsAndHashCode(callSuper = true)
public class EquipmentDetailsEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 产品SN
	 */
	@ApiModelProperty(value = "产品SN")
	private String serialNumber;
	/**
	 * 设备类型:inverte/battery/solarPanels
	 */
	@ApiModelProperty(value = "设备类型:inverte/battery/solarPanels")
	private String equipmentType;
	/**
	 * 设备图片业务主键
	 */
	@ApiModelProperty(value = "设备图片业务主键")
	private Long equipmentImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "附件信息")
	@TableField(exist = false)
	private List<AttachmentInfoEntity> attachmentInfoDesc;

	@ApiModelProperty(value = "附件描述")
	@TableField(exist = false)
	private String attachmentInfoDescView;
}

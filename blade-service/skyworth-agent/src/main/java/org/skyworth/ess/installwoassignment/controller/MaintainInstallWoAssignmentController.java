/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.installwoassignment.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.installwoassignment.service.InstallWoAssignmentService;
import org.skyworth.ess.surveyassignment.orderrelateduser.dto.OrderRelatedUserDTO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指派施工任务 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("maintain/installWoAssignment")
@Api(value = "运维流程-指派施工任务", tags = "指派施工任务接口")
public class MaintainInstallWoAssignmentController extends BladeController {

	private final InstallWoAssignmentService installWoAssignmentService;


	@PostMapping("/examineWoAssignment")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "运维流程-审核指派施工任务", notes = "传入orderRelatedUserDTO")
	public R<?> examineWoAssignment(@RequestBody OrderRelatedUserDTO orderRelatedUserDTO) {
		return installWoAssignmentService.maintainExamineWoAssignment(orderRelatedUserDTO);
	}


	@PostMapping("/selectAssignedConstructInfo")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "运维流程-查询指派施工人员信息", notes = "传入orderRelatedUserDTO")
	public R<?> selectAssignedConstructInfo(@RequestBody OrderRelatedUserDTO orderRelatedUserDTO) {
		return installWoAssignmentService.selectAssignedConstructInfo(orderRelatedUserDTO);
	}


}

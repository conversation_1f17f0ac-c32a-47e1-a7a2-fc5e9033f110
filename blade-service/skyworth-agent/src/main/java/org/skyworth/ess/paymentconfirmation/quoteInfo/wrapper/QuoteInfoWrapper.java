/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.paymentconfirmation.quoteInfo.wrapper;

import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.paymentconfirmation.quoteInfo.vo.QuoteInfoVO;
import java.util.Objects;

/**
 * 设备报价信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public class QuoteInfoWrapper extends BaseEntityWrapper<QuoteInfoEntity, QuoteInfoVO>  {

	public static QuoteInfoWrapper build() {
		return new QuoteInfoWrapper();
 	}

	@Override
	public QuoteInfoVO entityVO(QuoteInfoEntity quoteInfo) {
		QuoteInfoVO quoteInfoVO = Objects.requireNonNull(BeanUtil.copy(quoteInfo, QuoteInfoVO.class));

		//User createUser = UserCache.getUser(quoteInfo.getCreateUser());
		//User updateUser = UserCache.getUser(quoteInfo.getUpdateUser());
		//quoteInfoVO.setCreateUserName(createUser.getName());
		//quoteInfoVO.setUpdateUserName(updateUser.getName());

		return quoteInfoVO;
	}


}

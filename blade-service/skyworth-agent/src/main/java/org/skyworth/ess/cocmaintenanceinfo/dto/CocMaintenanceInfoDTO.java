/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.AttachmentInfoEntity;

import java.util.List;

/**
 * coc和维护信息 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CocMaintenanceInfoDTO extends CocMaintenanceInfoEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * app提交标识：1：保存；2：审批
	 */
	private String bizType;

/*	*//**
	 * temporaryCoc
	 * *//*
	private List<AttachmentInfoEntity> cocImg;*/

	/**
	 * fac
	 * */
	private List<AttachmentInfoEntity> facLandlordSignImg;

	private List<AttachmentInfoEntity> facBizKeyImg;

	private List<AttachmentInfoEntity> technicianSignImg;

	private List<AttachmentInfoEntity> technicianBizKeyImg;

	/**
	 * Balance-Payment
	 * */

	private List<AttachmentInfoEntity> docFile;

	/**
	 * finalCoc
	 * *//*
	private List<AttachmentInfoEntity> finalCocImg;*/


	/**
	 * QC-verify
	 * */
	private List<OrderNodeSubStatusEntity> content;

	/**
	 * 是否需要COC Creation步骤
	 *//*
	@ApiModelProperty(value = "是否需要COC Creation步骤, 0:否, 1:是")
	private Integer fiRequiredCocCreation;*/

	/**
	 * 是否有免费维修服务
	 */
	@ApiModelProperty(value = "是否有免费维修服务, 0:否, 1:是")
	private Integer fiRequiredFreeService;

	private String type;

	private String remark;


	/**
	 * 客户确认文件
	 */
	private List<AttachmentInfoEntity> clientConfirmationImg;

	/**
	 * 客户验收文件
	 */
	private List<AttachmentInfoEntity> customersAcceptanceFile;


	/**
	 * 最终支付确认是否发送邮件 1:是；0：否
	 */
	@ApiModelProperty(value = "最终支付确认是否发送邮件 1:是；0：否")
	private Integer paymentConfirmSendMail;
	/**
	 * 最终支付确认邮件抄送人，多个邮箱";"分割，最多5个
	 */
	@ApiModelProperty(value = "最终支付确认邮件抄送人，多个邮箱;分割，最多5个")
	private String paymentConfirmMailTo;
	/**
	 * 最终支付确认邮件密送人，多个邮箱";"分割，最多5个
	 */
	@ApiModelProperty(value = "最终支付确认邮件密送人，多个邮箱;分割，最多5个")
	private String paymentConfirmMailBcc;
	/**
	 * 最终支付确认邮件内容
	 */
	@ApiModelProperty(value = "最终支付确认邮件内容")
	private String paymentConfirmMailContent;

	//审批信息
	private OrderFlowDTO orderFlowDTO;

}

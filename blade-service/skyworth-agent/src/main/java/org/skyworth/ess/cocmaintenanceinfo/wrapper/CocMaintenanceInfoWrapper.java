/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import java.util.Objects;

/**
 * coc和维护信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class CocMaintenanceInfoWrapper extends BaseEntityWrapper<CocMaintenanceInfoEntity, CocMaintenanceInfoVO>  {

	public static CocMaintenanceInfoWrapper build() {
		return new CocMaintenanceInfoWrapper();
 	}

	@Override
	public CocMaintenanceInfoVO entityVO(CocMaintenanceInfoEntity cocMaintenanceInfo) {
		CocMaintenanceInfoVO cocMaintenanceInfoVO = Objects.requireNonNull(BeanUtil.copy(cocMaintenanceInfo, CocMaintenanceInfoVO.class));

		//User createUser = UserCache.getUser(cocMaintenanceInfo.getCreateUser());
		//User updateUser = UserCache.getUser(cocMaintenanceInfo.getUpdateUser());
		//cocMaintenanceInfoVO.setCreateUserName(createUser.getName());
		//cocMaintenanceInfoVO.setUpdateUserName(updateUser.getName());

		return cocMaintenanceInfoVO;
	}


}

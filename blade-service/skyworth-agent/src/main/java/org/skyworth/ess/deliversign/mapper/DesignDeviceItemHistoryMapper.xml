<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.deliversign.mapper.DeviceItemHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceItemHistoryResultMap" type="org.skyworth.ess.deliversign.entity.DeviceItemHistoryEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="operate_node" property="operateNode"/>
        <result column="item_code" property="itemCode"/>
        <result column="operate_type" property="operateType"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceItemHistoryPage" resultMap="deviceItemHistoryResultMap">
        select * from design_device_item_history where is_deleted = 0
    </select>


    <select id="exportDeviceItemHistory" resultType="org.skyworth.ess.deliversign.excel.DeviceItemHistoryExcel">
        SELECT * FROM design_device_item_history ${ew.customSqlSegment}
    </select>

</mapper>

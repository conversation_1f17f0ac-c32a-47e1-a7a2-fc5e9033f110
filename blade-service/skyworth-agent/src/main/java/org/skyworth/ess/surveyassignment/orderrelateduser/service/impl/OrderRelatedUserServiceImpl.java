/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.orderrelateduser.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.survey.info.service.ISurveyInfoService;
import org.skyworth.ess.surveyassignment.orderrelateduser.dto.OrderRelatedUserDTO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.excel.OrderRelatedUserExcel;
import org.skyworth.ess.surveyassignment.orderrelateduser.mapper.OrderRelatedUserMapper;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.skyworth.ess.surveyassignment.orderrelateduser.vo.OrderRelatedUserVO;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单关系人 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@AllArgsConstructor
public class OrderRelatedUserServiceImpl extends BaseServiceImpl<OrderRelatedUserMapper, OrderRelatedUserEntity> implements IOrderRelatedUserService {


	private final IOrderService IOrderService;

	private final IReviewOrderService iReviewOrderService;

	private final ISurveyInfoService surveyInfoService;


	/**
	 * @Description: 指定踏勘人员和电工
	 * @Param: [orderRelatedUserDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/28 14:22
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> designatedPerson(OrderRelatedUserDTO orderRelatedUserDTO) {
		// 验证orderId是否为空
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getInfoEntity().getOrderId())) {
			throw new BusinessException("agent.surveyAssignment.designatedPerson.orderId.notNull");
		}

		//保存指定的探勘人员和电工到探勘表
		SurveyInfoEntity infoEntity = orderRelatedUserDTO.getInfoEntity();
		if (ObjectUtils.isEmpty(infoEntity)) {
			throw new BusinessException("agent.surveyAssignment.designatedPerson.SurveyInfoEntity.notNull");
		}

		// 删除已有的踏勘和电气工程师信息
		deleteExistingSurveyInfo(orderRelatedUserDTO);

		//修改订单表的确认踏勘时间和意见
		if (ObjectUtils.isNotEmpty(infoEntity.getSurveyDateConfirm())) {
			LambdaUpdateWrapper<OrderEntity> orderEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			orderEntityLambdaUpdateWrapper.eq(OrderEntity::getId, infoEntity.getOrderId());
			orderEntityLambdaUpdateWrapper.set(OrderEntity::getSurveyDateConfirm, infoEntity.getSurveyDateConfirm())
//					.set(OrderEntity::getSurveyDateConfirmRemark, infoEntity.getSurveyDateConfirmRemark())
					;
			this.IOrderService.update(orderEntityLambdaUpdateWrapper);
		}
		//探勘和电气工程师人员信息
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = collectOrderUserInfo(infoEntity);
		//保存订单关系人表
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
			this.saveBatch(orderRelatedUserEntityList);
		}
		//完成审批
		OrderFlowDTO orderFlowDTO = orderRelatedUserDTO.getOrderFlowDTO();
		setWorkFlowData(orderFlowDTO, infoEntity);
		//审批流转
		R<?> examineApproveResult = iReviewOrderService.examineApprove(orderFlowDTO);
		if (!examineApproveResult.isSuccess()) {
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		return R.status(true);
	}


	/**
	 * @Description: 修改流程信息
	 * @Param: [orderFlowDTO, infoEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/29 15:37
	 **/
	private void setWorkFlowData(OrderFlowDTO orderFlowDTO, SurveyInfoEntity infoEntity) {
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//探勘人员
		variables.put("siteEngineer", infoEntity.getTechnicianId());
		//电气工程师
//		variables.put("electrician", infoEntity.getElectricianId());
	}


	/**
	 * @Description: 查询订单探勘时间
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/28 14:26
	 **/
	@Override
	public R<OrderEntity> selectExplorationDate(OrderDTO orderDTO) {
		if (ObjectUtils.isEmpty(orderDTO)) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.orderDTO.notNull");
		}
		if (ObjectUtils.isEmpty(orderDTO.getOrderEntity().getId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, orderDTO.getOrderEntity().getId());
		orderEntityLambdaQueryWrapper.select(OrderEntity::getId, OrderEntity::getOrderNumber, OrderEntity::getSurveyDate);
		return R.data(IOrderService.getOne(orderEntityLambdaQueryWrapper));
	}


	/**
	 * @Description: 查询该订单的踏勘人员和电工
	 * @Param: [orderRelatedUserDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/29 15:47
	 **/
	@Override
	public R<?> selectDesignatedPerson(SurveyInfoEntity infoEntity) {
		if (ObjectUtils.isEmpty(infoEntity)) {
			throw new BusinessException("agent.surveyAssignment.designatedPerson.SurveyInfoEntity.notNull");
		}
		//查询探勘信息
		if (ObjectUtils.isEmpty(infoEntity.getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		//查询探勘表查询人员名称
		SurveyInfoEntity resultSurveyInfoEntity = new SurveyInfoEntity();
		LambdaQueryWrapper<SurveyInfoEntity> surveyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		surveyInfoEntityLambdaQueryWrapper.eq(SurveyInfoEntity::getOrderId, infoEntity.getOrderId());
		SurveyInfoEntity surveyInfoEntity = this.surveyInfoService.getOne(surveyInfoEntityLambdaQueryWrapper);
		if (ObjectUtils.isNotEmpty(surveyInfoEntity)) {
			resultSurveyInfoEntity = surveyInfoEntity;
		}
		//查询踏勘时间和意见信息
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, infoEntity.getOrderId());
		orderEntityLambdaQueryWrapper.select(OrderEntity::getSurveyDate, OrderEntity::getSurveyDateConfirm);
		OrderEntity orderEntity = this.IOrderService.getOne(orderEntityLambdaQueryWrapper);
		if (ObjectUtils.isNotEmpty(orderEntity)) {
			//踏勘预计时间
			resultSurveyInfoEntity.setSurveyDate(orderEntity.getSurveyDate());
			//踏勘确认时间
			resultSurveyInfoEntity.setSurveyDateConfirm(orderEntity.getSurveyDateConfirm());
			//踏勘确认时间备注
//			resultSurveyInfoEntity.setSurveyDateConfirmRemark(orderEntity.getSurveyDateConfirmRemark());
		}
		//查询订单关系人表查询人员信息
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, infoEntity.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.SURVEY_WO_ASSIGN.getDictCode())
				.in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN.getValue()) //, UserTypeEnum.ELECTRICIAN.getValue()
		;
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = this.list(orderRelatedUserEntityLambdaQueryWrapper);
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
			//踏勘人员信息
			setSurveyPerson(orderRelatedUserEntityList, resultSurveyInfoEntity);
		}
		return R.data(resultSurveyInfoEntity);
	}


	/**
	 * @Description: 收集探勘和电气工程师人员信息
	 * @Param: [infoEntity]
	 * @Return: java.util.List<org.skyworth.ess.surveyassignment.entity.OrderRelatedUserEntity>
	 * @Author: baixu
	 * @Date: 2023/11/28 15:18
	 **/
	private List<OrderRelatedUserEntity> collectOrderUserInfo(SurveyInfoEntity infoEntity) {
		List<OrderRelatedUserEntity> addList = new ArrayList<>();
		// 兼容运维，可能只有其中一个
		if(infoEntity.getTechnicianId() != null) {
			//探勘人员
			OrderRelatedUserEntity orderTechnicianUserEntity = new OrderRelatedUserEntity();
			orderTechnicianUserEntity.setOrderId(infoEntity.getOrderId());
			orderTechnicianUserEntity.setNodeType(DictNodeBizCodeEnum.SURVEY_WO_ASSIGN.getDictCode());
			orderTechnicianUserEntity.setUserType(UserTypeEnum.SITE_TECHNICIAN.getName());
			orderTechnicianUserEntity.setUserId(infoEntity.getTechnicianId());
			orderTechnicianUserEntity.setUserName(infoEntity.getTechnicianName());
			addList.add(orderTechnicianUserEntity);
		}
//		if(infoEntity.getElectricianId() != null) {
//			//电气工程师
//			OrderRelatedUserEntity orderElectricianUserEntity = new OrderRelatedUserEntity();
//			orderElectricianUserEntity.setOrderId(infoEntity.getOrderId());
//			orderElectricianUserEntity.setNodeType(DictNodeBizCodeEnum.SURVEY_WO_ASSIGN.getDictCode());
//			orderElectricianUserEntity.setUserType(UserTypeEnum.ELECTRICIAN.getName());
//			orderElectricianUserEntity.setUserId(infoEntity.getElectricianId());
//			//电气工程师人员名称
//			orderElectricianUserEntity.setUserName(infoEntity.getElectricianName());
//			addList.add(orderElectricianUserEntity);
//		}
		return addList;
	}


	/**
	 * @Description: 设置踏勘人员和电气工程师信息
	 * @Param: [orderRelatedUserEntityList, surveyInfoEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 16:47
	 **/
	private void setSurveyPerson(List<OrderRelatedUserEntity> orderRelatedUserEntityList, SurveyInfoEntity surveyInfoEntity) {
		//踏勘人员
		List<OrderRelatedUserEntity> surveyUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.SITE_TECHNICIAN.getValue())).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(surveyUserList)) {
			surveyInfoEntity.setTechnicianId(surveyUserList.get(0).getUserId());
			surveyInfoEntity.setTechnicianName(surveyUserList.get(0).getUserName());
		}
		//电气工程师
//		List<OrderRelatedUserEntity> electricUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.ELECTRICIAN.getValue())).collect(Collectors.toList());
//		if (CollectionUtils.isNotEmpty(electricUserList)) {
//			surveyInfoEntity.setElectricianId(electricUserList.get(0).getUserId());
//			surveyInfoEntity.setElectricianName(electricUserList.get(0).getUserName());
//		}
	}

	@Override
	public IPage<OrderRelatedUserVO> selectOrderRelatedUserPage(IPage<OrderRelatedUserVO> page, OrderRelatedUserVO orderRelatedUser) {
		return page.setRecords(baseMapper.selectOrderRelatedUserPage(page, orderRelatedUser));
	}


	@Override
	public List<OrderRelatedUserExcel> exportOrderRelatedUser(Wrapper<OrderRelatedUserEntity> queryWrapper) {
		return baseMapper.exportOrderRelatedUser(queryWrapper);
	}

	/**
	 * @param orderRelatedUserDTO orderRelatedUserDTO
	 * @Description: 删除已有的踏勘和电气工程师信息
	 */
	private void deleteExistingSurveyInfo(OrderRelatedUserDTO orderRelatedUserDTO) {
		LambdaUpdateWrapper<OrderRelatedUserEntity> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(OrderRelatedUserEntity::getOrderId, orderRelatedUserDTO.getInfoEntity().getOrderId())
			.and(wrapper -> wrapper.eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN.getValue())
				);
		this.remove(updateWrapper);

		LambdaQueryWrapper<SurveyInfoEntity> surveyInfoWrapper = new LambdaQueryWrapper<>();
		surveyInfoWrapper.eq(SurveyInfoEntity::getOrderId, orderRelatedUserDTO.getInfoEntity().getOrderId());
		SurveyInfoEntity surveyInfoEntity = surveyInfoService.getOne(surveyInfoWrapper);
		if (Objects.isNull(surveyInfoEntity)) {
			surveyInfoService.save(orderRelatedUserDTO.getInfoEntity());
		}

	}


}

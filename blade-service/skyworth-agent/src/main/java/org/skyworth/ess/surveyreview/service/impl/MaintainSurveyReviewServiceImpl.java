package org.skyworth.ess.surveyreview.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springblade.common.constant.OrderStatusConstants;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.constant.HouseCategory;
import org.skyworth.ess.survey.constant.OperationType;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.skyworth.ess.surveyreview.entity.MaintainSurveyReviewEntity;
import org.skyworth.ess.surveyreview.service.IMaintainSurveyReviewService;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 踏勘审批
 *
 * @description:
 * @author: SDT50545
 * @since: 2024-04-18 14:33
 **/
@Service
@AllArgsConstructor
public class MaintainSurveyReviewServiceImpl implements IMaintainSurveyReviewService {
	private final IOrderNodeSubStatusService orderNodeSubStatusService;
	private final IReviewOrderService iReviewOrderService;
	private final IOrderRelatedUserService orderRelatedUserService;

	@Override
	public R<Boolean> saveSubNodeApprovalStatus(OrderNodeSubStatusEntity orderNodeSubStatusEntity) {
		orderNodeSubStatusService.saveOrderNodeSubStatus(orderNodeSubStatusEntity.getOrderId(),
			OperationType.APPROVE.getDec(), HouseCategory.SURVEY_REVIEW.getDec(), orderNodeSubStatusEntity.getSubNodeName(), orderNodeSubStatusEntity.getSubStatus());
		return R.status(true);
	}

	@Override
	public R<Boolean> submit(MaintainSurveyReviewEntity maintainSurveyReviewEntity) {
		if (ObjectUtils.isEmpty(maintainSurveyReviewEntity)) {
			throw new BusinessException("agent.surveyReview.submitObject.notNull");
		}
		OrderFlowDTO orderFlowDTO = maintainSurveyReviewEntity.getOrderFlowDTO();
		if (ObjectUtils.isEmpty(orderFlowDTO)) {
			throw new BusinessException("agent.surveyReview.flowParam.notNull");
		}
		// 审批结论
		Object examineApproveType = orderFlowDTO.getVariables().get("examineApproveType");
		//拒绝时清空小节点状态
		if (ObjectUtils.isNotEmpty(examineApproveType) && OrderStatusConstants.EXAMINE_APPROVE_NOT_PASS.equals(examineApproveType.toString())) {
			// 驳回流程设置审批人
			obtainApprovalForRejectNode(maintainSurveyReviewEntity.getOrderFlowDTO(),maintainSurveyReviewEntity.getOrderId());
		}
		//审批流转
		R<?> examineApproveResult = iReviewOrderService.examineApprove(orderFlowDTO);
		if (!examineApproveResult.isSuccess()) {
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		return R.status(true);
	}

	/**
	 * 获取审批人
	 *
	 * @param orderFlowDTO 入参
	 * <AUTHOR>
	 * @since 2024/5/20 18:48
	 **/
	@Override
	public void obtainApprovalForRejectNode(OrderFlowDTO orderFlowDTO,Long orderId) {
		//查询订单关系人表查询人员信息
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderId).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.SURVEY_WO_ASSIGN.getDictCode())
				.in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN.getValue()); // , UserTypeEnum.ELECTRICIAN.getValue()
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = orderRelatedUserService.list(orderRelatedUserEntityLambdaQueryWrapper);
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
			Map<String, Object> ariablesMap = orderFlowDTO.getVariables();
			//电气工程师
//			List<OrderRelatedUserEntity> electricUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.ELECTRICIAN.getValue())).collect(Collectors.toList());
//			if (CollectionUtils.isNotEmpty(electricUserList)) {
//				ariablesMap.put("electrician", electricUserList.get(0).getUserId());
//			}
			//踏勘人员
			List<OrderRelatedUserEntity> surveyUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.SITE_TECHNICIAN.getValue())).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(surveyUserList)) {
				//探勘人员
				ariablesMap.put("siteEngineer", surveyUserList.get(0).getUserId());
			}
		}
	}
}

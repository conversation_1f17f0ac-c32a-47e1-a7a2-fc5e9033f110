package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 支架视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/29 - 11 - 29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MountingAndRackingDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 硬件安装图片业务主键
	 */
	@ApiModelProperty(value = "硬件安装图片业务主键")
	private Long mountingHardwareImgBizKey;
	/**
	 * 支架图片业务主键
	 */
	@ApiModelProperty(value = "支架图片业务主键")
	private Long confirmClampsBracketsImgBizKey;
	/**
	 * 面板方向和角度图片业务主键
	 */
	@ApiModelProperty(value = "面板方向和角度图片业务主键")
	private Long mountingVerifyPanelTiltImgBizKey;
	/**
	 * 密封接口和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接口和开口图片业务主键")
	private Long allRoofJointsImgBizKey;

}

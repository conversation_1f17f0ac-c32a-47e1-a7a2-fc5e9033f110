/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocFacFileVo;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import org.skyworth.ess.cocmaintenanceinfo.wrapper.CocMaintenanceInfoWrapper;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * coc和维护信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("cocMaintenanceInfo")
@Api(value = "coc和维护信息", tags = "coc和维护信息接口")
@Slf4j
public class CocMaintenanceInfoController extends BladeController {

	private final ICocMaintenanceInfoService cocMaintenanceInfoService;

	/**
	 * coc和维护信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入cocMaintenanceInfo")
	public R<CocMaintenanceInfoVO> detail(CocMaintenanceInfoEntity cocMaintenanceInfo) {
		CocMaintenanceInfoEntity detail = cocMaintenanceInfoService.getOne(Condition.getQueryWrapper(cocMaintenanceInfo));
		return R.data(CocMaintenanceInfoWrapper.build().entityVO(detail));
	}
	/**
	 * coc和维护信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入cocMaintenanceInfo")
	public R<IPage<CocMaintenanceInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> cocMaintenanceInfo, Query query) {
		IPage<CocMaintenanceInfoEntity> pages = cocMaintenanceInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(cocMaintenanceInfo, CocMaintenanceInfoEntity.class));
		return R.data(CocMaintenanceInfoWrapper.build().pageVO(pages));
	}

	/**
	 * coc和维护信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入cocMaintenanceInfo")
	public R<IPage<CocMaintenanceInfoVO>> page(CocMaintenanceInfoVO cocMaintenanceInfo, Query query) {
		IPage<CocMaintenanceInfoVO> pages = cocMaintenanceInfoService.selectCocMaintenanceInfoPage(Condition.getPage(query), cocMaintenanceInfo);
		return R.data(pages);
	}

	/**
	 * coc和维护信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入cocMaintenanceInfo")
	public R save(@Valid @RequestBody CocMaintenanceInfoEntity cocMaintenanceInfo) {
		return R.status(cocMaintenanceInfoService.save(cocMaintenanceInfo));
	}

	/**
	 * coc和维护信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入cocMaintenanceInfo")
	public R update(@Valid @RequestBody CocMaintenanceInfoEntity cocMaintenanceInfo) {
		return R.status(cocMaintenanceInfoService.updateById(cocMaintenanceInfo));
	}

	/**
	 * coc和维护信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入cocMaintenanceInfo")
	public R submit(@Valid @RequestBody CocMaintenanceInfoEntity cocMaintenanceInfo) {
		return R.status(cocMaintenanceInfoService.saveOrUpdate(cocMaintenanceInfo));
	}

	/**
	 * coc和维护信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cocMaintenanceInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-cocMaintenanceInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入cocMaintenanceInfo")
	public void exportcocMaintenanceInfo(@ApiIgnore @RequestParam Map<String, Object> cocMaintenanceInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<CocMaintenanceInfoEntity> queryWrapper = Condition.getQueryWrapper(cocMaintenanceInfo, CocMaintenanceInfoEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(cocMaintenanceInfo::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(CocMaintenanceInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<CocMaintenanceInfoExcel> list = cocMaintenanceInfoService.exportCocMaintenanceInfo(queryWrapper);
		ExcelUtil.export(response, "coc和维护信息数据" + DateUtil.time(), "coc和维护信息数据表", list, CocMaintenanceInfoExcel.class);
	}


	/**
	 * 绿电QC信息-质检信息审核
	 * */
	@PostMapping("/qcVerify/audit")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "qc审核", notes = "CocMaintenanceInfoEntity")
	public R qcVerifyAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
			return R.status(cocMaintenanceInfoService.qcVerifyAudit(cocMaintenanceInfoDTO, BizConstant.NUMBER_ZERO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * 绿电QC审核-交付经理验收
	 * */
	@PostMapping("/qcVerify/deliveryManagerAudit")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "qc审核", notes = "CocMaintenanceInfoEntity")
	public R qcVerifyDeliveryManagerAudit(@RequestBody OrderFlowDTO orderFlowDTO) {
		try{
			return R.status(cocMaintenanceInfoService.qcVerifyDeliveryManagerAudit(orderFlowDTO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * QC信息-construction-详情
	 * */
	@GetMapping("/qcVerify/detail")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "qc审核详情", notes = "传入orderId")
	public R<List<OrderNodeSubStatusEntity>> qcVerifyDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.qcVerifyDetail(orderId));
	}


	/**
	 * 临时COC-审核
	 * */
	@PostMapping("/temporaryCoc/audit")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "temporaryCoc审核", notes = "CocMaintenanceInfoDTO")
	public R temporaryCocAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getTemporaryCocImgBizKey())){
//				return R.fail("please upload temporary coc file");
//			}
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getTemporaryCocStartDate())){
//				return R.fail("please select startDate");
//			}
			return R.status(cocMaintenanceInfoService.temporaryCocAudit(cocMaintenanceInfoDTO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}


	/**
	 * 临时COC-详情
	 * */
	@GetMapping("/temporaryCoc/detail")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "临时COC详情", notes = "传入orderId")
	public R<CocMaintenanceInfoDTO> temporaryCocDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.temporaryCocDetail(orderId));
	}



	/**
	 * 绿电-业主验收
	 * */
	@PostMapping("/fac/audit")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "fac-审核", notes = "CocMaintenanceInfoDTO")
	public R facAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
			if("2".equals(cocMaintenanceInfoDTO.getBizType())){
//				if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFacLandlordSign())){
//					return R.fail("please upload landlord signature");
//				}
				if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFacTechnicianSign())){
					return R.fail("please upload Signature image of technical engineer");
				}
//				if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFacDeclaration())){
//					return R.fail("please check declaration");
//				}
//				if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getCustomersAcceptance())){
//					return R.fail("customers acceptance not uploaded");
//				}
			}
			return R.status(cocMaintenanceInfoService.facAudit(cocMaintenanceInfoDTO));
		} catch (ServiceException se){
			log.error(se.getMessage());
			return R.fail(se.getMessage());
		} catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * fac-详情
	 * */
	@GetMapping("/fac/detail")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "fac-审核详情", notes = "传入orderId")
	public R<CocMaintenanceInfoDTO> facDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.facDetail(orderId));
	}

	@GetMapping("/fac/template/export")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导出fac模板", notes = "导出fac模板")
	public void exportFacTemplate(HttpServletResponse response,@RequestParam String fileType,
								  @RequestParam(required = false) String capacity,@RequestParam Long orderId) {
		CocFacFileVo cocFacFileVo = new CocFacFileVo(fileType,capacity,orderId);
		cocMaintenanceInfoService.exportFacTemplate(response,cocFacFileVo);
	}


	/**
	 * 绿电-余额支付-审核
	 * */
	@PostMapping("/balancePay/audit")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "balancePay-审核", notes = "CocMaintenanceInfoDTO")
	public R balancePayAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getBalancePaymentConfirmDocBizKey())){
				return R.fail("please upload Balance confirmation file");
			}
			return R.status(cocMaintenanceInfoService.balancePayAudit(cocMaintenanceInfoDTO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * balancePay-详情
	 * */
	@GetMapping("/balancePay/detail")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "balancePay-审核详情", notes = "传入orderId")
	public R<CocMaintenanceInfoDTO> balancePayDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.balancePayDetail(orderId));
	}

	/**
	 * finalCoc-审核
	 * */
	@PostMapping("/finalCoc/audit")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "finalCoc-审核", notes = "CocMaintenanceInfoDTO")
	public R finalCocAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFinalCocImgBizKey())){
//				return R.fail("please upload Final coc file");
//			}
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFinalCocStartDate())){
//				return R.fail("please select startDate");
//			}
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getClientConfirmationBizKey())){
//				return R.fail("please upload client confirmation file");
//			}
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getDelivery())){
//				return R.fail("please select delivery");
//			}
//			//选择了非永久则结束时间都为必填项
//			if(!(ValidationUtil.isNotEmpty(cocMaintenanceInfoDTO.getFinalCocType())&&cocMaintenanceInfoDTO.getFinalCocType()==1)){
//				if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFinalCocEndDate())){
//					return R.fail("please select termination Date");
//				}
//			}

			return R.status(cocMaintenanceInfoService.finalCocAudit(cocMaintenanceInfoDTO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * finalCoc-详情
	 * */
	@GetMapping("/finalCoc/detail")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "finalCoc-审核详情", notes = "传入orderId")
	public R<CocMaintenanceInfoDTO> finalCocDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.finalCocDetail(orderId));
	}

	/**
	 * 绿电-维保审批maintenance-审核
	 * */
	@PostMapping("/maintenance/audit")
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "maintenance-审核", notes = "CocMaintenanceInfoDTO")
	public R maintenance(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		try{
//			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getFreeMaintenanceServiceDate())){
//				return R.fail("please select free Maintenance Service Date");
//			}
			if(ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getWarrantyDate())){
				return R.fail("please select warranty Date");
			}
			return R.status(cocMaintenanceInfoService.maintenanceAudit(cocMaintenanceInfoDTO));
		}catch (Exception e){
			log.error(e.getMessage());
			return R.status(false);
		}
	}

	/**
	 * maintenance-详情
	 * */
	@GetMapping("/maintenance/detail")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "maintenance-审核详情", notes = "传入orderId")
	public R<BasicOrderDTO> maintenanceDetail(@RequestParam Long orderId) {
		return R.data(cocMaintenanceInfoService.maintenanceDetail(orderId));
	}

	@GetMapping("/historyOrderInstallInfoSyncToPlant")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "历史订单安装信息同步到站点表", notes = "传入orderIds")
	public R historyOrderInstallInfoSyncToPlant(@RequestParam(value = "orderIds", required = false) List<Long> orderIds) {
		cocMaintenanceInfoService.historyOrderInstallInfoSyncToPlant(orderIds);
		return R.success("同步完成");
	}

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.wrapper;

import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 订单表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class OrderWrapper extends BaseEntityWrapper<OrderEntity, OrderVO> {

	public static OrderWrapper build() {
		return new OrderWrapper();
	}

	@Override
	public OrderVO entityVO(OrderEntity order) {
		OrderVO orderVO = Objects.requireNonNull(BeanUtil.copy(order, OrderVO.class));

		//User createUser = UserCache.getUser(order.getCreateUser());
		//User updateUser = UserCache.getUser(order.getUpdateUser());
		//orderVO.setCreateUserName(createUser.getName());
		//orderVO.setUpdateUserName(updateUser.getName());

		return orderVO;
	}


}

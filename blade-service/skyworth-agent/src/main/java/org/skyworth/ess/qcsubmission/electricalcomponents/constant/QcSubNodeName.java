package org.skyworth.ess.qcsubmission.electricalcomponents.constant;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 子节点名称
 * org.skyworth.ess.qcsubmission.electricalcomponents.constant
 *
 * <AUTHOR>
 * @since 2023/12/25 - 12 - 25
 */
@Getter
public enum QcSubNodeName {
	/**
	 * 智能能量变换器
	 */
	INVERTER("inverter", "智能能量变换器", "Inverter"),
	/**
	 * 太阳能板
	 */
	SOLAR_PANELS("solarPanels", "太阳能板", "Solar Panels"),
	/**
	 * 储能包
	 */
	BATTERY("battery", "储能包", "Battery"),
	/**
	 * 电气组件
	 */
	ELECTRICAL_COMPONENTS("electricalComponents", "电气组件", "Electrical Components"),
	/**
	 * 接地设备
	 */
	GROUNDING_EQUIPMENT("groundingEquipment", "接地设备", "Grounding Equipment"),
	/**
	 * 安装和支架
	 */
	MOUNTING_AND_RACKING("mountingAndRacking", "安装和支架", "Mounting And Racking"),
	/**
	 * 安全和合规
	 */
	SAFETY_AND_COMPLIANCE("safetyAndCompliance", "安全和合规", "Safety And Compliance"),
	/**
	 * 系统测试
	 */
	SYSTEM_TESTING("systemTesting", "系统测试", "System Testing"),
	/**
	 * 最终检查
	 */
	FINAL_INSPECTION("finalInspection", "最终检查", "Final Inspection");
	private static final Map<String, QcSubNodeName> BY_NAME = new HashMap<>();

	static {
		for (QcSubNodeName qcSubNodeName : values()) {
			BY_NAME.put(qcSubNodeName.subNodeName, qcSubNodeName);
		}
	}

	private final String subNodeName;
	private final String subNodeNameCn;
	private final String subNodeNameEn;

	QcSubNodeName(String subNodeName, String subNodeNameCn, String subNodeNameEn) {
		this.subNodeName = subNodeName;
		this.subNodeNameCn = subNodeNameCn;
		this.subNodeNameEn = subNodeNameEn;
	}

	public static Map<String, String> qcSubNodeNameMap(String language) {
		Map<String, String> subNameMap = new HashMap<>();
		BY_NAME.forEach((key, value) -> {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(language)) {
				subNameMap.put(key, value.getSubNodeNameCn());
			} else {
				subNameMap.put(key, value.getSubNodeNameEn());
			}
		});
		return subNameMap;
	}

	public static List<String> qcSubNodeNameList() {
		List<String> subNameList = new ArrayList<>();
		BY_NAME.forEach((key, value) -> {
			subNameList.add(key);
		});
		return subNameList;
	}
}

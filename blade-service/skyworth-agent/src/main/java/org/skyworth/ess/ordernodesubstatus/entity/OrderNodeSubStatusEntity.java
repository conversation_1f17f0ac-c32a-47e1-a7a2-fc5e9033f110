/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 订单节点子状态 实体类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@TableName("business_order_node_sub_status")
@ApiModel(value = "OrderNodeSubStatus对象", description = "订单节点子状态")
@EqualsAndHashCode(callSuper = true)
public class OrderNodeSubStatusEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 业务类型（保存类型：save；审批类型：approve）
	 */
	@ApiModelProperty(value = "业务类型（保存类型：save；审批类型：approve）")
	private String businessType;
	/**
	 * 节点类型（业务字典agent_business_order_node_name）
	 */
	@ApiModelProperty(value = "节点类型（业务字典agent_business_order_node_name）")
	private String nodeName;
	/**
	 * 节点版本信息，适用于节点名称下需要记录多次记录（如ehs下自动驳回和手动驳回都需要记录历史，此场景不适用全增全删）
	 */
	@ApiModelProperty(value = "节点版本信息，适用于节点名称下需要记录多次记录（如ehs下自动驳回和手动驳回都需要记录历史，此场景不适用全增全删）")
	private String nodeVersion;
	/**
	 * 子节点类型
	 */
	@ApiModelProperty(value = "子节点类型")
	private String subNodeName;
	/**
	 * 子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分
	 */
	@ApiModelProperty(value = "子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分")
	private String subStatus;


	/**
	 * 子节点说明
	 */
	@ApiModelProperty(value = "子节点说明")
	private String subRemark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

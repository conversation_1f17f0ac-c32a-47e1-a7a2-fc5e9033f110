/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.mapper;

import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.vo.AdditionalInfoVO;
import org.skyworth.ess.additionalInfo.excel.AdditionalInfoExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片附加信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface AdditionalInfoMapper extends BaseMapper<AdditionalInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param additionalInfo
	 * @return
	 */
	List<AdditionalInfoVO> selectAdditionalInfoPage(IPage page, AdditionalInfoVO additionalInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<AdditionalInfoExcel> exportAdditionalInfo(@Param("ew") Wrapper<AdditionalInfoEntity> queryWrapper);

	/**
	 * 删除图片描述
	 *
	 * @param businessIds 入参
	 * <AUTHOR>
	 * @since 2023/11/30 14:09
	 **/
	void deleteByBusinessIds(@Param("businessIds") List<Long> businessIds);

}

/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.solarpanels.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 施工-光伏板信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SolarPanelsExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 光伏板图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("光伏板图片业务主键")
	private Long panelsImgBizKey;
	/**
	 * 序号和型号图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("序号和型号图片业务主键")
	private Long serialNumVerificationImgBizKey;
	/**
	 * 船损检验图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("船损检验图片业务主键")
	private Long inspecationShipDamageImgBizKey;
	/**
	 * 光伏板的方向和倾斜角度图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("光伏板的方向和倾斜角度图片业务主键")
	private Long verifyPanelTiltImgBizKey;
	/**
	 * 锚杆检查图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("锚杆检查图片业务主键")
	private Long inspectRoofFlashingImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("密封接头和开口图片业务主键")
	private Long allRoofSealedImgBizKey;
	/**
	 * 电缆和布线图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("电缆和布线图片业务主键")
	private Long verifyCableWiringImgBizKey;
	/**
	 * pvc管使用图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("pvc管使用图片业务主键")
	private Long confirmPvcPipeImgBizKey;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

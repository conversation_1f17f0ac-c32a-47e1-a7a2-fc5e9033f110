package org.skyworth.ess.surveyreview.service;

import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.surveyreview.entity.MaintainSurveyReviewEntity;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-04-18 14:31
 **/
public interface IMaintainSurveyReviewService {
	/**
	 * 踏勘信息子节点审批
	 *
	 * @param orderNodeSubStatusEntity 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2024/4/18 15:24
	 **/
	R<Boolean> saveSubNodeApprovalStatus(OrderNodeSubStatusEntity orderNodeSubStatusEntity);

	/**
	 * 提交踏勘审批
	 *
	 * @param maintainSurveyReviewEntity 入参
	 * @return R<Boolean>
	 * <AUTHOR>
	 * @since 2024/4/18 16:19
	 **/
	R<Boolean> submit(MaintainSurveyReviewEntity maintainSurveyReviewEntity);

	/**
	 * 查询施工人员和电气工程师
	 * @param orderFlowDTO 审批信息
	 * @param orderId 入参
	 * <AUTHOR>
	 * @since 2024/5/21 10:09
	 **/
	void obtainApprovalForRejectNode(OrderFlowDTO orderFlowDTO, Long orderId);
}

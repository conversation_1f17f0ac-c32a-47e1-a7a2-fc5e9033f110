package org.skyworth.ess.createorder.order.utils;

/**
 * 订单状态枚举类
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
public enum OrderStatus {
	HOUSE(1, "房屋"),
	SITE(2, "踏勘"),
	DESIGN(3, "设计"),
	CONSTRUCTION(4, "施工"),
	CANCEL(5,"取消"),

	OVER(6,"完成")
	;
	private Integer type;
	private String dec;

	OrderStatus(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}

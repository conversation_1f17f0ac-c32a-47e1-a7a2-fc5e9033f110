/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;

/**
 * 图片附加信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@TableName("image_additional_info")
@ApiModel(value = "AdditionalInfo对象", description = "图片附加信息")
@EqualsAndHashCode(callSuper = true)
public class AdditionalInfoEntity extends TenantEntity {

	/**
	 * 图片key
	 */
	@ApiModelProperty(value = "图片key")
	private Long imgBizKey;
	/**
	 * 图片说明
	 */
	@ApiModelProperty(value = "图片说明")
	private String imgRemark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

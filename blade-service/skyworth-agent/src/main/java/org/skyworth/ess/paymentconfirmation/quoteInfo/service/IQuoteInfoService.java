/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.paymentconfirmation.quoteInfo.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.paymentconfirmation.quoteInfo.dto.QuoteInfoDTO;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.excel.QuoteInfoExcel;
import org.skyworth.ess.paymentconfirmation.quoteInfo.vo.QuoteInfoVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 设备报价信息 服务类
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface IQuoteInfoService extends BaseService<QuoteInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param quoteInfo
	 * @return
	 */
	IPage<QuoteInfoVO> selectQuoteInfoPage(IPage<QuoteInfoVO> page, QuoteInfoVO quoteInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<QuoteInfoExcel> exportQuoteInfo(Wrapper<QuoteInfoEntity> queryWrapper);


	R<?> installPayConfirm(QuoteInfoDTO quoteInfoDTO);

	R<?> selectInstallPayConfirmInfo(QuoteInfoDTO quoteInfoDTO);
}

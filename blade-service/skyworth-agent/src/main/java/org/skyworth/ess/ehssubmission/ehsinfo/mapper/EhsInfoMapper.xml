<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.ehssubmission.ehsinfo.mapper.EhsInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="infoResultMap" type="org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
<!--        <result column="medical_emergency_number" property="medicalEmergencyNumber"/>-->
<!--        <result column="fire_department" property="fireDepartment"/>-->
<!--        <result column="police" property="police"/>-->
<!--        <result column="car_towing_service" property="carTowingService"/>-->
<!--        <result column="company_supervisor" property="companySupervisor"/>-->
        <result column="member_safety_checklist" property="memberSafetyChecklist"/>
        <result column="member_safety_checklist_img_biz_key" property="memberSafetyChecklistImgBizKey"/>
        <result column="first_aid_kit_img_biz_key" property="firstAidKitImgBizKey"/>
<!--        <result column="rescue_kit_img_biz_key" property="rescueKitImgBizKey"/>-->
<!--        <result column="fire_extinguisher_img_biz_key" property="fireExtinguisherImgBizKey"/>-->
<!--        <result column="acceptance_letter_img_biz_key" property="acceptanceLetterImgBizKey"/>-->
<!--        <result column="risk_assessment_img_biz_key" property="riskAssessmentImgBizKey"/>-->
<!--        <result column="risk_list" property="riskList"/>-->
<!--        <result column="risk_solution" property="riskSolution"/>-->
        <result column="declaration" property="declaration"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectInfoPage" resultMap="infoResultMap">
        select * from ehs_info where is_deleted = 0
    </select>


    <select id="exportInfo" resultType="org.skyworth.ess.ehssubmission.ehsinfo.excel.EhsInfoExcel">
        SELECT * FROM ehs_info ${ew.customSqlSegment}
    </select>

</mapper>

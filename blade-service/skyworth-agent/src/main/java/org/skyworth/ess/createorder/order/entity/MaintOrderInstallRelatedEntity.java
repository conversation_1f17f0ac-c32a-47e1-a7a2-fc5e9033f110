package org.skyworth.ess.createorder.order.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;


/**
 * <AUTHOR>
 */
@Data
@TableName("maint_order_install_related")
@ApiModel(value = "订单关系对象", description = "安装订单和维保订单关系表")
@EqualsAndHashCode(callSuper = true)
public class MaintOrderInstallRelatedEntity extends SkyWorthEntity {
    /** 安装订单id */
    @ApiModelProperty(name = "安装订单id",notes = "")
    private Long orderInstallId ;
    /** 维保订单id */
    @ApiModelProperty(name = "维保订单id",notes = "")
    private Long orderMaintId ;
}

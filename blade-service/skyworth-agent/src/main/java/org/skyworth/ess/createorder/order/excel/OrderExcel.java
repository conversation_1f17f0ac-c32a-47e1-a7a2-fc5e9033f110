/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 订单表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OrderExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单编号")
	private String orderNumber;
	/**
	 * 经销商id
	 */
	@ColumnWidth(20)
	@ExcelProperty("经销商id")
	private Long distributorId;
	/**
	 * 推广经理
	 */
	@ColumnWidth(20)
	@ExcelProperty("推广经理")
	private Long rolloutManagerId;
	/**
	 * 客户名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户名称")
	private String customerName;
	/**
	 * 客户电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户电话")
	private Integer customerPhone;
	/**
	 * 客户邮箱
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户邮箱")
	private String customerEmail;
	/**
	 * 客户公司名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("客户公司名称")
	private String customerCompanyName;
	/**
	 * 安装站点国家编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装站点国家编码")
	private String siteCountryCode;
	/**
	 * 安装站点省份编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装站点省份编码")
	private String siteProvinceCode;
	/**
	 * 安装站点城市编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装站点城市编码")
	private String siteCityCode;
	/**
	 * 地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("地址")
	private String siteAddress;
	/**
	 * 安装原因
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装原因")
	private String installReason;
	/**
	 * 安装预算
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装预算")
	private String installBudgets;
	/**
	 * 预计安装开始日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("预计安装开始日期")
	private Date tentativeInstallStartDate;
	/**
	 * 预计安装结束日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("预计安装结束日期")
	private Date tentativeInstallEndDate;
	/**
	 * 踏勘时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("踏勘时间")
	private Date surveyDate;
	/**
	 * 附加文档key
	 */
	@ColumnWidth(20)
	@ExcelProperty("附加文档key")
	private Long additionalDocBizKey;
	/**
	 * 第二联系人名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("第二联系人名称")
	private String secondContactName;
	/**
	 * 第二联系人电话
	 */
	@ColumnWidth(20)
	@ExcelProperty("第二联系人电话")
	private Integer secondContactPhone;
	/**
	 * 项目类型（业务字典agent_project_type）
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目类型（业务字典agent_project_type）")
	private String projectType;
	/**
	 * 项目类型中其他类型说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("项目类型中其他类型说明")
	private String projectTypeOtherRemark;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.qcsubmission.electricalcomponents.entity.ElectricalComponentsEntity;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.ElectricalComponentsVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.excel.ElectricalComponentsExcel;
import org.skyworth.ess.qcsubmission.electricalcomponents.wrapper.ElectricalComponentsWrapper;
import org.skyworth.ess.qcsubmission.electricalcomponents.service.IElectricalComponentsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 施工-电器元件信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-electricalcomponents/electricalcomponents")
@Api(value = "施工-电器元件信息", tags = "施工-电器元件信息接口")
public class ElectricalComponentsController extends BladeController {

	private final IElectricalComponentsService electricalcomponentsService;

	/**
	 * 施工-电器元件信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入electricalcomponents")
	public R<ElectricalComponentsVO> detail(ElectricalComponentsEntity electricalcomponents) {
		ElectricalComponentsEntity detail = electricalcomponentsService.getOne(Condition.getQueryWrapper(electricalcomponents));
		return R.data(ElectricalComponentsWrapper.build().entityVO(detail));
	}
	/**
	 * 施工-电器元件信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入electricalcomponents")
	public R<IPage<ElectricalComponentsVO>> list(@ApiIgnore @RequestParam Map<String, Object> electricalcomponents, Query query) {
		IPage<ElectricalComponentsEntity> pages = electricalcomponentsService.page(Condition.getPage(query), Condition.getQueryWrapper(electricalcomponents, ElectricalComponentsEntity.class));
		return R.data(ElectricalComponentsWrapper.build().pageVO(pages));
	}

	/**
	 * 施工-电器元件信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入electricalcomponents")
	public R<IPage<ElectricalComponentsVO>> page(ElectricalComponentsVO electricalcomponents, Query query) {
		IPage<ElectricalComponentsVO> pages = electricalcomponentsService.selectElectricalComponentsPage(Condition.getPage(query), electricalcomponents);
		return R.data(pages);
	}

	/**
	 * 施工-电器元件信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入electricalcomponents")
	public R save(@Valid @RequestBody ElectricalComponentsEntity electricalcomponents) {
		return R.status(electricalcomponentsService.save(electricalcomponents));
	}

	/**
	 * 施工-电器元件信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入electricalcomponents")
	public R update(@Valid @RequestBody ElectricalComponentsEntity electricalcomponents) {
		return R.status(electricalcomponentsService.updateById(electricalcomponents));
	}

	/**
	 * 施工-电器元件信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入electricalcomponents")
	public R submit(@Valid @RequestBody ElectricalComponentsEntity electricalcomponents) {
		return R.status(electricalcomponentsService.saveOrUpdate(electricalcomponents));
	}

	/**
	 * 施工-电器元件信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(electricalcomponentsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-electricalcomponents")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入electricalcomponents")
	public void exportElectricalComponents(@ApiIgnore @RequestParam Map<String, Object> electricalcomponents, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ElectricalComponentsEntity> queryWrapper = Condition.getQueryWrapper(electricalcomponents, ElectricalComponentsEntity.class);
		queryWrapper.lambda().eq(ElectricalComponentsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ElectricalComponentsExcel> list = electricalcomponentsService.exportElectricalComponents(queryWrapper);
		ExcelUtil.export(response, "施工-电器元件信息数据" + DateUtil.time(), "施工-电器元件信息数据表", list, ElectricalComponentsExcel.class);
	}

}

package org.skyworth.ess.materialcollection.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.design.deviceItem.dto.DeviceItemDTO;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.mapper.DeviceItemMapper;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.service.IMaintainDesignService;
import org.skyworth.ess.design.vo.DesignVO;
import org.skyworth.ess.design.vo.ItemMergeVO;
import org.skyworth.ess.design.vo.MaintainItemMergeVO;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.materialcollection.service.MaterialCollectionService;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 物料签收 实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
@AllArgsConstructor
public class MaterialCollectionServiceImpl extends BaseServiceImpl<DeviceItemMapper, DeviceItemEntity> implements MaterialCollectionService {


	private final IReviewOrderService iReviewOrderService;

	private final IDeviceItemService iDeviceItemService;

	private final IInstallRelatedInfoService infoService;

	private final IAttachmentInfoService iAttachmentInfoService;

	private final IOrderRelatedUserService iOrderRelatedUserService;

	private final IMaintainDesignService maintainDesignService;

	/**
	 * @Description: 物料签收审核
	 * @Param: [deviceItemDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/6 10:36
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> examineMaterialCollection(DeviceItemDTO deviceItemDTO) {
		if (CollectionUtils.isEmpty(deviceItemDTO.getDeviceItemEntityList())) {
			throw new BusinessException("agent.materialCollection.examineMaterialCollection.deviceItemEntityList.notNull");
		}
		if (ObjectUtils.isEmpty(deviceItemDTO.getOrderFlowDTO())) {
			throw new BusinessException("agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull");
		}
		//修改物料签收收货确认信息
		updateMaterialCollectionInfo(deviceItemDTO.getDeviceItemEntityList());
		//web端保存签名key
		if (StringUtils.isNotBlank(deviceItemDTO.getWebFlag())) {
			saveMaterialSignature(deviceItemDTO);
		}
		//查询施工队长，电工人员
		OrderFlowDTO orderFlowDTO = setMaintenanceWorkFlowData(deviceItemDTO.getOrderFlowDTO());
		//审批流转
		R<?> examineApproveResult = iReviewOrderService.examineApprove(orderFlowDTO);
		if (!examineApproveResult.isSuccess()) {
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		return R.status(true);
	}


	/**
	 * @Description: 查询物料签收信息
	 * @Param: [deviceItemDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/6 13:23
	 **/
	@Override
	public R<?> selectMaterialCollection(DeviceItemDTO deviceItemDTO) {
		DeviceItemDTO resultDeviceItemDto = new DeviceItemDTO();
		//查询物料签收信息
		ItemMergeVO itemMergeVO = iDeviceItemService.queryProductList(deviceItemDTO.getOrderId());
		resultDeviceItemDto.setItemMergeVO(itemMergeVO);
		return R.data(resultDeviceItemDto);
	}

	/**
	 * @ClassName   MaterialCollectionServiceImpl
	 * @Description 查询运维物料签收信息
	 * <AUTHOR>
	 * @Date        2024/4/17 15:48
	 * @version     V1.0
	 */
	@Override
	public R<?> selectMaintenanceMaterialCollection(DeviceItemDTO deviceItemDTO) {
		DeviceItemDTO resultDeviceItemDto = new DeviceItemDTO();
		// 查询物料签收信息(保修和非保修物料)
		DesignVO designVO = maintainDesignService.designDetail(deviceItemDTO.getOrderId());
		if (ObjectUtil.isNotEmpty(designVO)) {
			MaintainItemMergeVO maintainItemMergeVO = designVO.getMaintainItemMergeVO();
			resultDeviceItemDto.setMaintainItemMergeVO(maintainItemMergeVO);
		}
		return R.data(resultDeviceItemDto);
	}


	/**
	 * @Description: 查询签名信息
	 * @Param: [deviceItemDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2024/1/3 16:56
	 **/
	@Override
	public R<?> selectMaterialSignInfo(DeviceItemDTO deviceItemDTO) {
		DeviceItemDTO resultDeviceItemDto = new DeviceItemDTO();
		//查询签名图片key
		LambdaQueryWrapper<InstallRelatedInfoEntity> installRelatedInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		installRelatedInfoEntityLambdaQueryWrapper.eq(InstallRelatedInfoEntity::getOrderId, deviceItemDTO.getOrderId());
		InstallRelatedInfoEntity installRelatedInfoEntity = infoService.getOne(installRelatedInfoEntityLambdaQueryWrapper);
		//根据图片key查询图片和相应备注信息
		if (ObjectUtils.isNotEmpty(installRelatedInfoEntity)) {
			//安装技术经理签名图片key
			resultDeviceItemDto.setInstallTechnicianSignImgBizKey(installRelatedInfoEntity.getInstallTechnicianSignImgBizKey());
			//仓库管理员签名图片key
			resultDeviceItemDto.setWarehouseManagerSignImgBizKey(installRelatedInfoEntity.getWarehouseManagerSignImgBizKey());
			AttachmentDescVO attachmentInfo = iAttachmentInfoService.getAttachmentInfo(installRelatedInfoEntity);
			if (ObjectUtils.isNotEmpty(attachmentInfo)) {
				resultDeviceItemDto.setAttachmentInfo(attachmentInfo);
			}
		}
		return R.data(resultDeviceItemDto);
	}


	/**
	 * @Description: 修改物料签收收货确认信息
	 * @Param: [deviceItemEntityList, deviceItemDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/6 10:44
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void updateMaterialCollectionInfo(List<DeviceItemEntity> deviceItemEntityList) {
		//设置确认收货
		boolean updateFlag = this.updateBatchById(deviceItemEntityList);
		if (!updateFlag) {
			throw new BusinessException("agent.materialCollection.updateMaterialCollectionInfo.updateDeviceItem.fail");
		}
	}


	@Override
	public OrderFlowDTO setWorkFlowData(OrderFlowDTO orderFlowDTO) {
		if (ObjectUtils.isEmpty(orderFlowDTO.getBusinessId())) {
			throw new BusinessException("agent.materialCollection.setWorkFlowData.businessId.notNull");
		}
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//查询施工人员
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderFlowDTO.getBusinessId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		OrderRelatedUserEntity orderRelatedUserEntity = this.iOrderRelatedUserService.getOne(orderRelatedUserEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderRelatedUserEntity) || ObjectUtils.isEmpty(orderRelatedUserEntity.getUserId())) {
			throw new BusinessException("agent.materialCollection.setWorkFlowData.getRelatedUser.notNull");
		}
		variables.put("siteEngineer", orderRelatedUserEntity.getUserId());
		return orderFlowDTO;
	}

	@Override
	public OrderFlowDTO setMaintenanceWorkFlowData(OrderFlowDTO orderFlowDTO) {
		if (ObjectUtils.isEmpty(orderFlowDTO.getBusinessId())) {
			throw new BusinessException("agent.materialCollection.setWorkFlowData.businessId.notNull");
		}
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//查询施工人员
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderFlowDTO.getBusinessId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		OrderRelatedUserEntity orderRelatedUserEntity = this.iOrderRelatedUserService.getOne(orderRelatedUserEntityLambdaQueryWrapper);

		/*//查询电工人员
		LambdaQueryWrapper<OrderRelatedUserEntity> electricianLambdaQueryWrapper = new LambdaQueryWrapper<>();
		electricianLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderFlowDTO.getBusinessId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.ELECTRICIAN.getName());
		OrderRelatedUserEntity orderRelatedUserElectrician = this.iOrderRelatedUserService.getOne(electricianLambdaQueryWrapper);*/

		if (ObjectUtils.isEmpty(orderRelatedUserEntity)) {
			throw new BusinessException("agent.materialCollection.setWorkFlowData.getRelatedUser.notNull");
		}
		//施工人员
		if (ObjectUtils.isNotEmpty(orderRelatedUserEntity)) {
			variables.put("siteEngineer", orderRelatedUserEntity.getUserId());
		}
		//电工人员
		/*if (ObjectUtils.isNotEmpty(orderRelatedUserElectrician)) {
			variables.put("electrician", orderRelatedUserElectrician.getUserId());
		}*/
		return orderFlowDTO;
	}


	/**
	 * @Description: 保存物料签名
	 * @Param: [deviceItemDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/22 14:41
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> saveMaterialSignature(DeviceItemDTO deviceItemDTO) {
		//保存安装技术经理签名图片key和仓库管理员签名图片key
		LambdaUpdateWrapper<InstallRelatedInfoEntity> installRelatedInfoEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		installRelatedInfoEntityLambdaUpdateWrapper.eq(InstallRelatedInfoEntity::getOrderId, deviceItemDTO.getOrderId());
		//安装技术经理签名图片key
		if (ObjectUtils.isNotEmpty(deviceItemDTO.getInstallTechnicianSignImgBizKey())) {
			installRelatedInfoEntityLambdaUpdateWrapper.set(InstallRelatedInfoEntity::getInstallTechnicianSignImgBizKey, deviceItemDTO.getInstallTechnicianSignImgBizKey());
		}
		//仓库管理员签名图片key
		if (ObjectUtils.isNotEmpty(deviceItemDTO.getWarehouseManagerSignImgBizKey())) {
			installRelatedInfoEntityLambdaUpdateWrapper.set(InstallRelatedInfoEntity::getWarehouseManagerSignImgBizKey, deviceItemDTO.getWarehouseManagerSignImgBizKey());
		}
		this.infoService.update(installRelatedInfoEntityLambdaUpdateWrapper);
		if (ObjectUtils.isNotEmpty(deviceItemDTO.getSkyWorthFileEntity())) {
			//保存签名图片
			SkyWorthFileEntity skyWorthFileEntity = deviceItemDTO.getSkyWorthFileEntity();
			this.iReviewOrderService.setOrUpdateFileImgInfo(skyWorthFileEntity);
		}
		return R.status(true);
	}
}

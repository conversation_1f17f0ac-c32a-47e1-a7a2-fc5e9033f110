package org.skyworth.ess.installwoassignment.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum UserTypeEnum {

	/**
	 * 踏勘人员
	 */
//	SURVEY_USER(1, "SurveyUser"),


	/**
	 * 电气工程师
	 */
//	ELECTRICIAN(2, "Electrician"),


	/**
	 * 施工人员
	 */
	SITE_TECHNICIAN(3, "SiteTechnician"),


	/**
	 * 施工队长
	 */
	SITE_TECHNICIAN_LEADER(4, "SiteTechnicianLeader");


	public static Set<Integer> getIds() {
		Set<Integer> idList = new HashSet<>();
		for (UserTypeEnum item : UserTypeEnum.values()) {
			idList.add(item.getTypeId());
		}
		return idList;
	}


	public static String getValueByKey(Integer code) {
		UserTypeEnum[] constructionMaterialEnums = values();
		for (UserTypeEnum constructionMaterialEnum : constructionMaterialEnums) {
			if (constructionMaterialEnum.getKey().equals(code)) {
				return constructionMaterialEnum.getValue();
			}
		}
		return null;
	}


	public Integer getKey() {
		return typeId;
	}

	public String getValue() {
		return name;
	}


	/**
	 * 用户类别
	 */
	private final Integer typeId;


	/**
	 * 用户名称
	 */
	private final String name;


}

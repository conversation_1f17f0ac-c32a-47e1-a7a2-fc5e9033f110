package org.skyworth.ess.revieworder.orderworkflow.constant;

/**
 * 模块分类枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum ExamineApproveType {
	PASS("1", "同意"),
	REJECT("2", "拒绝"),
	CANCEL("3", "取消"),

	;

	private String type;
	private String dec;

	ExamineApproveType(String type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(String type) {
		for (ExamineApproveType houseModuleType : ExamineApproveType.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}

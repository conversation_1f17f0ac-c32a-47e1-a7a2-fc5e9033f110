package org.skyworth.ess.createorder.order.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.createorder.order.entity.MaintOrderInstallRelatedEntity;
import org.skyworth.ess.createorder.order.mapper.MaintOrderInstallRelatedMapper;
import org.skyworth.ess.createorder.order.service.IMaintOrderInstallRelatedService;
import org.skyworth.ess.createorder.order.vo.MaintRelatedVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaintOrderInstallRelatedServiceImpl extends BaseServiceImpl<MaintOrderInstallRelatedMapper, MaintOrderInstallRelatedEntity>
        implements IMaintOrderInstallRelatedService {
    @Override
    public List<MaintRelatedVO> queryMaintHistory(Long installOrderId) {
        return baseMapper.queryMaintHistory(installOrderId);
    }

    @Override
    public MaintRelatedVO queryInstallOrderByMaintId(Long maintOrderId) {
        return baseMapper.queryInstallOrderByMaintId(maintOrderId);
    }
}

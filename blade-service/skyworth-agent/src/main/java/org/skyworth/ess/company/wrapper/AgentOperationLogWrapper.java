/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.company.entity.AgentOperationLogEntity;
import org.skyworth.ess.company.vo.AgentOperationLogVO;
import java.util.Objects;

/**
 * 操作日志表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
public class AgentOperationLogWrapper extends BaseEntityWrapper<AgentOperationLogEntity, AgentOperationLogVO>  {

	public static AgentOperationLogWrapper build() {
		return new AgentOperationLogWrapper();
 	}

	@Override
	public AgentOperationLogVO entityVO(AgentOperationLogEntity agentOperationLog) {
		AgentOperationLogVO agentOperationLogVO = Objects.requireNonNull(BeanUtil.copy(agentOperationLog, AgentOperationLogVO.class));

		//User createUser = UserCache.getUser(agentOperationLog.getCreateUser());
		//User updateUser = UserCache.getUser(agentOperationLog.getUpdateUser());
		//agentOperationLogVO.setCreateUserName(createUser.getName());
		//agentOperationLogVO.setUpdateUserName(updateUser.getName());

		return agentOperationLogVO;
	}


}

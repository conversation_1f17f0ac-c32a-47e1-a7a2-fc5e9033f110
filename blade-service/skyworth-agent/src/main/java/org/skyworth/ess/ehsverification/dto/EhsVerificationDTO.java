/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehsverification.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ehs审核信息
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
public class EhsVerificationDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	//订单id
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;

	//ehs审核信息小节点名称
	@NotBlank(message = "{agent.ehsVerification.ehsNodeName.notNull}")
	private String ehsNodeName;

	//ehs审核信息小节点标志 同意：pass；驳回：reject
	@NotBlank(message = "{agent.ehsVerification.ehsInfoFlag.notNull}")
	private String ehsInfoFlag;


}

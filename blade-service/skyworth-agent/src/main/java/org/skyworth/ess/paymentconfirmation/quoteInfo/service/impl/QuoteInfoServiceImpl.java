/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.paymentconfirmation.quoteInfo.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.paymentconfirmation.quoteInfo.dto.QuoteInfoDTO;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.excel.QuoteInfoExcel;
import org.skyworth.ess.paymentconfirmation.quoteInfo.mapper.QuoteInfoMapper;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.skyworth.ess.paymentconfirmation.quoteInfo.vo.QuoteInfoVO;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * 设备报价信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Service
@AllArgsConstructor
public class QuoteInfoServiceImpl extends BaseServiceImpl<QuoteInfoMapper, QuoteInfoEntity> implements IQuoteInfoService {


	private final IReviewOrderService iReviewOrderService;

	private final IOrderService iOrderService;

	/**
	 * @Description: 审核安装付款确认/取消订单
	 * @Param: [quoteInfoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/4 13:29
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> installPayConfirm(QuoteInfoDTO quoteInfoDTO) {
		//修改安装付款确认信息
		boolean updateQuoteInfoFlag = updateQuoteInfo(quoteInfoDTO.getQuoteInfoEntity(), quoteInfoDTO);
		//审批流转
		if (updateQuoteInfoFlag) {
			R<?> examineApproveResult = iReviewOrderService.examineApprove(quoteInfoDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
		}
		return R.status(true);
	}

	/**
	 * @Description: 查询安装付款确认信息
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/4 15:55
	 **/
	@Override
	public R<?> selectInstallPayConfirmInfo(QuoteInfoDTO quoteInfoDTO) {
		validParamStatus(quoteInfoDTO);
		//根据订单id获取设备报价信息
		QuoteInfoEntity quoteInfoEntity = getQuoteInfoByOrderId(quoteInfoDTO.getQuoteInfoEntity());
		if (ObjectUtils.isEmpty(quoteInfoEntity)) {
			return R.data(quoteInfoDTO);
		}
		quoteInfoDTO.setQuoteInfoEntity(quoteInfoEntity);
		//获取附件信息
//		if (ObjectUtils.isNotEmpty(quoteInfoEntity.getPaymentConfirmDocBizKey())) {
//			SkyWorthFileEntity skyWorthFileEntity = this.iOrderService.getFileAttachmentInfo(Collections.singletonList(quoteInfoEntity.getPaymentConfirmDocBizKey()));
//			if (ObjectUtils.isNotEmpty(skyWorthFileEntity)) {
//				//附件信息
//				quoteInfoDTO.setAttachmentMap(skyWorthFileEntity.getAttachmentMap());
//				//附件描述
//				quoteInfoDTO.setImgDescViewMap(skyWorthFileEntity.getImgDescViewMap());
//			}
//		}
		return R.data(quoteInfoDTO);
	}


	/**
	 * @Description: 修改安装付款确认信息
	 * @Param: [quoteInfoEntity, quoteInfoDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/4 14:49
	 **/
	@Transactional(rollbackFor = Exception.class)
	public boolean updateQuoteInfo(QuoteInfoEntity quoteInfoEntity, QuoteInfoDTO quoteInfoDTO) {
		//保存或修改附件信息
		boolean updateQuoteInfoFlag = true;
		if (ObjectUtils.isEmpty(quoteInfoEntity.getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		QuoteInfoEntity quoteInfo = getQuoteInfoByOrderId(quoteInfoEntity);
		//查询设备报价信息
		if (ObjectUtils.isEmpty(quoteInfo)) {
			throw new BusinessException("agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull");
		}
		log.warn("quoteInfo: "+ JSON.toJSONString(quoteInfo));
		//保存收款证明文档key
//		quoteInfo.setPaymentConfirmDocBizKey(quoteInfoEntity.getPaymentConfirmDocBizKey());
//		quoteInfo.setPaymentConfirmMailTo(quoteInfoEntity.getPaymentConfirmMailTo());
//		quoteInfo.setPaymentConfirmMailBcc(quoteInfoEntity.getPaymentConfirmMailBcc());
//		quoteInfo.setPaymentConfirmSendMail(quoteInfoEntity.getPaymentConfirmSendMail());
//		quoteInfo.setPaymentConfirmMailContent(quoteInfoEntity.getPaymentConfirmMailContent());
		boolean updateFlag = this.updateById(quoteInfo);
		if (!updateFlag) {
			throw new BusinessException("agent.quoteInfo.updateQuoteInfo.updateQuoteInfo.fail");
		}
		if (ObjectUtils.isNotEmpty(quoteInfoDTO.getSkyWorthFileEntity())) {
			//保存收款证明文档附件
			SkyWorthFileEntity skyWorthFileEntity = quoteInfoDTO.getSkyWorthFileEntity();
			updateQuoteInfoFlag = this.iReviewOrderService.setOrUpdateFileImgInfo(skyWorthFileEntity);
		}
		return updateQuoteInfoFlag;
	}


	/**
	 * @Description: 根据订单id获取设备报价信息
	 * @Param: [quoteInfoEntity]
	 * @Return: org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity
	 * @Author: baixu
	 * @Date: 2023/12/4 16:27
	 **/
	private QuoteInfoEntity getQuoteInfoByOrderId(QuoteInfoEntity quoteInfoEntity) {
		LambdaQueryWrapper<QuoteInfoEntity> quoteInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		quoteInfoEntityLambdaQueryWrapper.eq(QuoteInfoEntity::getOrderId, quoteInfoEntity.getOrderId());
		return this.getOne(quoteInfoEntityLambdaQueryWrapper);
	}

	/**
	 * @Description: 校验参数
	 * @Param: [orderRelatedUserDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 13:46
	 **/
	private void validParamStatus(QuoteInfoDTO quoteInfoDTO) {
		if (ObjectUtils.isEmpty(quoteInfoDTO)) {
			throw new BusinessException("agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull");
		}
		if (ObjectUtils.isEmpty(quoteInfoDTO.getQuoteInfoEntity())) {
			throw new BusinessException("agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull");
		}
		if (ObjectUtils.isEmpty(quoteInfoDTO.getQuoteInfoEntity().getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
	}


	@Override
	public IPage<QuoteInfoVO> selectQuoteInfoPage(IPage<QuoteInfoVO> page, QuoteInfoVO quoteInfo) {
		return page.setRecords(baseMapper.selectQuoteInfoPage(page, quoteInfo));
	}


	@Override
	public List<QuoteInfoExcel> exportQuoteInfo(Wrapper<QuoteInfoEntity> queryWrapper) {
		return baseMapper.exportQuoteInfo(queryWrapper);
	}
}

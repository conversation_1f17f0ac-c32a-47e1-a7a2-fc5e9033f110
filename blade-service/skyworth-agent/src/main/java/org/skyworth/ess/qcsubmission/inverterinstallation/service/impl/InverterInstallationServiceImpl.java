/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.inverterinstallation.service.impl;

import org.skyworth.ess.qcsubmission.inverterinstallation.entity.InverterInstallationEntity;
import org.skyworth.ess.qcsubmission.inverterinstallation.vo.InverterInstallationVO;
import org.skyworth.ess.qcsubmission.inverterinstallation.excel.InverterInstallationExcel;
import org.skyworth.ess.qcsubmission.inverterinstallation.mapper.InverterInstallationMapper;
import org.skyworth.ess.qcsubmission.inverterinstallation.service.IInverterInstallationService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 施工-智能能量变换器信息; 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class InverterInstallationServiceImpl extends BaseServiceImpl<InverterInstallationMapper, InverterInstallationEntity> implements IInverterInstallationService {

	@Override
	public IPage<InverterInstallationVO> selectInverterInstallationPage(IPage<InverterInstallationVO> page, InverterInstallationVO inverterinstallation) {
		return page.setRecords(baseMapper.selectInverterInstallationPage(page, inverterinstallation));
	}


	@Override
	public List<InverterInstallationExcel> exportInverterInstallation(Wrapper<InverterInstallationEntity> queryWrapper) {
		List<InverterInstallationExcel> inverterinstallationList = baseMapper.exportInverterInstallation(queryWrapper);
		//inverterinstallationList.forEach(inverterinstallation -> {
		//	inverterinstallation.setTypeName(DictCache.getValue(DictEnum.YES_NO, InverterInstallation.getType()));
		//});
		return inverterinstallationList;
	}

}

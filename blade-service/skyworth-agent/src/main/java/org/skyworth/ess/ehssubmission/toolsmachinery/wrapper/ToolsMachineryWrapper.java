/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.toolsmachinery.wrapper;

import org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity;
import org.skyworth.ess.ehssubmission.toolsmachinery.vo.ToolsMachineryVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * ehs相关工具设备 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
public class ToolsMachineryWrapper extends BaseEntityWrapper<ToolsMachineryEntity, ToolsMachineryVO>  {

	public static ToolsMachineryWrapper build() {
		return new ToolsMachineryWrapper();
 	}

	@Override
	public ToolsMachineryVO entityVO(ToolsMachineryEntity toolsMachinery) {
		ToolsMachineryVO toolsMachineryVO = Objects.requireNonNull(BeanUtil.copy(toolsMachinery, ToolsMachineryVO.class));

		//User createUser = UserCache.getUser(toolsMachinery.getCreateUser());
		//User updateUser = UserCache.getUser(toolsMachinery.getUpdateUser());
		//toolsMachineryVO.setCreateUserName(createUser.getName());
		//toolsMachineryVO.setUpdateUserName(updateUser.getName());

		return toolsMachineryVO;
	}


}

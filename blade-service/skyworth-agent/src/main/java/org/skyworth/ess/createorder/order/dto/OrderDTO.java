/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.dto;


import lombok.Data;
import org.skyworth.ess.revieworder.orderworkflow.vo.ReviewOrderVO;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springblade.system.entity.User;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 订单表 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
public class OrderDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long orderId;

    @Valid
	private ReviewOrderVO orderEntity;

	//附件信息
	private SkyWorthFileEntity skyWorthFileEntity;


	//代理商部门id
	private List<Long> deptIdList;


	//代理商id
	private Long agentId;


	//选取代理商后传入的代理商部门id
	private Long deptId;


	//用户信息
	private List<User> userList;


	//公司名称
	private String companyName;

	//审批信息
	private OrderFlowDTO orderFlowDTO;

	//交付经理名称
	private String deliveryManagerName;

}

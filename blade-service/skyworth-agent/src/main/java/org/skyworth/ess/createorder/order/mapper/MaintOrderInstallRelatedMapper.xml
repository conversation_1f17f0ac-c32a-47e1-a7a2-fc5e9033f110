<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.createorder.order.mapper.MaintOrderInstallRelatedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderResultMap" type="org.skyworth.ess.createorder.order.entity.MaintOrderInstallRelatedEntity">
        <result column="id" property="id"/>
        <result column="order_install_id" property="orderInstallId"/>
        <result column="order_maint_id" property="orderMaintId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>

    <select id="queryMaintHistory" resultMap="maintResultMap">
        select bo.id,bo.order_number  ,bo.order_type ,bo.create_time,f.wf_current_status  from maint_order_install_related oirm
        inner join business_order bo on oirm .order_maint_id =bo.id
        inner join business_order_work_flow f on bo.id=f.order_id
        where oirm .order_install_id = #{installOrderId}
        order by bo.id
    </select>

    <select id="queryInstallOrderByMaintId" resultMap="maintResultMap">
        select oirm.order_install_id,bo.customer_name ,bo.project_type ,bo.install_reason,bo.install_budgets,bo.second_contact_name,bo.second_contact_phone
        ,c.warranty_date, c.temporary_coc_start_date, c.temporary_coc_img_biz_key
        from business_order bo inner join maint_order_install_related oirm on oirm.order_install_id  =bo.id and oirm.is_deleted =0
        left join coc_maintenance_info c on bo.id =c.order_id and c.is_deleted =0
        where bo.is_deleted =0 and oirm.order_maint_id = #{maintOrderId}
    </select>

    <resultMap id="maintResultMap" type="org.skyworth.ess.createorder.order.vo.MaintRelatedVO">
        <result column="id" property="id"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_type" property="orderType"/>
        <result column="create_time" property="orderCreateTime"/>
        <result column="customer_name" property="customerName"/>
        <result column="project_type" property="projectType"/>
        <result column="warranty_date" property="warrantyExpirationDate"/>
        <result column="temporary_coc_start_date" property="cocCreationDate"/>
        <result column="temporary_coc_img_biz_key" property="cocCreationDateImgBizKey"/>
        <result column="order_install_id" property="orderInstallId"/>
        <result column="wf_current_status" property="wfCurrentStatus"/>
        <result column="install_reason" property="installReason"/>
        <result column="install_budgets" property="installBudgets"/>
        <result column="second_contact_name" property="secondContactName"/>
        <result column="second_contact_phone" property="secondContactPhone"/>
    </resultMap>
</mapper>

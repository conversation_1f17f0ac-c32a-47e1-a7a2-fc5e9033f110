/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcInfoDTOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcShowDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.SubmitQcInfoRequestDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.service.IElectricalComponentsService;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.SubNodeSaveStatusVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 施工-电器元件信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("qc/submission")
@Api(value = "QC基本信息展示", tags = "施工-电器元件信息接口")
public class QcSubmissionController extends BladeController {

	private final IElectricalComponentsService electricalComponentsService;



	/**
	 * 获取QC基础信息
	 */
	@GetMapping("/getBasicQcInfo")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "QC基础信息", notes = "传入orderId")
	@ResponseStatus(HttpStatus.OK)
	public R<QcInfoVO> getBasicQcInfo(String orderId) {
		QcInfoVO qcInfoVO= electricalComponentsService.getBasicQcInfo(orderId);
		return R.data(qcInfoVO);
	}
	/**
	 * QC详情分段展示
	 */
	@GetMapping("/getQcInfoDetail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "QC详情分段展示", notes = "传入orderId")
	public R<QcInfoVOs> getQcInfoDetail(QcShowDTO qcShowDTO) {
			QcInfoVOs qcInfoVoS = electricalComponentsService.getQcInfoDetail(qcShowDTO);
			return R.data(qcInfoVoS);
	}

	/**
	 * QC数据保存与修改
	 */
	@PostMapping("/saveOrUpdateQcInfo")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "qc数据保存", notes = "传入qcInfoDTOs")
	public R<Boolean> saveOrUpdateQcInfo(@RequestBody QcInfoDTOs qcInfoDtos) {
		return R.status(electricalComponentsService.saveOrUpdateQcInfo(qcInfoDtos));
	}

	/**
	 * 获取QC小节点save标识
	 */
	@GetMapping("/listSubNodeSaveStatus")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "获取QC小节点保存状态", notes = "传入订单id")
	public R<List<SubNodeSaveStatusVO>> listSubNodeSaveStatus(@RequestParam Long orderId) {
		List<SubNodeSaveStatusVO> listSubNodeSaveStatusVO = electricalComponentsService.listSubNodeSaveStatus(orderId);
		return R.data(listSubNodeSaveStatusVO);
	}

	/**
	 * 获取QC小节点审核标识
	 */
	@GetMapping("/listSubNodeApproveStatus")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "获取QC小节点保存状态", notes = "传入订单id")
	public R<List<SubNodeSaveStatusVO>> listSubNodeApproveStatus(@RequestParam Long orderId) {
		List<SubNodeSaveStatusVO> listSubNodeSaveStatusVO = electricalComponentsService.listSubNodeApproveStatus(orderId);
		return R.data(listSubNodeSaveStatusVO);
	}

	/**
	 * QC提交流程流转
	 */
	@PostMapping("/submitQcInfo")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "QC提交流程流转", notes = "传入orderFlowDTO")
	public R<Boolean> submitQcInfo(@RequestBody SubmitQcInfoRequestDTO requestDTO) {
		Boolean flag = electricalComponentsService.submitQcInfo(requestDTO);
		return R.status(flag);
	}


}

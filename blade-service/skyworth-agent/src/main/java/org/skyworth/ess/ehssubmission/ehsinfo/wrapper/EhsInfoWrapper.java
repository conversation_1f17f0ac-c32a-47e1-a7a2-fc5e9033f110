/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.wrapper;

import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.vo.EhsInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * ehs信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
public class EhsInfoWrapper extends BaseEntityWrapper<EhsInfoEntity, EhsInfoVO>  {

	public static EhsInfoWrapper build() {
		return new EhsInfoWrapper();
 	}

	@Override
	public EhsInfoVO entityVO(EhsInfoEntity info) {
		EhsInfoVO infoVO = Objects.requireNonNull(BeanUtil.copy(info, EhsInfoVO.class));

		//User createUser = UserCache.getUser(info.getCreateUser());
		//User updateUser = UserCache.getUser(info.getUpdateUser());
		//infoVO.setCreateUserName(createUser.getName());
		//infoVO.setUpdateUserName(updateUser.getName());

		return infoVO;
	}


}

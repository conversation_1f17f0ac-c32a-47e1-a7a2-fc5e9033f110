/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.excel;


import lombok.Data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 订单节点子状态 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OrderNodeSubStatusExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 业务类型（保存类型：save；审批类型：approve）
	 */
	@ColumnWidth(20)
	@ExcelProperty("业务类型（保存类型：save；审批类型：approve）")
	private String businessType;
	/**
	 * 节点类型（业务字典agent_business_order_node_name）
	 */
	@ColumnWidth(20)
	@ExcelProperty("节点类型（业务字典agent_business_order_node_name）")
	private String nodeName;
	/**
	 * 子节点类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("子节点类型")
	private String subNodeName;
	/**
	 * 子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分
	 */
	@ColumnWidth(20)
	@ExcelProperty("子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分")
	private String subStatus;
	/**
	 * 子节点说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("子节点说明")
	private String subRemark;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

package org.skyworth.ess.qcsubmission.electricalcomponents.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * qc视图VO
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QcInfoVO {
	private static final long serialVersionUID = 1L;
	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderNumber;
	/**
	 * 安装站点国家编码
	 */
	@ApiModelProperty(value = "安装站点国家编码")
	private String siteCountryCode;
	/**
	 * 安装站点省份编码
	 */
	@ApiModelProperty(value = "安装站点省份编码")
	private String siteProvinceCode;
	/**
	 * 安装站点城市编码
	 */
	@ApiModelProperty(value = "安装站点城市编码")
	private String siteCityCode;
	/**
	 * 安装站点国家名称
	 */
	@ApiModelProperty(value = "安装站点国家名称")
	private String siteCountryName;

	/**
	 * 安装站点省份名称
	 */
	@ApiModelProperty(value = "安装站点省份名称")
	private String siteProvinceName;

	/**
	 * 安装站点城市名称
	 */
	@ApiModelProperty(value = "安装站点城市名称")
	private String siteCityName;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String siteAddress;
	/**
	 * 施工日期
	 */
	@ApiModelProperty(value = "施工日期")
	private Date constructionDate;
	/**
	 * 项目类型（业务字典agent_project_type）
	 */
	@ApiModelProperty(value = "项目类型（业务字典agent_project_type）")
	private String projectType;
	/**
	 * 项目类型中其他类型说明
	 */
	@ApiModelProperty(value = "项目类型中其他类型说明")
	private String projectTypeOtherRemark;
	/**
	 * 项目类型名称
	 */
	@ApiModelProperty(value = "项目类型名称")
	private String projectTypeName;
	/**
	 * 屋顶类型
	 */
	@ApiModelProperty(value = "屋顶类型")
	private String roofType;
	/**
	 * 屋顶类型名称
	 */
	@ApiModelProperty(value = "屋顶类型名称")
	private String roofTypeName;
	/**
	 * 其它屋顶类型
	 */
	@ApiModelProperty(value = "其它屋顶类型")
	private String roofTypeOther;
	/**
	 * 屋顶类型图片key
	 */
	@ApiModelProperty(value = "屋顶类型图片key")
	private Long roofTypeImgBizKey;
	/**
	 * 太阳能储能板安装位置
	 */
	@ApiModelProperty(value = "太阳能储能板安装位置")
	private String solarPanelsMountingPosition;
	/**
	 * 太阳能储能板安装位置名称
	 */
	@ApiModelProperty(value = "太阳能储能板安装位置名称")
	private String solarPanelsMountingPositionName;
	/**
	 * 太阳能储能板其它安装位置
	 */
	@ApiModelProperty(value = "太阳能储能板其它安装位置")
	private String solarPanelsMountingPositionOther;
	/**
	 * 太阳能储能板安装位置key
	 */
	@ApiModelProperty(value = "太阳能储能板安装位置key")
	private Long solarPanelsMountingPositionImgBizKey;
	/**
	 * 电力基础设施
	 */
	@ApiModelProperty(value = "电力基础设施")
	private String infrastructure;
	/**
	 * 电力基础设施名称
	 */
	@ApiModelProperty(value = "电力基础设施名称")
	private String infrastructureName;
	/**
	 * 电力基础设施连接类型
	 */
	@ApiModelProperty(value = "电力基础设施连接类型")
	private String infrastructureConnectionType;
	/**
	 * 电力基础设施连接类型名称
	 */
	@ApiModelProperty(value = "电力基础设施连接类型名称")
	private String infrastructureConnectionTypeName;
	/**
	 * 其它电力基础设施连接类型
	 */
	@ApiModelProperty(value = "其它电力基础设施连接类型")
	private String infrastructureConnectionTypeOther;
	/**
	 * 电力基础设施图片key
	 */
	@ApiModelProperty(value = "电力基础设施图片key")
	private Long infrastructureImgBizKey;
	/**
	 * 板类型
	 */
	@ApiModelProperty(value = "板类型")
	private String panelType;
	/**
	 * 板类型名称
	 */
	@ApiModelProperty(value = "板类型名称")
	private String panelTypeName;
	/**
	 * 其它板类型
	 */
	@ApiModelProperty(value = "其它板类型")
	private String panelTypeOther;
	/**
	 * 板类型图片KEY
	 */
	@ApiModelProperty(value = "板类型图片key")
	private Long panelTypeImgBizKey;
	/**
	 * 板方向
	 */
	@ApiModelProperty(value = "板方向")
	private String panelOrientation;
	/**
	 * 板方向名称
	 */
	@ApiModelProperty(value = "板方向名称")
	private String panelOrientationName;
	/**
	 * 其它板方向
	 */
	@ApiModelProperty(value = "其它板方向")
	private String panelOrientationOther;
	/**
	 * 板方向图片key
	 */
	@ApiModelProperty(value = "板方向图片key")
	private Long panelOrientationImgBizKey;
	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private String longitude;

	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String latitude;
	/**
	 * 图片及图片说明
	 */
	@ApiModelProperty(value = "图片及图片说明")
	private AttachmentDescVO attachmentDescVO;

	/**
	 * 距离限制
	 */
	@ApiModelProperty(value = "距离限制")
	private String distanceRestriction;

}

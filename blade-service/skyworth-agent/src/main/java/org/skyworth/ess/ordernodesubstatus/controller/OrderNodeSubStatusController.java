/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.excel.OrderNodeSubStatusExcel;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.ordernodesubstatus.vo.OrderNodeSubStatusVO;
import org.skyworth.ess.ordernodesubstatus.wrapper.OrderNodeSubStatusWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单节点子状态 控制器
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("orderNodeSubStatus")
@Api(value = "订单节点子状态", tags = "订单节点子状态接口")
public class OrderNodeSubStatusController extends BladeController {

	private final IOrderNodeSubStatusService orderNodeSubStatusService;

	/**
	 * 订单节点子状态 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入orderNodeSubStatus")
	public R<OrderNodeSubStatusVO> detail(OrderNodeSubStatusEntity orderNodeSubStatus) {
		OrderNodeSubStatusEntity detail = orderNodeSubStatusService.getOne(Condition.getQueryWrapper(orderNodeSubStatus));
		return R.data(OrderNodeSubStatusWrapper.build().entityVO(detail));
	}
	/**
	 * 订单节点子状态 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入orderNodeSubStatus")
	public R<IPage<OrderNodeSubStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> orderNodeSubStatus, Query query) {
		IPage<OrderNodeSubStatusEntity> pages = orderNodeSubStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(orderNodeSubStatus, OrderNodeSubStatusEntity.class));
		return R.data(OrderNodeSubStatusWrapper.build().pageVO(pages));
	}

	/**
	 * 订单节点子状态 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入orderNodeSubStatus")
	public R<IPage<OrderNodeSubStatusVO>> page(OrderNodeSubStatusVO orderNodeSubStatus, Query query) {
		IPage<OrderNodeSubStatusVO> pages = orderNodeSubStatusService.selectOrderNodeSubStatusPage(Condition.getPage(query), orderNodeSubStatus);
		return R.data(pages);
	}

	/**
	 * 订单节点子状态 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入orderNodeSubStatus")
	public R save(@Valid @RequestBody OrderNodeSubStatusEntity orderNodeSubStatus) {
		return R.status(orderNodeSubStatusService.save(orderNodeSubStatus));
	}

	/**
	 * 订单节点子状态 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入orderNodeSubStatus")
	public R update(@Valid @RequestBody OrderNodeSubStatusEntity orderNodeSubStatus) {
		return R.status(orderNodeSubStatusService.updateById(orderNodeSubStatus));
	}

	/**
	 * 订单节点子状态 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入orderNodeSubStatus")
	public R submit(@Valid @RequestBody OrderNodeSubStatusEntity orderNodeSubStatus) {
		return R.status(orderNodeSubStatusService.saveOrUpdate(orderNodeSubStatus));
	}

	/**
	 * 订单节点子状态 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(orderNodeSubStatusService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-orderNodeSubStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入orderNodeSubStatus")
	public void exportOrderNodeSubStatus(@ApiIgnore @RequestParam Map<String, Object> orderNodeSubStatus, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<OrderNodeSubStatusEntity> queryWrapper = Condition.getQueryWrapper(orderNodeSubStatus, OrderNodeSubStatusEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(OrderNodeSubStatus::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(OrderNodeSubStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<OrderNodeSubStatusExcel> list = orderNodeSubStatusService.exportOrderNodeSubStatus(queryWrapper);
		ExcelUtil.export(response, "订单节点子状态数据" + DateUtil.time(), "订单节点子状态数据表", list, OrderNodeSubStatusExcel.class);
	}


	/**
	 * 获取节点保存标志列表
	 *
	 * @param orderId 订单ID
	 * @param nodeName 节点名称
	 * @return 保存标志列表
	 */
	@GetMapping("/queryOrderNodeSubStatusList")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取节点保存标志列表", notes = "传入orderId,nodeName")
	public R<Object> queryOrderNodeSubStatusList(@RequestParam Long orderId, @RequestParam String nodeName) {
		return R.data(orderNodeSubStatusService.queryOrderNodeSubStatusList(orderId,nodeName));
	}

}

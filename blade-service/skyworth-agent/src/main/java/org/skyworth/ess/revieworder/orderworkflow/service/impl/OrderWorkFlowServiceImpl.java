/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.orderworkflow.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.excel.OrderWorkFlowExcel;
import org.skyworth.ess.revieworder.orderworkflow.mapper.OrderWorkFlowMapper;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单流程关系表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Service
public class OrderWorkFlowServiceImpl extends BaseServiceImpl<OrderWorkFlowMapper, OrderWorkFlowEntity> implements IOrderWorkFlowService {

	@Override
	public IPage<OrderWorkFlowVO> selectOrderWorkFlowPage(IPage<OrderWorkFlowVO> page, OrderWorkFlowVO orderWorkFlow) {
		return page.setRecords(baseMapper.selectOrderWorkFlowPage(page, orderWorkFlow));
	}


	@Override
	public List<OrderWorkFlowExcel> exportOrderWorkFlow(Wrapper<OrderWorkFlowEntity> queryWrapper) {
		List<OrderWorkFlowExcel> orderWorkFlowList = baseMapper.exportOrderWorkFlow(queryWrapper);
		//orderWorkFlowList.forEach(orderWorkFlow -> {
		//	orderWorkFlow.setTypeName(DictCache.getValue(DictEnum.YES_NO, OrderWorkFlow.getType()));
		//});
		return orderWorkFlowList;
	}

	@Override
	public OrderWorkFlowVO getWorkFlowDataByOrderId(OrderWorkFlowDTO orderWorkFlow) {
		OrderWorkFlowVO orderWorkFlowVO = new OrderWorkFlowVO();
		LambdaQueryWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderWorkFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderWorkFlow.getOrderId()).eq(OrderWorkFlowEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		OrderWorkFlowEntity orderWorkFlowEntity = this.getOne(orderWorkFlowEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderWorkFlowEntity)) {
			return orderWorkFlowVO;
		}
		BeanUtil.copyProperties(orderWorkFlowEntity, orderWorkFlowVO);
		return orderWorkFlowVO;
	}

	@Override
	public int updateWarehouseApproveCreateTime(OrderWorkFlowEntity params) {
		return baseMapper.updateWarehouseApproveCreateTime(params);
	}

	@Override
	public OrderWorkFlowVO queryWorkFlowWithDesign(Long orderId) {
		return baseMapper.queryWorkFlowWithDesign(orderId);
	}
}

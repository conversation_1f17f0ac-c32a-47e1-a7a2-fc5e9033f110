/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.orderrelateduser.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.vo.OrderRelatedUserVO;
import java.util.Objects;

/**
 * 订单关系人 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public class OrderRelatedUserWrapper extends BaseEntityWrapper<OrderRelatedUserEntity, OrderRelatedUserVO>  {

	public static OrderRelatedUserWrapper build() {
		return new OrderRelatedUserWrapper();
 	}

	@Override
	public OrderRelatedUserVO entityVO(OrderRelatedUserEntity orderRelatedUser) {
		OrderRelatedUserVO orderRelatedUserVO = Objects.requireNonNull(BeanUtil.copy(orderRelatedUser, OrderRelatedUserVO.class));

		//User createUser = UserCache.getUser(orderRelatedUser.getCreateUser());
		//User updateUser = UserCache.getUser(orderRelatedUser.getUpdateUser());
		//orderRelatedUserVO.setCreateUserName(createUser.getName());
		//orderRelatedUserVO.setUpdateUserName(updateUser.getName());

		return orderRelatedUserVO;
	}


}

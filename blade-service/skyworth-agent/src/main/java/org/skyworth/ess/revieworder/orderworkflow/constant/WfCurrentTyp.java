package org.skyworth.ess.revieworder.orderworkflow.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * WfCurrentTypEnum
 * 当前处理节点类型(1:人；2：角色)
 * org.springblade.flow.business.emun
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Getter
@AllArgsConstructor
public enum WfCurrentTyp {

	/**
	 * 当前处理节点类型(1:人；2：角色)
	 */
	NAME(1, "user"),

	/**
	 * 当前处理节点类型(1:人；2：角色)
	 */
	ROLE(2, "role");

	/**
	 * 类型
	 */
	private final int type;
	/**
	 * 描述
	 */
	private final String des;


	public static WfCurrentTyp of(String wfCurrentTypEnum) {
		if (wfCurrentTypEnum == null) {
			return null;
		}
		WfCurrentTyp[] values = WfCurrentTyp.values();
		for (WfCurrentTyp currentTyp : values) {
			if (Objects.equals(currentTyp.des, wfCurrentTypEnum)) {
				return currentTyp;
			}
		}
		return null;
	}

	}

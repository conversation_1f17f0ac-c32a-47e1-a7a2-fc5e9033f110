/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.solarpanels.service.impl;

import org.skyworth.ess.qcsubmission.solarpanels.entity.SolarPanelsEntity;
import org.skyworth.ess.qcsubmission.solarpanels.vo.SolarPanelsVO;
import org.skyworth.ess.qcsubmission.solarpanels.excel.SolarPanelsExcel;
import org.skyworth.ess.qcsubmission.solarpanels.mapper.SolarPanelsMapper;
import org.skyworth.ess.qcsubmission.solarpanels.service.ISolarPanelsService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 施工-光伏板信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class SolarPanelsServiceImpl extends BaseServiceImpl<SolarPanelsMapper, SolarPanelsEntity> implements ISolarPanelsService {

	@Override
	public IPage<SolarPanelsVO> selectSolarPanelsPage(IPage<SolarPanelsVO> page, SolarPanelsVO solarpanels) {
		return page.setRecords(baseMapper.selectSolarPanelsPage(page, solarpanels));
	}


	@Override
	public List<SolarPanelsExcel> exportSolarPanels(Wrapper<SolarPanelsEntity> queryWrapper) {
		List<SolarPanelsExcel> solarpanelsList = baseMapper.exportSolarPanels(queryWrapper);
		//solarpanelsList.forEach(solarpanels -> {
		//	solarpanels.setTypeName(DictCache.getValue(DictEnum.YES_NO, SolarPanels.getType()));
		//});
		return solarpanelsList;
	}

}

package org.skyworth.ess.createorder.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.constant.OrderTypeEnum;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MaintenanceOrderController
 * @Description 运维工单控制器
 * @Date 2024/4/16 11:19
 */
@RestController
@AllArgsConstructor
@RequestMapping("maintenanceOrder")
@Api(value = "01-运维工单列表", tags = "运维管理流程相关接口")
public class MaintenanceOrderController extends BladeController {

    private final IOrderService orderService;

    /**
     * 订单表 自定义分页
     */
    @PostMapping("/page/{size}/{current}")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "01-运维工单列表-分页查询", notes = "传入order")
    @PreAuth("hasPermission('agent:blade-order:maintenanceOrder:page')")
    public R<IPage<OrderVO>> page(@RequestBody OrderVO order, Query query) {
        OrderEntity orderEntity = order.getOrderEntity() == null ? new OrderEntity() : order.getOrderEntity();
        orderEntity.setOrderType(OrderTypeEnum.EPC_MAINTENANCE.getOrderType());
        order.setOrderEntity(orderEntity);
        IPage<OrderVO> pages = orderService.selectOrderPage(Condition.getPage(query), order);
        return R.data(pages);
    }


}

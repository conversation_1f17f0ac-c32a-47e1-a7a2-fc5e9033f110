/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.materialcollection.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.design.deviceItem.dto.DeviceItemDTO;
import org.skyworth.ess.materialcollection.service.MaterialCollectionService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 物料签收 控制器
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-material/materialCollection")
@Api(value = "物料签收", tags = "物料签收接口")
public class MaterialCollectionController extends BladeController {

	private final MaterialCollectionService materialCollectionService;


	@PostMapping("/saveMaterialSignature")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存物料签名", notes = "传入deviceItemDTO")
	public R<?> saveMaterialSignature(@RequestBody @Valid DeviceItemDTO deviceItemDTO) {
		return materialCollectionService.saveMaterialSignature(deviceItemDTO);
	}


	@PostMapping("/examineMaterialCollection")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "审核物料签收", notes = "传入deviceItemDTO")
	public R<?> examineMaterialCollection(@RequestBody @Valid DeviceItemDTO deviceItemDTO) {
		return materialCollectionService.examineMaterialCollection(deviceItemDTO);
	}


	@PostMapping("/selectMaterialCollection")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "查询物料签收信息", notes = "传入deviceItemDTO")
	public R<?> selectMaterialCollection(@RequestBody @Valid DeviceItemDTO deviceItemDTO) {
		return materialCollectionService.selectMaterialCollection(deviceItemDTO);
	}


	@PostMapping("/selectMaterialSignInfo")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "查询签名信息", notes = "传入deviceItemDTO")
	public R<?> selectMaterialSignInfo(@RequestBody @Valid DeviceItemDTO deviceItemDTO) {
		return materialCollectionService.selectMaterialSignInfo(deviceItemDTO);
	}


}

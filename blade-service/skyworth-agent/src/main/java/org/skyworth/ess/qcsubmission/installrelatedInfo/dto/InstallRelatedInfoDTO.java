/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.installrelatedInfo.dto;

import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 安装相关信息 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstallRelatedInfoDTO extends SkyWorthFileEntity {
	private static final long serialVersionUID = 1L;

	@NotNull(message = "{agent.installationDate.installRelatedInfoEntity.notNull}")
	@Valid
	private InstallRelatedInfoEntity installRelatedInfoEntity;

	//附件信息
	private SkyWorthFileEntity skyWorthFileEntity;

	//审批信息
	private OrderFlowDTO orderFlowDTO;

}

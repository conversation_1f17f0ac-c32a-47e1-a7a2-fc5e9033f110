package org.skyworth.ess.createorder.order.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.constant.OrderTypeEnum;
import org.skyworth.ess.createorder.order.service.IOrderCreateService;
import org.springblade.common.utils.tool.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderCreateFactory {
	@Autowired
	private IOrderCreateService orderInstallCreateServiceImpl;
	@Autowired
	private IOrderCreateService maintOrderCreateServiceImpl;
	private ConcurrentHashMap<String,IOrderCreateService> map ;

	@PostConstruct
	private void init() {
		this.map = new ConcurrentHashMap<>();
		map.put(OrderTypeEnum.GUARDIAN.getOrderType(), orderInstallCreateServiceImpl);
		map.put(OrderTypeEnum.EPC_MAINTENANCE.getOrderType(), maintOrderCreateServiceImpl);
	}
	public IOrderCreateService getOrderCreateService(String orderType) {
		if(StringUtils.isEmpty(orderType)) {
			return orderInstallCreateServiceImpl;
		}
		return map.get(orderType);
	}
}

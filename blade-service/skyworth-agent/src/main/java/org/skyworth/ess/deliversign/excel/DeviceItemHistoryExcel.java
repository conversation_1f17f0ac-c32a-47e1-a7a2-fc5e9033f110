/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.deliversign.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 货物操作记录 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceItemHistoryExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 操作节点（deliver_item设备发货，sign_item物料签收）
	 */
	@ColumnWidth(20)
	@ExcelProperty("操作节点（deliver_item设备发货，sign_item物料签收）")
	private String operateNode;
	/**
	 * 配料型号编码
	 */
	@ColumnWidth(20)
	@ExcelProperty("配料型号编码")
	private String itemCode;
	/**
	 * 操作类型：货物状态（un_deliver未发货，deliver已发货，sign签收，loss差损）
	 */
	@ColumnWidth(20)
	@ExcelProperty("操作类型：货物状态（un_deliver未发货，deliver已发货，sign签收，loss差损）")
	private String operateType;
	/**
	 * 说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("说明")
	private String remark;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.deliversign.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.deliversign.entity.DeviceItemHistoryEntity;
import org.skyworth.ess.deliversign.vo.DeviceItemHistoryVO;
import java.util.Objects;

/**
 * 货物操作记录 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
public class DeviceItemHistoryWrapper extends BaseEntityWrapper<DeviceItemHistoryEntity, DeviceItemHistoryVO>  {

	public static DeviceItemHistoryWrapper build() {
		return new DeviceItemHistoryWrapper();
 	}

	@Override
	public DeviceItemHistoryVO entityVO(DeviceItemHistoryEntity deviceItemHistory) {
		DeviceItemHistoryVO deviceItemHistoryVO = Objects.requireNonNull(BeanUtil.copy(deviceItemHistory, DeviceItemHistoryVO.class));

		//User createUser = UserCache.getUser(deviceItemHistory.getCreateUser());
		//User updateUser = UserCache.getUser(deviceItemHistory.getUpdateUser());
		//deviceItemHistoryVO.setCreateUserName(createUser.getName());
		//deviceItemHistoryVO.setUpdateUserName(updateUser.getName());

		return deviceItemHistoryVO;
	}


}

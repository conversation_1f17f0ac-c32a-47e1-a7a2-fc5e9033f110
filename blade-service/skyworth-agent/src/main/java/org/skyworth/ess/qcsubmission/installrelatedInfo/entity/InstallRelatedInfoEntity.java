/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.installrelatedInfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 安装相关信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("install_related_info")
@ApiModel(value = "InstallRelatedInfo对象", description = "安装相关信息")
@EqualsAndHashCode(callSuper = true)
public class InstallRelatedInfoEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;
	/**
	 * 反馈文档图片key
	 */
	@ApiModelProperty(value = "反馈文档图片key")
	private Long feedbackDocImgBizKey;
	/**
	 * 施工日期
	 */
	@ApiModelProperty(value = "施工日期")
	@NotNull(message = "{agent.installationDate.constructionDate.notNull}")
	private Date constructionDate;
	/**
	 * 安装技术经理签名图片key
	 */
	@ApiModelProperty(value = "安装技术经理签名图片key")
	private Long installTechnicianSignImgBizKey;
	/**
	 * 仓库管理员签名图片key
	 */
	@ApiModelProperty(value = "仓库管理员签名图片key")
	private Long warehouseManagerSignImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

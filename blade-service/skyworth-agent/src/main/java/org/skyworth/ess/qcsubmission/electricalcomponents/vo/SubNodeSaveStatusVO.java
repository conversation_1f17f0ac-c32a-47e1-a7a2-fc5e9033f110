package org.skyworth.ess.qcsubmission.electricalcomponents.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 订单表列表 视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/12/7 - 12 - 07
 */
@Data
public class SubNodeSaveStatusVO {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 业务类型（保存类型：save；审批类型：approve）
	 */
	@ApiModelProperty(value = "业务类型（保存类型：save；审批类型：approve）")
	private String businessType;
	/**
	 * 节点类型（业务字典agent_business_order_node_name）
	 */
	@ApiModelProperty(value = "节点类型（业务字典agent_business_order_node_name）")
	private String nodeName;
	/**
	 * 子节点类型
	 */
	@ApiModelProperty(value = "子节点类型")
	private String subNodeName;
	/**
	 * 子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分
	 */
	@ApiModelProperty(value = "子节点状态（business为approve时为：pass/reject；为save时finish/unfinish）；finish：全部填完；unfinish：只填部分")
	private String subStatus;

}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.mountingandracking.service.impl;

import org.skyworth.ess.qcsubmission.mountingandracking.entity.MountingAndRackingEntity;
import org.skyworth.ess.qcsubmission.mountingandracking.vo.MountingAndRackingVO;
import org.skyworth.ess.qcsubmission.mountingandracking.excel.MountingAndRackingExcel;
import org.skyworth.ess.qcsubmission.mountingandracking.mapper.MountingAndRackingMapper;
import org.skyworth.ess.qcsubmission.mountingandracking.service.IMountingAndRackingService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 施工-支架信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class MountingAndRackingServiceImpl extends BaseServiceImpl<MountingAndRackingMapper, MountingAndRackingEntity> implements IMountingAndRackingService {

	@Override
	public IPage<MountingAndRackingVO> selectMountingAndRackingPage(IPage<MountingAndRackingVO> page, MountingAndRackingVO mountingandracking) {
		return page.setRecords(baseMapper.selectMountingAndRackingPage(page, mountingandracking));
	}


	@Override
	public List<MountingAndRackingExcel> exportMountingAndRacking(Wrapper<MountingAndRackingEntity> queryWrapper) {
		List<MountingAndRackingExcel> mountingandrackingList = baseMapper.exportMountingAndRacking(queryWrapper);
		//mountingandrackingList.forEach(mountingandracking -> {
		//	mountingandracking.setTypeName(DictCache.getValue(DictEnum.YES_NO, MountingAndRacking.getType()));
		//});
		return mountingandrackingList;
	}

}

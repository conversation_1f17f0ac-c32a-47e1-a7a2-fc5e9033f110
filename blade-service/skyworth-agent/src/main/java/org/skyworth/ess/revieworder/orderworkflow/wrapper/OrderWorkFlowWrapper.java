/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.orderworkflow.wrapper;


import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 订单流程关系表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class OrderWorkFlowWrapper extends BaseEntityWrapper<OrderWorkFlowEntity, OrderWorkFlowVO> {

	public static OrderWorkFlowWrapper build() {
		return new OrderWorkFlowWrapper();
 	}

	@Override
	public OrderWorkFlowVO entityVO(OrderWorkFlowEntity orderWorkFlow) {
		OrderWorkFlowVO orderWorkFlowVO = Objects.requireNonNull(BeanUtil.copy(orderWorkFlow, OrderWorkFlowVO.class));

		//User createUser = UserCache.getUser(orderWorkFlow.getCreateUser());
		//User updateUser = UserCache.getUser(orderWorkFlow.getUpdateUser());
		//orderWorkFlowVO.setCreateUserName(createUser.getName());
		//orderWorkFlowVO.setUpdateUserName(updateUser.getName());

		return orderWorkFlowVO;
	}


}

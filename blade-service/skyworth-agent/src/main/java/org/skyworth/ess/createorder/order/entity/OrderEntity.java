/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 订单表 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("business_order")
@ApiModel(value = "Order对象", description = "订单表")
@EqualsAndHashCode(callSuper = true)
public class OrderEntity extends TenantEntity {

	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	private String orderNumber;
	/**
	 * 经销商id
	 */
	@ApiModelProperty(value = "经销商id")
	@NotNull(message = "{agent.createOrder.distributorId.notNull}")
	private Long distributorId;
	/**
	 * 推广经理
	 */
	@ApiModelProperty(value = "推广经理")
	@NotNull(message = "{agent.createOrder.rolloutManagerId.notNull}")
	private Long rolloutManagerId;
	/**
	 * 客户名称
	 */
	@ApiModelProperty(value = "客户名称")
	@NotBlank(message = "{agent.createOrder.customerName.notNull}")
	private String customerName;
	/**
	 * 客户电话
	 */
	@ApiModelProperty(value = "客户电话")
	@NotBlank(message = "{agent.createOrder.customerPhone.notNull}")
	private String customerPhone;
	/**
	 * 客户邮箱
	 */
//	@ApiModelProperty(value = "客户邮箱")
//	@NotBlank(message = "{agent.createOrder.customerEmail.notNull}")
//	private String customerEmail;
	/**
	 * 客户公司名称
	 */
//	@ApiModelProperty(value = "客户公司名称")
//	private String customerCompanyName;
	/**
	 * 安装站点国家编码
	 */
	@ApiModelProperty(value = "安装站点国家编码")
	private String siteCountryCode;
	/**
	 * 安装站点省份编码
	 */
	@ApiModelProperty(value = "安装站点省份编码")
	private String siteProvinceCode;
	/**
	 * 安装站点城市编码
	 */
	@ApiModelProperty(value = "安装站点城市编码")
	private String siteCityCode;
	/**
	 * 安装站点区县编码
	 */
	private String siteCountyCode;
	/**
	 * 地址
	 */
	@ApiModelProperty(value = "地址")
	private String siteAddress;
	/**
	 * 经度
	 */
	@ApiModelProperty(value = "经度")
	private String longitude;
	/**
	 * 纬度
	 */
	@ApiModelProperty(value = "纬度")
	private String latitude;
	/**
	 * 安装原因
	 */
//	@ApiModelProperty(value = "安装原因")
//	private String installReason;
	/**
	 * 安装预算
	 */
	@ApiModelProperty(value = "安装预算")
	private String installBudgets;
	/**
	 * 预计安装开始日期
	 */
//	@ApiModelProperty(value = "预计安装开始日期")
//	private Date tentativeInstallStartDate;
	/**
	 * 预计安装结束日期
	 */
//	@ApiModelProperty(value = "预计安装结束日期")
//	private Date tentativeInstallEndDate;
	/**
	 * 踏勘时间
	 */
	@ApiModelProperty(value = "踏勘时间")
	private Date surveyDate;
	/**
	 * 附加文档key
	 */
//	@ApiModelProperty(value = "附加文档key")
//	private Long additionalDocBizKey;
	/**
	 * 第二联系人名称
	 */
//	@ApiModelProperty(value = "第二联系人名称")
//	private String secondContactName;
	/**
	 * 第二联系人电话
	 */
//	@ApiModelProperty(value = "第二联系人电话")
//	private String secondContactPhone;
	/**
	 * 项目类型（业务字典agent_project_type）
	 */
	@ApiModelProperty(value = "项目类型（业务字典agent_project_type）")
	@NotBlank(message = "{agent.createOrder.projectType.notNull}")
	private String projectType;
	/**
	 * 项目类型中其他类型说明
	 */
	@ApiModelProperty(value = "项目类型中其他类型说明")
	private String projectTypeOtherRemark;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	/**
	 * 踏勘时间确认
	 */
	@ApiModelProperty(value = "踏勘时间确认")
	private Date surveyDateConfirm;


	/**
	 * 踏勘日期更改原因
	 */
//	@ApiModelProperty(value = "踏勘日期更改原因")
//	private String surveyDateConfirmRemark;


	/**
	 * 订单所属部门
	 */
	@ApiModelProperty(value = "订单所属部门")
	private Long belongDeptId;


	/**
	 * 代理商公司名称
	 */
	@TableField(exist = false)
	private String distributorName;
	/**
	 * 推广经理姓名
	 */
	@TableField(exist = false)
	private String rolloutManagerName;

	/**
	 * 推广经理电话
	 */
	@TableField(exist = false)
	private String rolloutManagerPhone;
	/**
	 * 安装站点国家名称
	 */
	@TableField(exist = false)
	private String siteCountryName;

	/**
	 * 安装站点省份名称
	 */
	@TableField(exist = false)
	private String siteProvinceName;

	/**
	 * 安装站点城市名称
	 */
	@TableField(exist = false)
	private String siteCityName;
	@TableField(exist = false)
	private String siteCountyName;
	/**
	 * 项目类型名称
	 */
	@TableField(exist = false)
	private String projectTypeName;

	/**
	 * 建站地址，拼接全地址
	 */
	@TableField(exist = false)
	private String siteDetailAddress;

	/**
	 * 距离限制
	 */
//	@ApiModelProperty(value = "距离限制")
//	private String distanceRestriction;
	// 维保字段 begin
	/**
	 * 订单类型：guardian 智墅管家
	 */
	private String orderType ;
	/**
	 * 维保问题描述
	 */
//	private String maintProblemDescriptionRemark;
	/**
	 * 维保问题描述图片key
	 */
//	private Long maintProblemDescriptionImgBizKey;
	/**
	 * 维保解决办法描述
	 */
//	private String maintSolutionRemark;
	/**
	 * 维保解决办法图片key
	 */
//	private Long maintSolutionImgBizKey;
	/**
	 * 维保呼叫次数
	 */
//	private Integer maintCallInTimes;
	/**
	 * 维保费用类型：业务字典 agent_maint_fee_type
	 */
//	private String maintFeeType;
//	private String maintFeeTypeRemark;
	/**
	 * 费用类型名称
	 */
	@TableField(exist = false)
	private String maintFeeTypeName;

	/**
	 * 维保选择的安装订单id
	 */
	@TableField(exist = false)
	private Long orderInstallId;
//	@ApiModelProperty(value = "维保踏勘文档key")
//	private Long maintSurveyReferenceDocBizKey;
	// 维保字段 end

}

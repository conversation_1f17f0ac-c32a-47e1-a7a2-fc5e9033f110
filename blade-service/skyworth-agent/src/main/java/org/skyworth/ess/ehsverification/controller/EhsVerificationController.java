/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehsverification.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ehssubmission.ehsinfo.dto.EhsInfoDTO;
import org.skyworth.ess.ehsverification.dto.EhsVerificationDTO;
import org.skyworth.ess.ehsverification.service.IEhsVerificationService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * ehs信息 控制器
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-ehs/verification")
@Api(value = "ehs审核信息", tags = "ehs审核信息接口")
public class EhsVerificationController extends BladeController {

	private final IEhsVerificationService iEhsVerificationService;


	/**
	 * ehs小节点审核状态提交
	 */
	@PostMapping("/ehsExamineNodeStatusCommit")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "ehs所有节点审核信息提交", notes = "传入infoDTO")
	public R<?> ehsExamineNodeStatusCommit(@RequestBody @Valid EhsVerificationDTO ehsVerificationDTO) {
		return iEhsVerificationService.ehsExamineNodeStatusCommit(ehsVerificationDTO);
	}



	/**
	 * 提交ehs最终节点审核
	 */
	@PostMapping("/submitEhsFinalExamine")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "提交ehs最终节点审核", notes = "传入infoDTO")
	public R<?> submitEhsFinalExamine(@RequestBody EhsInfoDTO infoDTO) {
		return iEhsVerificationService.submitEhsFinalExamine(infoDTO);
	}

}

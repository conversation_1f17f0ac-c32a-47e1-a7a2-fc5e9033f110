/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.solarpanels.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 施工-光伏板信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("qc_solar_panels")
@ApiModel(value = "SolarPanels对象", description = "施工-光伏板信息")
@EqualsAndHashCode(callSuper = true)
public class SolarPanelsEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 光伏板图片业务主键
	 */
	@ApiModelProperty(value = "光伏板图片业务主键")
	private Long panelsImgBizKey;
	/**
	 * 序号和型号图片业务主键
	 */
//	@ApiModelProperty(value = "序号和型号图片业务主键")
//	private Long serialNumVerificationImgBizKey;
	/**
	 * 船损检验图片业务主键
	 */
	@ApiModelProperty(value = "船损检验图片业务主键")
	private Long inspecationShipDamageImgBizKey;
	/**
	 * 光伏板的方向和倾斜角度图片业务主键
	 */
	@ApiModelProperty(value = "光伏板的方向和倾斜角度图片业务主键")
	private Long panelsVerifyPanelTiltImgBizKey;
	/**
	 * 锚杆检查图片业务主键
	 */
	@ApiModelProperty(value = "锚杆检查图片业务主键")
	private Long inspectRoofFlashingImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接头和开口图片业务主键")
	private Long allRoofSealedImgBizKey;
	/**
	 * 电缆和布线图片业务主键
	 */
	@ApiModelProperty(value = "电缆和布线图片业务主键")
	private Long panelsVerifyCableWiringImgBizKey;
	/**
	 * pvc管使用图片业务主键
	 */
	@ApiModelProperty(value = "pvc管使用图片业务主键")
	private Long confirmPvcPipeImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

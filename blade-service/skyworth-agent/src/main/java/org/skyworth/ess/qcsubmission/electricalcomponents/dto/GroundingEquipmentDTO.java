package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 接地设备视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GroundingEquipmentDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 接地设备图片业务主键
	 */
	@ApiModelProperty(value = "接地设备图片业务主键")
	private Long geGroundingCableImgBizKey;
	/**
	 * 避雷针图片业务主键
	 */
	@ApiModelProperty(value = "避雷针图片业务主键")
	private Long geLightningRodImgBizKey;
	/**
	 * 接地系统图片主键
	 */
	@ApiModelProperty(value = "接地系统图片主键")
	private Long geGroundSystemConnetImgBizKey;
	/**
	 * db接地值主键
	 */
	@ApiModelProperty(value = "db接地值主键")
	private Long geGroundingValueDbImgBizKey;
	/**
	 * 面板接地值主键
	 */
	@ApiModelProperty(value = "面板接地值主键")
	private Long geGroundingValuePanelsImgBizKey;

}

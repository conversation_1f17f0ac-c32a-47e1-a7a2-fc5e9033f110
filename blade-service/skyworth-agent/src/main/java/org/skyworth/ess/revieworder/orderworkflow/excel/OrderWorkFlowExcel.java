/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.orderworkflow.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;


/**
 * 订单流程关系表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OrderWorkFlowExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单表id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单表id")
	private Long orderId;
	/**
	 * 工作流实例id
	 */
	@ColumnWidth(20)
	@ExcelProperty("工作流实例id")
	private String wfInstanceId;
	/**
	 * 任务id
	 */
	@ColumnWidth(20)
	@ExcelProperty("任务id")
	private String taskId;
	/**
	 * 上一步审批状态（1:通过；2：不通过；3：待审批）
	 */
	@ColumnWidth(20)
	@ExcelProperty("上一步审批状态（1:通过；2：不通过；3：待审批）")
	private Integer auditStatus;
	/**
	 * 工作流当前状态/订单进度
	 */
	@ColumnWidth(20)
	@ExcelProperty("工作流当前状态/订单进度")
	private String wfCurrentStatus;
	/**
	 * 当前处理节点类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("当前处理节点类型")
	private String wfCurrentType;
	/**
	 * 当前处理节点角色或人
	 */
	@ColumnWidth(20)
	@ExcelProperty("当前处理节点角色或人")
	private String wfCurrentRole;
	/**
	 * 当前处理节点角色或人名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("当前处理节点角色或人名称")
	private String wfCurrentRoleName;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

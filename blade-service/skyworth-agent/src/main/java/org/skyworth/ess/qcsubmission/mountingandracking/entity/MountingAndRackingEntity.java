/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.mountingandracking.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 施工-支架信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("qc_mounting_and_racking")
@ApiModel(value = "MountingAndRacking对象", description = "施工-支架信息")
@EqualsAndHashCode(callSuper = true)
public class MountingAndRackingEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 硬件安装图片业务主键
	 */
	@ApiModelProperty(value = "硬件安装图片业务主键")
	private Long mountingHardwareImgBizKey;
	/**
	 * 支架图片业务主键
	 */
	@ApiModelProperty(value = "支架图片业务主键")
	private Long confirmClampsBracketsImgBizKey;
	/**
	 * 面板方向和角度图片业务主键
	 */
	@ApiModelProperty(value = "面板方向和角度图片业务主键")
	private Long mountingVerifyPanelTiltImgBizKey;
	/**
	 * 密封接口和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接口和开口图片业务主键")
	private Long allRoofJointsImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}

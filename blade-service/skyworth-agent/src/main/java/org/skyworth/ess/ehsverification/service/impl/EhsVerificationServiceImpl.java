/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehsverification.service.impl;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springblade.common.constant.OrderStatusConstants;
import org.skyworth.ess.ehssubmission.ehsinfo.dto.EhsInfoDTO;
import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.mapper.EhsInfoMapper;
import org.skyworth.ess.ehsverification.dto.EhsVerificationDTO;
import org.skyworth.ess.ehsverification.service.IEhsVerificationService;
import org.skyworth.ess.materialcollection.service.MaterialCollectionService;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.constant.HouseCategory;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.stereotype.Service;

/**
 * ehs信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Service
@AllArgsConstructor
public class EhsVerificationServiceImpl extends BaseServiceImpl<EhsInfoMapper, EhsInfoEntity> implements IEhsVerificationService {


	private final IOrderNodeSubStatusService orderNodeSubStatusService;

	private final IReviewOrderService iReviewOrderService;

	private final MaterialCollectionService materialCollectionService;

	/**
	 * @Description: ehs所有节点审核信息提交
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/8 11:19
	 **/
	@Override
	public R<?> ehsExamineNodeStatusCommit(EhsVerificationDTO ehsVerificationDTO) {
		orderNodeSubStatusService.saveOrderNodeSubStatus(ehsVerificationDTO.getOrderId(), "approve", HouseCategory.EHS_VERIFICATION.getDec(), ehsVerificationDTO.getEhsNodeName(), ehsVerificationDTO.getEhsInfoFlag());
		return R.status(true);
	}


	/**
	 * @Description: 提交ehs最终节点审核
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/12 15:03
	 **/
	@Override
	public R<?> submitEhsFinalExamine(EhsInfoDTO infoDTO) {
		if (ObjectUtils.isEmpty(infoDTO)) {
			throw new BusinessException("agent.ehsSubmission.selectEhsInfo.ehsInfoDTO.notNull");
		}
		if (ObjectUtils.isEmpty(infoDTO.getOrderFlowDTO())) {
			throw new BusinessException("agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull");
		}
		//查询施工队长
		OrderFlowDTO orderFlowDTO = materialCollectionService.setMaintenanceWorkFlowData(infoDTO.getOrderFlowDTO());
		String examineApproveType = infoDTO.getOrderFlowDTO().getVariables().get("examineApproveType").toString();
		//拒绝时清空小节点状态
		if (ObjectUtils.isNotEmpty(examineApproveType) && OrderStatusConstants.EXAMINE_APPROVE_NOT_PASS.equals(examineApproveType)) {
			orderNodeSubStatusService.deleteByOrderId(Long.parseLong(infoDTO.getOrderFlowDTO().getBusinessId()), HouseCategory.EHS_VERIFICATION.getDec(), "approve");
		}
		//审批流转
		R<?> examineApproveResult = iReviewOrderService.examineApprove(orderFlowDTO);
		if (!examineApproveResult.isSuccess()) {
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		return R.status(true);
	}


}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.aspect.FileSave;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IMaintOrderInstallRelatedService;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.MaintRelatedVO;
import org.skyworth.ess.device.client.IDeviceExitFactoryClient;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.skyworth.ess.qcsubmission.electricalcomponents.constant.QcSubNodeName;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.*;
import org.skyworth.ess.qcsubmission.electricalcomponents.entity.ElectricalComponentsEntity;
import org.skyworth.ess.qcsubmission.electricalcomponents.excel.ElectricalComponentsExcel;
import org.skyworth.ess.qcsubmission.electricalcomponents.mapper.ElectricalComponentsMapper;
import org.skyworth.ess.qcsubmission.electricalcomponents.service.IElectricalComponentsService;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.ElectricalComponentsVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.SubNodeSaveStatusVO;
import org.skyworth.ess.qcsubmission.equipmentdetails.entity.EquipmentDetailsEntity;
import org.skyworth.ess.qcsubmission.equipmentdetails.service.IEquipmentDetailsService;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.skyworth.ess.qcsubmission.inverterinstallation.entity.InverterInstallationEntity;
import org.skyworth.ess.qcsubmission.inverterinstallation.service.IInverterInstallationService;
import org.skyworth.ess.qcsubmission.mountingandracking.entity.MountingAndRackingEntity;
import org.skyworth.ess.qcsubmission.mountingandracking.service.IMountingAndRackingService;
import org.skyworth.ess.qcsubmission.qcotherInformation.entity.QcOtherInformationEntity;
import org.skyworth.ess.qcsubmission.qcotherInformation.service.IQcOtherInformationService;
import org.skyworth.ess.qcsubmission.solarpanels.entity.SolarPanelsEntity;
import org.skyworth.ess.qcsubmission.solarpanels.service.ISolarPanelsService;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.constant.OperationStatus;
import org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity;
import org.skyworth.ess.survey.houseelectrical.service.IHouseElectricalService;
import org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity;
import org.skyworth.ess.survey.housestructure.service.IHouseStructureService;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.ObjectUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 施工-电器元件信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
@AllArgsConstructor
@Slf4j
public class ElectricalComponentsServiceImpl extends BaseServiceImpl<ElectricalComponentsMapper, ElectricalComponentsEntity> implements IElectricalComponentsService {

	private final IMaintOrderInstallRelatedService orderInstallRelatedMaintService;

	private final IDictBizClient dictBizClient;

	private final IOrderService orderService;

	private final IHouseStructureService houseStructureService;

	private final IHouseElectricalService houseElectricalService;

	private final IInverterInstallationService inverterInstallationService;

	private final IQcOtherInformationService qcOtherInformationService;

	private final ISolarPanelsService solarPanelsService;

	private final IMountingAndRackingService mountingAndRackingService;

	private final IOrderNodeSubStatusService orderNodeSubStatusService;

	private final IReviewOrderService reviewOrderService;

	private final ISysClient sysClient;

	private final IAttachmentInfoService attachmentInfo;

	private final IAttachmentInfoClient attachmentInfoClient;

	private final IAdditionalInfoService additionalInfoService;

	private final IEquipmentDetailsService equipmentDetailsService;

	private final IDeviceExitFactoryClient deviceExitFactoryClient;

	private final IQuoteInfoService quoteInfoService;

	/**
	 * qcSubmission的节点key
	 */
	public static final String QC_SUBMISSION_KEY = "qcSubmission";

	/**
	 * QCVerification的节点key
	 */
	public static final String QC_VERIFICATION_KEY = "qcVerification";

	/**
	 * 保存的业务类型
	 */
	private static final String BUSINESS_TYPE_SAVE = "save";
	/**
	 * 审核的业务类型
	 */
	private static final String BUSINESS_TYPE_APPROVE = "approve";
	/**
	 * web端请求标识
	 */
	public static final String WEB = "web";

	/**
	 * app端请求标识
	 */
	public static final String APP = "app";


	@Override
	public IPage<ElectricalComponentsVO> selectElectricalComponentsPage(IPage<ElectricalComponentsVO> page, ElectricalComponentsVO electricalcomponents) {
		return page.setRecords(baseMapper.selectElectricalComponentsPage(page, electricalcomponents));
	}


	@Override
	public List<ElectricalComponentsExcel> exportElectricalComponents(Wrapper<ElectricalComponentsEntity> queryWrapper) {
		return baseMapper.exportElectricalComponents(queryWrapper);
	}

	@Override
	public QcInfoVO getMaintenanceBasicQcInfo(String orderId) {
		if (StringUtils.isBlank(orderId)) {
			throw new BusinessException("agent.qcSubmission.getBasicInfo.orderId.notEmpty");
		}
		QcInfoVO qcInfoVO = new QcInfoVO();
		try {
			//1.设置查询订单指定字段值条件
			OrderEntity orderEntity = getOrderEntity(orderId);
			//查询项目类型字典
			R<List<DictBiz>> deviceBatteryMatch = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_PROJECT_TYPE.getDictCode(), CommonUtil.getCurrentLanguage());
			if (ObjectUtils.isNotEmpty(deviceBatteryMatch) || CollectionUtils.isNotEmpty(deviceBatteryMatch.getData())) {
				Map<String, String> projectTypeDictMap = deviceBatteryMatch.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (v1, v2) -> v2));
				// 设置维保信息
				// 维保部分信息从原订单获取
				MaintRelatedVO maintRelatedVO = orderInstallRelatedMaintService.queryInstallOrderByMaintId(Long.valueOf(orderId));
				if (MapUtil.isNotEmpty(projectTypeDictMap) && ObjectUtil.isNotEmpty(maintRelatedVO) && ObjectUtils.isNotEmpty(projectTypeDictMap.get(maintRelatedVO.getProjectType()))) {
					orderEntity.setProjectTypeName(projectTypeDictMap.get(maintRelatedVO.getProjectType()));
				}
				orderEntity.setProjectType(maintRelatedVO.getProjectType());
			}
			//1.1将指定值设置到qcInfoVO
			BeanUtils.copyProperties(orderEntity, qcInfoVO);
			//1.2设置国家、省份、城市名称
			fillRegionInfo(qcInfoVO);
			//1.3设置项目类型名称
			fillProjectTypeName(qcInfoVO);

			//2.设置查询安装相关指定字段值条件
			QuoteInfoEntity quoteInfoEntity = getInstallRelatedInfoEntity(orderId);
			//2.1将指定值设置到qcInfoVO
			qcInfoVO.setConstructionDate(quoteInfoEntity.getConstructionDate());

			//3.设置查询电力基础设施指定字段值条件
			HouseElectricalEntity houseElectricalEntity = getMaintenanceHouseElectricalEntity(orderId);
			//3.1将指定值设置到qcInfoVO
			if (ObjectUtil.isNotEmpty(houseElectricalEntity)) {
				BeanUtils.copyProperties(houseElectricalEntity, qcInfoVO);
			}
			//3.2设置电力基础设施名称和电力基础设施连接类型名称
			fillHouseElectricalInfo(qcInfoVO);

			//4.设置查询房屋结构指定字段值条件
			HouseStructureEntity houseStructureEntity = getMaintenanceHouseStructureEntity(orderId);
			//4.1将指定值设置到qcInfoVO,排除projectType,只取orderEntity中的projectType属性
			if (ObjectUtil.isNotEmpty(houseStructureEntity)) {
				BeanUtils.copyProperties(houseStructureEntity, qcInfoVO, "projectType");
			}
			//4.2设置屋顶类型名称、安装结构名称、板类型名称、板朝向名称
			fillHouseStructureInfo(qcInfoVO);

			//5.获取图片和图片说明；图片属性名称与key值转换
			qcInfoVO.setAttachmentDescVO(attachmentInfo.getAttachmentInfo(qcInfoVO));
		} catch (Exception e) {
			log.error("Error occurred while fetching QCInfo data: {}", e.getMessage());
			throw new BusinessException("agent.qcSubmission.getBasicInfo.exception");
		}
		return qcInfoVO;
	}

	@Override
	public QcInfoVO getBasicQcInfo(String orderId) {
		if (StringUtils.isBlank(orderId)) {
			throw new BusinessException("agent.qcSubmission.getBasicInfo.orderId.notEmpty");
		}
		QcInfoVO qcInfoVO = new QcInfoVO();
		try {
			//1.设置查询订单指定字段值条件
			OrderEntity orderEntity = getOrderEntity(orderId);
			//1.1将指定值设置到qcInfoVO
			BeanUtils.copyProperties(orderEntity, qcInfoVO);
			//1.2设置国家、省份、城市名称
			fillRegionInfo(qcInfoVO);
			//1.3设置项目类型名称
			fillProjectTypeName(qcInfoVO);

			//2.设置查询安装相关指定字段值条件
			QuoteInfoEntity quoteInfoEntity = getInstallRelatedInfoEntity(orderId);
			//2.1将指定值设置到qcInfoVO
			qcInfoVO.setConstructionDate(quoteInfoEntity.getConstructionDate());

			//3.设置查询电力基础设施指定字段值条件
			HouseElectricalEntity houseElectricalEntity = getHouseElectricalEntity(orderId);
			//3.1将指定值设置到qcInfoVO
			BeanUtils.copyProperties(houseElectricalEntity, qcInfoVO);
			//3.2设置电力基础设施名称和电力基础设施连接类型名称
			fillHouseElectricalInfo(qcInfoVO);

			//4.设置查询房屋结构指定字段值条件
			HouseStructureEntity houseStructureEntity = getHouseStructureEntity(orderId);
			//4.1将指定值设置到qcInfoVO,排除projectType,只取orderEntity中的projectType属性
			BeanUtils.copyProperties(houseStructureEntity, qcInfoVO, "projectType");
			//4.2设置屋顶类型名称、安装结构名称、板类型名称、板朝向名称
			fillHouseStructureInfo(qcInfoVO);

			//5.获取图片和图片说明；图片属性名称与key值转换
			qcInfoVO.setAttachmentDescVO(attachmentInfo.getAttachmentInfo(qcInfoVO));
		} catch (Exception e) {
			log.error("Error occurred while fetching QCInfo data: {}", e.getMessage());
			throw new BusinessException("agent.qcSubmission.getBasicInfo.exception");
		}
		return qcInfoVO;
	}

	/**
	 * @param orderId 订单id
	 * @return 订单信息
	 * @Description: 查询订单信息
	 */
	private OrderEntity getOrderEntity(String orderId) {
		LambdaQueryWrapper<OrderEntity> orderWrapper = new LambdaQueryWrapper<>();
		orderWrapper.eq(OrderEntity::getId, orderId)
			.select(OrderEntity::getOrderNumber, OrderEntity::getProjectType, OrderEntity::getSiteCountryCode, OrderEntity::getSiteProvinceCode, OrderEntity::getSiteCityCode, OrderEntity::getSiteAddress,OrderEntity::getLongitude,
				OrderEntity::getLatitude);
		return Optional.ofNullable(orderService.getOne(orderWrapper))
			.orElseThrow(() -> {
				log.error("Order with ID " + orderId + " does not exist");
				return new BusinessException("agent.qcSubmission.getOrderEntity.orderId.notExist", orderId);
			});
	}

	/**
	 * @param orderId 订单id
	 * @return 安装相关信息
	 * @Description: 查询安装相关信息
	 */
	private QuoteInfoEntity getInstallRelatedInfoEntity(String orderId) {
		LambdaQueryWrapper<QuoteInfoEntity> relatedWrapper = new LambdaQueryWrapper<>();
		relatedWrapper.eq(QuoteInfoEntity::getOrderId, orderId)
			.select(QuoteInfoEntity::getConstructionDate);
		return Optional.ofNullable(quoteInfoService.getOne(relatedWrapper))
			.orElseThrow(() -> {
				log.error("RelatedInfo with ID " + orderId + " does not exist");
				return new BusinessException("agent.qcSubmission.getInstallRelatedInfoEntity.orderId.notExist", orderId);
			});
	}

	/**
	 * @param orderId 订单id
	 * @return 电力基础设施信息
	 * @Description: 查询电力基础设施信息
	 */
	private HouseElectricalEntity getHouseElectricalEntity(String orderId) {
		LambdaQueryWrapper<HouseElectricalEntity> houseElectricalWrapper = new LambdaQueryWrapper<>();
		houseElectricalWrapper.eq(HouseElectricalEntity::getOrderId, orderId)
			.select(HouseElectricalEntity::getInfrastructure, HouseElectricalEntity::getInfrastructureConnectionType, HouseElectricalEntity::getInfrastructureImgBizKey, HouseElectricalEntity::getInfrastructureConnectionTypeOther);
		return Optional.ofNullable(houseElectricalService.getOne(houseElectricalWrapper))
			.orElseThrow(() -> {
				log.error("HouseElectrical with ID " + orderId + " does not exist");
				return new BusinessException("agent.qcSubmission.getHouseElectricalEntity.orderId.notExist", orderId);
			});
	}

	/**
	 * @param orderId 订单id
	 * @return 电力基础设施信息
	 * @Description: 查询电力基础设施信息
	 */
	private HouseElectricalEntity getMaintenanceHouseElectricalEntity(String orderId) {
		LambdaQueryWrapper<HouseElectricalEntity> houseElectricalWrapper = new LambdaQueryWrapper<>();
		houseElectricalWrapper.eq(HouseElectricalEntity::getOrderId, orderId)
			.select(HouseElectricalEntity::getInfrastructure, HouseElectricalEntity::getInfrastructureConnectionType, HouseElectricalEntity::getInfrastructureImgBizKey, HouseElectricalEntity::getInfrastructureConnectionTypeOther);
		return houseElectricalService.getOne(houseElectricalWrapper);
	}

	/**
	 * @param orderId 订单id
	 * @return 房屋结构信息
	 * @Description: 查询房屋结构信息
	 */
	private HouseStructureEntity getMaintenanceHouseStructureEntity(String orderId) {
		LambdaQueryWrapper<HouseStructureEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
		houseStructureWrapper.eq(HouseStructureEntity::getOrderId, orderId)
			.select(HouseStructureEntity::getRoofType, HouseStructureEntity::getSolarPanelsMountingPosition, HouseStructureEntity::getPanelOrientation, HouseStructureEntity::getRoofTypeImgBizKey,  HouseStructureEntity::getSolarPanelsMountingPositionImgBizKey, HouseStructureEntity::getPanelOrientationImgBizKey, HouseStructureEntity::getSolarPanelsMountingPositionOther, HouseStructureEntity::getPanelOrientationOther, HouseStructureEntity::getRoofTypeOther);
		return houseStructureService.getOne(houseStructureWrapper);
	}

	/**
	 * @param orderId 订单id
	 * @return 房屋结构信息
	 * @Description: 查询房屋结构信息
	 */
	private HouseStructureEntity getHouseStructureEntity(String orderId) {
		LambdaQueryWrapper<HouseStructureEntity> houseStructureWrapper = new LambdaQueryWrapper<>();
		houseStructureWrapper.eq(HouseStructureEntity::getOrderId, orderId)
			.select(HouseStructureEntity::getRoofType, HouseStructureEntity::getSolarPanelsMountingPosition, HouseStructureEntity::getPanelOrientation, HouseStructureEntity::getRoofTypeImgBizKey,  HouseStructureEntity::getSolarPanelsMountingPositionImgBizKey, HouseStructureEntity::getPanelOrientationImgBizKey,  HouseStructureEntity::getSolarPanelsMountingPositionOther, HouseStructureEntity::getPanelOrientationOther, HouseStructureEntity::getRoofTypeOther);
		return Optional.ofNullable(houseStructureService.getOne(houseStructureWrapper))
			.orElseThrow(() -> {
				log.error("HouseStructure with ID " + orderId + " does not exist");
				return new BusinessException("agent.qcSubmission.getHouseStructureEntity.orderId.notExist", orderId);
			});
	}

	/**
	 * @param qcInfoVO 最终返回的VO
	 * @Description: 填充区域信息
	 */
	private void fillRegionInfo(QcInfoVO qcInfoVO) {
		//1.1.1 查询字典省市区字典
		List<String> regionIdList = Arrays.asList(qcInfoVO.getSiteCountryCode(), qcInfoVO.getSiteProvinceCode(), qcInfoVO.getSiteCityCode());
		R<List<Region>> regionListResult = sysClient.getRegionList(regionIdList);
		if (ObjectUtils.isNotEmpty(regionListResult) && CollectionUtils.isNotEmpty(regionListResult.getData())) {
			//转换地域字典信息
			updateOrderEntityWithRegionName(regionListResult, qcInfoVO);
		}
	}

	/**
	 * @param qcInfoVO 最终返回的VO
	 * @Description: 填充项目类型信息
	 */
	private void fillProjectTypeName(QcInfoVO qcInfoVO) {
		//1.1.2 查询字典项目类型字典
		Map<String, String> projectTypeDictMap = orderService.getDictBizListByName(DictBizCodeEnum.AGENT_PROJECT_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(projectTypeDictMap) && ObjectUtils.isNotEmpty(projectTypeDictMap.get(qcInfoVO.getProjectType()))) {
			qcInfoVO.setProjectTypeName(projectTypeDictMap.get(qcInfoVO.getProjectType()));
		}
	}

	/**
	 * @param qcInfoVO 最终返回的VO
	 * @Description: 填充电力基础设施信息
	 */
	private void fillHouseElectricalInfo(QcInfoVO qcInfoVO) {
		//3.1.1 查询字典电力基础设施字典,获取电力基础设施连接类型名称和电力基础设施名称
		Map<String, String> infrastructureDictMap = orderService.getDictBizListByName(DictBizCodeEnum.INFRASTRUCTURE.getDictCode());
		if (MapUtil.isNotEmpty(infrastructureDictMap) && ObjectUtils.isNotEmpty(infrastructureDictMap.get(qcInfoVO.getInfrastructure()))) {
			qcInfoVO.setInfrastructureName(infrastructureDictMap.get(qcInfoVO.getInfrastructure()));
		}
		Map<String, String> infrastructureConnectionTypeDictMap = orderService.getDictBizListByName(DictBizCodeEnum.INFRASTRUCTURE_CONNECTION_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(infrastructureConnectionTypeDictMap) && ObjectUtils.isNotEmpty(infrastructureConnectionTypeDictMap.get(qcInfoVO.getInfrastructureConnectionType()))) {
			qcInfoVO.setInfrastructureConnectionTypeName(infrastructureConnectionTypeDictMap.get(qcInfoVO.getInfrastructureConnectionType()));
		}
	}

	/**
	 * 填充房屋结构信息
	 *
	 * @param qcInfoVO 最终返回的VO
	 */
	private void fillHouseStructureInfo(QcInfoVO qcInfoVO) {
		//4.1.1 查询字典房屋结构字典
		//获取屋顶类型名称、安装结构名称、板类型名称、板朝向名称
		Map<String, String> roofTypeDictMap = orderService.getDictBizListByName(DictBizCodeEnum.ROOF_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(roofTypeDictMap) && ObjectUtils.isNotEmpty(roofTypeDictMap.get(qcInfoVO.getRoofType()))) {
			qcInfoVO.setRoofTypeName(roofTypeDictMap.get(qcInfoVO.getRoofType()));
		}
		Map<String, String> mountingStructureDictMap = orderService.getDictBizListByName(DictBizCodeEnum.MOUNTING_STRUCTURE.getDictCode());
		if (MapUtil.isNotEmpty(mountingStructureDictMap) && ObjectUtils.isNotEmpty(mountingStructureDictMap.get(qcInfoVO.getSolarPanelsMountingPosition()))) {
			qcInfoVO.setSolarPanelsMountingPositionName(mountingStructureDictMap.get(qcInfoVO.getSolarPanelsMountingPosition()));
		}
		Map<String, String> panelTypeDictMap = orderService.getDictBizListByName(DictBizCodeEnum.PANEL_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(panelTypeDictMap) && ObjectUtils.isNotEmpty(panelTypeDictMap.get(qcInfoVO.getPanelType()))) {
			qcInfoVO.setPanelTypeName(panelTypeDictMap.get(qcInfoVO.getPanelType()));
		}
		Map<String, String> panelOrientationDictMap = orderService.getDictBizListByName(DictBizCodeEnum.PANEL_ORIENTATION.getDictCode());
		if (MapUtil.isNotEmpty(panelOrientationDictMap) && ObjectUtils.isNotEmpty(panelOrientationDictMap.get(qcInfoVO.getPanelOrientation()))) {
			qcInfoVO.setPanelOrientationName(panelOrientationDictMap.get(qcInfoVO.getPanelOrientation()));
		}
	}


	/**
	 * 转换地域字典信息。
	 *
	 * @param regionListResult 地域字典信息
	 * @param qcInfoVO         订单信息
	 * <AUTHOR>
	 */
	private void updateOrderEntityWithRegionName(R<List<Region>> regionListResult, QcInfoVO qcInfoVO) {
		List<Region> regionList = regionListResult.getData();
		//转换国家字典
		Map<String, String> regionMap = regionList.stream().collect(Collectors.toMap(Region::getCode, Region::getName));
		if (ObjectUtils.isNotEmpty(regionMap.get(qcInfoVO.getSiteCountryCode()))) {
			qcInfoVO.setSiteCountryName(regionMap.get(qcInfoVO.getSiteCountryCode()));
		}
		//转换省字典
		if (ObjectUtils.isNotEmpty(regionMap.get(qcInfoVO.getSiteProvinceCode()))) {
			qcInfoVO.setSiteProvinceName(regionMap.get(qcInfoVO.getSiteProvinceCode()));
		}
		//转换城市字典
		if (ObjectUtils.isNotEmpty(regionMap.get(qcInfoVO.getSiteCityCode()))) {
			qcInfoVO.setSiteCityName(regionMap.get(qcInfoVO.getSiteCityCode()));
		}
	}

	@Override
	public QcInfoVOs getQcInfoDetail(QcShowDTO qcShowDTO) {
		if (ObjectUtils.isEmpty(qcShowDTO) && StringUtils.isBlank(qcShowDTO.getOrderId())) {
			throw new BusinessException("agent.qcSubmission.getQcInfoDetail.qcShowDTO.notEmpty");
		}
		String orderId = qcShowDTO.getOrderId();
		QcInfoVOs qcInfoVos = new QcInfoVOs();
		//1.查询智能能量变换器安装
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayInverterInstallation())) {
			loadAndSetInverterInstallation(orderId, qcInfoVos);
		}
		// 2.查询储能信息
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayBatteryInstallation())) {
			loadAndSetBatteryInstallation(orderId, qcInfoVos);
		}
		//2.查询太阳能储能板
		if (Boolean.TRUE.equals(qcShowDTO.getDisplaySolarPanels())) {
			loadAndSetSolarPanels(orderId, qcInfoVos);
		}
		//3.查询电气元件
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayElectricalComponents())) {
			loadAndSetElectricalComponents(orderId, qcInfoVos);
		}
		//4.查询接地设备
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayGroundingEquipment())) {
			loadAndSetGroundingEquipment(orderId, qcInfoVos);
		}
		//5.查询安装和支架
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayMountingAndRacking())) {
			loadAndSetMountingAndRacking(orderId, qcInfoVos);
		}
		//6.查询安全和合规性
		if (Boolean.TRUE.equals(qcShowDTO.getDisplaySafetyAndCompliance())) {
			loadAndSetSafetyAndCompliance(orderId, qcInfoVos);
		}
		//7.查询系统测试
		if (Boolean.TRUE.equals(qcShowDTO.getDisplaySystemTesting())) {
			loadAndSetSystemTesting(orderId, qcInfoVos);
		}
		//8.查询最终检查
		if (Boolean.TRUE.equals(qcShowDTO.getDisplayFinalInspection())) {
			loadAndSetFinalInspection(orderId, qcInfoVos);
		}

		return qcInfoVos;
	}

	/**
	 * @param qcInfoVos    返回的VO
	 * @param componentDTO 组件DTO
	 * @Description: 图片属性名称与key值转换
	 */
	private void loadAttachmentDescVO(QcInfoVOs qcInfoVos, Object componentDTO) {
		try {
			qcInfoVos.setAttachmentDescVO(attachmentInfo.getAttachmentInfo(componentDTO));
		} catch (Exception e) {
			log.error("Error fetching attachment or imgDescView info for {}: {}", componentDTO.getClass().getSimpleName(), e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadAttachmentDescVO.exception", componentDTO.getClass().getSimpleName());
		}
	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置智能能量变换器安装信息
	 */
	private void loadAndSetInverterInstallation(String orderId, QcInfoVOs qcInfoVos) {
		InverterInstallationDTO inverterInstallationDTO = loadInverterInstallationVO(orderId);
		List<EquipmentDetailsEntity> equipmentDetailsEntityList = queryEquipmentDetails(orderId, QcSubNodeName.INVERTER.getSubNodeName());
		inverterInstallationDTO.setEquipmentDetailsList(equipmentDetailsEntityList);
		qcInfoVos.setInverterInstallationDTO(inverterInstallationDTO);
		loadAttachmentDescVO(qcInfoVos, inverterInstallationDTO);
	}

	/**
	 * 查询设备明细
	 *
	 * @param orderId     订单id
	 * @param subNodeName 入参
	 * @return List<EquipmentDetailsEntity>
	 * <AUTHOR>
	 * @since 2024/6/23 15:47
	 **/
	private List<EquipmentDetailsEntity> queryEquipmentDetails(String orderId, String subNodeName) {
		List<EquipmentDetailsEntity> equipmentDetailsEntityList = equipmentDetailsService.list(Wrappers.<EquipmentDetailsEntity>lambdaQuery().eq(EquipmentDetailsEntity::getOrderId, orderId).eq(EquipmentDetailsEntity::getEquipmentType, subNodeName));
		if (CollectionUtils.isEmpty(equipmentDetailsEntityList)) {
			return equipmentDetailsEntityList;
		}
		List<Long> businessIds = new ArrayList<>();
		equipmentDetailsEntityList.forEach(e -> {
			businessIds.add(e.getEquipmentImgBizKey());
		});
		// 一次查出图片与图片备注
		Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
		Map<Long, String> attachmentInfoViewMap = additionalInfoService.selectAdditionalMapByBusinessIds(businessIds);
		equipmentDetailsEntityList.forEach(e -> {
			if (e.getEquipmentImgBizKey() != null) {
				e.setAttachmentInfoDesc(attachmentInfoMap.get(e.getEquipmentImgBizKey()));
				e.setAttachmentInfoDescView(attachmentInfoViewMap.get(e.getEquipmentImgBizKey()));
			}
		});
		return equipmentDetailsEntityList;
	}

	/**
	 * 查询储能信息
	 *
	 * @param orderId
	 * @param qcInfoVos 入参
	 * @return void
	 * <AUTHOR>
	 * @since 2024/6/13 19:13
	 **/
	private void loadAndSetBatteryInstallation(String orderId, QcInfoVOs qcInfoVos) {
		BatteryInstallationDTO batteryInstallationDTO = loadBatteryInstallationVO(orderId);
		List<EquipmentDetailsEntity> equipmentDetailsEntityList = queryEquipmentDetails(orderId, QcSubNodeName.BATTERY.getSubNodeName());
		batteryInstallationDTO.setEquipmentDetailsList(equipmentDetailsEntityList);
		qcInfoVos.setBatteryInstallationDTO(batteryInstallationDTO);
		loadAttachmentDescVO(qcInfoVos, batteryInstallationDTO);
	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置太阳能储能板信息
	 */
	private void loadAndSetSolarPanels(String orderId, QcInfoVOs qcInfoVos) {
		SolarPanelsDTO solarPanelsDTO = loadSolarPanelsVO(orderId);
		List<EquipmentDetailsEntity> equipmentDetailsEntityList = queryEquipmentDetails(orderId, QcSubNodeName.SOLAR_PANELS.getSubNodeName());
		solarPanelsDTO.setEquipmentDetailsList(equipmentDetailsEntityList);
		qcInfoVos.setSolarPanelsDTO(solarPanelsDTO);
		loadAttachmentDescVO(qcInfoVos, solarPanelsDTO);
	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置电气元件信息
	 */
	private void loadAndSetElectricalComponents(String orderId, QcInfoVOs qcInfoVos) {
		ElectricalComponentsDTO electricalComponentsDTO = loadElectricalComponentsVO(orderId);
		qcInfoVos.setElectricalComponentsDTO(electricalComponentsDTO);
		loadAttachmentDescVO(qcInfoVos, electricalComponentsDTO);
	}


	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置接地设备信息
	 */

	private void loadAndSetGroundingEquipment(String orderId, QcInfoVOs qcInfoVos) {
		GroundingEquipmentDTO groundingEquipmentDTO = loadGroundingEquipmentVO(orderId);
		qcInfoVos.setGroundingEquipmentDTO(groundingEquipmentDTO);
		loadAttachmentDescVO(qcInfoVos, groundingEquipmentDTO);
	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置安装和支架信息
	 */
	private void loadAndSetMountingAndRacking(String orderId, QcInfoVOs qcInfoVos) {
		MountingAndRackingDTO mountingAndRackingDTO = loadMountingAndRackingVO(orderId);
		qcInfoVos.setMountingAndRackingDTO(mountingAndRackingDTO);
		loadAttachmentDescVO(qcInfoVos, mountingAndRackingDTO);

	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置安全和合规性信息
	 */
	private void loadAndSetSafetyAndCompliance(String orderId, QcInfoVOs qcInfoVos) {
		SafetyAndComplianceDTO safetyAndComplianceDTO = loadSafetyAndComplianceVO(orderId);
		qcInfoVos.setSafetyAndComplianceDTO(safetyAndComplianceDTO);
		loadAttachmentDescVO(qcInfoVos, safetyAndComplianceDTO);
	}


	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置系统测试信息
	 */
	private void loadAndSetSystemTesting(String orderId, QcInfoVOs qcInfoVos) {
		SystemTestingDTO systemTestingDTO = loadSystemTestingVO(orderId);
		qcInfoVos.setSystemTestingDTO(systemTestingDTO);
		loadAttachmentDescVO(qcInfoVos, systemTestingDTO);
	}

	/**
	 * @param orderId   订单id
	 * @param qcInfoVos 返回的VO
	 * @Description: 设置最终检查信息
	 */

	private void loadAndSetFinalInspection(String orderId, QcInfoVOs qcInfoVos) {
		FinalInspectionDTO finalInspectionDTO = loadFinalInspectionVO(orderId);
		qcInfoVos.setFinalInspectionDTO(finalInspectionDTO);
		loadAttachmentDescVO(qcInfoVos, finalInspectionDTO);
	}

	/**
	 * 查询储能信息
	 *
	 * @param orderId 入参
	 * @return InverterInstallationDTO
	 * <AUTHOR>
	 * @since 2024/6/13 19:47
	 **/
	private BatteryInstallationDTO loadBatteryInstallationVO(String orderId) {
		BatteryInstallationDTO batteryInstallationDTO = new BatteryInstallationDTO();
		try {
			// 创建查询条件
			LambdaQueryWrapper<InverterInstallationEntity> installationQueryWrapper = new LambdaQueryWrapper<>();
			installationQueryWrapper.eq(InverterInstallationEntity::getOrderId, orderId);
			// 执行查询
			InverterInstallationEntity inverterInstallationEntity = inverterInstallationService.getOne(installationQueryWrapper);
			if (inverterInstallationEntity != null) {
				batteryInstallationDTO.setId(inverterInstallationEntity.getId());
//				batteryInstallationDTO.setNumberOfBatteries(inverterInstallationEntity.getNumberOfBatteries());
//				batteryInstallationDTO.setNumberOfBatteriesOtherRemark(inverterInstallationEntity.getNumberOfBatteriesOtherRemark());
			}
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.error("Error occurred while fetching batteryInstallation data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadBatteryInstallation.exception");
		}
		return batteryInstallationDTO;
	}

	/**
	 * @param orderId 订单id
	 * @return InverterInstallationDTO
	 * @Description: 查询智能能量变换器安装信息
	 */
	private InverterInstallationDTO loadInverterInstallationVO(String orderId) {
		InverterInstallationEntity inverterInstallationEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<InverterInstallationEntity> installationQueryWrapper = new LambdaQueryWrapper<>();
			installationQueryWrapper.eq(InverterInstallationEntity::getOrderId, orderId);
			// 执行查询
			inverterInstallationEntity = inverterInstallationService.getOne(installationQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.error("Error occurred while fetching InverterInstallation data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadInverterInstallationVO.exception");
		}
		// 如果查询结果为空，则返回一个空的InverterInstallationDTO对象
		if (inverterInstallationEntity == null) {
			return new InverterInstallationDTO();
		}
		// 将实体数据映射到 VO
		InverterInstallationDTO inverterInstallationDTO = new InverterInstallationDTO();
		BeanUtils.copyProperties(inverterInstallationEntity, inverterInstallationDTO);
		// 查询字典智能能量变换器类型字典
		Map<String, String> inverterTypeDictMap = orderService.getDictBizListByName(DictBizCodeEnum.INVERTER_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(inverterTypeDictMap) && ObjectUtils.isNotEmpty(inverterTypeDictMap.get(inverterInstallationEntity.getInverterType()))) {
			inverterInstallationDTO.setInverterTypeName(inverterTypeDictMap.get(inverterInstallationEntity.getInverterType()));
		}

		return inverterInstallationDTO;
	}


	private SolarPanelsDTO loadSolarPanelsVO(String orderId) {
		SolarPanelsEntity solarPanelsEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<SolarPanelsEntity> solarPanelsQueryWrapper = new LambdaQueryWrapper<>();
			solarPanelsQueryWrapper.eq(SolarPanelsEntity::getOrderId, orderId);
			// 执行查询
			solarPanelsEntity = solarPanelsService.getOne(solarPanelsQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching SolarPanels data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadSolarPanelsVO.exception");
		}
		// 如果查询结果为空，则返回一个空的SolarPanelsDTO对象
		if (solarPanelsEntity == null) {
			return new SolarPanelsDTO();
		}
		// 将实体数据映射到 VO
		SolarPanelsDTO solarPanelsDTO = new SolarPanelsDTO();
		if (solarPanelsEntity != null) {
			BeanUtils.copyProperties(solarPanelsEntity, solarPanelsDTO);
		}
		return solarPanelsDTO;
	}

	private ElectricalComponentsDTO loadElectricalComponentsVO(String orderId) {
		ElectricalComponentsEntity electricalComponentsEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<ElectricalComponentsEntity> electricalComponentsQueryWrapper = new LambdaQueryWrapper<>();
			electricalComponentsQueryWrapper.eq(ElectricalComponentsEntity::getOrderId, orderId);
			// 执行查询
			electricalComponentsEntity = this.getOne(electricalComponentsQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching ElectricalComponent data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadElectricalComponentsVO.exception");
		}
		// 如果查询结果为空，则返回一个空的ElectricalComponentsDTO对象
		if (electricalComponentsEntity == null) {
			return new ElectricalComponentsDTO();
		}
		// 将实体数据映射到 VO
		ElectricalComponentsDTO electricalComponentsDTO = new ElectricalComponentsDTO();
		if (electricalComponentsEntity != null) {
			BeanUtils.copyProperties(electricalComponentsEntity, electricalComponentsDTO);
		}
		return electricalComponentsDTO;
	}

	private GroundingEquipmentDTO loadGroundingEquipmentVO(String orderId) {
		QcOtherInformationEntity qcOtherInformationEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<QcOtherInformationEntity> qcOtherInformationQueryWrapper = new LambdaQueryWrapper<>();
			qcOtherInformationQueryWrapper.eq(QcOtherInformationEntity::getOrderId, orderId);
			// 执行查询
			qcOtherInformationEntity = qcOtherInformationService.getOne(qcOtherInformationQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching GroundingEquipment data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadGroundingEquipmentVO.exception");
		}
		// 如果查询结果为空，则返回一个空的GroundingEquipmentDTO对象
		if (qcOtherInformationEntity == null) {
			return new GroundingEquipmentDTO();
		}
		// 将实体数据映射到 VO
		GroundingEquipmentDTO groundingEquipmentDTO = new GroundingEquipmentDTO();
		if (qcOtherInformationEntity != null) {
			BeanUtils.copyProperties(qcOtherInformationEntity, groundingEquipmentDTO);
		}
		return groundingEquipmentDTO;
	}

	private MountingAndRackingDTO loadMountingAndRackingVO(String orderId) {
		MountingAndRackingEntity mountingAndRackingEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<MountingAndRackingEntity> mountingAndRackingQueryWrapper = new LambdaQueryWrapper<>();
			mountingAndRackingQueryWrapper.eq(MountingAndRackingEntity::getOrderId, orderId);
			// 执行查询
			mountingAndRackingEntity = mountingAndRackingService.getOne(mountingAndRackingQueryWrapper);
		} catch (Exception e) {
			e.printStackTrace();
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching MountingAndRacking data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadMountingAndRackingVO.exception");
		}
		// 如果查询结果为空，则返回一个空的MountingAndRackingDTO对象
		if (mountingAndRackingEntity == null) {
			return new MountingAndRackingDTO();
		}
		// 将实体数据映射到 VO
		MountingAndRackingDTO mountingAndRackingDTO = new MountingAndRackingDTO();
		if (mountingAndRackingEntity != null) {
			BeanUtils.copyProperties(mountingAndRackingEntity, mountingAndRackingDTO);
		}
		return mountingAndRackingDTO;
	}

	private SafetyAndComplianceDTO loadSafetyAndComplianceVO(String orderId) {
		QcOtherInformationEntity qcOtherInformationEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<QcOtherInformationEntity> qcOtherInformationQueryWrapper = new LambdaQueryWrapper<>();
			qcOtherInformationQueryWrapper.eq(QcOtherInformationEntity::getOrderId, orderId);
			// 执行查询
			qcOtherInformationEntity = qcOtherInformationService.getOne(qcOtherInformationQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching SafetyAndCompliance data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadSafetyAndComplianceVO.exception");
		}
		// 如果查询结果为空，则返回一个空的SafetyAndComplianceDTO对象
		if (qcOtherInformationEntity == null) {
			return new SafetyAndComplianceDTO();
		}
		// 将实体数据映射到 VO
		SafetyAndComplianceDTO safetyAndComplianceDTO = new SafetyAndComplianceDTO();
		if (qcOtherInformationEntity != null) {
			BeanUtils.copyProperties(qcOtherInformationEntity, safetyAndComplianceDTO);
		}
		return safetyAndComplianceDTO;
	}

	private SystemTestingDTO loadSystemTestingVO(String orderId) {
		QcOtherInformationEntity qcOtherInformationEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<QcOtherInformationEntity> qcOtherInformationQueryWrapper = new LambdaQueryWrapper<>();
			qcOtherInformationQueryWrapper.eq(QcOtherInformationEntity::getOrderId, orderId);
			// 执行查询
			qcOtherInformationEntity = qcOtherInformationService.getOne(qcOtherInformationQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志、抛出自定义异常
			log.info("Error occurred while fetching SystemTesting data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadSystemTestingVO.exception");
		}
		// 如果查询结果为空，则返回一个空的SystemTestingDTO对象
		if (qcOtherInformationEntity == null) {
			return new SystemTestingDTO();
		}
		// 将实体数据映射到 VO
		SystemTestingDTO systemTestingDTO = new SystemTestingDTO();
		if (qcOtherInformationEntity != null) {
			BeanUtils.copyProperties(qcOtherInformationEntity, systemTestingDTO);
		}
		return systemTestingDTO;
	}

	private FinalInspectionDTO loadFinalInspectionVO(String orderId) {
		QcOtherInformationEntity qcOtherInformationEntity;
		try {
			// 创建查询条件
			LambdaQueryWrapper<QcOtherInformationEntity> qcOtherInformationQueryWrapper = new LambdaQueryWrapper<>();
			qcOtherInformationQueryWrapper.eq(QcOtherInformationEntity::getOrderId, orderId);
			// 执行查询
			qcOtherInformationEntity = qcOtherInformationService.getOne(qcOtherInformationQueryWrapper);
		} catch (Exception e) {
			// 处理异常，记录日志或抛出自定义异常
			log.info("Error occurred while fetching FinalInspection data:{} ", e.getMessage());
			throw new BusinessException("agent.qcSubmission.loadFinalInspectionVO.exception");
		}
		// 如果查询结果为空，则返回一个空的FinalInspectionDTO对象
		if (qcOtherInformationEntity == null) {
			return new FinalInspectionDTO();
		}
		// 将实体数据映射到 VO
		FinalInspectionDTO finalInspectionDTO = new FinalInspectionDTO();
		if (qcOtherInformationEntity != null) {
			BeanUtils.copyProperties(qcOtherInformationEntity, finalInspectionDTO);
		}
		return finalInspectionDTO;
	}

	@Override
	@FileSave
	public Boolean saveOrUpdateMaintenanceQcInfo(QcInfoDTOs qcInfoDtos) {
		// 首先检查qcInfoDtos对象本身是否为null
		if (ObjectUtils.isEmpty(qcInfoDtos)) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.notEmpty");
		}
		// 检查orderId是否为null
		if (qcInfoDtos.getOrderId() == null) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty");
		}
		// 直接获取SubNodeStatusDTO对象
		SubNodeSaveStatusDTO subNodeSaveStatus = qcInfoDtos.getSubNodeSaveStatus();

		//设置节点名称
		qcInfoDtos.setNodeName(QC_SUBMISSION_KEY);
		//获取订单id
		Long orderId = qcInfoDtos.getOrderId();
		//根据小节点名称执行不同的操作
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getInverter()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getInverter())) {
			//1 保存或更新智能能量变换器信息inverter_installation表中的数据
			saveOrUpdateInverterInstallation(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.INVERTER.getSubNodeName(), subNodeSaveStatus.getInverter());
		}
		// 储能信息
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getBattery()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getBattery())) {
			saveOrUpdateBatteryInstallation(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.BATTERY.getSubNodeName(), subNodeSaveStatus.getBattery());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSolarPanels()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSolarPanels())) {
			//2 保存或更新太阳能储能板信息SolarPanels表中的数据
			saveOrUpdateSolarPanels(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SOLAR_PANELS.getSubNodeName(), subNodeSaveStatus.getSolarPanels());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getElectricalComponents()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getElectricalComponents())) {
			//3 保存或更新电气元件信息ElectricalComponents表中的数据
			saveOrUpdateElectricalComponent(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.ELECTRICAL_COMPONENTS.getSubNodeName(), subNodeSaveStatus.getElectricalComponents());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getGroundingEquipment()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getGroundingEquipment())) {
			//4 保存或更新接地设备信息GroundingEquipment数据,包含在qc_other_information表中
			saveOrUpdateGroundingEquipment(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.GROUNDING_EQUIPMENT.getSubNodeName(), subNodeSaveStatus.getGroundingEquipment());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getMountingAndRacking()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getMountingAndRacking())) {
			//5 保存或更新安装和支架信息MountingAndRacking表中的数据
			saveOrUpdateMountingAndRacking(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.MOUNTING_AND_RACKING.getSubNodeName(), subNodeSaveStatus.getMountingAndRacking());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSafetyAndCompliance()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSafetyAndCompliance())) {
			//6 保存或更新安全和合规性信息SafetyAndCompliance中的数据,包含在qc_other_information表中
			saveOrUpdateSafetyAndCompliance(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SAFETY_AND_COMPLIANCE.getSubNodeName(), subNodeSaveStatus.getSafetyAndCompliance());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSystemTesting()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSystemTesting())) {
			//7 保存或更新系统测试信息SystemTesting表中的数据,包含在qc_other_information表中
			saveOrUpdateSystemTesting(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SYSTEM_TESTING.getSubNodeName(), subNodeSaveStatus.getSystemTesting());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getFinalInspection()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getFinalInspection())) {
			//8 保存或更新最终检查信息FinalInspection中的数据,包含在qc_other_information表中
			saveOrUpdateFinalInspection(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.FINAL_INSPECTION.getSubNodeName(), subNodeSaveStatus.getFinalInspection());
		}
		return true;
	}

	@Override
	@FileSave
	public Boolean saveOrUpdateQcInfo(QcInfoDTOs qcInfoDtos) {
		// 首先检查qcInfoDtos对象本身是否为null
		if (ObjectUtils.isEmpty(qcInfoDtos)) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.notEmpty");
		}
		// 检查orderId是否为null
		if (qcInfoDtos.getOrderId() == null) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty");
		}
		// 直接获取SubNodeStatusDTO对象
		SubNodeSaveStatusDTO subNodeSaveStatus = qcInfoDtos.getSubNodeSaveStatus();

		//设置节点名称
		qcInfoDtos.setNodeName(QC_SUBMISSION_KEY);
		//获取订单id
		Long orderId = qcInfoDtos.getOrderId();
		//根据小节点名称执行不同的操作
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getInverter()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getInverter())) {
			saveOrUpdateInverterInstallation(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.INVERTER.getSubNodeName(), subNodeSaveStatus.getInverter());
		}
		// 储能信息
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getBattery()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getBattery())) {
			saveOrUpdateBatteryInstallation(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.BATTERY.getSubNodeName(), subNodeSaveStatus.getBattery());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSolarPanels()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSolarPanels())) {
			//2 保存或更新太阳能储能板信息SolarPanels表中的数据
			saveOrUpdateSolarPanels(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SOLAR_PANELS.getSubNodeName(), subNodeSaveStatus.getSolarPanels());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getElectricalComponents()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getElectricalComponents())) {
			//3 保存或更新电气元件信息ElectricalComponents表中的数据
			saveOrUpdateElectricalComponent(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.ELECTRICAL_COMPONENTS.getSubNodeName(), subNodeSaveStatus.getElectricalComponents());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getGroundingEquipment()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getGroundingEquipment())) {
			//4 保存或更新接地设备信息GroundingEquipment数据,包含在qc_other_information表中
			saveOrUpdateGroundingEquipment(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.GROUNDING_EQUIPMENT.getSubNodeName(), subNodeSaveStatus.getGroundingEquipment());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getMountingAndRacking()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getMountingAndRacking())) {
			//5 保存或更新安装和支架信息MountingAndRacking表中的数据
			saveOrUpdateMountingAndRacking(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.MOUNTING_AND_RACKING.getSubNodeName(), subNodeSaveStatus.getMountingAndRacking());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSafetyAndCompliance()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSafetyAndCompliance())) {
			//6 保存或更新安全和合规性信息SafetyAndCompliance中的数据,包含在qc_other_information表中
			saveOrUpdateSafetyAndCompliance(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SAFETY_AND_COMPLIANCE.getSubNodeName(), subNodeSaveStatus.getSafetyAndCompliance());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getSystemTesting()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getSystemTesting())) {
			//7 保存或更新系统测试信息SystemTesting表中的数据,包含在qc_other_information表中
			saveOrUpdateSystemTesting(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.SYSTEM_TESTING.getSubNodeName(), subNodeSaveStatus.getSystemTesting());
		}
		if (OperationStatus.UNFINISH.getDec().equals(subNodeSaveStatus.getFinalInspection()) || OperationStatus.FINISH.getDec().equals(subNodeSaveStatus.getFinalInspection())) {
			//8 保存或更新最终检查信息FinalInspection中的数据,包含在qc_other_information表中
			saveOrUpdateFinalInspection(qcInfoDtos);
			saveOrUpdateOrderNodeSubStatus(orderId, QcSubNodeName.FINAL_INSPECTION.getSubNodeName(), subNodeSaveStatus.getFinalInspection());
		}
		return true;
	}


	public void saveOrUpdateInverterInstallation(QcInfoDTOs qcInfoDtos) {
		InverterInstallationDTO inverterInstallationDTO = qcInfoDtos.getInverterInstallationDTO();
		// 直接通过DTO提供的ID判断是更新还是新增
		if (inverterInstallationDTO != null) {
			if (inverterInstallationDTO.getId() != null) {
				InverterInstallationEntity inverterInstallationEntity = inverterInstallationService.getById(inverterInstallationDTO.getId());
				if (inverterInstallationEntity != null) {
					// 忽略id，因为不需要更新id
					BeanUtils.copyProperties(inverterInstallationDTO, inverterInstallationEntity, "id");
					inverterInstallationService.updateById(inverterInstallationEntity);
				} else {
					// 前端提供了一个无效的ID
					// 抛出异常记录日志
					log.error("InverterInstallation with ID " + inverterInstallationDTO.getId() + " does not exist");
					throw new BusinessException("agent.qcSubmission.saveOrUpdateInverterInstallation.InverterInstallation.notExist", inverterInstallationDTO.getId());
				}
			} else {
				// 初始化新实例
				InverterInstallationEntity inverterInstallationEntity = new InverterInstallationEntity();
				BeanUtils.copyProperties(qcInfoDtos.getInverterInstallationDTO(), inverterInstallationEntity);
				inverterInstallationEntity.setOrderId(qcInfoDtos.getOrderId());
				inverterInstallationService.save(inverterInstallationEntity);
			}
			// 处理智能能量变换器设备列表信息
			saveDeviceInformation(inverterInstallationDTO.getEquipmentDetailsList(), QcSubNodeName.INVERTER, qcInfoDtos.getOrderId());
		}
	}

	/**
	 * 保存设备信息
	 *
	 * @param equipmentDetailsList 设备信息
	 * @param qcSubNodeName        设备类型
	 * <AUTHOR>
	 * @since 2024/6/13 9:15
	 **/
	private void saveDeviceInformation(List<EquipmentDetailsEntity> equipmentDetailsList, QcSubNodeName qcSubNodeName, Long orderId) {
		LambdaUpdateWrapper<EquipmentDetailsEntity> updateWrapper = null;
		switch (qcSubNodeName) {
			case INVERTER:
				validSerialNumberExists(equipmentDetailsList, BizConstant.NUMBER_ZERO);
				setEquipmentType(equipmentDetailsList, QcSubNodeName.INVERTER.getSubNodeName(), orderId);
				updateWrapper = getUpdateWrapper(orderId, QcSubNodeName.INVERTER.getSubNodeName());
				break;
			case BATTERY:
				validSerialNumberExists(equipmentDetailsList, BizConstant.NUMBER_ONE);
				setEquipmentType(equipmentDetailsList, QcSubNodeName.BATTERY.getSubNodeName(), orderId);
				updateWrapper = getUpdateWrapper(orderId, QcSubNodeName.BATTERY.getSubNodeName());
				break;
			case SOLAR_PANELS:
				setEquipmentType(equipmentDetailsList, QcSubNodeName.SOLAR_PANELS.getSubNodeName(), orderId);
				updateWrapper = getUpdateWrapper(orderId, QcSubNodeName.SOLAR_PANELS.getSubNodeName());
				break;
			default:
				break;
		}
		if (updateWrapper != null) {
			equipmentDetailsService.update(updateWrapper);
			// 清理id
			equipmentDetailsList.forEach(a -> {
				a.setId(null);
			});
			equipmentDetailsService.saveBatch(equipmentDetailsList);
		}
	}

	private void setEquipmentType(List<EquipmentDetailsEntity> equipmentDetailsList, String equipmentType, Long orderId) {
		equipmentDetailsList.forEach(a -> {
			a.setOrderId(orderId);
			a.setEquipmentType(equipmentType);
		});
	}

	/**
	 * 验证设备是否存在
	 *
	 * @param equipmentDetailsList 设备信息
	 * @param deviceType           入参
	 * <AUTHOR>
	 * @since 2024/6/14 13:57
	 **/
	private void validSerialNumberExists(List<EquipmentDetailsEntity> equipmentDetailsList, Integer deviceType) {
		if (CollectionUtils.isEmpty(equipmentDetailsList)) {
			return;
		}
		// 检查设备详情列表中所有设备的序列号是否都为空或不存在
		boolean serialNumberAllNull = equipmentDetailsList.stream()
			.allMatch(details -> details.getSerialNumber() == null || details.getSerialNumber().isEmpty());
		// 如果所有设备的序列号都为空或不存在，则不进行后续操作
		if (serialNumberAllNull) {
			return;
		}
		// 通过流操作获取设备详情列表中重复的序列号字符串
		String duplicateSerialNumbers = equipmentDetailsList.stream()
			// 过滤出序列号不为空的设备详情
			.filter(a -> StringUtils.isNotBlank(a.getSerialNumber()))
			// 按序列号分组，并计算每个序列号出现的次数
			.collect(Collectors.groupingBy(EquipmentDetailsEntity::getSerialNumber, Collectors.counting()))
			// 过滤出出现次数大于1的序列号分组
			.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			// 提取重复序列号的键（即序列号本身）
			.map(Map.Entry::getKey)
			// 将重复的序列号用逗号连接成字符串
			.collect(Collectors.joining(","));
		// 存在重复记录则抛出异常
		if (StringUtils.isNotBlank(duplicateSerialNumbers)) {
			throw new BusinessException("agent.qcSubmission.serialNumber.repeat", duplicateSerialNumbers);
		}
		// 初始化一个字符串列表，用于存储设备的序列号
		List<String> serialNumberList = new ArrayList<>();
		// 从设备详情列表中提取序列号，去除重复项，并将序列号添加到列表中
		equipmentDetailsList.stream().map(EquipmentDetailsEntity::getSerialNumber).filter(StringUtils::isNotBlank).distinct().forEach(serialNumberList::add);
		// 调用远程接口，检查是否存在不存在的序列号，并获取检查结果
		String notExistSerialNumber = deviceExitFactoryClient.validSerialNumberIsExists(serialNumberList, deviceType).getData();
		// 如果检查结果中存在不存在的序列号，则根据设备类型抛出相应的业务异常
		if (StringUtils.isNotBlank(notExistSerialNumber)) {
			if (BizConstant.NUMBER_ZERO.equals(deviceType)) {
				throw new BusinessException("agent.qcSubmission.invert.not.exist", notExistSerialNumber);
			} else if (BizConstant.NUMBER_ONE.equals(deviceType)) {
				throw new BusinessException("agent.qcSubmission.battery.not.exist", notExistSerialNumber);
			}
		}
	}

	/**
	 * 获取设备明细修改参数
	 *
	 * @param orderId       订单id
	 * @param qcSubNodeName 入参
	 * @return LambdaUpdateWrapper<EquipmentDetailsDTO>
	 * <AUTHOR>
	 * @since 2024/6/13 10:01
	 **/
	private LambdaUpdateWrapper<EquipmentDetailsEntity> getUpdateWrapper(Long orderId, String qcSubNodeName) {
		return Wrappers.<EquipmentDetailsEntity>lambdaUpdate().set(EquipmentDetailsEntity::getIsDeleted, BizConstant.NUMBER_ONE).eq(
			EquipmentDetailsEntity::getOrderId, orderId).eq(EquipmentDetailsEntity::getEquipmentType, qcSubNodeName).eq(EquipmentDetailsEntity::getIsDeleted, BizConstant.NUMBER_ZERO);
	}

	public void saveOrUpdateBatteryInstallation(QcInfoDTOs qcInfoDtos) {
		BatteryInstallationDTO batteryInstallationDTO = qcInfoDtos.getBatteryInstallationDTO();
		// 直接通过DTO提供的ID判断是更新还是新增
		if (batteryInstallationDTO != null) {
			if (batteryInstallationDTO.getId() != null) {
//				LambdaUpdateWrapper<InverterInstallationEntity> updateWrapper = Wrappers.<InverterInstallationEntity>lambdaUpdate().set(InverterInstallationEntity::getNumberOfBatteries, batteryInstallationDTO.getNumberOfBatteries()).set(InverterInstallationEntity::getNumberOfBatteriesOtherRemark, batteryInstallationDTO.getNumberOfBatteriesOtherRemark()).eq(InverterInstallationEntity::getId, batteryInstallationDTO.getId());
//				inverterInstallationService.update(updateWrapper);
			} else {
				Long orderId = qcInfoDtos.getOrderId();
				InverterInstallationEntity inverterInstallationEntity = inverterInstallationService.getOne(Wrappers.<InverterInstallationEntity>lambdaQuery().eq(InverterInstallationEntity::getOrderId, orderId));
				// 记录存在则更新
				if (inverterInstallationEntity != null) {
//					LambdaUpdateWrapper<InverterInstallationEntity> updateWrapper = Wrappers.<InverterInstallationEntity>lambdaUpdate().set(InverterInstallationEntity::getNumberOfBatteries, batteryInstallationDTO.getNumberOfBatteries()).set(InverterInstallationEntity::getNumberOfBatteriesOtherRemark, batteryInstallationDTO.getNumberOfBatteriesOtherRemark()).eq(InverterInstallationEntity::getId, inverterInstallationEntity.getId());
//					inverterInstallationService.update(updateWrapper);
				} else {
					// 初始化新实例
//					InverterInstallationEntity addInverterInstallationEntity = new InverterInstallationEntity();
//					addInverterInstallationEntity.setNumberOfBatteries(batteryInstallationDTO.getNumberOfBatteries());
//					addInverterInstallationEntity.setNumberOfBatteriesOtherRemark(batteryInstallationDTO.getNumberOfBatteriesOtherRemark());
//					addInverterInstallationEntity.setOrderId(qcInfoDtos.getOrderId());
//					inverterInstallationService.save(addInverterInstallationEntity);
				}
			}
			// 处理储能设备列表信息
			saveDeviceInformation(batteryInstallationDTO.getEquipmentDetailsList(), QcSubNodeName.BATTERY, qcInfoDtos.getOrderId());
		}
	}

	public void saveOrUpdateSolarPanels(QcInfoDTOs qcInfoDtos) {
		SolarPanelsDTO solarPanelsDTO = qcInfoDtos.getSolarPanelsDTO();
		// 直接通过DTO提供的ID判断是更新还是新增
		if (solarPanelsDTO != null) {
			if (solarPanelsDTO.getId() != null) {
				SolarPanelsEntity solarPanelsEntity = solarPanelsService.getById(solarPanelsDTO.getId());
				if (solarPanelsEntity != null) {
					// 忽略id，因为不需要更新id
					BeanUtils.copyProperties(solarPanelsDTO, solarPanelsEntity, "id");
					solarPanelsService.updateById(solarPanelsEntity);
				} else {
					// 前端提供了一个无效的ID
					// 抛出异常记录日志
					log.error("SolarPanels with ID " + solarPanelsDTO.getId() + " does not exist");
					throw new BusinessException("agent.qcSubmission.saveOrUpdateSolarPanels.SolarPanels.notExist", solarPanelsDTO.getId());
				}
			} else {
				// 初始化新实例
				SolarPanelsEntity solarPanelsEntity = new SolarPanelsEntity();
				BeanUtils.copyProperties(qcInfoDtos.getSolarPanelsDTO(), solarPanelsEntity);
				solarPanelsEntity.setOrderId(qcInfoDtos.getOrderId());
				solarPanelsService.save(solarPanelsEntity);
			}
			// 处理光伏板设备列表信息
			saveDeviceInformation(solarPanelsDTO.getEquipmentDetailsList(), QcSubNodeName.SOLAR_PANELS, qcInfoDtos.getOrderId());
		}
	}

	public void saveOrUpdateElectricalComponent(QcInfoDTOs qcInfoDtos) {
		ElectricalComponentsDTO electricalComponentsDTO = qcInfoDtos.getElectricalComponentsDTO();
		// 直接通过DTO提供的ID判断是更新还是新增
		if (electricalComponentsDTO != null) {
			if (electricalComponentsDTO.getId() != null) {
				ElectricalComponentsEntity electricalComponentsEntity = this.getById(electricalComponentsDTO.getId());
				if (electricalComponentsEntity != null) {
					// 忽略id，因为不需要更新id
					BeanUtils.copyProperties(electricalComponentsDTO, electricalComponentsEntity, "id");
					this.updateById(electricalComponentsEntity);
				} else {
					// 前端提供了一个无效的ID
					// 抛出异常记录日志
					log.error("ElectricalComponents with ID " + electricalComponentsDTO.getId() + " does not exist");
					throw new BusinessException("agent.qcSubmission.saveOrUpdateElectricalComponent.ElectricalComponents.notExist", electricalComponentsDTO.getId());
				}
			} else {
				// 初始化新实例
				ElectricalComponentsEntity electricalComponentsEntity = new ElectricalComponentsEntity();
				BeanUtils.copyProperties(qcInfoDtos.getElectricalComponentsDTO(), electricalComponentsEntity);
				electricalComponentsEntity.setOrderId(qcInfoDtos.getOrderId());
				this.save(electricalComponentsEntity);
			}
		}
	}

	public void saveOrUpdateGroundingEquipment(QcInfoDTOs qcInfoDtos) {
		// 先尝试获取现有的QcOtherInformationEntity
		QcOtherInformationEntity qcOtherInformationEntity = qcOtherInformationService.getOne(new LambdaQueryWrapper<QcOtherInformationEntity>()
			.eq(QcOtherInformationEntity::getOrderId, qcInfoDtos.getOrderId()));
		if (qcOtherInformationEntity != null) {
			// 更新现有的QcOtherInformationEntity
			BeanUtils.copyProperties(qcInfoDtos.getGroundingEquipmentDTO(), qcOtherInformationEntity, "id");
			qcOtherInformationService.updateById(qcOtherInformationEntity);
		} else {
			// 初始化新实例
			qcOtherInformationEntity = new QcOtherInformationEntity();
			BeanUtils.copyProperties(qcInfoDtos.getGroundingEquipmentDTO(), qcOtherInformationEntity);
			qcOtherInformationEntity.setOrderId(qcInfoDtos.getOrderId());
			qcOtherInformationService.save(qcOtherInformationEntity);
		}
	}

	public void saveOrUpdateMountingAndRacking(QcInfoDTOs qcInfoDtos) {
		MountingAndRackingDTO mountingAndRackingDTO = qcInfoDtos.getMountingAndRackingDTO();
		// 直接通过DTO提供的ID判断是更新还是新增
		if (mountingAndRackingDTO != null) {
			if (mountingAndRackingDTO.getId() != null) {
				MountingAndRackingEntity mountingAndRackingEntity = mountingAndRackingService.getById(mountingAndRackingDTO.getId());
				if (mountingAndRackingEntity != null) {
					// 忽略id，因为不需要更新id
					BeanUtils.copyProperties(mountingAndRackingDTO, mountingAndRackingEntity, "id");
					mountingAndRackingService.updateById(mountingAndRackingEntity);
				} else {
					// 前端提供了一个无效的ID
					// 抛出异常记录日志
					log.error("MountingAndRacking with ID " + mountingAndRackingDTO.getId() + " does not exist");
					throw new BusinessException("agent.qcSubmission.saveOrUpdateMountingAndRacking.MountingAndRacking.notExist", mountingAndRackingDTO.getId());
				}
			} else {
				// 初始化新实例
				MountingAndRackingEntity mountingAndRackingEntity = new MountingAndRackingEntity();
				BeanUtils.copyProperties(qcInfoDtos.getMountingAndRackingDTO(), mountingAndRackingEntity);
				mountingAndRackingEntity.setOrderId(qcInfoDtos.getOrderId());
				mountingAndRackingService.save(mountingAndRackingEntity);
			}
		}
	}

	public void saveOrUpdateSafetyAndCompliance(QcInfoDTOs qcInfoDtos) {
		// 先尝试获取现有的QcOtherInformationEntity
		QcOtherInformationEntity qcOtherInformationEntity = qcOtherInformationService.getOne(new LambdaQueryWrapper<QcOtherInformationEntity>()
			.eq(QcOtherInformationEntity::getOrderId, qcInfoDtos.getOrderId()));
		if (qcOtherInformationEntity != null) {
			// 更新现有的QcOtherInformationEntity
			BeanUtils.copyProperties(qcInfoDtos.getSafetyAndComplianceDTO(), qcOtherInformationEntity, "id");
			qcOtherInformationService.updateById(qcOtherInformationEntity);
		} else {
			// 初始化新实例
			qcOtherInformationEntity = new QcOtherInformationEntity();
			BeanUtils.copyProperties(qcInfoDtos.getSafetyAndComplianceDTO(), qcOtherInformationEntity);
			qcOtherInformationEntity.setOrderId(qcInfoDtos.getOrderId());
			qcOtherInformationService.save(qcOtherInformationEntity);
		}
	}

	public void saveOrUpdateSystemTesting(QcInfoDTOs qcInfoDtos) {
		// 先尝试获取现有的QcOtherInformationEntity
		QcOtherInformationEntity qcOtherInformationEntity = qcOtherInformationService.getOne(new LambdaQueryWrapper<QcOtherInformationEntity>()
			.eq(QcOtherInformationEntity::getOrderId, qcInfoDtos.getOrderId()));
		if (qcOtherInformationEntity != null) {
			// 更新现有的QcOtherInformationEntity
			BeanUtils.copyProperties(qcInfoDtos.getSystemTestingDTO(), qcOtherInformationEntity, "id");
			qcOtherInformationService.updateById(qcOtherInformationEntity);
		} else {
			// 初始化新实例
			QcOtherInformationEntity newEntity = new QcOtherInformationEntity();
			BeanUtils.copyProperties(qcInfoDtos.getSystemTestingDTO(), newEntity);
			newEntity.setOrderId(qcInfoDtos.getOrderId());
			qcOtherInformationService.save(newEntity);
		}
	}

	public void saveOrUpdateFinalInspection(QcInfoDTOs qcInfoDtos) {
		// 先尝试获取现有的QcOtherInformationEntity
		QcOtherInformationEntity qcOtherInformationEntity = qcOtherInformationService.getOne(new LambdaQueryWrapper<QcOtherInformationEntity>()
			.eq(QcOtherInformationEntity::getOrderId, qcInfoDtos.getOrderId()));
		if (qcOtherInformationEntity != null) {
			// 更新现有的QcOtherInformationEntity
			BeanUtils.copyProperties(qcInfoDtos.getFinalInspectionDTO(), qcOtherInformationEntity, "id");
			qcOtherInformationService.updateById(qcOtherInformationEntity);
		} else {
			// 初始化新实例
			QcOtherInformationEntity newEntity = new QcOtherInformationEntity();
			BeanUtils.copyProperties(qcInfoDtos.getFinalInspectionDTO(), newEntity);
			newEntity.setOrderId(qcInfoDtos.getOrderId());
			qcOtherInformationService.save(newEntity);
		}
	}

	private void saveOrUpdateOrderNodeSubStatus(Long orderId, String subNodeName, String saveStatus) {
		OrderNodeSubStatusEntity orderNodeSubStatusEntity = orderNodeSubStatusService.getOne(new LambdaQueryWrapper<OrderNodeSubStatusEntity>()
			.eq(OrderNodeSubStatusEntity::getOrderId, orderId)
			.eq(OrderNodeSubStatusEntity::getSubNodeName, subNodeName)
			.eq(OrderNodeSubStatusEntity::getNodeName, QC_SUBMISSION_KEY));
		if (orderNodeSubStatusEntity != null) {
			orderNodeSubStatusEntity.setSubStatus(saveStatus);
			// 更新现有记录
			orderNodeSubStatusService.updateById(orderNodeSubStatusEntity);
		} else {
			OrderNodeSubStatusEntity newStatus = new OrderNodeSubStatusEntity();
			newStatus.setOrderId(orderId);
			newStatus.setSubNodeName(subNodeName);
			newStatus.setSubStatus(saveStatus);
			newStatus.setBusinessType(BUSINESS_TYPE_SAVE);
			newStatus.setNodeName(QC_SUBMISSION_KEY);
			// 保存新记录
			orderNodeSubStatusService.save(newStatus);
		}
	}

	@Override
	public List<SubNodeSaveStatusVO> listSubNodeSaveStatus(Long orderId) {
		validateOrderId(orderId);
		List<OrderNodeSubStatusEntity> listOrderNodeSubStatusEntity = orderNodeSubStatusService.list(new LambdaQueryWrapper<OrderNodeSubStatusEntity>()
			.eq(OrderNodeSubStatusEntity::getOrderId, orderId)
			.eq(OrderNodeSubStatusEntity::getNodeName, QC_SUBMISSION_KEY)
			.eq(OrderNodeSubStatusEntity::getBusinessType, BUSINESS_TYPE_SAVE)
			.eq(OrderNodeSubStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		return BeanUtil.copyToList(listOrderNodeSubStatusEntity, SubNodeSaveStatusVO.class);
	}

	@Override
	public Boolean submitQcInfoMaintenance(SubmitQcInfoRequestDTO requestDTO) {
		//区分app与web
		String clientType = requestDTO.getClientType();
		if (StringUtils.isBlank(clientType)) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.clientType.notEmpty");
		}
		//orderFlow判断非空
		if (ObjectUtils.isEmpty(requestDTO.getOrderFlow())) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.orderFlowDTO.notEmpty");
		}
		//orderFlow中的businessId判断非空
		if (StringUtils.isBlank(requestDTO.getOrderFlow().getBusinessId())) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.orderDTO.businessId.notEmpty");
		}
		// 预定义所有必须完成的子节点
		List<String> requiredSubNodes = List.of(QcSubNodeName.FINAL_INSPECTION.getSubNodeName());
		// 根据clientType区分逻辑处理
		if (WEB.equals(clientType)) {
			// 执行Web特有的逻辑处理
			//判断非空
			if (ObjectUtils.isEmpty(requestDTO.getQcInfoDtos())) {
				throw new BusinessException("agent.qcSubmission.submitQcInfo.qcInfoDtos.notEmpty");
			}
			this.saveOrUpdateQcInfo(requestDTO.getQcInfoDtos());
			//保存附件或图片信息
			if (ObjectUtils.isNotEmpty(requestDTO.getQcInfoDtos().getBatchVO())) {
				BatchVO<AttachmentInfoEntity> batchVO = requestDTO.getQcInfoDtos().getBatchVO();
				if (CollectionUtils.isEmpty(batchVO.getAddList()) && CollectionUtils.isEmpty(batchVO.getUpdateList()) && CollectionUtils.isEmpty(batchVO.getDeleteList())) {
					log.error("The picture information is empty");
				} else {
					R<?> attachmentInfo = attachmentInfoClient.saveAndUpdate(requestDTO.getQcInfoDtos().getBatchVO());
					if (!attachmentInfo.isSuccess()) {
						throw new BusinessException("agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail");
					}
				}
			}
			//保存备注信息
			if (ObjectUtils.isNotEmpty(requestDTO.getQcInfoDtos().getImgDescOperationList())) {
				List<Map<String, String>> imgDescOperationList = requestDTO.getQcInfoDtos().getImgDescOperationList();
				List<AdditionalInfoEntity> additionalInfoEntityList = JSON.parseArray(JSON.toJSONString(imgDescOperationList), AdditionalInfoEntity.class);
				//过滤下备注key为空的情况
				long count = additionalInfoEntityList.stream().filter(re -> ObjectUtils.isNotEmpty(re.getImgBizKey())).count();
				if (count > 0) {
					additionalInfoService.saveAdditionalInfoEntityList(additionalInfoEntityList);
				}
			}
			// 验证指定子节点是否都已经完成
			validateSubNodeCompletion(Long.parseLong(requestDTO.getOrderFlow().getBusinessId()), requiredSubNodes);
			// 执行工作流处理
			reviewOrderService.examineApprove(requestDTO.getOrderFlow());
		} else if (APP.equals(clientType)) {
			// 验证指定子节点是否都已经完成
			validateSubNodeCompletion(Long.parseLong(requestDTO.getOrderFlow().getBusinessId()), requiredSubNodes);
			// 执行工作流处理
			reviewOrderService.examineApprove(requestDTO.getOrderFlow());
		} else {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.clientType.notSupported");
		}
		return true;
	}

	@Override
	public Boolean submitQcInfo(SubmitQcInfoRequestDTO requestDTO) {
		//区分app与web
		String clientType = requestDTO.getClientType();
		if (StringUtils.isBlank(clientType)) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.clientType.notEmpty");
		}
		//orderFlow判断非空
		if (ObjectUtils.isEmpty(requestDTO.getOrderFlow())) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.orderFlowDTO.notEmpty");
		}
		//orderFlow中的businessId判断非空
		if (StringUtils.isBlank(requestDTO.getOrderFlow().getBusinessId())) {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.orderDTO.businessId.notEmpty");
		}
		// 预定义所有必须完成的子节点
		List<String> requiredSubNodes = QcSubNodeName.qcSubNodeNameList();
		// 根据clientType区分逻辑处理
		if (WEB.equals(clientType)) {
			// 执行Web特有的逻辑处理
			//判断非空
			if (ObjectUtils.isEmpty(requestDTO.getQcInfoDtos())) {
				throw new BusinessException("agent.qcSubmission.submitQcInfo.qcInfoDtos.notEmpty");
			}
			this.saveOrUpdateQcInfo(requestDTO.getQcInfoDtos());
			//保存附件或图片信息
			if (ObjectUtils.isNotEmpty(requestDTO.getQcInfoDtos().getBatchVO())) {
				BatchVO<AttachmentInfoEntity> batchVO = requestDTO.getQcInfoDtos().getBatchVO();
				if (CollectionUtils.isEmpty(batchVO.getAddList()) && CollectionUtils.isEmpty(batchVO.getUpdateList()) && CollectionUtils.isEmpty(batchVO.getDeleteList())) {
					log.error("The picture information is empty");
				} else {
					R<?> attachmentInfo = attachmentInfoClient.saveAndUpdate(requestDTO.getQcInfoDtos().getBatchVO());
					if (!attachmentInfo.isSuccess()) {
						throw new BusinessException("agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail");
					}
				}
			}
			//保存备注信息
			if (ObjectUtils.isNotEmpty(requestDTO.getQcInfoDtos().getImgDescOperationList())) {
				List<Map<String, String>> imgDescOperationList = requestDTO.getQcInfoDtos().getImgDescOperationList();
				List<AdditionalInfoEntity> additionalInfoEntityList = JSON.parseArray(JSON.toJSONString(imgDescOperationList), AdditionalInfoEntity.class);
				//过滤下备注key为空的情况
				long count = additionalInfoEntityList.stream().filter(re -> ObjectUtils.isNotEmpty(re.getImgBizKey())).count();
				if (count > 0) {
					additionalInfoService.saveAdditionalInfoEntityList(additionalInfoEntityList);
				}
			}
			// 验证所有必须的子节点是否都已经完成
			validateSubNodeCompletion(Long.parseLong(requestDTO.getOrderFlow().getBusinessId()), requiredSubNodes);
			// 执行工作流处理
			reviewOrderService.examineApprove(requestDTO.getOrderFlow());
		} else if (APP.equals(clientType)) {
			// 验证所有必须的子节点是否都已经完成
			validateSubNodeCompletion(Long.parseLong(requestDTO.getOrderFlow().getBusinessId()), requiredSubNodes);
			// 执行工作流处理
			reviewOrderService.examineApprove(requestDTO.getOrderFlow());
		} else {
			throw new BusinessException("agent.qcSubmission.submitQcInfo.clientType.notSupported");
		}
		return true;
	}

	@Override
	public List<SubNodeSaveStatusVO> listSubNodeApproveStatus(Long orderId) {
		if (ObjectUtils.isEmpty(orderId)) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty");
		}
		List<OrderNodeSubStatusEntity> listOrderNodeSubStatusEntity = orderNodeSubStatusService.list(new LambdaQueryWrapper<OrderNodeSubStatusEntity>()
			.eq(OrderNodeSubStatusEntity::getOrderId, orderId)
			.eq(OrderNodeSubStatusEntity::getNodeName, QC_VERIFICATION_KEY)
			.eq(OrderNodeSubStatusEntity::getBusinessType, BUSINESS_TYPE_APPROVE)
			.eq(OrderNodeSubStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		return BeanUtil.copyToList(listOrderNodeSubStatusEntity, SubNodeSaveStatusVO.class);
	}

	/**
	 * 验证订单id
	 *
	 * @param orderId 订单id
	 */

	private void validateOrderId(Long orderId) {
		if (ObjectUtils.isEmpty(orderId)) {
			throw new BusinessException("agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty");
		}
	}

	/**
	 * 验证子节点是否完成
	 *
	 * @param orderId          订单id
	 * @param requiredSubNodes 子节点名称
	 * @throws BusinessException 异常
	 */
	public void validateSubNodeCompletion(Long orderId, List<String> requiredSubNodes) throws BusinessException {
		// 查询数据库以获取所有子节点的当前状态
		List<SubNodeSaveStatusVO> subNodeSaveStatusVos = this.listSubNodeSaveStatus(orderId);
		// 收集所有未完成的子节点名称
		List<String> unFinishSubNodeNames = new ArrayList<>();
		// 获取QC节点名称
		Map<String, String> nodeMap = QcSubNodeName.qcSubNodeNameMap(CommonUtil.getCurrentLanguage());
		// 确保所有必须的子节点都有记录，并且是完成状态
		for (String requiredSubNode : requiredSubNodes) {
			boolean isFinished = subNodeSaveStatusVos.stream()
				.anyMatch(vo -> vo.getSubNodeName().equals(requiredSubNode) && OperationStatus.FINISH.getDec().equals(vo.getSubStatus()));
			if (!isFinished) {
				// 从NODE_MAP获取对应的值
				String nodeName = nodeMap.get(requiredSubNode);
				unFinishSubNodeNames.add(nodeName);
			}
		}
		// 检查是否有任何子节点处于未完成状态
		if (!unFinishSubNodeNames.isEmpty()) {
			// 构造未完成子节点的消息
			String joinedUnFinishSubNodeNames = String.join(", ", unFinishSubNodeNames);
			// 使用动态构造的消息抛出异常
			throw new BusinessException("agent.qcSubmission.submitQcInfo.subNodeSaveStatus.unFinish", joinedUnFinishSubNodeNames);
		}
	}

}

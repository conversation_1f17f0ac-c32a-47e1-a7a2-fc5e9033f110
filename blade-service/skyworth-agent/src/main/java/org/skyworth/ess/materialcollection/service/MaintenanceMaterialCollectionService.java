/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.materialcollection.service;

import org.skyworth.ess.design.deviceItem.dto.DeviceItemDTO;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MaintenanceMaterialCollectionService
 * @Description 运维物料签收service
 * @Date 2024/4/17 15:31
 */
public interface MaintenanceMaterialCollectionService extends BaseService<DeviceItemEntity> {


    R<?> examineMaterialCollection(DeviceItemDTO deviceItemDTO);

    R<?> selectMaterialCollection(DeviceItemDTO deviceItemDTO);


    OrderFlowDTO setWorkFlowData(OrderFlowDTO orderFlowDTO);

    R<?> saveMaterialSignature(DeviceItemDTO deviceItemDTO);

    R<?> selectMaterialSignInfo(DeviceItemDTO deviceItemDTO);
}

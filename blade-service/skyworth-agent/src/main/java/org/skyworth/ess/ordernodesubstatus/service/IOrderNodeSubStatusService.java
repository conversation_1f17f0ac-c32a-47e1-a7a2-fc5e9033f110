/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.excel.OrderNodeSubStatusExcel;
import org.skyworth.ess.ordernodesubstatus.vo.OrderNodeSubStatusVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 订单节点子状态 服务类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface IOrderNodeSubStatusService extends BaseService<OrderNodeSubStatusEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param orderNodeSubStatus
	 * @return
	 */
	IPage<OrderNodeSubStatusVO> selectOrderNodeSubStatusPage(IPage<OrderNodeSubStatusVO> page, OrderNodeSubStatusVO orderNodeSubStatus);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<OrderNodeSubStatusExcel> exportOrderNodeSubStatus(Wrapper<OrderNodeSubStatusEntity> queryWrapper);

	List<OrderNodeSubStatusEntity> getNodeByOrderId(Long orderId,String businessType);

	Map<String,Object> getWorkFlowInfoByOrderId(long orderId);

	void saveOrderNodeSubStatus(Long orderId, String businessType, String nodeName, String subNodeName, String subStatus);

	Object queryOrderNodeSubStatusList(Long orderId, String nodeName);

	int deleteByOrderId(long orderId,String nodeName, String bizType);


}

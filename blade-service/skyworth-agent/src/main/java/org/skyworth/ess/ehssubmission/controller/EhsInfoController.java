/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ehssubmission.ehsinfo.dto.EhsInfoDTO;
import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.excel.EhsInfoExcel;
import org.skyworth.ess.ehssubmission.ehsinfo.service.IEhsInfoService;
import org.skyworth.ess.ehssubmission.ehsinfo.vo.EhsInfoVO;
import org.skyworth.ess.ehssubmission.ehsinfo.wrapper.EhsInfoWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * ehs信息 控制器
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-ehs/info")
@Api(value = "ehs信息", tags = "ehs信息接口")
public class EhsInfoController extends BladeController {

	private final IEhsInfoService infoService;

	//private final EhsRejectJob ehsRejectJob;

	/**
	 * ehs信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入info")
	public R<EhsInfoVO> detail(EhsInfoEntity info) {
		EhsInfoEntity detail = infoService.getOne(Condition.getQueryWrapper(info));
		return R.data(EhsInfoWrapper.build().entityVO(detail));
	}

	/**
	 * ehs信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入info")
	public R<IPage<EhsInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> info, Query query) {
		IPage<EhsInfoEntity> pages = infoService.page(Condition.getPage(query), Condition.getQueryWrapper(info, EhsInfoEntity.class));
		return R.data(EhsInfoWrapper.build().pageVO(pages));
	}

	/**
	 * ehs信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入info")
	public R<IPage<EhsInfoVO>> page(EhsInfoVO info, Query query) {
		IPage<EhsInfoVO> pages = infoService.selectInfoPage(Condition.getPage(query), info);
		return R.data(pages);
	}

	/**
	 * ehs信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入info")
	public R save(@Valid @RequestBody EhsInfoEntity info) {
		return R.status(infoService.save(info));
	}

	/**
	 * ehs信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入info")
	public R update(@Valid @RequestBody EhsInfoEntity info) {
		return R.status(infoService.updateById(info));
	}

	/**
	 * ehs信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入info")
	public R submit(@Valid @RequestBody EhsInfoEntity info) {
		return R.status(infoService.saveOrUpdate(info));
	}

	/**
	 * ehs信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(infoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 查询订单信息ehs基本信息和相关工具设备信息
	 */
	@PostMapping("/selectEhsInfo")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询订单信息ehs基本信息和相关工具设备信息", notes = "传入infoDTO")
	public R<?> selectEhsInfo(@RequestBody EhsInfoDTO infoDTO) {
		return infoService.selectEhsInfo(infoDTO);
	}


	/**
	 * 新增ehs基本信息和相关工具设备信息
	 */
	@PostMapping("/insertEhsInfo")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "新增ehs基本信息和相关工具设备信息", notes = "传入infoDTO")
	public R<?> insertEhsInfo(@RequestBody EhsInfoDTO infoDTO) {
		return infoService.insertEhsInfo(infoDTO);
	}


	/**
	 * 提交ehs信息进行审核
	 */
	@PostMapping("/submitEhsInfoExamine")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "提交ehs信息进行审核", notes = "传入infoDTO")
	public R<?> submitEhsInfoExamine(@RequestBody @Valid EhsInfoDTO infoDTO) {
		return infoService.submitEhsInfoExamine(infoDTO);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-info")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导出数据", notes = "传入info")
	public void exportInfo(@ApiIgnore @RequestParam Map<String, Object> info, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<EhsInfoEntity> queryWrapper = Condition.getQueryWrapper(info, EhsInfoEntity.class);
		queryWrapper.lambda().eq(EhsInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<EhsInfoExcel> list = infoService.exportInfo(queryWrapper);
		ExcelUtil.export(response, "ehs信息数据" + DateUtil.time(), "ehs信息数据表", list, EhsInfoExcel.class);
	}

}

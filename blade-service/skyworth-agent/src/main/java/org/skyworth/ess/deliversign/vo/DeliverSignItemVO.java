package org.skyworth.ess.deliversign.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.valid.ValidGroups;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DeliverSignItemVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotBlank(message = "{agent.device.item.sign.itemCode.notNull}",groups = {ValidGroups.AddGroup.class, ValidGroups.EditGroup.class})
    private String itemCode;
    @NotBlank(message = "{agent.device.item.sign.remark.notNull}",groups = ValidGroups.AddGroup.class)
    private String remark;
    @NotNull(message = "{agent.device.item.sign.orderId.notNull}",groups = {ValidGroups.AddGroup.class, ValidGroups.EditGroup.class})
    private Long orderId;
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity;
import org.skyworth.ess.ehssubmission.toolsmachinery.excel.ToolsMachineryExcel;
import org.skyworth.ess.ehssubmission.toolsmachinery.service.IToolsMachineryService;
import org.skyworth.ess.ehssubmission.toolsmachinery.vo.ToolsMachineryVO;
import org.skyworth.ess.ehssubmission.toolsmachinery.wrapper.ToolsMachineryWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * ehs相关工具设备 控制器
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-toolsMachinery/toolsMachinery")
@Api(value = "ehs相关工具设备", tags = "ehs相关工具设备接口")
public class ToolsMachineryController extends BladeController {

	private final IToolsMachineryService toolsMachineryService;

	/**
	 * ehs相关工具设备 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入toolsMachinery")
	public R<ToolsMachineryVO> detail(ToolsMachineryEntity toolsMachinery) {
		ToolsMachineryEntity detail = toolsMachineryService.getOne(Condition.getQueryWrapper(toolsMachinery));
		return R.data(ToolsMachineryWrapper.build().entityVO(detail));
	}
	/**
	 * ehs相关工具设备 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入toolsMachinery")
	public R<IPage<ToolsMachineryVO>> list(@ApiIgnore @RequestParam Map<String, Object> toolsMachinery, Query query) {
		IPage<ToolsMachineryEntity> pages = toolsMachineryService.page(Condition.getPage(query), Condition.getQueryWrapper(toolsMachinery, ToolsMachineryEntity.class));
		return R.data(ToolsMachineryWrapper.build().pageVO(pages));
	}

	/**
	 * ehs相关工具设备 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入toolsMachinery")
	public R<IPage<ToolsMachineryVO>> page(ToolsMachineryVO toolsMachinery, Query query) {
		IPage<ToolsMachineryVO> pages = toolsMachineryService.selectToolsMachineryPage(Condition.getPage(query), toolsMachinery);
		return R.data(pages);
	}

	/**
	 * ehs相关工具设备 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入toolsMachinery")
	public R save(@Valid @RequestBody ToolsMachineryEntity toolsMachinery) {
		return R.status(toolsMachineryService.save(toolsMachinery));
	}

	/**
	 * ehs相关工具设备 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入toolsMachinery")
	public R update(@Valid @RequestBody ToolsMachineryEntity toolsMachinery) {
		return R.status(toolsMachineryService.updateById(toolsMachinery));
	}

	/**
	 * ehs相关工具设备 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入toolsMachinery")
	public R submit(@Valid @RequestBody ToolsMachineryEntity toolsMachinery) {
		return R.status(toolsMachineryService.saveOrUpdate(toolsMachinery));
	}

	/**
	 * ehs相关工具设备 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(toolsMachineryService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-toolsMachinery")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入toolsMachinery")
	public void exportToolsMachinery(@ApiIgnore @RequestParam Map<String, Object> toolsMachinery, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<ToolsMachineryEntity> queryWrapper = Condition.getQueryWrapper(toolsMachinery, ToolsMachineryEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(ToolsMachinery::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(ToolsMachineryEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<ToolsMachineryExcel> list = toolsMachineryService.exportToolsMachinery(queryWrapper);
		ExcelUtil.export(response, "ehs相关工具设备数据" + DateUtil.time(), "ehs相关工具设备数据表", list, ToolsMachineryExcel.class);
	}

}

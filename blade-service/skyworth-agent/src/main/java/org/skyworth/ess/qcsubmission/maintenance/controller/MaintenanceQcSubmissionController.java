package org.skyworth.ess.qcsubmission.maintenance.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcInfoDTOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcShowDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.SubmitQcInfoRequestDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.service.IElectricalComponentsService;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.SubNodeSaveStatusVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MaintenanceQcSubmissionController
 * @Description 运维QC提交控制器
 * @Date 2024/4/17 10:24
 */
@RestController
@AllArgsConstructor
@RequestMapping("maintenanceQc/submission")
@Api(value = "19-运维QC", tags = "运维管理流程相关接口")
public class MaintenanceQcSubmissionController extends BladeController {

    private final IElectricalComponentsService electricalComponentsService;


    /**
     * 获取QC基础信息
     */
    @GetMapping("/getBasicQcInfo")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "19-运维QC-QC基础信息", notes = "传入orderId")
    @ResponseStatus(HttpStatus.OK)
    public R<QcInfoVO> getBasicQcInfo(String orderId) {
        QcInfoVO qcInfoVO = electricalComponentsService.getMaintenanceBasicQcInfo(orderId);
        return R.data(qcInfoVO);
    }

    /**
     * QC详情分段展示
     */
    @GetMapping("/getQcInfoDetail")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "19-运维QC-QC详情分段展示", notes = "传入orderId")
    public R<QcInfoVOs> getQcInfoDetail(QcShowDTO qcShowDTO) {
        QcInfoVOs qcInfoVoS = electricalComponentsService.getQcInfoDetail(qcShowDTO);
        return R.data(qcInfoVoS);
    }

    /**
     * QC保存与修改
     */
    @PostMapping("/saveOrUpdateQcInfo")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "19-运维QC-qc数据保存", notes = "传入qcInfoDTOs")
    public R<Boolean> saveOrUpdateQcInfo(@RequestBody QcInfoDTOs qcInfoDtos) {
        return R.status(electricalComponentsService.saveOrUpdateMaintenanceQcInfo(qcInfoDtos));
    }

    /**
     * 查询QC子节点状态列表
     */
    @GetMapping("/listSubNodeSaveStatus")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "19-运维QC-获取QC小节点保存状态", notes = "传入订单id")
    public R<List<SubNodeSaveStatusVO>> listSubNodeSaveStatus(@RequestParam Long orderId) {
        List<SubNodeSaveStatusVO> listSubNodeSaveStatusVO = electricalComponentsService.listSubNodeSaveStatus(orderId);
        return R.data(listSubNodeSaveStatusVO);
    }

    /**
     * QC提交
     */
    @PostMapping("/submitQcInfo")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "19-运维QC-QC提交", notes = "传入orderFlowDTO")
    public R<Boolean> submitQcInfo(@RequestBody SubmitQcInfoRequestDTO requestDTO) {
        Boolean flag = electricalComponentsService.submitQcInfoMaintenance(requestDTO);
        return R.status(flag);
    }


}

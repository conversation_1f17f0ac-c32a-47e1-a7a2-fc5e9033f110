/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.paymentconfirmation.quoteInfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备报价信息 实体类
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@TableName("device_quote_info")
@ApiModel(value = "QuoteInfo对象", description = "设备报价信息")
@EqualsAndHashCode(callSuper = true)
public class QuoteInfoEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;
	/**
	 * 报价附件key
	 */
	@ApiModelProperty(value = "报价附件key")
	private Long quoteDocBizKey;

	@ApiModelProperty(value = "收货姓名")
	private String receiptName;

	@ApiModelProperty(value = "收货电话")
	private String receiptPhone;

	@ApiModelProperty(value = "收货地址")
	private String receiptAddress;
	/**
	 * 收货人签名图片key
	 */
	private Long receiptUserSignImgBizKey;
	// 指派施工的安装日期
	private Date constructionDate;
	/**
	 * 收款证明文档key
	 */
//	@ApiModelProperty(value = "收款证明文档key")
//	private Long paymentConfirmDocBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 抄送人
	 */
//	@ApiModelProperty(value = "抄送人")
//	private String ccMail;
	/**
	 * 私密抄送人
	 */
//	@ApiModelProperty(value = "私密抄送人")
//	private String bccMail;
	/**
	 * 付款确认是否发送邮件 1:是；0：否
	 */
//	@ApiModelProperty(value = "付款确认是否发送邮件 1:是；0：否")
//	private Integer paymentConfirmSendMail;
	/**
	 * 付款确认邮件抄送人，多个邮箱";"分割，最多5个
	 */
//	@ApiModelProperty(value = "付款确认邮件抄送人，多个邮箱\";\"分割，最多5个")
//	private String paymentConfirmMailTo;
	/**
	 * 付款确认邮件密送人，多个邮箱";"分割，最多5个
	 */
//	@ApiModelProperty(value = "付款确认邮件密送人，多个邮箱\";\"分割，最多5个")
//	private String paymentConfirmMailBcc;
	/**
	 * 付款确认邮件内容
	 */
//	@ApiModelProperty(value = "付款确认邮件内容")
//	private String paymentConfirmMailContent;
}

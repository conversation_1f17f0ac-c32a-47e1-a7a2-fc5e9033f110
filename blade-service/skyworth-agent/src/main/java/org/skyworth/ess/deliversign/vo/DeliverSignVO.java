package org.skyworth.ess.deliversign.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class DeliverSignVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private OrderFlowDTO orderFlowDTO;
    // 发货 、 签收 提交的 物料信息
    private List<DeliverSignItemVO> deliverSignItemVOList;
    // 签名图片信息
    private Long receiptUserSignImgBizKey;

    //附件信息
    private SkyWorthFileEntity skyWorthFileEntity;
}

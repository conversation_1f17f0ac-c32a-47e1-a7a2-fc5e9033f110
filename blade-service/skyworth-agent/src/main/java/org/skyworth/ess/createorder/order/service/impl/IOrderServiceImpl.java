/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.service.impl;


import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.constant.OrderTypeEnum;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.excel.OrderExcel;
import org.skyworth.ess.createorder.order.mapper.OrderMapper;
import org.skyworth.ess.createorder.order.service.IMaintOrderInstallRelatedService;
import org.skyworth.ess.createorder.order.service.IOrderCreateService;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.MaintOrderVO;
import org.skyworth.ess.createorder.order.vo.MaintRelatedVO;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.entity.*;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */

@Slf4j
@Service
public class IOrderServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> implements IOrderService {

	@Autowired
	private IDictBizClient dictBizClient;
	@Autowired
	private IOrderWorkFlowService iOrderWorkFlowService;
	@Autowired
	private IAgentCompanyInfoService iAgentCompanyInfoService;
	@Autowired
	private IAttachmentInfoClient attachmentInfoService;
	@Autowired
	private IUserSearchClient userSearchClient;
	@Autowired
	private IAgentCompanyInfoService companyInfoService;
	@Autowired
	private IAdditionalInfoService iAdditionalInfoService;
	@Autowired
	private ISysClient iSysClient;
	@Autowired
	private IMaintOrderInstallRelatedService orderInstallRelatedMaintService;
	//代理商编码
	private static final String BUSINESS_MANAGER_ROLE_CODE = "018";
	@Autowired
	private OrderCreateFactory orderCreateFactory;
	@Lazy
	@Autowired
	private ICocMaintenanceInfoService cocMaintenanceInfoService;
	@Override
	public IPage<OrderVO> selectOrderPage(IPage<OrderVO> page, OrderVO order) {
		BladeUser user = AuthUtil.getUser();
		if (user.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有订单
			if (roleInnerFlag != null && roleInnerFlag) {
				order.setRoleType(CommonConstant.ROLE_TYPE_INNER);
			} else {
				order.setRoleType(CommonConstant.ROLE_TYPE_OUT);
				// 如果为外部角色，
				// 代理商，1、根据登录人对应的部门（代理商部门），对应订单表中的创建部门（代理商部门）； 2、登录人还有可能是安装商。  取2者交集
				// 安装商，无部门，判断登录人是否在 订单人员关系中，如果存在，则能查看相应订单
				if (StringUtil.isNotBlank(user.getDeptId()) && !CommonConstant.DEFAULT_VALUE_MINUS_ONE.equals(user.getDeptId())) {
					// 代理商
					order.setUserDeptId(user.getDeptId());
					order.setInstallUserId(user.getUserId());
					order.setAgentOrInstallType("agent");
				} else {
					// 安装商
					order.setInstallUserId(user.getUserId());
					order.setAgentOrInstallType("install");
				}
			}
			// orderEntity为空或者orderType如果为空，则默认为guardian
			String orderType;
			OrderEntity orderEntity = order.getOrderEntity() == null ? new OrderEntity() : order.getOrderEntity();
			if (StringUtil.isBlank(orderEntity.getOrderType())) {
				orderEntity.setOrderType(OrderTypeEnum.GUARDIAN.getOrderType());
				orderType = OrderTypeEnum.GUARDIAN.getOrderType();
				order.setOrderEntity(orderEntity);
            } else {
                orderType = orderEntity.getOrderType();
            }
			List<OrderVO> orderVOS = baseMapper.selectOrderPage(page, order);
			if (CollectionUtil.isNotEmpty(orderVOS)) {
				// 建站地址
				this.setSiteDetailAddress(orderVOS);
				String currentLanguage = CommonUtil.getCurrentLanguage();
				List<DictBiz> bizList = new ArrayList<>();
				if (OrderTypeEnum.GUARDIAN.getOrderType().equals(orderType)) {
					bizList = dictBizClient.getListByLang(DictBizCodeEnum.WF_SCHEDULE.getDictCode(), currentLanguage).getData().stream().filter(dictBiz -> CommonConstant.AGENT_TENANT_ID.equals(dictBiz.getTenantId())).collect(Collectors.toList());
				} else if (OrderTypeEnum.EPC_MAINTENANCE.getOrderType().equals(orderType)) {
					bizList = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_MAINT_WORKFLOW_NODE_CODE.getDictCode(), currentLanguage).getData().stream().filter(dictBiz -> CommonConstant.AGENT_TENANT_ID.equals(dictBiz.getTenantId())).collect(Collectors.toList());
				}
				Map<String, String> bizMap = bizList.stream().collect(HashMap::new, (map, dictBiz) -> map.put(dictBiz.getDictKey(), dictBiz.getDictValue()), HashMap::putAll);
				orderVOS.forEach(p -> {
					p.setWfCurrentStatusName(bizMap.get(p.getWfCurrentStatus()));
				});
				// 业务经理和订单创建人
				this.setUserName(orderVOS);
			}
			return page.setRecords(orderVOS);
		}
		return page.setRecords(new ArrayList<>());
	}

	private void setUserName(List<OrderVO> orderVoList) {
		List<Long> userIdList = orderVoList.stream().map(OrderVO::getOrderCreateUser).distinct().collect(Collectors.toList());
		List<Long> rolloutManagerIdList = orderVoList.stream().filter(p -> p.getOrderEntity().getRolloutManagerId() != null).map(p -> p.getOrderEntity().getRolloutManagerId()).distinct().collect(Collectors.toList());
		userIdList.addAll(rolloutManagerIdList);
		R<List<User>> userResult = userSearchClient.listByUserIds(userIdList);
		if (userResult.getCode() != CommonConstant.REST_FUL_RESULT_SUCCESS || CollectionUtil.isEmpty(userResult.getData())) {
			return;
		}
		Map<Long, User> userMap = userResult.getData().stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		orderVoList.forEach(p -> {
			if (userMap.containsKey(p.getOrderCreateUser())) {
				User orderUser = userMap.get(p.getOrderCreateUser());
				p.setOrderCreateUserName(orderUser.getRealName());
				p.setOrderCreateUserPhone(orderUser.getPhone());
			}
			if (userMap.containsKey(p.getOrderEntity().getRolloutManagerId())) {
				User orderUser = userMap.get(p.getOrderEntity().getRolloutManagerId());
				p.getOrderEntity().setRolloutManagerName(orderUser.getRealName());
				p.getOrderEntity().setRolloutManagerPhone(orderUser.getPhone());
			}
		});
	}


	/**
	 * @Description: 查询代理商公司
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/27 13:37
	 **/
	@Override
	public R<List<AgentCompanyInfoEntity>> selectAgencyCompany(OrderDTO orderDTO) {
		List<AgentCompanyInfoEntity> infoEntityList = new LinkedList<>();
		Boolean inOutIdentification = getInOutIdentification();
		//如果包含创维内部角色，则可查看所有代理商公司
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = getQueryWrapper(orderDTO);
		List<AgentCompanyInfoEntity> allAgentCompanyInfoList = iAgentCompanyInfoService.list(queryWrapper);
		if (Boolean.TRUE.equals(inOutIdentification)) {
			//内部标识返回全部代理商公司
			infoEntityList = allAgentCompanyInfoList;
		} else if (Boolean.FALSE.equals(inOutIdentification)) {
			//如果参数有部门id，就根据部门id查询
			if (CollectionUtils.isNotEmpty(orderDTO.getDeptIdList())) {
				//根据部门id查询是否有匹配的代理商公司
				queryWrapper.in(AgentCompanyInfoEntity::getDeptId, orderDTO.getDeptIdList());
				infoEntityList = iAgentCompanyInfoService.list(queryWrapper);
			} else {
				return R.data(infoEntityList);
			}
		}
		return R.data(infoEntityList);
	}


	/**
	 * @Description: 代理商下交付经理用户信息
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/27 15:13
	 **/
	@Override
	public R<?> selectDeliveryManager(OrderDTO orderDTO) {
		if (ObjectUtils.isEmpty(orderDTO)) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.orderDTO.notNull");
		}
		//代理商id
		if (ObjectUtils.isEmpty(orderDTO.getAgentId())) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.agentId.notNull");
		}
		List<User> resultUserList = new LinkedList<>();
		//根据代理商id查询代理商部门id
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getById(orderDTO.getAgentId());
		if (ObjectUtils.isEmpty(agentCompanyInfoEntity)) {
			return R.data(resultUserList);
		}
		//获取代理商部门id
		Long deptId = agentCompanyInfoEntity.getDeptId();
		if (ObjectUtils.isEmpty(deptId)) {
			return R.data(resultUserList);
		}
		//根据角色编码查询角色id
		R<Role> roleInfoByCodeResult = iSysClient.getRoleInfoByCode(BUSINESS_MANAGER_ROLE_CODE);
		if (!roleInfoByCodeResult.isSuccess() || ObjectUtils.isEmpty(roleInfoByCodeResult.getData())) {
			return R.data(resultUserList);
		}
		//根据角色id查询该角色下用户列表
		R<List<User>> userListByRoleId = userSearchClient.listByRole(String.valueOf(roleInfoByCodeResult.getData().getId()));
		// 判断查询结果是否为空
		if (ObjectUtils.isEmpty(userListByRoleId) || ObjectUtils.isEmpty(userListByRoleId.getData())) {
			return R.data(resultUserList);
		}
		//根据部门id筛选
		resultUserList = userListByRoleId.getData().stream().filter(user -> StringUtil.isNotBlank(user.getDeptId()) && user.getDeptId().contains(String.valueOf(deptId))).collect(Collectors.toList());
		if (StringUtil.isNotBlank(orderDTO.getDeliveryManagerName()) && CollectionUtils.isNotEmpty(resultUserList)) {
			//根据交付经理名称筛选
			resultUserList = resultUserList.stream().filter(user -> user.getRealName().toLowerCase().contains(orderDTO.getDeliveryManagerName().toLowerCase())).collect(Collectors.toList());
		}
		return R.data(resultUserList);
	}


	/**
	 * @Description: 新增订单
	 * @Param: [order]
	 * @Return: boolean
	 * @Author: baixu
	 * @Date: 2023/11/24 14:14
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> insertOrder(OrderEntity order) {
		IOrderCreateService orderCreateService = orderCreateFactory.getOrderCreateService(order.getOrderType());
		orderCreateService.saveOrder(() -> order);
		return R.status(true);
	}

	/**
	 * @Description: 查询订单公共信息
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/24 15:59
	 **/
	@Override
	public R<?> selectBaseOrderInfo(OrderEntity order) {
		//订单参数校验
		orderParamValid(order);
		OrderVO orderVO = new OrderVO();
		//根据订单id查询订单公共信息
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, order.getId());
		orderEntityLambdaQueryWrapper.select(OrderEntity::getId, OrderEntity::getOrderNumber, OrderEntity::getUpdateTime, OrderEntity::getCreateTime, OrderEntity::getDistributorId, OrderEntity::getRolloutManagerId);
		OrderEntity orderEntity = this.getOne(orderEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderEntity)) {
			return R.data(orderVO);
		}
		this.setNameInfo(orderEntity);
		orderVO.setOrderEntity(orderEntity);
		//查询订单进度
		LambdaQueryWrapper<OrderWorkFlowEntity> workFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		workFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderEntity.getId());
		OrderWorkFlowEntity orderWorkFlowEntity = iOrderWorkFlowService.getOne(workFlowEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderWorkFlowEntity)) {
			return R.data(orderVO);
		}
		//订单进展
		orderVO.setWfCurrentStatus(orderWorkFlowEntity.getWfCurrentStatus());
		// 设置taskId
		orderVO.setTaskId(orderWorkFlowEntity.getTaskId());
		//流程实例id
		orderVO.setWfInstanceId(orderWorkFlowEntity.getWfInstanceId());
		return R.data(orderVO);
	}

	private void setNameInfo(OrderEntity orderEntity) {
		//代理商公司字典转换
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(AgentCompanyInfoEntity::getId, orderEntity.getDistributorId());
		AgentCompanyInfoEntity agentCompany = companyInfoService.getOne(queryWrapper);
		if (ObjectUtils.isNotEmpty(agentCompany)) {
			orderEntity.setDistributorName(agentCompany.getCompanyName());
		}
		//交付经理名称字典转换
		R<List<User>> userListResultInfo = userSearchClient.listByUser(String.valueOf(orderEntity.getRolloutManagerId()));
		if (ObjectUtils.isNotEmpty(userListResultInfo) && CollectionUtils.isNotEmpty(userListResultInfo.getData())) {
			//用户id是唯一的 这里取第一个
			orderEntity.setRolloutManagerName(userListResultInfo.getData().get(0).getRealName());
		}
	}


	/**
	 * @Description: 查询订单基本信息
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/24 17:20
	 **/
	@Override
	public R<?> selectBasicOrderInfo(OrderEntity order) {
		//订单参数校验
		orderParamValid(order);
		//查询订单信息
		OrderVO orderVO = new OrderVO();
		OrderEntity orderEntity = this.getById(order.getId());
		if (ObjectUtils.isEmpty(orderEntity)) {
			return R.data(orderVO);
		}
		List<CocMaintenanceInfoEntity> cocList = cocMaintenanceInfoService.list(Wrappers.<CocMaintenanceInfoEntity>lambdaQuery()
				.eq(CocMaintenanceInfoEntity::getOrderId, order.getId()));
		if(CollectionUtil.isNotEmpty(cocList)) {
			CocMaintenanceInfoEntity cocMaintenanceInfoEntity = cocList.get(0);
			String warrantyDate = cocMaintenanceInfoEntity.getWarrantyDate();
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
			try {
			if(StringUtil.isNotBlank(warrantyDate)) {
				orderVO.setWarrantyExpirationDate(simpleDateFormat.parse(warrantyDate));
			}
//			String temporaryCocStartDate = cocMaintenanceInfoEntity.getTemporaryCocStartDate();
//			if(StringUtil.isNotBlank(temporaryCocStartDate)) {
//				orderVO.setCocCreationDate(simpleDateFormat.parse(temporaryCocStartDate));
//			}
			} catch (ParseException e) {
				log.error("get coc date is error");
			}
//			orderVO.setCocCreationDateImgBizKey(cocMaintenanceInfoEntity.getTemporaryCocImgBizKey());
		}
		//查询字典省市区字典并赋值字典名称
		orderEntity = findRegionInfoByCode(orderEntity);
		//查询项目类型字典
		Map<String, String> projectTypeDictMap = getDictBizListByName(DictBizCodeEnum.AGENT_PROJECT_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(projectTypeDictMap) && ObjectUtils.isNotEmpty(projectTypeDictMap.get(orderEntity.getProjectType()))) {
			orderEntity.setProjectTypeName(projectTypeDictMap.get(orderEntity.getProjectType()));
		}
		// 设置维保信息
		this.setMaintInfo(orderEntity,orderVO,projectTypeDictMap);
		//根据附件key查询附件信息
		this.setNameInfo(orderEntity);
		List<Long> businessIdList = new ArrayList<>();
//		businessIdList.add(orderEntity.getAdditionalDocBizKey());
		businessIdList.add(orderVO.getCocCreationDateImgBizKey());
//		businessIdList.add(orderEntity.getMaintProblemDescriptionImgBizKey());
//		businessIdList.add(orderEntity.getMaintSolutionImgBizKey());
		List<Long> bizKeyIsNotNullList = businessIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
		if(CollectionUtil.isNotEmpty(bizKeyIsNotNullList)) {
			SkyWorthFileEntity skyWorthFileEntity = getFileAttachmentInfo(businessIdList);
			if (ObjectUtils.isNotEmpty(skyWorthFileEntity)) {
				//附件信息
				orderVO.setAttachmentMap(skyWorthFileEntity.getAttachmentMap());
				//附件描述
				orderVO.setImgDescViewMap(skyWorthFileEntity.getImgDescViewMap());
			}
		}
		orderVO.setOrderEntity(orderEntity);
		return R.data(orderVO);
	}

	private void setMaintInfo(OrderEntity orderEntity,OrderVO orderVO,Map<String, String> projectTypeDictMap) {
		if(OrderTypeEnum.GUARDIAN.getOrderType().equals(orderEntity.getOrderType()) || StringUtils.isEmpty(orderEntity.getOrderType())) {
			return;
		}
		// 维保部分信息从原订单获取
		MaintRelatedVO maintRelatedVO = orderInstallRelatedMaintService.queryInstallOrderByMaintId(orderEntity.getId());
		if (MapUtil.isNotEmpty(projectTypeDictMap) && ObjectUtils.isNotEmpty(projectTypeDictMap.get(maintRelatedVO.getProjectType()))) {
			orderEntity.setProjectTypeName(projectTypeDictMap.get(maintRelatedVO.getProjectType()));
		}
		orderEntity.setProjectType(maintRelatedVO.getProjectType());
//		orderEntity.setInstallReason(maintRelatedVO.getInstallReason());
		orderEntity.setInstallBudgets(maintRelatedVO.getInstallBudgets());
//		orderEntity.setSecondContactName(maintRelatedVO.getSecondContactName());
//		orderEntity.setSecondContactPhone(maintRelatedVO.getSecondContactPhone());
//		orderEntity.setCustomerName(maintRelatedVO.getCustomerName());
		orderVO.setWarrantyExpirationDate(maintRelatedVO.getWarrantyExpirationDate());
		orderVO.setCocCreationDate(maintRelatedVO.getCocCreationDate());
		orderVO.setCocCreationDateImgBizKey(maintRelatedVO.getCocCreationDateImgBizKey());
		orderEntity.setOrderInstallId(maintRelatedVO.getOrderInstallId());
//		if(StringUtils.isNotEmpty(orderEntity.getMaintFeeType())) {
//			R<List<DictBiz>> listAllLang = dictBizClient.getListAllLang(DictBizCodeEnum.AGENT_MAINT_FEE_TYPE.getDictCode());
//			List<DictBiz> data = listAllLang.getData();
//			if(CollectionUtil.isNotEmpty(data)) {
//				String currentLanguage = CommonUtil.getCurrentLanguage();
//				Map<String,String> dictMap = data.stream().filter(p -> currentLanguage.equals(p.getLanguage())).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
//				orderEntity.setMaintFeeTypeName(dictMap.get(orderEntity.getMaintFeeType()));
//			}
//		}

	}

	/**
	 * @Description: 查询字典省市区字典并赋值字典名称
	 * @Param: [orderEntity]
	 * @Return: org.skyworth.ess.createorder.order.entity.OrderEntity
	 * @Author: baixu
	 * @Date: 2023/12/7 14:30
	 **/
	@Override
	public OrderEntity findRegionInfoByCode(OrderEntity orderEntity) {
		//查询字典省市区字典
		List<String> regionIdList = Arrays.asList(orderEntity.getSiteCountryCode(), orderEntity.getSiteProvinceCode(),
				orderEntity.getSiteCityCode(),orderEntity.getSiteCountyCode());
		R<List<Region>> regionListResult = iSysClient.getRegionList(regionIdList);
		log.info("maintenanceDetail get region name result : {}", regionListResult);
		if (ObjectUtils.isNotEmpty(regionListResult) && CollectionUtils.isNotEmpty(regionListResult.getData())) {
			//转换地域字典信息
			setRegionNameByCode(regionListResult, orderEntity);
		}
		return orderEntity;
	}


	/**
	 * @Description: 转换地域字典信息
	 * @Param: [regionListResult, orderEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/30 14:52
	 **/
	private void setRegionNameByCode(R<List<Region>> regionListResult, OrderEntity orderEntity) {
		List<Region> regionList = regionListResult.getData();
		//转换国家字典
		Map<String, String> regionMap = regionList.stream().collect(Collectors.toMap(Region::getCode, Region::getName));
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteCountryCode()))) {
			orderEntity.setSiteCountryName(regionMap.get(orderEntity.getSiteCountryCode()));
		}
		//转换省字典
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteProvinceCode()))) {
			orderEntity.setSiteProvinceName(regionMap.get(orderEntity.getSiteProvinceCode()));
		}
		//转换城市字典
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteCityCode()))) {
			orderEntity.setSiteCityName(regionMap.get(orderEntity.getSiteCityCode()));
		}
		//转换区县字典
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteCountyCode()))) {
			orderEntity.setSiteCountyName(regionMap.get(orderEntity.getSiteCountyCode()));
		}
	}


	/**
	 * @Description: 根据字典类别名称获取字典
	 * @Param: [dictName]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/27 17:18
	 **/
	@Override
	public Map<String, String> getDictBizListByName(String type) {
		R<List<DictBiz>> deviceBatteryMatch = dictBizClient.getListByLang(type, CommonUtil.getCurrentLanguage());
		if (ObjectUtils.isEmpty(deviceBatteryMatch) || CollectionUtils.isEmpty(deviceBatteryMatch.getData())) {
			return new HashMap<>();
		}
		return deviceBatteryMatch.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (v1, v2) -> v2));
	}


	/**
	 * @Description: 构建代理商公司查询条件
	 * @Param: [orderDTO]
	 * @Return: com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<org.skyworth.ess.company.entity.AgentCompanyInfoEntity>
	 * @Author: baixu
	 * @Date: 2023/12/1 18:14
	 **/
	private LambdaQueryWrapper<AgentCompanyInfoEntity> getQueryWrapper(OrderDTO orderDTO) {
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		//根据公司名称模糊查询
		if (StringUtil.isNotBlank(orderDTO.getCompanyName())) {
			agentCompanyInfoEntityLambdaQueryWrapper.likeRight(AgentCompanyInfoEntity::getCompanyName, orderDTO.getCompanyName());
		}
		return agentCompanyInfoEntityLambdaQueryWrapper;
	}


	/**
	 * @Description: 获取用户角色内外部标识
	 * @Param: []
	 * @Return: java.lang.Boolean
	 * @Author: baixu
	 * @Date: 2023/12/19 10:58
	 **/
	private Boolean getInOutIdentification() {
		BladeUser user = AuthUtil.getUser();
		if (ObjectUtils.isNotEmpty(user.getDetail()) && ObjectUtils.isNotEmpty(user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG))) {
			return (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
		}
		return null;
	}


	/**
	 * @Description: 订单参数校验
	 * @Param: [order]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 16:21
	 **/
	private void orderParamValid(OrderEntity order) {
		if (ObjectUtils.isEmpty(order)) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.orderDTO.notNull");
		}
		//订单id
		if (ObjectUtils.isEmpty(order.getId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
	}


	/**
	 * @Description: 获取附件信息和相关描述信息
	 * @Param: [orderEntity, orderVO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/30 18:44
	 **/
	@Override
	public SkyWorthFileEntity getFileAttachmentInfo(List<Long> businessIdList) {
		SkyWorthFileEntity skyWorthFileEntity = new SkyWorthFileEntity();
		//根据businessId查询附件信息
		R<Map<Long, List<AttachmentInfoEntity>>> byBusinessIdsResult = attachmentInfoService.findByBusinessIds(businessIdList);
		if (ObjectUtils.isNotEmpty(byBusinessIdsResult) && ObjectUtils.isNotEmpty(byBusinessIdsResult.getData())) {
			skyWorthFileEntity.setAttachmentMap(byBusinessIdsResult.getData());
			//根据businessId查询附件描述信息
			Map<Long, String> additionalMap = iAdditionalInfoService.selectAdditionalMapByBusinessIds(businessIdList);
			if (ObjectUtils.isNotEmpty(additionalMap)) {
				skyWorthFileEntity.setImgDescViewMap(additionalMap);
			}
		}
		return skyWorthFileEntity;
	}


	@Override
	public List<OrderExcel> exportOrder(Wrapper<OrderEntity> queryWrapper) {
		return baseMapper.exportOrder(queryWrapper);
	}

	// 设置订单详细地址
	private void setSiteDetailAddress(List<OrderVO> orderVOList) {
		List<String> regionCodeList = new ArrayList<>();
		for (OrderVO orderVO : orderVOList) {
			regionCodeList.add(orderVO.getOrderEntity().getSiteCountryCode());
			regionCodeList.add(orderVO.getOrderEntity().getSiteProvinceCode());
			regionCodeList.add(orderVO.getOrderEntity().getSiteCityCode());
		}
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(regionCodeNotNullList)) {
			return;
		}
		R<List<Region>> regionResult = iSysClient.getRegionList(regionCodeNotNullList);
		List<Region> regionList = regionResult.getData();
		if (CollectionUtil.isEmpty(regionList)) {
			return;
		}
		for (OrderVO order : orderVOList) {
			StringBuilder address = new StringBuilder();
			OrderEntity orderEntity = order.getOrderEntity();
			for (Region region : regionList) {
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteCountryCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteCountryName(region.getName());
				}
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteProvinceCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteProvinceName(region.getName());
				}
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteCityCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteCityName(region.getName());
				}
			}
			orderEntity.setSiteDetailAddress(address.append(" ").append(orderEntity.getSiteAddress() == null ? "" : orderEntity.getSiteAddress()).toString());
		}


	}

	@Override
	public R<List<OrderVO>> maintQueryInstallOrder(OrderEntity orderEntity) {
		if(StringUtil.isBlank(orderEntity.getCustomerName())) {
			throw new BusinessException("agent.maint.customerName.notNull");
		}
		List<OrderVO> orderVO = baseMapper.maintQueryInstallOrder(orderEntity);
		//查询项目类型字典
		Map<String, String> projectTypeDictMap = getDictBizListByName(DictBizCodeEnum.AGENT_PROJECT_TYPE.getDictCode());
		for (OrderVO vo : orderVO) {
			OrderEntity temp = vo.getOrderEntity();
			temp.setProjectTypeName(projectTypeDictMap.get(temp.getProjectType()));

		}
		this.setSiteDetailAddress(orderVO);
		for (OrderVO vo : orderVO) {
			if(vo.getCocCreationDateImgBizKey() == null) {
				continue;
			}
			List<Long> bizKey = Collections.singletonList(vo.getCocCreationDateImgBizKey());
			SkyWorthFileEntity skyWorthFileEntity = getFileAttachmentInfo(bizKey);
			if (ObjectUtils.isNotEmpty(skyWorthFileEntity)) {
				//附件信息
				vo.setAttachmentMap(skyWorthFileEntity.getAttachmentMap());
				//附件描述
				vo.setImgDescViewMap(skyWorthFileEntity.getImgDescViewMap());
			}
		}
		return R.data(orderVO);
	}
	@Override
	public R<List<MaintRelatedVO>> maintQueryMaintOrderHistory(Long installOrderId) {
		OrderVO orderVO = baseMapper.queryOrderWorkFlowInfo(installOrderId);
		if(orderVO == null) {
			return R.data(new ArrayList<>());
		}
		R<List<DictBiz>> listAllLang = dictBizClient.getListAllLang(DictBizCodeEnum.AGENT_ORDER_TYPE.getDictCode());
		List<DictBiz> data = listAllLang.getData();
		Map<String, String> dictMap = new HashMap<>();
		if(CollectionUtil.isNotEmpty(data)) {
			String currentLanguage = CommonUtil.getCurrentLanguage();
			dictMap = data.stream().filter(p -> currentLanguage.equals(p.getLanguage())).collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue));
		}
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<DictBiz> bizList = dictBizClient.getListByLang(DictBizCodeEnum.WF_SCHEDULE.getDictCode(), currentLanguage).getData().stream().filter(dictBiz -> CommonConstant.AGENT_TENANT_ID.equals(dictBiz.getTenantId())).collect(Collectors.toList());
		Map<String, String> bizMap = bizList.stream().collect(HashMap::new, (map, dictBiz) -> map.put(dictBiz.getDictKey(), dictBiz.getDictValue()), HashMap::putAll);

		List<MaintRelatedVO> result = new ArrayList<>();
		MaintRelatedVO vo = new MaintRelatedVO();
		vo.setId(orderVO.getOrderEntity().getId());
		vo.setOrderNumber(orderVO.getOrderEntity().getOrderNumber());
		vo.setOrderType(orderVO.getOrderEntity().getOrderType());
		vo.setOrderTypeName(dictMap.get(orderVO.getOrderEntity().getOrderType()));
		vo.setOrderCreateTime(orderVO.getOrderCreateTime());
		vo.setWfCurrentStatus(orderVO.getWfCurrentStatus());
		vo.setWfCurrentStatusName(bizMap.get(orderVO.getWfCurrentStatus()));
		result.add(vo);
		List<MaintRelatedVO> maintRelatedVoS = orderInstallRelatedMaintService.queryMaintHistory(installOrderId);
		Map<String, String> maintBizMap = new HashMap<>();
		if(CollectionUtil.isNotEmpty(maintRelatedVoS)) {
			List<DictBiz> maintBizList = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_MAINT_WORKFLOW_NODE_CODE.getDictCode(), currentLanguage).getData().stream().filter(dictBiz -> CommonConstant.AGENT_TENANT_ID.equals(dictBiz.getTenantId())).collect(Collectors.toList());
			 maintBizMap = maintBizList.stream().collect(HashMap::new, (map, dictBiz) -> map.put(dictBiz.getDictKey(), dictBiz.getDictValue()), HashMap::putAll);

		}
		for(MaintRelatedVO maintRelatedVO : maintRelatedVoS) {
			maintRelatedVO.setOrderTypeName(dictMap.get(maintRelatedVO.getOrderType()));
			maintRelatedVO.setWfCurrentStatusName(maintBizMap.get(maintRelatedVO.getWfCurrentStatus()));
		}
		result.addAll(maintRelatedVoS);
		return R.data(result);
	}
	@Override
	public R<Boolean> maintSaveCancelOrder(MaintOrderVO maintOrderVO) {
		Long orderInstallId = maintOrderVO.getOrderEntity().getOrderInstallId();
		if(orderInstallId == null) {
			throw new BusinessException("agent.maint.orderInstallId.notNull");
		}
		IOrderCreateService orderCreateService = orderCreateFactory.getOrderCreateService(maintOrderVO.getOrderEntity().getOrderType());
		return R.data(orderCreateService.saveCancelOrder(() -> maintOrderVO));
	}
	@Override
	public R<Boolean> maintinsertOrder(MaintOrderVO maintOrderVO) {
		Long orderInstallId = maintOrderVO.getOrderEntity().getOrderInstallId();
		if(orderInstallId == null) {
			throw new BusinessException("agent.maint.orderInstallId.notNull");
		}
		IOrderCreateService orderCreateService = orderCreateFactory.getOrderCreateService(maintOrderVO.getOrderEntity().getOrderType());
		return R.data(orderCreateService.saveOrder(() -> maintOrderVO));
	}
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.installrelatedInfo.service.impl;

import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.vo.InstallRelatedInfoVO;
import org.skyworth.ess.qcsubmission.installrelatedInfo.excel.InstallRelatedInfoExcel;
import org.skyworth.ess.qcsubmission.installrelatedInfo.mapper.InstallRelatedInfoMapper;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * 安装相关信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
public class InstallRelatedInfoServiceImpl extends BaseServiceImpl<InstallRelatedInfoMapper, InstallRelatedInfoEntity> implements IInstallRelatedInfoService {

	@Override
	public IPage<InstallRelatedInfoVO> selectInstallRelatedInfoPage(IPage<InstallRelatedInfoVO> page, InstallRelatedInfoVO relatedInfo) {
		return page.setRecords(baseMapper.selectInstallRelatedInfoPage(page, relatedInfo));
	}


	@Override
	public List<InstallRelatedInfoExcel> exportInstallRelatedInfo(Wrapper<InstallRelatedInfoEntity> queryWrapper) {
		List<InstallRelatedInfoExcel> relatedInfoList = baseMapper.exportInstallRelatedInfo(queryWrapper);
		//relatedInfoList.forEach(relatedInfo -> {
		//	relatedInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, InstallRelatedInfo.getType()));
		//});
		return relatedInfoList;
	}

}

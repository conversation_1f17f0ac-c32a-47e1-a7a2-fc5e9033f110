/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.toolsmachinery.service.impl;

import org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity;
import org.skyworth.ess.ehssubmission.toolsmachinery.excel.ToolsMachineryExcel;
import org.skyworth.ess.ehssubmission.toolsmachinery.vo.ToolsMachineryVO;
import org.skyworth.ess.ehssubmission.toolsmachinery.mapper.ToolsMachineryMapper;
import org.skyworth.ess.ehssubmission.toolsmachinery.service.IToolsMachineryService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseServiceImpl;
import java.util.List;

/**
 * ehs相关工具设备 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Service
public class ToolsMachineryServiceImpl extends BaseServiceImpl<ToolsMachineryMapper, ToolsMachineryEntity> implements IToolsMachineryService {

	@Override
	public IPage<ToolsMachineryVO> selectToolsMachineryPage(IPage<ToolsMachineryVO> page, ToolsMachineryVO toolsMachinery) {
		return page.setRecords(baseMapper.selectToolsMachineryPage(page, toolsMachinery));
	}


	@Override
	public List<ToolsMachineryExcel> exportToolsMachinery(Wrapper<ToolsMachineryEntity> queryWrapper) {
		List<ToolsMachineryExcel> toolsMachineryList = baseMapper.exportToolsMachinery(queryWrapper);
		//toolsMachineryList.forEach(toolsMachinery -> {
		//	toolsMachinery.setTypeName(DictCache.getValue(DictEnum.YES_NO, ToolsMachinery.getType()));
		//});
		return toolsMachineryList;
	}

}

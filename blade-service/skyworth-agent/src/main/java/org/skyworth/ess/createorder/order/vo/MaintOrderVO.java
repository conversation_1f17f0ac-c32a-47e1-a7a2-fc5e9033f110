package org.skyworth.ess.createorder.order.vo;

import lombok.Data;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MaintOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private OrderEntity orderEntity;
    //附件信息
    private SkyWorthFileEntity skyWorthFileEntity;
}

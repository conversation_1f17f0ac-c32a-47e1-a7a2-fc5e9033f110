/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.orderworkflow.service;


import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;


/**
 * 订单表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface IReviewOrderService extends BaseService<OrderEntity> {


	R<?> examineEditOrCancelOrder(OrderDTO orderDTO);

	R<?> examineApprove(OrderFlowDTO orderFlowDTO);


	boolean setOrUpdateFileImgInfo(SkyWorthFileEntity skyWorthFileEntity);

}

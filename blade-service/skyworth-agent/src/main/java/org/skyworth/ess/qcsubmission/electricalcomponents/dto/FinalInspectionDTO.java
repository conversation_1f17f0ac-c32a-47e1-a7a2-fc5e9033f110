package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 最终检验实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/29 - 11 - 29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinalInspectionDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 门外安装完成图片业务主键
	 */
	@ApiModelProperty(value = "门外安装完成图片业务主键")
	private Long fiCompletedInstallOutImgBizKey;
	/**
	 * 门内安装完成图片业务主键
	 */
	@ApiModelProperty(value = "门内安装完成图片业务主键")
	private Long fiCompletedInstallInImgBizKey;
	/**
	 * 环境清洁图片业务主键
	 */
	@ApiModelProperty(value = "环境清洁图片业务主键")
	private Long fiEnvironCleanImgBizKey;
	/**
	 * 建议
	 */
	@ApiModelProperty(value = "建议")
	private String fiRecommendations;
	/**
	 * QVWI电视图片业务主键
	 */
	@ApiModelProperty(value = "QVWI电视图片业务主键")
	private Long fiQvwiTvImgBizKey;
	/**
	 * 是否需要COC Creation步骤
	 */
	@ApiModelProperty(value = "是否需要COC Creation步骤, 0:否, 1:是")
	private Integer fiRequiredCocCreation;
	/**
	 * 是否需要COC Creation步骤的备注
	 */
	@ApiModelProperty(value = "是否需要COC Creation步骤的备注")
	private String fiRequiredCocCreationRemark;
}

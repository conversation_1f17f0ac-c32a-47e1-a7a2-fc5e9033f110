/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.wrapper;

import org.skyworth.ess.vo.AgentCompanyVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.vo.AgentCompanyInfoVO;
import java.util.Objects;

/**
 * 代理商公司信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public class AgentCompanyInfoWrapper extends BaseEntityWrapper<AgentCompanyInfoEntity, AgentCompanyVO>  {

	public static AgentCompanyInfoWrapper build() {
		return new AgentCompanyInfoWrapper();
 	}

	@Override
	public AgentCompanyVO entityVO(AgentCompanyInfoEntity agentCompanyInfo) {
		AgentCompanyVO agentCompanyVO = Objects.requireNonNull(BeanUtil.copy(agentCompanyInfo, AgentCompanyVO.class));

		//User createUser = UserCache.getUser(agentCompanyInfo.getCreateUser());
		//User updateUser = UserCache.getUser(agentCompanyInfo.getUpdateUser());
		//agentCompanyInfoVO.setCreateUserName(createUser.getName());
		//agentCompanyInfoVO.setUpdateUserName(updateUser.getName());

		return agentCompanyVO;
	}


}

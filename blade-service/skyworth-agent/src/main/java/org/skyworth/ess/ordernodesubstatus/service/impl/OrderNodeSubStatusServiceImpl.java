/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ordernodesubstatus.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.excel.OrderNodeSubStatusExcel;
import org.skyworth.ess.ordernodesubstatus.mapper.OrderNodeSubStatusMapper;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.ordernodesubstatus.vo.ModuleFunctionalStatusVO;
import org.skyworth.ess.ordernodesubstatus.vo.OrderNodeSubStatusVO;
import org.skyworth.ess.survey.constant.HouseCategory;
import org.skyworth.ess.survey.constant.HouseModuleType;
import org.skyworth.ess.survey.constant.OperationStatus;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 订单节点子状态 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
public class OrderNodeSubStatusServiceImpl extends BaseServiceImpl<OrderNodeSubStatusMapper, OrderNodeSubStatusEntity> implements IOrderNodeSubStatusService {
	@Autowired
	private IUserClient userClient;

	@Override
	public IPage<OrderNodeSubStatusVO> selectOrderNodeSubStatusPage(IPage<OrderNodeSubStatusVO> page, OrderNodeSubStatusVO orderNodeSubStatus) {
		return page.setRecords(baseMapper.selectOrderNodeSubStatusPage(page, orderNodeSubStatus));
	}


	@Override
	public List<OrderNodeSubStatusExcel> exportOrderNodeSubStatus(Wrapper<OrderNodeSubStatusEntity> queryWrapper) {
		List<OrderNodeSubStatusExcel> orderNodeSubStatusList = baseMapper.exportOrderNodeSubStatus(queryWrapper);
		//orderNodeSubStatusList.forEach(orderNodeSubStatus -> {
		//	orderNodeSubStatus.setTypeName(DictCache.getValue(DictEnum.YES_NO, OrderNodeSubStatus.getType()));
		//});
		return orderNodeSubStatusList;
	}

	@Override
	public List<OrderNodeSubStatusEntity> getNodeByOrderId(Long orderId,String businessType) {
		return baseMapper.getNodeByOrderId(orderId,businessType);
	}

	@Override
	public Map<String, Object> getWorkFlowInfoByOrderId(long orderId) {
		return baseMapper.getWorkFlowInfoByOrderId(orderId);
	}

	@Override
	public void saveOrderNodeSubStatus(Long orderId, String businessType, String nodeName, String subNodeName, String subStatus) {
		LambdaQueryWrapper<OrderNodeSubStatusEntity> orderNodeSubStatusWrapper = new LambdaQueryWrapper<>();
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getOrderId, orderId);
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getBusinessType, businessType);
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getNodeName, nodeName);
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getSubNodeName, subNodeName);
		OrderNodeSubStatusEntity orderNodeSubStatusEntity = this.getOne(orderNodeSubStatusWrapper);
		if (orderNodeSubStatusEntity == null) {
			orderNodeSubStatusEntity = new OrderNodeSubStatusEntity();
			orderNodeSubStatusEntity.setOrderId(orderId);
			orderNodeSubStatusEntity.setBusinessType(businessType);
			orderNodeSubStatusEntity.setNodeName(nodeName);
			orderNodeSubStatusEntity.setSubNodeName(subNodeName);
			orderNodeSubStatusEntity.setSubStatus(subStatus);
			this.save(orderNodeSubStatusEntity);
		} else {
			orderNodeSubStatusEntity = new OrderNodeSubStatusEntity();
			orderNodeSubStatusEntity.setSubStatus(subStatus);
			this.update(orderNodeSubStatusEntity, orderNodeSubStatusWrapper);
		}
	}

	@Override
	public Object queryOrderNodeSubStatusList(Long orderId, String nodeName) {
		JSONObject jsonObject = new JSONObject();
		LambdaQueryWrapper<OrderNodeSubStatusEntity> orderNodeSubStatusWrapper = new LambdaQueryWrapper<>();
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getOrderId, orderId);
		orderNodeSubStatusWrapper.eq(OrderNodeSubStatusEntity::getNodeName, nodeName);
		List<OrderNodeSubStatusEntity> orderNodeSubStatusList = this.list(orderNodeSubStatusWrapper);
		List<ModuleFunctionalStatusVO> moduleFunctionalStatusList = new ArrayList<ModuleFunctionalStatusVO>();

		orderNodeSubStatusList.forEach(e -> {
			ModuleFunctionalStatusVO moduleFunctionalStatus = new ModuleFunctionalStatusVO();
			BeanUtils.copyProperties(e, moduleFunctionalStatus);
			moduleFunctionalStatusList.add(moduleFunctionalStatus);
		});

		if (HouseCategory.SURVEY.getDec().equals(nodeName)) {
			List<String> technicianModuleType = Arrays.asList(HouseModuleType.HOUSEINFO.getDec(),HouseModuleType.TECHNICIANSIGNATURE.getDec(),HouseModuleType.TECHNICIANOWNERPHOTO.getDec());
			List<String> electricianModuleType = Arrays.asList(HouseModuleType.HOUSEELECTRICAL.getDec(),HouseModuleType.HOUSEELECTRICALAPPLY.getDec(),HouseModuleType.ELECTRICIANSIGNATURE.getDec(),HouseModuleType.ELECTRICIANOWNERPHOTO.getDec());
			OrderNodeSubStatusEntity technician = getMaxSubmitDate(orderNodeSubStatusList,technicianModuleType);
			OrderNodeSubStatusEntity electrician = getMaxSubmitDate(orderNodeSubStatusList,electricianModuleType);
			if (technician != null) {
				jsonObject.put("technicianSubmitDate",technician.getCreateTime());
				R<User> technicianUser =  userClient.userInfoById(technician.getCreateUser());
				if (technicianUser != null) {
					jsonObject.put("technicianUserName", technicianUser.getData().getRealName());
				}
			} else {
				jsonObject.put("technicianSubmitDate",null);
				jsonObject.put("technicianUserName", null);
			}
			if (electrician != null) {
				jsonObject.put("electricianSubmitDate",electrician.getCreateTime());
				R<User> electricianUser =  userClient.userInfoById(electrician.getCreateUser());
				if (electricianUser != null) {
					jsonObject.put("electricianUserName", electricianUser.getData().getRealName());
				}
			} else {
				jsonObject.put("electricianSubmitDate",null);
				jsonObject.put("electricianUserName", null);
			}
			jsonObject.put("moduleFunctionalStatus",dealSurveyStatusList(moduleFunctionalStatusList));
			return jsonObject;
		} else {
			jsonObject.put("moduleFunctionalStatus",moduleFunctionalStatusList);
		}
		return jsonObject;
	}

	@Override
	public int deleteByOrderId(long orderId,String nodeName, String bizType) {
		return baseMapper.deleteByOrderId(orderId,nodeName,bizType);
	}

	private OrderNodeSubStatusEntity getMaxSubmitDate(List<OrderNodeSubStatusEntity> orderNodeSubStatusList, List<String> houseModuleType) {
		List<OrderNodeSubStatusEntity> subNodeList = new ArrayList<>();
		if (CollectionUtils.isEmpty(orderNodeSubStatusList)) {
			return null;
		}
		for (OrderNodeSubStatusEntity subNode : orderNodeSubStatusList) {
			if (houseModuleType.contains(subNode.getSubNodeName())) {
				subNodeList.add(subNode);
			}
		}
		if (subNodeList.size() != houseModuleType.size()) {
			return null;
		}

		for (OrderNodeSubStatusEntity subNode : subNodeList) {
			if (!houseModuleType.contains(subNode.getSubNodeName()) || OperationStatus.UNFINISH.getDec().equals(subNode.getSubStatus())) {
				return null;
			}
		}

		OrderNodeSubStatusEntity maxOrderNodeSubStatusEntity = null;
		int i = 1;
		for (OrderNodeSubStatusEntity orderNodeSubStatusEntity : subNodeList) {
			if (i == 1) {
				maxOrderNodeSubStatusEntity = orderNodeSubStatusEntity;
				continue;
			}
			Date cureDate = orderNodeSubStatusEntity.getCreateTime();
			if (cureDate.compareTo(maxOrderNodeSubStatusEntity.getCreateTime()) > 0) {
				maxOrderNodeSubStatusEntity = orderNodeSubStatusEntity;
			}
			i++;
		}
		return maxOrderNodeSubStatusEntity;
	}

	private List<ModuleFunctionalStatusVO> dealSurveyStatusList(List<ModuleFunctionalStatusVO> moduleFunctionalStatusList) {
		List<ModuleFunctionalStatusVO> mduleFunctionalStatus = new ArrayList<>();
		List<ModuleFunctionalStatusVO> electricianModuleStatus = new ArrayList<>();
		List<String> electricianModuleType = Arrays.asList(HouseModuleType.HOUSEELECTRICAL.getDec(), HouseModuleType.HOUSEELECTRICALAPPLY.getDec());
		if (CollectionUtils.isNotEmpty(moduleFunctionalStatusList)) {
			for (ModuleFunctionalStatusVO moduleFunctionalStatus : moduleFunctionalStatusList) {
				if (electricianModuleType.contains(moduleFunctionalStatus.getSubNodeName())) {
					electricianModuleStatus.add(moduleFunctionalStatus);
				} else {
					mduleFunctionalStatus.add(moduleFunctionalStatus);
				}
			}
		}

		if (CollectionUtils.isNotEmpty(electricianModuleStatus)) {
			boolean flg = true;
			for (ModuleFunctionalStatusVO moduleFunctionalStatus : electricianModuleStatus) {
				if (OperationStatus.UNFINISH.getDec().equals(moduleFunctionalStatus.getSubStatus())) {
					flg = false;
					break;
				}
			}
			ModuleFunctionalStatusVO moduleFunctionalStatus = new ModuleFunctionalStatusVO();
			moduleFunctionalStatus.setNodeName(HouseCategory.SURVEY.getDec());
			moduleFunctionalStatus.setSubNodeName(HouseModuleType.ELECTRICIAN.getDec());
			if (flg) {
				moduleFunctionalStatus.setSubStatus(OperationStatus.FINISH.getDec());
			} else {
				moduleFunctionalStatus.setSubStatus(OperationStatus.UNFINISH.getDec());
			}
			mduleFunctionalStatus.add(moduleFunctionalStatus);
		}
		return mduleFunctionalStatus;
	}

}

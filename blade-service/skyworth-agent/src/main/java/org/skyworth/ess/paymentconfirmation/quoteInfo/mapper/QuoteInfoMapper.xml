<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.paymentconfirmation.quoteInfo.mapper.QuoteInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="quoteInfoResultMap" type="org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="quote_doc_biz_key" property="quoteDocBizKey"/>
        <result column="receipt_name" property="receiptName"/>
        <result column="receipt_phone" property="receiptPhone"/>
        <result column="receipt_address" property="receiptAddress"/>
        <result column="receipt_user_sign_img_biz_key" property="receiptUserSignImgBizKey"/>
        <result column="construction_date" property="constructionDate"/>
<!--        <result column="to_email" property="toEmail"/>-->
<!--        <result column="content" property="content"/>-->
<!--        <result column="payment_confirm_doc_biz_key" property="paymentConfirmDocBizKey"/>-->
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
<!--        <result column="cc_mail" property="ccMail"/>-->
<!--        <result column="bcc_mail" property="bccMail"/>-->
<!--        <result column="send_email" property="sendEmail"/>-->
<!--        <result column="payment_confirm_send_mail" property="paymentConfirmSendMail"/>-->
<!--        <result column="payment_confirm_mail_to" property="paymentConfirmMailTo"/>-->
<!--        <result column="payment_confirm_mail_bcc" property="paymentConfirmMailBcc"/>-->
<!--        <result column="payment_confirm_mail_content" property="paymentConfirmMailContent"/>-->
    </resultMap>


    <select id="selectQuoteInfoPage" resultMap="quoteInfoResultMap">
        select * from device_quote_info where is_deleted = 0
    </select>


    <select id="exportQuoteInfo" resultType="org.skyworth.ess.paymentconfirmation.quoteInfo.excel.QuoteInfoExcel">
        SELECT * FROM device_quote_info ${ew.customSqlSegment}
    </select>

</mapper>

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.excel.AdditionalInfoExcel;
import org.skyworth.ess.additionalInfo.mapper.AdditionalInfoMapper;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.additionalInfo.vo.AdditionalInfoVO;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图片附加信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
public class AdditionalInfoServiceImpl extends BaseServiceImpl<AdditionalInfoMapper, AdditionalInfoEntity> implements IAdditionalInfoService {

	@Override
	public IPage<AdditionalInfoVO> selectAdditionalInfoPage(IPage<AdditionalInfoVO> page, AdditionalInfoVO additionalInfo) {
		return page.setRecords(baseMapper.selectAdditionalInfoPage(page, additionalInfo));
	}


	@Override
	public List<AdditionalInfoExcel> exportAdditionalInfo(Wrapper<AdditionalInfoEntity> queryWrapper) {
		return baseMapper.exportAdditionalInfo(queryWrapper);
	}

	@Override
	public void saveAdditionalInfoEntityList(List<AdditionalInfoEntity> additionalInfoEntityList) {
		if (CollectionUtils.isNullOrEmpty(additionalInfoEntityList)) {
			return;
		}
		List<Long> businessIds = additionalInfoEntityList.stream().map(AdditionalInfoEntity::getImgBizKey).filter(Objects::nonNull).collect(Collectors.toList());
		// 删除描述
		if (!CollectionUtils.isNullOrEmpty(businessIds)) {
			baseMapper.deleteByBusinessIds(businessIds);
		}
		// 插入数据,过滤掉描述为空的数据
		List<AdditionalInfoEntity> notNullAdditionalInfoList = additionalInfoEntityList.stream().filter(a -> StringUtils.isNotBlank(a.getImgRemark())).collect(Collectors.toList());
		if (!CollectionUtils.isNullOrEmpty(notNullAdditionalInfoList)) {
			super.saveBatch(notNullAdditionalInfoList);
		}
	}

	@Override
	public Map<Long, String> selectAdditionalMapByBusinessIds(List<Long> businessIds) {
		if (CollectionUtils.isNullOrEmpty(businessIds)) {
			return new HashMap<>(0);
		}
		List<AdditionalInfoEntity> additionalInfoEntityList = super.list(Wrappers.<AdditionalInfoEntity>lambdaQuery().in(AdditionalInfoEntity::getImgBizKey, businessIds).eq(AdditionalInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		if (CollectionUtils.isNullOrEmpty(additionalInfoEntityList)) {
			return new HashMap<>(0);
		}
		return additionalInfoEntityList.stream().collect(Collectors.toMap(AdditionalInfoEntity::getImgBizKey, AdditionalInfoEntity::getImgRemark, (a, b) -> a));
	}

}

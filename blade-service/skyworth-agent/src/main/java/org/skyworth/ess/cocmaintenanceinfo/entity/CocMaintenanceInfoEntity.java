/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import java.util.List;

/**
 * coc和维护信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("coc_maintenance_info")
@ApiModel(value = "cocMaintenanceInfo对象", description = "业主和维保相关")
@EqualsAndHashCode(callSuper = true)
public class CocMaintenanceInfoEntity extends SkyWorthFileEntity {

	/**
	 * 订单
	 */
	@ApiModelProperty(value = "订单")
	private Long orderId;
	/**
	 * 房东签名图片key
	 */
	@ApiModelProperty(value = "房东签名图片key")
	private Long facLandlordSign;

	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	private Long facPlantId;
	/**
	 * 站点名称
	 */
	@ApiModelProperty(value = "站点名称")
	private String facPlantName;
	/**
	 * fac技术工程师签名图片key
	 */
	@ApiModelProperty(value = "fac技术工程师签名图片key")
	private Long facTechnicianSign;
	/**
	 * 声明（1勾选）
	 */
	@ApiModelProperty(value = "声明（1勾选）")
	private Integer facDeclaration;
	/**
	 * 余额确认文件key
	 */
	@ApiModelProperty(value = "余额确认文件key")
	private Long balancePaymentConfirmDocBizKey;

	@ApiModelProperty(value = "余额确认文件")
	@TableField(exist = false)
	private List<AttachmentInfoEntity> docFile;
	/**
	 * 免费维修时间
	 */
	@ApiModelProperty(value = "免费维修时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private String freeMaintenanceServiceDate;
	/**
	 * 保修时间
	 */
	@ApiModelProperty(value = "保修时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private String warrantyDate;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 签名人与户主关系
	 */
	@ApiModelProperty(value = "签名人与户主关系")
	private String signerRelationship;

	/**
	 * 是否有免费维修服务
	 */
	@ApiModelProperty(value = "是否有免费维修服务, 0:否, 1:是")
	private Integer fiRequiredFreeService;
}

/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.deliversign.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 货物操作记录 实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@TableName("design_device_item_history")
@ApiModel(value = "DeviceItemHistory对象", description = "货物操作记录")
@EqualsAndHashCode(callSuper = true)
public class DeviceItemHistoryEntity extends SkyWorthEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 操作节点（deliver_item设备发货，sign_item物料签收）
	 */
	@ApiModelProperty(value = "操作节点（ deliver_item 设备发货，sign_item 物料签收）")
	private String operateNode;
	/**
	 * 配料型号编码
	 */
	@ApiModelProperty(value = "配料型号编码")
	private String itemCode;
	/**
	 * 操作类型：货物状态（un_deliver未发货，deliver已发货，sign签收，loss差损）
	 */
	@ApiModelProperty(value = "操作类型：货物状态（un_deliver未发货，deliver已发货，sign签收，loss差损）")
	private String operateType;
	private String quantity;
	/**
	 * 说明
	 */
	@ApiModelProperty(value = "说明")
	private String remark;


}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 订单审核
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-order/reviewOrder")
@Api(value = "订单审核节点", tags = "订单审核接口")
public class ReviewOrderController extends BladeController {

	private final IReviewOrderService orderService;


	/**
	 * 审核修改订单
	 */
	@PostMapping("/examineEditOrder")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "审核修改订单", notes = "传入orderDTO")
	public R<?> examineEditOrder(@Valid @RequestBody OrderDTO orderDTO) {
		return orderService.examineEditOrCancelOrder(orderDTO);
	}


	/**
	 * 审核取消订单
	 */
	@PostMapping("/examineCancelOrder")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "审核取消订单", notes = "传入orderDTO")
	public R<?> examineCancelOrder(@RequestBody OrderDTO orderDTO) {
		return orderService.examineEditOrCancelOrder(orderDTO);
	}


}

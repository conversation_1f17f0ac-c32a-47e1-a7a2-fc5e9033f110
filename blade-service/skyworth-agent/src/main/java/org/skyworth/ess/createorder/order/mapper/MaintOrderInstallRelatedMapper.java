/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.createorder.order.entity.MaintOrderInstallRelatedEntity;
import org.skyworth.ess.createorder.order.vo.MaintRelatedVO;

import java.util.List;

/**
 * 订单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface MaintOrderInstallRelatedMapper extends BaseMapper<MaintOrderInstallRelatedEntity> {

    // 根据安装订单id 查询维保记录
    List<MaintRelatedVO> queryMaintHistory(@Param("installOrderId")Long installOrderId);

    //根据 维保订单id查询 安装订单信息
    MaintRelatedVO queryInstallOrderByMaintId(@Param("maintOrderId")Long maintOrderId);
}

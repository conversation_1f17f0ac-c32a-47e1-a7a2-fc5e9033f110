/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyreview.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.surveyreview.entity.MaintainSurveyReviewEntity;
import org.skyworth.ess.surveyreview.service.IMaintainSurveyReviewService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 踏勘信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("maintain/surveyReview")
@Api(value = "踏勘信息审批", tags = "踏勘信息信息接口")
public class MaintainSurveyReviewController extends BladeController {

	private final IMaintainSurveyReviewService maintainSurveyReviewService;
	/**
	 * 踏勘信息子节点审批
	 * @param orderNodeSubStatusEntity 入参
	 * @return R<?>
	 * <AUTHOR>
	 * @since 2024/4/18 15:24
	 **/
	@PostMapping("/saveSubNodeApprovalStatus")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "踏勘信息子节点审批", notes = "传入orderNodeSubStatusEntity")
	public R<?> saveSubNodeApprovalStatus(@RequestBody @Valid OrderNodeSubStatusEntity orderNodeSubStatusEntity) {
		return maintainSurveyReviewService.saveSubNodeApprovalStatus(orderNodeSubStatusEntity);
	}
	/**
	 * 踏勘信息审批提交
	 * @param maintainSurveyReviewEntity 入参
	 * @return R<?>
	 * <AUTHOR>
	 * @since 2024/4/18 15:43
	 **/
	@PostMapping("/submit")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "踏勘信息审批提交", notes = "传入maintainSurveyReviewEntity")
	public R<?> submit(@RequestBody @Valid MaintainSurveyReviewEntity maintainSurveyReviewEntity) {
		return maintainSurveyReviewService.submit(maintainSurveyReviewEntity);
	}
}

package org.skyworth.ess.createorder.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.springblade.common.constant.OrderStatusConstants;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.mapper.OrderMapper;
import org.skyworth.ess.createorder.order.service.IOrderCreateService;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.RedisSeqEnums;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

import static org.springblade.common.constant.CommonConstant.INTERIOR_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("orderInstallCreateServiceImpl")
@RequiredArgsConstructor
public class OrderInstallCreateServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> implements IOrderCreateService {
	private final IFlowClient flowClient;

	private final IDictBizClient dictBizClient;

	private final ISysClient iSysClient;

	private final IAgentCompanyInfoService iAgentCompanyInfoService;

	private final IOrderWorkFlowService iOrderWorkFlowService;

	private final SendMail sendMail;
	@Override
	public <T> Boolean saveOrder(Supplier<T> supplier) {
		OrderEntity order = (OrderEntity) supplier.get();
		//获取订单编号
		R<String> getOrderNumberResult = iSysClient.getRedisUniqueId(RedisSeqEnums.ORDER_NO_SEQ);

		if (getOrderNumberResult == null || !getOrderNumberResult.isSuccess() || ObjectUtils.isEmpty(getOrderNumberResult.getData())) {
			throw new BusinessException("agent.createOrder.insertOrder.getOrderNumber.notNull");
		}
		//获取用户角色内外部标识
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		agentCompanyInfoEntityLambdaQueryWrapper.eq(AgentCompanyInfoEntity::getId, order.getDistributorId());
		agentCompanyInfoEntityLambdaQueryWrapper.select(AgentCompanyInfoEntity::getCompanyAttributes, AgentCompanyInfoEntity::getDeptId);
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getOne(agentCompanyInfoEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(agentCompanyInfoEntity) || ObjectUtils.isEmpty(agentCompanyInfoEntity.getCompanyAttributes())) {
			throw new BusinessException("agent.createOrder.insertOrder.getCompanyAttributes.fail");
		}

		//通过内外部标识拼接订单编号前缀
		String prefixFlag = INTERIOR_TYPE.equals(agentCompanyInfoEntity.getCompanyAttributes()) ? "i" : "e";
		String orderNumber = prefixFlag + getOrderNumberResult.getData();
		order.setOrderNumber(orderNumber);
		//添加代理商部门
		order.setBelongDeptId(agentCompanyInfoEntity.getDeptId());
		//保存和取消后重新提交
		//补充操作信息
		boolean saveFlag = this.save(order);
		if (!saveFlag) {
			throw new BusinessException("agent.createOrder.insertOrder.saveOrderFail");
		}
		//开启审批流程
		R<OrderFlowVO> orderProcess = flowClient.startOrderProcess(String.valueOf(order.getId()), CommonConstant.PROCESS_DEFINITION_KEY);
		if (!orderProcess.isSuccess() || ObjectUtils.isEmpty(orderProcess.getData())) {
			throw new BusinessException("agent.createOrder.insertOrder.startProcessFail");
		}
		OrderFlowVO orderFlowVO = orderProcess.getData();
		//保存审批流相关信息
		OrderWorkFlowEntity orderWorkFlowEntity = getOrderWorkFlowEntity(orderFlowVO);
		orderWorkFlowEntity.setOrderId(order.getId());
		boolean workFlowSaveFlag = iOrderWorkFlowService.save(orderWorkFlowEntity);
		if (!workFlowSaveFlag) {
			throw new BusinessException("agent.createOrder.insertOrder.workFlowSaveFail");
		}

		return true;
	}

	/**
	 * @Description: 转换工作流信息
	 * @Param: [orderFlowVO]
	 * @Return: org.skyworth.ess.orderworkflow.entity.OrderWorkFlowEntity
	 * @Author: baixu
	 * @Date: 2023/11/24 15:38
	 **/
	private OrderWorkFlowEntity getOrderWorkFlowEntity(OrderFlowVO orderFlowVO) {
		OrderWorkFlowEntity orderWorkFlowEntity = new OrderWorkFlowEntity();
		//流程实例id
		orderWorkFlowEntity.setWfInstanceId(orderFlowVO.getProcessInstanceId());
		//任务id
		orderWorkFlowEntity.setTaskId(orderFlowVO.getTaskId());
		//工作流当前状态/订单进度
		orderWorkFlowEntity.setWfCurrentStatus(orderFlowVO.getTaskName());
		//当前处理节点类型
		orderWorkFlowEntity.setWfCurrentType(OrderStatusConstants.CURRENT_ROLE);
		//当前处理节点角色或人
		orderWorkFlowEntity.setWfCurrentRole(orderFlowVO.getAssignee());
		//当前处理节点角色或人名称
		orderWorkFlowEntity.setWfCurrentRoleName(orderFlowVO.getAssigneeName());
		//上一步审批状态
		orderWorkFlowEntity.setAuditStatus(OrderStatusConstants.AUDIT_STATUS_PASS);
		return orderWorkFlowEntity;
	}
}

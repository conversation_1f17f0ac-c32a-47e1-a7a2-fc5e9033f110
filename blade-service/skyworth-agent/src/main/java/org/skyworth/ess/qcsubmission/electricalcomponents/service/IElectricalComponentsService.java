/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcInfoDTOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.QcShowDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.dto.SubmitQcInfoRequestDTO;
import org.skyworth.ess.qcsubmission.electricalcomponents.entity.ElectricalComponentsEntity;
import org.skyworth.ess.qcsubmission.electricalcomponents.excel.ElectricalComponentsExcel;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.ElectricalComponentsVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVO;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.QcInfoVOs;
import org.skyworth.ess.qcsubmission.electricalcomponents.vo.SubNodeSaveStatusVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 施工-电器元件信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface IElectricalComponentsService extends BaseService<ElectricalComponentsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page 分页
	 * @param electricalcomponents	电器元件信息
	 * @return	分页数据
	 */
	IPage<ElectricalComponentsVO> selectElectricalComponentsPage(IPage<ElectricalComponentsVO> page, ElectricalComponentsVO electricalcomponents);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper	查询条件
	 * @return	导出数据
	 */
	List<ElectricalComponentsExcel> exportElectricalComponents(Wrapper<ElectricalComponentsEntity> queryWrapper);
	/**
	 * 根据订单id获取QC基本信息
	 * @param orderId 订单的唯一标识符
	 * @return QcInfoVO 包含订单QC信息的对象
	 */
	QcInfoVO getMaintenanceBasicQcInfo(String orderId);
	/**
	 * 根据订单id获取QC基本信息
	 * @param orderId 订单的唯一标识符
	 * @return QcInfoVO 包含订单QC信息的对象
	 */
	QcInfoVO getBasicQcInfo(String orderId);
	/**
	 * 根据选择获取QC分段详细信息
	 * @param qcShowDTO 是否显示分段QC信息
	 * @return	QcInfoVOs 包含分段QC信息的对象
	 */
	QcInfoVOs getQcInfoDetail(QcShowDTO qcShowDTO);
	/**
	 * 保存或更新QC信息
	 * @param qcInfoDtos 接受的QC信息
	 * @return Boolean 保存或更新QC信息是否成功
	 */
	Boolean saveOrUpdateQcInfo(QcInfoDTOs qcInfoDtos);
	/**
	 * 保存或更新QC信息
	 * @param qcInfoDtos 接受的QC信息
	 * @return Boolean 保存或更新QC信息是否成功
	 */
	Boolean saveOrUpdateMaintenanceQcInfo(QcInfoDTOs qcInfoDtos);

	/**
	 * 获取QC子节点save状态信息
	 * @param orderId 订单id
	 * @return	SubNodeSaveStatusVO 包含子节点save状态信息的对象
	 */
	List<SubNodeSaveStatusVO> listSubNodeSaveStatus(Long orderId);

	/**
	 * 提交QC信息流程流转
	 * @param requestDTO	流程流转信息
	 * @return Boolean 提交QC信息流程流转是否成功
	 */
	Boolean submitQcInfo(SubmitQcInfoRequestDTO requestDTO);
	/**
	 * 提交QC信息流程流转
	 * @param requestDTO	流程流转信息
	 * @return Boolean 提交QC信息流程流转是否成功
	 */
	Boolean submitQcInfoMaintenance(SubmitQcInfoRequestDTO requestDTO);
	/**
	 * 获取QC子节点approve状态信息
	 * @param orderId	订单id
	 * @return	SubNodeSaveStatusVO 包含子节点approve状态信息的对象
	 */
	List<SubNodeSaveStatusVO> listSubNodeApproveStatus(Long orderId);
}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.installationdate.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.installationdate.service.InstallationDateService;
import org.skyworth.ess.qcsubmission.installrelatedInfo.dto.InstallRelatedInfoDTO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 确认施工日期 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("maintain/installationDate")
@Api(value = "确认施工日期", tags = "确认施工日期接口")
public class MaintainInstallationDateController extends BladeController {

	private final InstallationDateService installationDateService;


	@PostMapping("/examineInstallationDate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "审核确认施工日期", notes = "传入installRelatedInfoDTO")
	public R<?> examineInstallationDate(@RequestBody @Valid InstallRelatedInfoDTO installRelatedInfoDTO) {
		return installationDateService.examineInstallationDate(installRelatedInfoDTO);
	}


	/**
	 * 查询安装日期和文件
	 */
	@PostMapping("/selectInstallDateInfo")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "查询安装日期和文件", notes = "传入installRelatedInfoDTO")
	public R<?> selectInstallDateInfo(@RequestBody InstallRelatedInfoDTO installRelatedInfoDTO) {
		return installationDateService.selectInstallDateInfo(installRelatedInfoDTO);
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.cocmaintenanceinfo.mapper.CocMaintenanceInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cocMaintenanceInfoResultMap" type="org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
       <result column="fac_landlord_sign" property="facLandlordSign"/>
        <result column="fac_plant_id" property="facPlantId"/>
        <result column="fac_plant_name" property="facPlantName"/>
        <result column="fac_technician_sign" property="facTechnicianSign"/>
        <result column="fac_declaration" property="facDeclaration"/>
        <result column="balance_payment_confirm_doc_biz_key" property="balancePaymentConfirmDocBizKey"/>
        <result column="free_maintenance_service_date" property="freeMaintenanceServiceDate"/>
        <result column="warranty_date" property="warrantyDate"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="fac_declaration" property="facDeclaration"/>
        <result column="fi_required_free_service" property="fiRequiredFreeService"/>
    </resultMap>


    <resultMap id="basicOrderInfo" type="org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO">
         <result column="order_number" property="orderId"/>
         <result column="site_country_code" property="siteCountryCode"/>
         <result column="site_province_code" property="siteProvinceCode"/>
         <result column="site_city_code" property="siteCityCode"/>
         <result column="site_address" property="siteAddress"/>
         <result column="constructionDate" property="constructionDate"/>
         <result column="warranty_date" property="warrantyDate"/>
         <result column="free_maintenance_service_date" property="freeMaintenanceServiceDate"/>
         <result column="fi_required_free_service" property="fiRequiredFreeService"/>
    </resultMap>

    <insert id="insertCocMaintenance">
        INSERT INTO coc_maintenance_info (
            id,
            order_id,
<!--            temporary_coc_img_biz_key,-->
<!--            temporary_coc_start_date,-->
            fac_landlord_sign,
           /* fac_landlord_doc_img_biz_key,*/
            fac_plant_id,
            fac_plant_name,
            fac_technician_sign,
<!--            fac_technician_img_biz_key,-->
<!--            fac_declaration,-->
            balance_payment_confirm_doc_biz_key,
<!--            final_coc_img_biz_key,-->
<!--            final_coc_type,-->
<!--            final_coc_start_date,-->
<!--            final_coc_end_date,-->
            free_maintenance_service_date,
            warranty_date,
            create_user,
            create_user_account,
            create_time,
            update_user,
            update_user_account,
            update_time,
            tenant_id,
            create_dept
<!--            delivery_number,-->
<!--            client_confirmation_biz_key,-->
<!--            delivery,-->
<!--            customers_acceptance,-->
            signer_relationship,
<!--            payment_confirm_send_mail,-->
<!--            payment_confirm_mail_to,-->
<!--            payment_confirm_mail_bcc,-->
<!--            payment_confirm_mail_content,-->
            fi_required_free_service
        )
        VALUES (
            #{id},
            #{orderId},
<!--            #{temporaryCocImgBizKey},-->
<!--            #{temporaryCocStartDate},-->
            #{facLandlordSign},
<!--            #{facLandlordDocImgBizKey},-->
            #{facPlantId},
            #{facPlantName},
            #{facTechnicianSign},
<!--            #{facTechnicianImgBizKey},-->
<!--            #{facDeclaration},-->
            #{balancePaymentConfirmDocBizKey},
<!--            #{finalCocImgBizKey},-->
<!--            #{finalCocType},-->
<!--            #{finalCocStartDate},-->
<!--            #{finalCocEndDate},-->
            #{freeMaintenanceServiceDate},
            #{warrantyDate},
            #{createUser},
            #{createUserAccount},
            #{createTime},
            #{updateUser},
            #{updateUserAccount},
            #{updateTime},
            #{tenantId},
            #{createDept}
<!--            #{deliveryNumber},-->
<!--            #{clientConfirmationBizKey},-->
<!--            #{delivery},-->
<!--            #{customersAcceptance},-->
           #{signerRelationship},
<!--            #{paymentConfirmSendMail},-->
<!--            #{paymentConfirmMailTo},-->
<!--            #{paymentConfirmMailBcc},-->
<!--            #{paymentConfirmMailContent},-->
           #{fiRequiredFreeService}
        )
        ON DUPLICATE KEY UPDATE
        <trim prefix="" suffixOverrides=",">
<!--            <if test="temporaryCocImgBizKey"> temporary_coc_img_biz_key = #{temporaryCocImgBizKey}, </if>-->
<!--            <if test="temporaryCocStartDate"> temporary_coc_start_date = #{temporaryCocStartDate}, </if>-->
            <if test="facLandlordSign"> fac_landlord_sign = #{facLandlordSign}, </if>
<!--            <if test="facLandlordDocImgBizKey"> fac_landlord_doc_img_biz_key = #{facLandlordDocImgBizKey}, </if>-->
            <if test="facPlantId"> fac_plant_id = #{facPlantId}, </if>
            <if test="facPlantName"> fac_plant_name = #{facPlantName}, </if>
            <if test="facTechnicianSign"> fac_technician_sign = #{facTechnicianSign}, </if>
<!--            <if test="facTechnicianImgBizKey"> fac_technician_img_biz_key = #{facTechnicianImgBizKey}, </if>-->
<!--            <if test="facDeclaration"> fac_declaration = #{facDeclaration}, </if>-->
<!--            <if test="balancePaymentConfirmDocBizKey"> balance_payment_confirm_doc_biz_, key = #{balancePaymentConfirmDocBizKey}</if>-->
<!--            <if test="finalCocImgBizKey"> final_coc_img_biz_key = #{finalCocImgBizKey}, </if>-->
<!--            <if test="finalCocType"> final_coc_type = #{finalCocType}, </if>-->
<!--            <if test="finalCocStartDate"> final_coc_start_date = #{finalCocStartDate}, </if>-->
<!--            <if test="finalCocEndDate"> final_coc_end_date = #{finalCocEndDate}, </if>-->
            <if test="freeMaintenanceServiceDate"> free_maintenance_service_date = #{freeMaintenanceServiceDate}, </if>
            <if test="warrantyDate"> warranty_date = #{warrantyDate}, </if>
            <if test="createUser"> create_user = #{createUser}, </if>
            <if test="createUserAccount"> create_user_account = #{createUserAccount}, </if>
            <if test="createTime"> create_time = #{createTime}, </if>
            <if test="updateUser"> update_user = #{updateUser}, </if>
            <if test="updateUserAccount"> update_user_account = #{updateUserAccount}, </if>
            <if test="updateTime"> update_time = #{updateTime}, </if>
            <if test="tenantId"> tenant_id = #{tenantId}, </if>
            <if test="createDept"> create_dept = #{createDept} </if>
<!--            <if test="deliveryNumber"> delivery_number = #{deliveryNumber}, </if>-->
<!--            <if test="clientConfirmationBizKey"> client_confirmation_biz_key = #{clientConfirmationBizKey}, </if>-->
<!--            <if test="delivery"> delivery = #{delivery}, </if>-->
<!--            <if test="customersAcceptance"> customers_acceptance = #{customersAcceptance}, </if>-->
            <if test="signerRelationship"> signer_relationship = #{signerRelationship}, </if>
<!--            <if test="paymentConfirmSendMail"> payment_confirm_send_mail = #{paymentConfirmSendMail}, </if>-->
<!--            <if test="paymentConfirmMailTo"> payment_confirm_mail_to = #{paymentConfirmMailTo}, </if>-->
<!--            <if test="paymentConfirmMailBcc"> payment_confirm_mail_bcc = #{paymentConfirmMailBcc}, </if>-->
<!--            <if test="paymentConfirmMailContent"> payment_confirm_mail_content = #{paymentConfirmMailContent}, </if>-->
            <if test="fiRequiredFreeService"> fi_required_free_service = #{fiRequiredFreeService}</if>
        </trim>
    </insert>


    <update id="updateCocMaintenanceByOrderId">
        UPDATE coc_maintenance_info SET
        <foreach collection="data" item="val" index="key" separator=",">
            ${key} = #{val}
        </foreach>
        WHERE order_id = #{orderId}
    </update>


    <select id="selectCocMaintenanceInfoPage" resultMap="cocMaintenanceInfoResultMap">
        select * from coc_maintenance_info where is_deleted = 0
    </select>


    <select id="exportCocMaintenanceInfo" resultType="org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel">
        SELECT * FROM coc_maintenance_info ${ew.customSqlSegment}
    </select>
    <select id="selectBasicOrderInfo" resultMap="basicOrderInfo">
        select bo.order_number, bo.site_country_code, bo.site_province_code, bo.site_city_code ,bo.site_county_code , bo.site_address ,
        CONVERT(iri.construction_date, CHAR) as constructionDate, cmi.warranty_date ,cmi.free_maintenance_service_date, fi_required_free_service
        from business_order  bo
        inner join device_quote_info  iri
        on bo.id= iri.order_id
        inner join coc_maintenance_info cmi on cmi.order_id =bo.id
        where bo.id =#{orderId}
          and iri.is_deleted =0 and cmi.is_deleted =0
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.qcsubmission.solarpanels.mapper.SolarPanelsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="solarpanelsResultMap" type="org.skyworth.ess.qcsubmission.solarpanels.entity.SolarPanelsEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="panels_img_biz_key" property="panelsImgBizKey"/>
<!--        <result column="serial_num_verification_img_biz_key" property="serialNumVerificationImgBizKey"/>-->
        <result column="inspecation_ship_damage_img_biz_key" property="inspecationShipDamageImgBizKey"/>
        <result column="panels_verify_panel_tilt_img_biz_key" property="panelsVerifyPanelTiltImgBizKey"/>
        <result column="inspect_roof_flashing_img_biz_key" property="inspectRoofFlashingImgBizKey"/>
        <result column="all_roof_sealed_img_biz_key" property="allRoofSealedImgBizKey"/>
        <result column="panels_verify_cable_wiring_img_biz_key" property="panelsVerifyCableWiringImgBizKey"/>
        <result column="confirm_pvc_pipe_img_biz_key" property="confirmPvcPipeImgBizKey"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectSolarPanelsPage" resultMap="solarpanelsResultMap">
        select * from qc_solar_panels where is_deleted = 0
    </select>


    <select id="exportSolarPanels" resultType="org.skyworth.ess.qcsubmission.solarpanels.excel.SolarPanelsExcel">
        SELECT * FROM qc_solar_panels ${ew.customSqlSegment}
    </select>

</mapper>

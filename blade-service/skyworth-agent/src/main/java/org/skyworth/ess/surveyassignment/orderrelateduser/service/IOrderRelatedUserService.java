/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.orderrelateduser.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.dto.OrderRelatedUserDTO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.excel.OrderRelatedUserExcel;
import org.skyworth.ess.surveyassignment.orderrelateduser.vo.OrderRelatedUserVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * 订单关系人 服务类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface IOrderRelatedUserService extends BaseService<OrderRelatedUserEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param orderRelatedUser
	 * @return
	 */
	IPage<OrderRelatedUserVO> selectOrderRelatedUserPage(IPage<OrderRelatedUserVO> page, OrderRelatedUserVO orderRelatedUser);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<OrderRelatedUserExcel> exportOrderRelatedUser(Wrapper<OrderRelatedUserEntity> queryWrapper);


    R<?> designatedPerson(OrderRelatedUserDTO orderRelatedUserDTO);

	R<OrderEntity> selectExplorationDate(OrderDTO orderDTO);

	R<?> selectDesignatedPerson(SurveyInfoEntity infoEntity);
}

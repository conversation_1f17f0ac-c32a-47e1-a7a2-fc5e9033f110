/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * coc和维护信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class CocMaintenanceInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单")
	private Long orderId;
	/**
	 * 临时coc图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("临时coc图片key")
	private Long temporaryCocImgBizKey;
	/**
	 * 临时coc开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("临时coc开始时间")
	private Date temporaryCocStartDate;
	/**
	 * 临时coc结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("临时coc结束时间")
	private Date temporaryCocEndDate;
	/**
	 * 房东签名图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房东签名图片key")
	private Long facLandlordSign;
	/**
	 * 房东文件图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房东文件图片key")
	private Long facLandlordDocImgBizKey;
	/**
	 * 站点id
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点id")
	private Long facPlantId;
	/**
	 * 站点名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("站点名称")
	private String facPlantName;
	/**
	 * fac技术工程师签名图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("fac技术工程师签名图片key")
	private Long facTechnicianSign;
	/**
	 * fac技术工程师文件图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("fac技术工程师文件图片key")
	private Long facTechnicianImgBizKey;
	/**
	 * 余额确认文件key
	 */
	@ColumnWidth(20)
	@ExcelProperty("余额确认文件key")
	private Long balancePaymentConfirmDocBizKey;
	/**
	 * 最终coc图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("最终coc图片key")
	private Long finalCocImgBizKey;
	/**
	 * 最终coc类型（1永久）
	 */
	@ColumnWidth(20)
	@ExcelProperty("最终coc类型（1永久）")
	private Integer finalCocType;
	/**
	 * 最终coc开始时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("最终coc开始时间")
	private Date finalCocStartDate;
	/**
	 * 最终coc结束时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("最终coc结束时间")
	private Date finalCocEndDate;
	/**
	 * 免费维修时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("免费维修时间")
	private Date freeMaintenanceServiceDate;
	/**
	 * 保修时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("保修时间")
	private Date warrantyDate;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}

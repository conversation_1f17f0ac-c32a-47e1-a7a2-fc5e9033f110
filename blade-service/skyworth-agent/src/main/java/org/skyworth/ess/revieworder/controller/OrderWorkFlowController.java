/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.excel.OrderWorkFlowExcel;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.skyworth.ess.revieworder.orderworkflow.wrapper.OrderWorkFlowWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单流程关系表 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-orderWorkFlow/orderWorkFlow")
@Api(value = "订单流程关系表", tags = "订单流程关系表接口")
public class OrderWorkFlowController extends BladeController {

	private final IOrderWorkFlowService orderWorkFlowService;

	/**
	 * 订单流程关系表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入orderWorkFlow")
	public R<OrderWorkFlowVO> detail(OrderWorkFlowEntity orderWorkFlow) {
		OrderWorkFlowEntity detail = orderWorkFlowService.getOne(Condition.getQueryWrapper(orderWorkFlow));
		return R.data(OrderWorkFlowWrapper.build().entityVO(detail));
	}
	/**
	 * 订单流程关系表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入orderWorkFlow")
	public R<IPage<OrderWorkFlowVO>> list(@ApiIgnore @RequestParam Map<String, Object> orderWorkFlow, Query query) {
		IPage<OrderWorkFlowEntity> pages = orderWorkFlowService.page(Condition.getPage(query), Condition.getQueryWrapper(orderWorkFlow, OrderWorkFlowEntity.class));
		return R.data(OrderWorkFlowWrapper.build().pageVO(pages));
	}

	/**
	 * 订单流程关系表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入orderWorkFlow")
	public R<IPage<OrderWorkFlowVO>> page(OrderWorkFlowVO orderWorkFlow, Query query) {
		IPage<OrderWorkFlowVO> pages = orderWorkFlowService.selectOrderWorkFlowPage(Condition.getPage(query), orderWorkFlow);
		return R.data(pages);
	}

	/**
	 * 订单流程关系表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入orderWorkFlow")
	public R save(@Valid @RequestBody OrderWorkFlowEntity orderWorkFlow) {
		return R.status(orderWorkFlowService.save(orderWorkFlow));
	}

	/**
	 * 订单流程关系表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入orderWorkFlow")
	public R update(@Valid @RequestBody OrderWorkFlowEntity orderWorkFlow) {
		return R.status(orderWorkFlowService.updateById(orderWorkFlow));
	}

	/**
	 * 订单流程关系表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入orderWorkFlow")
	public R submit(@Valid @RequestBody OrderWorkFlowEntity orderWorkFlow) {
		return R.status(orderWorkFlowService.saveOrUpdate(orderWorkFlow));
	}

	/**
	 * 订单流程关系表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(orderWorkFlowService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-orderWorkFlow")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入orderWorkFlow")
	public void exportOrderWorkFlow(@ApiIgnore @RequestParam Map<String, Object> orderWorkFlow, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<OrderWorkFlowEntity> queryWrapper = Condition.getQueryWrapper(orderWorkFlow, OrderWorkFlowEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(OrderWorkFlow::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(OrderWorkFlowEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<OrderWorkFlowExcel> list = orderWorkFlowService.exportOrderWorkFlow(queryWrapper);
		ExcelUtil.export(response, "订单流程关系表数据" + DateUtil.time(), "订单流程关系表数据表", list, OrderWorkFlowExcel.class);
	}

}

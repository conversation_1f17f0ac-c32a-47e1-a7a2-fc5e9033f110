/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.installwoassignment.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.springblade.common.constant.OrderStatusConstants;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.installwoassignment.service.InstallWoAssignmentService;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.surveyassignment.orderrelateduser.dto.OrderRelatedUserDTO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.mapper.OrderRelatedUserMapper;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.common.constant.RoleCodeEnum;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.Role;
import org.springblade.system.feign.ISysClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 安装相关信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
@AllArgsConstructor
public class InstallWoAssignmentServiceImpl extends BaseServiceImpl<OrderRelatedUserMapper, OrderRelatedUserEntity> implements InstallWoAssignmentService {

	private final IReviewOrderService iReviewOrderService;

	private final IOrderWorkFlowService iOrderWorkFlowService;

	private final IOrderService iOrderService;

	private ISysClient iSysClient;
	private final IQuoteInfoService quoteInfoService;

	/**
	 * @Description: 审核指派施工任务
	 * @Param: [orderRelatedUserDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/5 10:53
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> examineWoAssignment(OrderRelatedUserDTO orderRelatedUserDTO) {

		//校验参数
		validParamStatus(orderRelatedUserDTO);
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = collectWoAssignmentInfo(orderRelatedUserDTO);
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
			//点击modify后的操作
//			if (CommonConstant.MODIFY_STATUS.equals(orderRelatedUserDTO.getModifyStatus())) {
//				modifyStatusDealInfo(orderRelatedUserEntityList, orderRelatedUserDTO);
//			} else {
			//点击pass后的操作
			this.saveBatch(orderRelatedUserEntityList);
			// 保存踏勘日期
			LambdaQueryWrapper<QuoteInfoEntity> queryWrapper = Wrappers.<QuoteInfoEntity>lambdaQuery()
					.eq(QuoteInfoEntity::getOrderId, orderRelatedUserDTO.getOrderFlowDTO().getBusinessId());
			QuoteInfoEntity quoteInfoEntity = quoteInfoService.getOne(queryWrapper);
			if(ObjectUtils.isEmpty(quoteInfoEntity)) {
				throw new BusinessException("agent.install.woAssignment.quoteInfo.isNull");
			}
			LambdaUpdateWrapper<QuoteInfoEntity> updateWrapper = new LambdaUpdateWrapper<QuoteInfoEntity>().set(QuoteInfoEntity::getConstructionDate, orderRelatedUserDTO.getConstructionDate())
					.eq(QuoteInfoEntity::getId, quoteInfoEntity.getId()).eq(QuoteInfoEntity::getOrderId, orderRelatedUserDTO.getOrderFlowDTO().getBusinessId());
			quoteInfoService.update(updateWrapper);
			setWorkFlowData(orderRelatedUserDTO);
			//审批流转
			R<?> examineApproveResult = iReviewOrderService.examineApprove(orderRelatedUserDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
//			}
		}
		return R.status(true);
	}

	/**
	 * @Description: 审核指派施工任务
	 * @Param: [orderRelatedUserDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/5 10:53
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> maintainExamineWoAssignment(OrderRelatedUserDTO orderRelatedUserDTO) {
		Map<String, Object> variables = orderRelatedUserDTO.getOrderFlowDTO().getVariables();
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = collectWoAssignmentInfo(orderRelatedUserDTO);
		// 点击modify后的操作
		if (CommonConstant.MODIFY_STATUS.equals(orderRelatedUserDTO.getModifyStatus())) {
			if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
				//校验参数
				validMaintainParamStatus(orderRelatedUserDTO);
				maintainModifyStatusDealInfo(orderRelatedUserEntityList, orderRelatedUserDTO);
			}
		} else {
			// 通过操作->验证数据格式->数据保存->设置工作流流程参数
			if (OrderStatusConstants.EXAMINE_APPROVE_PASS.equals(examineApproveType)) {
				validMaintainParamStatus(orderRelatedUserDTO);
				//点击pass后的操作
				if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
					this.saveBatch(orderRelatedUserEntityList);
				}
				// 设置审批人，电气工程师+施工队长
				setWorkFlowData(orderRelatedUserDTO);
			}
			//审批流转
			R<?> examineApproveResult = iReviewOrderService.examineApprove(orderRelatedUserDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
		}
		return R.status(true);
	}

	/**
	 * @Description: 查询指派施工人员信息
	 * @Param: [orderRelatedUserDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/5 15:25
	 **/
	@Override
	public R<?> selectAssignedConstructInfo(OrderRelatedUserDTO orderRelatedUserDTO) {
		if (ObjectUtils.isEmpty(orderRelatedUserDTO)) {
			throw new BusinessException("agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull");
		}
		//订单id
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		//查询订单表中代理商id
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, orderRelatedUserDTO.getOrderId()).select(OrderEntity::getDistributorId);
		OrderEntity orderEntity = iOrderService.getOne(orderEntityLambdaQueryWrapper);
		if (ObjectUtils.isNotEmpty(orderEntity)) {
			orderRelatedUserDTO.setDistributorId(orderEntity.getDistributorId());
		}
		//查询订单关系人表查询人员信息
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderRelatedUserDTO.getOrderId())
				.eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode())
				.in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = this.list(orderRelatedUserEntityLambdaQueryWrapper);
		if (CollectionUtils.isNotEmpty(orderRelatedUserEntityList)) {
			//设置施工队长和电气工程师名称
			setInstallUserName(orderRelatedUserEntityList, orderRelatedUserDTO);
		}
		LambdaQueryWrapper<QuoteInfoEntity> queryWrapper = Wrappers.<QuoteInfoEntity>lambdaQuery()
				.eq(QuoteInfoEntity::getOrderId, orderRelatedUserDTO.getOrderId());
		QuoteInfoEntity quoteInfoEntity = quoteInfoService.getOne(queryWrapper);
		if(ObjectUtils.isEmpty(quoteInfoEntity)) {
			throw new BusinessException("agent.install.woAssignment.quoteInfo.isNull");
		}
		orderRelatedUserDTO.setConstructionDate(quoteInfoEntity.getConstructionDate());
		return R.data(orderRelatedUserDTO);
	}


	/**
	 * @Description: 获取按钮状态
	 * @Param: [orderRelatedUserDTO]
	 * @Return: java.lang.String
	 * @Author: baixu
	 * @Date: 2023/12/19 14:37
	 **/
	private String getButtonStatus(OrderRelatedUserDTO orderRelatedUserDTO) {
		//根据当前登录用户id查询所在角色
		R<List<Role>> userRoleInfoByUserId = iSysClient.findUserRoleInfoByUserId(String.valueOf(AuthUtil.getUserId()));
		//获取角色code
		List<String> userCodeList = userRoleInfoByUserId.getData().stream().map(Role::getRoleCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
		//只有在这三种角色下才可操作 代理商管理员，创维交付经理，代理商交付经理
		Boolean checkFlag = checkIfOperate(userCodeList, String.join(",", Arrays.asList(RoleCodeEnum.ROLLOUT_MANAGER.getRoleCode(), RoleCodeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode(), RoleCodeEnum.ADMIN.getRoleCode())));
		String buttonStatus;
		if (checkFlag) {
			LambdaQueryWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
			orderWorkFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderRelatedUserDTO.getOrderId()).select(OrderWorkFlowEntity::getWfCurrentStatus);
			OrderWorkFlowEntity orderWorkFlowEntity = iOrderWorkFlowService.getOne(orderWorkFlowEntityLambdaQueryWrapper);
			if (ObjectUtils.isEmpty(orderWorkFlowEntity)) {
				throw new BusinessException("agent.installWoAssignment.getButtonStatus.orderWorkFlowEntity.notNull");
			}
			//节点名称
			String wfCurrentStatus = orderWorkFlowEntity.getWfCurrentStatus();
			//流程节点为施工选人节点 按钮状态为Pass
			if (DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode().equals(wfCurrentStatus)) {
				buttonStatus = CommonConstant.PASS_STATUS;
			} else if (DictNodeBizCodeEnum.MATERIAL_COLLECTION.getDictCode().equals(wfCurrentStatus)) {
				//流程节点为施工选人节点 按钮状态为Pass
				buttonStatus = CommonConstant.MODIFY_STATUS;
			} else {
				buttonStatus = CommonConstant.DISABLE_STATUS;
			}
		} else {
			buttonStatus = CommonConstant.DISABLE_STATUS;
		}
		return buttonStatus;
	}


	/**
	 * @Description: 设置施工队长和电气工程师名称
	 * @Param: [orderRelatedUserEntityList, orderRelatedUserDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/5 17:45
	 **/
	private void setInstallUserName(List<OrderRelatedUserEntity> orderRelatedUserEntityList, OrderRelatedUserDTO orderRelatedUserDTO) {
		//根据人员类型分组，每组取第一个
		Map<String, OrderRelatedUserEntity> orderRelatedUserEntityMap = orderRelatedUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getUserType())).collect(Collectors.groupingBy(OrderRelatedUserEntity::getUserType, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
		orderRelatedUserEntityMap.forEach((key, value) -> {
			//施工队长
			if (UserTypeEnum.SITE_TECHNICIAN_LEADER.getName().equals(key)) {
				orderRelatedUserDTO.setInstallUserId(value.getUserId());
				orderRelatedUserDTO.setInstallUserName(value.getUserName());
			}
			//电气工程师
//			if (UserTypeEnum.ELECTRICIAN.getName().equals(key)) {
//				orderRelatedUserDTO.setElectricUserId(value.getUserId());
//				orderRelatedUserDTO.setElectricUserName(value.getUserName());
//			}
			/*//电气工程师
			if (UserTypeEnum.ELECTRICIAN.getName().equals(key)) {
				orderRelatedUserDTO.setElectricUserId(value.getUserId());
				orderRelatedUserDTO.setElectricUserName(value.getUserName());
			}*/
		});
	}

	@Transactional(rollbackFor = Exception.class)
	public void maintainModifyStatusDealInfo(List<OrderRelatedUserEntity> orderRelatedUserEntityList, OrderRelatedUserDTO orderRelatedUserDTO) {
		//按用户类别分组
		Map<String, OrderRelatedUserEntity> orderRelatedUserEntityMap = orderRelatedUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getUserType())).collect(Collectors.groupingBy(OrderRelatedUserEntity::getUserType, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
		List<String> userIdList = new ArrayList<>();
		List<String> userNameList = new ArrayList<>();
		// 先删除之前选的施工队长和电气工程师
		super.remove(Wrappers.<OrderRelatedUserEntity>lambdaQuery().eq(OrderRelatedUserEntity::getOrderId, orderRelatedUserDTO.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()));
		// 保存施工队长和电气工程师
		this.saveBatch(orderRelatedUserEntityList);
		//施工队长
		OrderRelatedUserEntity siteTechnicianLeaderEntity = orderRelatedUserEntityMap.get(UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		if (ObjectUtils.isNotEmpty(siteTechnicianLeaderEntity)) {
			userIdList.add(orderRelatedUserDTO.getInstallUserId().toString());
			// 施工队长姓名
			if (StringUtils.isNotBlank(siteTechnicianLeaderEntity.getUserName())) {
				userNameList.add(siteTechnicianLeaderEntity.getUserName());
			}
		}
		//电气工程师
//		OrderRelatedUserEntity electricianEntity = orderRelatedUserEntityMap.get(UserTypeEnum.ELECTRICIAN.getName());
//		if (ObjectUtils.isNotEmpty(electricianEntity)) {
//			userIdList.add(orderRelatedUserDTO.getElectricUserId().toString());
//			// 电气工程师姓名
//			if (StringUtils.isNotBlank(electricianEntity.getUserName())) {
//				userNameList.add(electricianEntity.getUserName());
//			}
//		}
		//不需要审批流转，只修改订单流程表的信息
		LambdaUpdateWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		orderWorkFlowEntityLambdaUpdateWrapper.eq(OrderWorkFlowEntity::getOrderId, orderRelatedUserDTO.getOrderId());
		orderWorkFlowEntityLambdaUpdateWrapper.set(OrderWorkFlowEntity::getWfCurrentRole, String.join(",", userIdList));
		orderWorkFlowEntityLambdaUpdateWrapper.set(OrderWorkFlowEntity::getWfCurrentRoleName, String.join(",", userNameList));
		iOrderWorkFlowService.update(orderWorkFlowEntityLambdaUpdateWrapper);
	}

	/**
	 * @Description: 点击modify后的操作
	 * @Param: [orderRelatedUserEntityList, orderRelatedUserDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/5 15:14
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void modifyStatusDealInfo(List<OrderRelatedUserEntity> orderRelatedUserEntityList, OrderRelatedUserDTO orderRelatedUserDTO) {
		//按用户类别分组
		Map<String, OrderRelatedUserEntity> orderRelatedUserEntityMap = orderRelatedUserEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getUserType())).collect(Collectors.groupingBy(OrderRelatedUserEntity::getUserType, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
		//施工队长
		if (ObjectUtils.isNotEmpty(orderRelatedUserEntityMap.get(UserTypeEnum.SITE_TECHNICIAN_LEADER.getName()))) {
			setOrderRelatedPersonInfo(orderRelatedUserEntityMap, orderRelatedUserDTO, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
		}
		//电气工程师
//		if (ObjectUtils.isNotEmpty(orderRelatedUserEntityMap.get(UserTypeEnum.ELECTRICIAN.getName()))) {
//			setOrderRelatedPersonInfo(orderRelatedUserEntityMap, orderRelatedUserDTO, UserTypeEnum.ELECTRICIAN.getName());
//		}
		//不需要审批流转，只修改订单流程表的信息
		LambdaUpdateWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		orderWorkFlowEntityLambdaUpdateWrapper.eq(OrderWorkFlowEntity::getOrderId, orderRelatedUserDTO.getOrderId());
		orderWorkFlowEntityLambdaUpdateWrapper.set(OrderWorkFlowEntity::getWfCurrentRole, String.valueOf(orderRelatedUserDTO.getInstallUserId()));
		if (StringUtils.isNotBlank(orderRelatedUserEntityMap.get(UserTypeEnum.SITE_TECHNICIAN_LEADER.getName()).getUserName())) {
			String userName = orderRelatedUserEntityMap.get(UserTypeEnum.SITE_TECHNICIAN_LEADER.getName()).getUserName();
			orderWorkFlowEntityLambdaUpdateWrapper.set(OrderWorkFlowEntity::getWfCurrentRoleName, userName);
		}
		iOrderWorkFlowService.update(orderWorkFlowEntityLambdaUpdateWrapper);
	}


	/**
	 * @Description: 修改订单人员关系信息
	 * @Param: [orderRelatedUserEntityMap, orderRelatedUserDTO, typeName]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/18 10:28
	 **/
	private void setOrderRelatedPersonInfo(Map<String, OrderRelatedUserEntity> orderRelatedUserEntityMap, OrderRelatedUserDTO orderRelatedUserDTO, String typeName) {
		OrderRelatedUserEntity siteOrderRelatedUserEntity = orderRelatedUserEntityMap.get(typeName);
		LambdaUpdateWrapper<OrderRelatedUserEntity> siteOrderRelatedUserEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		siteOrderRelatedUserEntityLambdaUpdateWrapper.eq(OrderRelatedUserEntity::getOrderId, orderRelatedUserDTO.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode());
		//人员类别
		siteOrderRelatedUserEntityLambdaUpdateWrapper.eq(OrderRelatedUserEntity::getUserType, typeName);
		siteOrderRelatedUserEntityLambdaUpdateWrapper.set(OrderRelatedUserEntity::getUserId, siteOrderRelatedUserEntity.getUserId()).set(OrderRelatedUserEntity::getUserName, siteOrderRelatedUserEntity.getUserName());
		this.update(siteOrderRelatedUserEntityLambdaUpdateWrapper);
	}


	/**
	 * @Description: 收集施工人员和电气工程师信息
	 * @Param: [orderRelatedUserDTO]
	 * @Return: java.util.List<org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity>
	 * @Author: baixu
	 * @Date: 2023/12/5 11:16
	 **/
	private List<OrderRelatedUserEntity> collectWoAssignmentInfo(OrderRelatedUserDTO orderRelatedUserDTO) {
		List<OrderRelatedUserEntity> userList = new ArrayList<>();
		//施工人员
		if (orderRelatedUserDTO.getInstallUserId() != null) {
			OrderRelatedUserEntity installUserEntity = new OrderRelatedUserEntity();
			installUserEntity.setOrderId(Long.parseLong(orderRelatedUserDTO.getOrderFlowDTO().getBusinessId()));
			installUserEntity.setNodeType(DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode());
			installUserEntity.setUserType(UserTypeEnum.SITE_TECHNICIAN_LEADER.getName());
			installUserEntity.setUserId(orderRelatedUserDTO.getInstallUserId());
			installUserEntity.setUserName(orderRelatedUserDTO.getInstallUserName());
			userList.add(installUserEntity);
		}
//		if (orderRelatedUserDTO.getElectricUserId() != null) {
//			//电气工程师
//			OrderRelatedUserEntity electricUserEntity = new OrderRelatedUserEntity();
//			electricUserEntity.setOrderId(orderRelatedUserDTO.getOrderId());
//			electricUserEntity.setNodeType(DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode());
//			electricUserEntity.setUserType(UserTypeEnum.ELECTRICIAN.getName());
//			electricUserEntity.setUserId(orderRelatedUserDTO.getElectricUserId());
//			electricUserEntity.setUserName(orderRelatedUserDTO.getElectricUserName());
//			userList.add(electricUserEntity);
//		}
		return userList;

	}

	private void validMaintainParamStatus(OrderRelatedUserDTO orderRelatedUserDTO) {
		//订单id
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		if (ObjectUtils.isEmpty(orderRelatedUserDTO)) {
			throw new BusinessException("agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull");
		}
		//施工队长id和电气工程师id
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getInstallUserId()) ) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserPerson.notNull");
		}
		//施工队长名称和电气工程师名称
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getInstallUserName())) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserPersonName.notNull");
		}
		//按钮状态
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getModifyStatus())) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.modifyStatus.notNull");
		}
		//禁用状态
		if (CommonConstant.DISABLE_STATUS.equals(orderRelatedUserDTO.getModifyStatus())) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserDTO.modifyStatus.error");
		}
	}

	/**
	 * @Description: 校验参数
	 * @Param: [orderRelatedUserDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 13:46
	 **/
	private void validParamStatus(OrderRelatedUserDTO orderRelatedUserDTO) {
		if (ObjectUtils.isEmpty(orderRelatedUserDTO)) {
			throw new BusinessException("agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull");
		}
		//施工队长id
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getInstallUserId())) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserPerson.notNull");
		}
		//施工队长名称
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getInstallUserName())) {
			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserPersonName.notNull");
		}
		//订单id
		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getOrderFlowDTO().getBusinessId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		if (orderRelatedUserDTO.getConstructionDate() == null) {
			throw new BusinessException("agent.installWoAssignment.constructionDate.notNull");
		}
		//按钮状态
//		if (ObjectUtils.isEmpty(orderRelatedUserDTO.getModifyStatus())) {
//			throw new BusinessException("agent.installWoAssignment.validParamStatus.modifyStatus.notNull");
//		}
		//禁用状态
//		if (CommonConstant.DISABLE_STATUS.equals(orderRelatedUserDTO.getModifyStatus())) {
//			throw new BusinessException("agent.installWoAssignment.validParamStatus.orderRelatedUserDTO.modifyStatus.error");
//		}
	}


	private Boolean checkIfOperate(List<String> roleCodeList, String role) {
		for (String roleCode : roleCodeList) {
			if (role.contains(roleCode)) {
				return true;
			}
		}
		return false;
	}


	private void setWorkFlowData(OrderRelatedUserDTO orderRelatedUserDTO) {
		OrderFlowDTO orderFlowDTO = orderRelatedUserDTO.getOrderFlowDTO();
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//施工人员
		variables.put("siteEngineer", orderRelatedUserDTO.getInstallUserId());
		//电气工程师
//		variables.put("electrician", orderRelatedUserDTO.getElectricUserId());
	}


}

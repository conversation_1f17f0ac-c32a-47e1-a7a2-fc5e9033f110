/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.deliversign.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.deliversign.entity.DeviceItemHistoryEntity;
import org.skyworth.ess.deliversign.service.IDeviceItemHistoryService;
import org.skyworth.ess.deliversign.vo.DeviceItemHistoryVO;
import org.skyworth.ess.deliversign.wrapper.DeviceItemHistoryWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 货物操作记录 控制器
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/deviceItemHistory")
@Api(value = "货物操作记录", tags = "货物操作记录接口")
public class DeviceItemHistoryController extends BladeController {

	private final IDeviceItemHistoryService deviceItemHistoryService;

	/**
	 * 货物操作记录 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceItemHistory")
	public R<DeviceItemHistoryVO> detail(DeviceItemHistoryEntity deviceItemHistory) {
		DeviceItemHistoryEntity detail = deviceItemHistoryService.getOne(Condition.getQueryWrapper(deviceItemHistory));
		return R.data(DeviceItemHistoryWrapper.build().entityVO(detail));
	}


	/**
	 * 货物操作记录 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceItemHistory")
	public R<IPage<DeviceItemHistoryVO>> page(DeviceItemHistoryVO deviceItemHistory, Query query) {
		IPage<DeviceItemHistoryVO> pages = deviceItemHistoryService.selectDeviceItemHistoryPage(Condition.getPage(query), deviceItemHistory);
		return R.data(pages);
	}

	/**
	 * 货物操作记录 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入deviceItemHistory")
	public R save(@Valid @RequestBody DeviceItemHistoryEntity deviceItemHistory) {
		return R.status(deviceItemHistoryService.save(deviceItemHistory));
	}


	/**
	 * 货物操作记录 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入deviceItemHistory")
	public R submit(@Valid @RequestBody DeviceItemHistoryEntity deviceItemHistory) {
		return R.status(deviceItemHistoryService.saveOrUpdate(deviceItemHistory));
	}




}

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.mountingandracking.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.qcsubmission.mountingandracking.entity.MountingAndRackingEntity;
import org.skyworth.ess.qcsubmission.mountingandracking.vo.MountingAndRackingVO;
import org.skyworth.ess.qcsubmission.mountingandracking.excel.MountingAndRackingExcel;
import org.skyworth.ess.qcsubmission.mountingandracking.wrapper.MountingAndRackingWrapper;
import org.skyworth.ess.qcsubmission.mountingandracking.service.IMountingAndRackingService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 施工-支架信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-mountingandracking/mountingandracking")
@Api(value = "施工-支架信息", tags = "施工-支架信息接口")
public class MountingAndRackingController extends BladeController {

	private final IMountingAndRackingService mountingandrackingService;

	/**
	 * 施工-支架信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入mountingandracking")
	public R<MountingAndRackingVO> detail(MountingAndRackingEntity mountingandracking) {
		MountingAndRackingEntity detail = mountingandrackingService.getOne(Condition.getQueryWrapper(mountingandracking));
		return R.data(MountingAndRackingWrapper.build().entityVO(detail));
	}
	/**
	 * 施工-支架信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入mountingandracking")
	public R<IPage<MountingAndRackingVO>> list(@ApiIgnore @RequestParam Map<String, Object> mountingandracking, Query query) {
		IPage<MountingAndRackingEntity> pages = mountingandrackingService.page(Condition.getPage(query), Condition.getQueryWrapper(mountingandracking, MountingAndRackingEntity.class));
		return R.data(MountingAndRackingWrapper.build().pageVO(pages));
	}

	/**
	 * 施工-支架信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入mountingandracking")
	public R<IPage<MountingAndRackingVO>> page(MountingAndRackingVO mountingandracking, Query query) {
		IPage<MountingAndRackingVO> pages = mountingandrackingService.selectMountingAndRackingPage(Condition.getPage(query), mountingandracking);
		return R.data(pages);
	}

	/**
	 * 施工-支架信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入mountingandracking")
	public R save(@Valid @RequestBody MountingAndRackingEntity mountingandracking) {
		return R.status(mountingandrackingService.save(mountingandracking));
	}

	/**
	 * 施工-支架信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入mountingandracking")
	public R update(@Valid @RequestBody MountingAndRackingEntity mountingandracking) {
		return R.status(mountingandrackingService.updateById(mountingandracking));
	}

	/**
	 * 施工-支架信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入mountingandracking")
	public R submit(@Valid @RequestBody MountingAndRackingEntity mountingandracking) {
		return R.status(mountingandrackingService.saveOrUpdate(mountingandracking));
	}

	/**
	 * 施工-支架信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(mountingandrackingService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-mountingandracking")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入mountingandracking")
	public void exportMountingAndRacking(@ApiIgnore @RequestParam Map<String, Object> mountingandracking, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<MountingAndRackingEntity> queryWrapper = Condition.getQueryWrapper(mountingandracking, MountingAndRackingEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(MountingAndRacking::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(MountingAndRackingEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<MountingAndRackingExcel> list = mountingandrackingService.exportMountingAndRacking(queryWrapper);
		ExcelUtil.export(response, "施工-支架信息数据" + DateUtil.time(), "施工-支架信息数据表", list, MountingAndRackingExcel.class);
	}

}

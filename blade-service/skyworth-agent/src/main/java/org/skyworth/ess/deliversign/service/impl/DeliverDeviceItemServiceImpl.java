package org.skyworth.ess.deliversign.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.deliversign.entity.DeviceItemHistoryEntity;
import org.skyworth.ess.deliversign.service.IDeliverDeviceItemService;
import org.skyworth.ess.deliversign.service.IDeviceItemHistoryService;
import org.skyworth.ess.deliversign.vo.DeliverSignItemVO;
import org.skyworth.ess.deliversign.vo.DeliverSignVO;
import org.skyworth.ess.deliversign.vo.ItemNodeEnum;
import org.skyworth.ess.deliversign.vo.ItemStatusEnum;
import org.skyworth.ess.design.designinfo.entity.DesignInfoEntity;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemStatusVO;
import org.skyworth.ess.design.service.IDesignService;
import org.skyworth.ess.design.vo.ProductVO;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.skyworth.ess.quote.entity.DeviceQuoteInfo;
import org.skyworth.ess.quote.service.IDeviceQuoteInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.sku.entity.SkuBaseInfoEntity;
import org.skyworth.ess.sku.service.ISkuBaseInfoService;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class DeliverDeviceItemServiceImpl implements IDeliverDeviceItemService {
    private final IDeviceItemService deviceItemService;
    private final IDesignService designService;
    private final IReviewOrderService reviewOrderService;
    private final IDeviceItemHistoryService deviceItemHistoryService;
    private final IQuoteInfoService quoteInfoService;
    private final IAttachmentInfoClient attachmentInfoClient;
    private ISkuBaseInfoService skuBaseInfoService;
    private IDictBizClient dictBizClient;
    private final IOrderRelatedUserService iOrderRelatedUserService;
    @Override
    public DeviceItemStatusVO queryDeliverDeviceItemList(Long orderId) {
        DeviceItemStatusVO deviceItemStatusVO = deviceItemService.queryDeliverDeviceItemList(orderId);
        DesignInfoEntity designInfo = designService.getOne(Wrappers.<DesignInfoEntity>lambdaQuery()
                .eq(DesignInfoEntity::getOrderId, orderId));
        Date firstDeliverGoodsTime = designInfo.getFirstDeliverGoodsTime();
        // 设置是否第一次发货
        if (Objects.nonNull(firstDeliverGoodsTime)){
            deviceItemStatusVO.setFirstDeliverGoodsFlag(true);
        }else {
            deviceItemStatusVO.setFirstDeliverGoodsFlag(false);
        }
        return deviceItemStatusVO;
    }

    @Override
    public DeviceItemStatusVO querySignDeviceItemList(Long orderId) {
        QuoteInfoEntity quoteInfoEntity = quoteInfoService.getOne(Wrappers.<QuoteInfoEntity>lambdaQuery().eq(QuoteInfoEntity::getOrderId, orderId));
        DeviceItemStatusVO deviceItemStatusVO = deviceItemService.querySignDeviceItemList(orderId);
        deviceItemStatusVO.setReceiptUserSignImgBizKey(quoteInfoEntity.getReceiptUserSignImgBizKey());
        if(quoteInfoEntity.getReceiptUserSignImgBizKey() == null) {
            return deviceItemStatusVO;
        }
        List<Long> businessIds = new ArrayList<>();
        businessIds.add(quoteInfoEntity.getReceiptUserSignImgBizKey());
        Map<Long, List<AttachmentInfoEntity>> attachmentInfoMap = attachmentInfoClient.findByBusinessIds(businessIds).getData();
        if (attachmentInfoMap.isEmpty()) {
            return deviceItemStatusVO;
        }
        deviceItemStatusVO.setAttachmentInfo(attachmentInfoMap.get(businessIds.get(0)));
        return deviceItemStatusVO;
    }

    @Override
    public R<?> deliverAudit(DeliverSignVO deliverSignVO) {
        log.info("deliverAudit begin  " );
        List<DeliverSignItemVO> deliverSignItemVOList = deliverSignVO.getDeliverSignItemVOList();
        if (deliverSignItemVOList == null || deliverSignItemVOList.isEmpty()) {
            log.info("deliverAudit deliverSignItemVOList is null  " );
            throw new BusinessException("deliverSign.item.empty");
        }
        String orderId = deliverSignVO.getOrderFlowDTO().getBusinessId();
        if(StringUtils.isEmpty(orderId)) {
            log.info("deliverAudit orderId is null  " );
            throw new BusinessException("agent.qcSubmission.getBasicInfo.orderId.notEmpty");
        }
        DesignInfoEntity designInfo = designService.getOne(Wrappers.<DesignInfoEntity>lambdaQuery()
                .eq(DesignInfoEntity::getOrderId, Long.parseLong(orderId)));
        log.info("deliverAudit orderId : {} ,designInfo : {}  " ,orderId, designInfo);
        List<String> itemCodeList = deliverSignItemVOList.stream().map(DeliverSignItemVO::getItemCode).collect(Collectors.toList());
        // 查询数据库物料状态，提交过来的数据可能包含 未发货的、差损发货的物料
        List<DeviceItemEntity> dbDeviceItemList = deviceItemService.batchQueryByItemCode(Long.parseLong(orderId), itemCodeList);
        Map<String, String> deliverItemStatusMap = dbDeviceItemList.stream().collect((Collectors.toMap(DeviceItemEntity::getItemCode, DeviceItemEntity::getItemStatus, (v1, v2) -> v1)));
        log.info("deliverAudit orderId : {} ,deliverItemStatusMap : {}  " ,orderId,deliverItemStatusMap);
        // 将提交的物料状态更新为已发货（如果是差损的，则更新为差损发货）
        this.updateItemStatus(deliverSignItemVOList, orderId, deliverItemStatusMap,ItemStatusEnum.DELIVER);
        // 保存物料历史，为发货（如果是差损的，则更新为差损发货）
        this.saveItemHistory(dbDeviceItemList,orderId,deliverItemStatusMap,ItemNodeEnum.DELIVER_ITEM,ItemStatusEnum.DELIVER);
        // 如果第一次提交发货，则提交审核
        Date firstDeliverGoodsTime = designInfo.getFirstDeliverGoodsTime();
        log.info("deliverAudit orderId : {} ,firstDeliverGoodsTime : {}  " ,orderId,firstDeliverGoodsTime);
        if(firstDeliverGoodsTime == null) {
            DesignInfoEntity updateDesign = new DesignInfoEntity();
            updateDesign.setId(designInfo.getId());
            updateDesign.setFirstDeliverGoodsTime(new Date());
            designService.updateById(updateDesign);
            log.info("signSubmit examineApprove : {}  " , orderId);
            Map<String, Object> variables = deliverSignVO.getOrderFlowDTO().getVariables();
            getUserInfo(Long.parseLong(orderId),variables);
            return reviewOrderService.examineApprove(deliverSignVO.getOrderFlowDTO());
        }
        return R.status(true);
    }
    private void getUserInfo(Long orderId, Map<String, Object> variables) {
        LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, orderId).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.SURVEY_WO_ASSIGN.getDictCode()).
                in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN.getName());
        List<OrderRelatedUserEntity> orderRelatedUserEntityList = iOrderRelatedUserService.list(orderRelatedUserEntityLambdaQueryWrapper);
        //踏勘人员
        List<OrderRelatedUserEntity> surveyUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.SITE_TECHNICIAN.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(surveyUserList)) {
            variables.put("siteEngineer", surveyUserList.get(0).getUserId());
        }
    }

    @Override
    public R<?> signAudit(DeliverSignVO deliverSignVO) {
        log.info("signAudit begin  " );
        List<DeliverSignItemVO> deliverSignItemVOList = deliverSignVO.getDeliverSignItemVOList();
//        if (deliverSignItemVOList == null || deliverSignItemVOList.isEmpty()) {
//            log.info("signAudit deliverSignItemVOList is null  " );
//            throw new BusinessException("deliverSign.item.empty");
//        }
        String orderId = deliverSignVO.getOrderFlowDTO().getBusinessId();
        if(StringUtils.isEmpty(orderId)) {
            log.info("signAudit orderId is null  " );
            throw new BusinessException("agent.qcSubmission.getBasicInfo.orderId.notEmpty");
        }
        log.info("signAudit orderId : {}  " ,orderId );
        if (deliverSignItemVOList == null || deliverSignItemVOList.isEmpty()) {
            log.info("signAudit deliverSignItemVOList is null ,all item sign ,orderId : {}",orderId);
        } else {
            log.info("signAudit deliverSignItemVOList is not null ,orderId : {}",orderId);
            List<String> itemCodeList = deliverSignItemVOList.stream().map(DeliverSignItemVO::getItemCode).collect(Collectors.toList());
            // 查询数据库物料状态，提交过来的数据可能包含 未发货的、差损发货的物料
            List<DeviceItemEntity> dbDeviceItemList = deviceItemService.batchQueryByItemCode(Long.parseLong(orderId), itemCodeList);
            // 如果包含差损的，则不能签收，必须全部发货，才能提交签收
            this.validateItemStatus(dbDeviceItemList, orderId);
            // 将提交的物料状态更新为已签收
            Map<String, String> deliverItemStatusMap = dbDeviceItemList.stream()
                    .collect((Collectors.toMap(DeviceItemEntity::getItemCode, DeviceItemEntity::getItemStatus, (v1, v2) -> v1)));
            log.info("signAudit updateItemStatus : {}  ", orderId);
            this.updateItemStatus(deliverSignItemVOList, orderId, deliverItemStatusMap, ItemStatusEnum.SIGN);
            log.info("signAudit saveItemHistory : {}  ", orderId);
            // 保存物料历史 为签收
            this.saveItemHistory(dbDeviceItemList, orderId, deliverItemStatusMap, ItemNodeEnum.SIGN_ITEM, ItemStatusEnum.SIGN);
        }
        // 更新收货人签收图片key信息
        LambdaUpdateWrapper<QuoteInfoEntity> updateQuoteEntity = Wrappers.<QuoteInfoEntity>lambdaUpdate()
                .set(QuoteInfoEntity::getReceiptUserSignImgBizKey, deliverSignVO.getReceiptUserSignImgBizKey())
                .eq(QuoteInfoEntity::getOrderId, Long.parseLong(orderId));
        quoteInfoService.update(updateQuoteEntity);
        log.info("signAudit attachmentInfoClient : {}  " ,orderId );
        // 保存签名图片附件
        if(Objects.nonNull(deliverSignVO.getSkyWorthFileEntity())) {
            attachmentInfoClient.saveAndUpdate(deliverSignVO.getSkyWorthFileEntity().getBatchVO());
        }
        // 提交工作流审核
        reviewOrderService.examineApprove(deliverSignVO.getOrderFlowDTO());
        log.info("signAudit examineApprove : {}  " ,orderId );
        return R.status(true);
    }

    @Override
    public R<?> signLoss(DeliverSignItemVO deliverSignItemVO) {
        log.info("signLoss begin : {}  " , deliverSignItemVO.getOrderId());
        LambdaUpdateWrapper<DeviceItemEntity> updateWrapper = Wrappers.<DeviceItemEntity>lambdaUpdate().set(DeviceItemEntity::getItemStatus, ItemStatusEnum.LOSS.getCode())
                .set(DeviceItemEntity::getRemark, deliverSignItemVO.getRemark()).eq(DeviceItemEntity::getItemCode, deliverSignItemVO.getItemCode())
                .eq(DeviceItemEntity::getOrderId, deliverSignItemVO.getOrderId()).eq(DeviceItemEntity::getIsDeleted, 0);
        deviceItemService.update(updateWrapper);
        List<String> itemCodeList = Collections.singletonList(deliverSignItemVO.getItemCode());
        // 更新差损原因和货物状态为差损
        List<DeviceItemEntity> dbDeviceItemList = deviceItemService.batchQueryByItemCode(Long.valueOf(deliverSignItemVO.getOrderId()), itemCodeList);
        Map<String, String> deliverItemStatusMap = dbDeviceItemList.stream().collect((Collectors.toMap(DeviceItemEntity::getItemCode, DeviceItemEntity::getItemStatus, (v1, v2) -> v1)));
        // 写入历史
        this.saveItemHistory(dbDeviceItemList,String.valueOf(deliverSignItemVO.getOrderId()),deliverItemStatusMap
                ,ItemNodeEnum.SIGN_ITEM,ItemStatusEnum.LOSS);
        return R.status(true);
    }

    @Override
    public R<?> signSubmit(DeliverSignItemVO deliverSignItemVO) {
        log.info("signSubmit begin : {}  " , deliverSignItemVO.getOrderId());
        LambdaUpdateWrapper<DeviceItemEntity> updateWrapper = Wrappers.<DeviceItemEntity>lambdaUpdate().set(DeviceItemEntity::getItemStatus, ItemStatusEnum.SIGN.getCode())
                .eq(DeviceItemEntity::getItemCode, deliverSignItemVO.getItemCode())
                .eq(DeviceItemEntity::getOrderId, deliverSignItemVO.getOrderId()).eq(DeviceItemEntity::getIsDeleted, 0);
        deviceItemService.update(updateWrapper);
        List<String> itemCodeList = Collections.singletonList(deliverSignItemVO.getItemCode());
        // 更新货物状态为签收
        List<DeviceItemEntity> dbDeviceItemList = deviceItemService.batchQueryByItemCode(Long.valueOf(deliverSignItemVO.getOrderId()), itemCodeList);
        Map<String, String> deliverItemStatusMap = dbDeviceItemList.stream().collect((Collectors.toMap(DeviceItemEntity::getItemCode, DeviceItemEntity::getItemStatus, (v1, v2) -> v1)));
        log.info("signSubmit saveItemHistory : {}  " , deliverSignItemVO.getOrderId());
        // 写入历史
        this.saveItemHistory(dbDeviceItemList,String.valueOf(deliverSignItemVO.getOrderId()),deliverItemStatusMap
                ,ItemNodeEnum.SIGN_ITEM,ItemStatusEnum.SIGN);
        return R.status(true);
    }
    @Override
    public Map<String, List<ProductVO>> signHistory(Long orderId) {
        Map<String, List<ProductVO>> resultMap = new HashMap<>();
        LambdaQueryWrapper<DeviceItemHistoryEntity> queryWrapper = Wrappers.<DeviceItemHistoryEntity>lambdaQuery().eq(DeviceItemHistoryEntity::getOrderId, orderId)
                .eq(DeviceItemHistoryEntity::getOperateNode, ItemNodeEnum.SIGN_ITEM.getCode())
                .eq(DeviceItemHistoryEntity::getOperateType, ItemStatusEnum.SIGN.getCode());
        List<DeviceItemHistoryEntity> historyEntityList = deviceItemHistoryService.list(queryWrapper);
        if(CollectionUtil.isEmpty(historyEntityList)) {
            return resultMap;
        }
        R<List<DictBiz>> listByLang = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_SKU_DEVICE_TYPE.getDictCode(), CommonUtil.getCurrentLanguage());
        Map<String, String> skuTypeMap = listByLang.getData().stream().collect(Collectors.toMap(DictBiz::getDictValue, DictBiz::getDictKey, (p, p1) -> p));
        List<String> skuCodeList = historyEntityList.stream().map(DeviceItemHistoryEntity::getItemCode).collect(Collectors.toList());
        List<SkuBaseInfoEntity> skuBaseInfoEntities = skuBaseInfoService.querySkuBaseInfoBySkuCodeList(skuCodeList);
        Map<String, List<SkuBaseInfoEntity>> skuBaseMap = skuBaseInfoEntities.stream().collect(Collectors.groupingBy(SkuBaseInfoEntity::getSkuCode));
        // 查询物料状态
        R<List<DictBiz>> dictList = dictBizClient.getListByLang(DictBizCodeEnum.AGENT_DEVICE_ITEM_STATUS.getDictCode(), CommonUtil.getCurrentLanguage());
        Map<String, String> deviceItemStatusDictMap = dictList.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (p, p1) -> p));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<ProductVO> signList = new ArrayList<>();
        for(DeviceItemHistoryEntity historyEntity : historyEntityList) {
            ProductVO productVO = new ProductVO();
            productVO.setItemCode(historyEntity.getItemCode());
            productVO.setItemStatus(historyEntity.getOperateType());
            productVO.setItemStatusName(deviceItemStatusDictMap.get(historyEntity.getOperateType()));
            productVO.setQuantity(Long.parseLong(historyEntity.getQuantity()));
            List<SkuBaseInfoEntity> skuBaseInfoEntityList = skuBaseMap.get(historyEntity.getItemCode());
            String format = simpleDateFormat.format(historyEntity.getCreateTime());
            productVO.setGroupDate(format);
            signList.add(productVO);
            if(CollectionUtil.isEmpty(skuBaseInfoEntityList)) {
                continue;
            }
            SkuBaseInfoEntity skuBaseInfo = skuBaseInfoEntityList.get(0);
            productVO.setSkuName(skuBaseInfo.getSkuName());
            productVO.setSkuDeviceType(skuBaseInfo.getSkuDeviceType());
            productVO.setSkuDeviceTypeName(skuTypeMap.get(skuBaseInfo.getSkuDeviceType()));
            productVO.setStandards(skuBaseInfo.getStandards());
            productVO.setSkuCompany(skuBaseInfo.getSkuCompany());
            productVO.setUnit(skuBaseInfo.getUnit());
        }

        resultMap = signList.stream().collect(Collectors.groupingBy(ProductVO::getGroupDate, () -> new TreeMap<>(Comparator.reverseOrder()), Collectors.toList()));
        return resultMap;
    }
    private void validateItemStatus(List<DeviceItemEntity> dbDeviceItemList, String orderId) {
        StringBuilder sb = new StringBuilder();
        // 如果包含差损的，则不能签收
        for(DeviceItemEntity dbData : dbDeviceItemList) {
            if(ItemStatusEnum.LOSS.getCode().equals(dbData.getItemStatus())) {
                log.info("signAudit orderId : {} ,itemCode : {} ,itemStatus : {}  " , orderId,dbData.getItemCode(),dbData.getItemStatus());
                sb.append(dbData.getItemCode()).append(",");
            }
        }
        if(StringUtils.isNotEmpty(sb.toString())) {
            throw new BusinessException("agent.deliverSign.item.status.has.not.deliver",sb.toString());
        }
    }

    private void updateItemStatus(List<DeliverSignItemVO> deliverSignItemVOList, String orderId, Map<String, String> deliverItemStatusMap,ItemStatusEnum itemStatusEnum) {
        List<DeviceItemEntity> submitItemList = deliverSignItemVOList.stream().map(p -> {
            DeviceItemEntity deviceItemEntity = new DeviceItemEntity();
            deviceItemEntity.setOrderId(Long.parseLong(orderId));
            deviceItemEntity.setItemCode(p.getItemCode());
            String itemStatus = deliverItemStatusMap.get(p.getItemCode());
            log.info("deliverAudit orderId : {} , itemCode : {}, itemStatus : {} ", orderId,p.getItemCode(),itemStatus);
            // 如果是差损的发货，则更新为差损发货，未发货的更新为已发货
            if(ItemStatusEnum.LOSS.getCode().equals(itemStatus)) {
                deviceItemEntity.setItemStatus(ItemStatusEnum.LOSS_DELIVER.getCode());
            } else {
                deviceItemEntity.setItemStatus(itemStatusEnum.getCode());
            }
            return deviceItemEntity;
        }).collect(Collectors.toList());
        deviceItemService.batchUpdateByItemCode(Long.parseLong(orderId),submitItemList);
    }

    private void saveItemHistory(List<DeviceItemEntity> dbDeviceItemList, String orderId,Map<String, String> dbDeliverItemStatusMap,
                                 ItemNodeEnum itemNodeEnum,ItemStatusEnum itemStatusEnum) {
        List<DeviceItemHistoryEntity> insertList = new ArrayList<>();
        for(DeviceItemEntity deviceItemEntity : dbDeviceItemList) {
            DeviceItemHistoryEntity insertDeviceItemHistoryEntity = new DeviceItemHistoryEntity();
            insertDeviceItemHistoryEntity.setOrderId(Long.parseLong(orderId));
            insertDeviceItemHistoryEntity.setItemCode(deviceItemEntity.getItemCode());
            insertDeviceItemHistoryEntity.setOperateNode(itemNodeEnum.getCode());
            String itemStatus = dbDeliverItemStatusMap.get(deviceItemEntity.getItemCode());
            if(ItemStatusEnum.LOSS.getCode().equals(itemStatus)) {
                insertDeviceItemHistoryEntity.setOperateType(ItemStatusEnum.LOSS_DELIVER.getCode());
            } else {
                insertDeviceItemHistoryEntity.setOperateType(itemStatusEnum.getCode());
            }
//            insertDeviceItemHistoryEntity.setOperateType(deviceItemEntity.getItemStatus());
            insertDeviceItemHistoryEntity.setQuantity(deviceItemEntity.getQuantity());
            insertDeviceItemHistoryEntity.setRemark(deviceItemEntity.getRemark());
            insertList.add(insertDeviceItemHistoryEntity);
        }
        deviceItemHistoryService.saveBatch(insertList);
    }
}

package org.skyworth.ess.createorder.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.common.service.IOperateFileService;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.springblade.common.constant.OrderStatusConstants;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.entity.MaintOrderInstallRelatedEntity;
import org.skyworth.ess.createorder.order.mapper.OrderMapper;
import org.skyworth.ess.createorder.order.service.IOrderCreateService;
import org.skyworth.ess.createorder.order.service.IMaintOrderInstallRelatedService;
import org.skyworth.ess.createorder.order.vo.MaintOrderVO;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.mail.SendMail;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springblade.system.entity.RedisSeqEnums;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("maintOrderCreateServiceImpl")
@RequiredArgsConstructor
public class MaintOrderCreateServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> implements IOrderCreateService {
	private final IFlowClient flowClient;

	private final IDictBizClient dictBizClient;

	private final ISysClient iSysClient;

	private final IOrderWorkFlowService iOrderWorkFlowService;

	private final SendMail sendMail;

	private final IMaintOrderInstallRelatedService maintOrderInstallRelatedService;
	private final IOperateFileService operateFileService;
	private final IAgentCompanyInfoService iAgentCompanyInfoService;
	@Override
	public <T> Boolean saveOrder(Supplier<T> supplier) {
		MaintOrderVO maintOrderVO = (MaintOrderVO) supplier.get();
		OrderEntity order = maintOrderVO.getOrderEntity();
		String orderNumber = saveOrderInfo(order);
		operateFileService.saveFile(maintOrderVO.getSkyWorthFileEntity());
		OrderFlowVO orderFlowVO = this.startFlow(flowClient, String.valueOf(order.getId()), CommonConstant.PROCESS_MAINTENANCE_KEY);
		this.insertBusinessFlow(iOrderWorkFlowService,String.valueOf(order.getId()),orderFlowVO);
		this.afterSendEmail(dictBizClient,sendMail,orderNumber,order);
//		//开启审批流程
//		R<OrderFlowVO> orderProcess = flowClient.startOrderProcess(String.valueOf(order.getId()));
//		if (!orderProcess.isSuccess() || ObjectUtils.isEmpty(orderProcess.getData())) {
//			throw new BusinessException("agent.createOrder.insertOrder.startProcessFail");
//		}
//		OrderFlowVO orderFlowVO = orderProcess.getData();
//		//保存审批流相关信息
//		OrderWorkFlowEntity orderWorkFlowEntity = getOrderWorkFlowEntity(orderFlowVO);
//		orderWorkFlowEntity.setOrderId(order.getId());
//		boolean workFlowSaveFlag = iOrderWorkFlowService.save(orderWorkFlowEntity);
//		if (!workFlowSaveFlag) {
//			throw new BusinessException("agent.createOrder.insertOrder.workFlowSaveFail");
//		}

		return true;
	}
	@Override
	public <T> Boolean saveCancelOrder(Supplier<T> supplier) {
		MaintOrderVO maintOrderVO = (MaintOrderVO) supplier.get();
		OrderEntity order = maintOrderVO.getOrderEntity();
		this.saveOrderInfo(order);
		operateFileService.saveFile(maintOrderVO.getSkyWorthFileEntity());
		OrderFlowVO orderFlowVO = new OrderFlowVO();
		orderFlowVO.setTaskName(OrderStatusConstants.ORDER_STATUS_CLOSED);
		this.insertBusinessFlow(iOrderWorkFlowService,String.valueOf(order.getId()),orderFlowVO);
		return true;
	}

	private String saveOrderInfo(OrderEntity order) {
		R<String> getOrderNumberResult = iSysClient.getRedisUniqueId(RedisSeqEnums.ORDER_MAINTENANCE_NO_SEQ);
		if (getOrderNumberResult == null || !getOrderNumberResult.isSuccess() || ObjectUtils.isEmpty(getOrderNumberResult.getData())) {
			throw new BusinessException("agent.createOrder.insertOrder.getOrderNumber.notNull");
		}
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		agentCompanyInfoEntityLambdaQueryWrapper.eq(AgentCompanyInfoEntity::getId, order.getDistributorId());
		agentCompanyInfoEntityLambdaQueryWrapper.select(AgentCompanyInfoEntity::getCompanyAttributes, AgentCompanyInfoEntity::getDeptId);
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getOne(agentCompanyInfoEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(agentCompanyInfoEntity) || ObjectUtils.isEmpty(agentCompanyInfoEntity.getCompanyAttributes())) {
			throw new BusinessException("agent.createOrder.insertOrder.getCompanyAttributes.fail");
		}

		//添加代理商部门
		order.setBelongDeptId(agentCompanyInfoEntity.getDeptId());
		String orderNumber = getOrderNumberResult.getData();
		order.setOrderNumber(orderNumber);
		boolean saveFlag = this.save(order);
		if (!saveFlag) {
			throw new BusinessException("agent.createOrder.insertOrder.saveOrderFail");
		}
		// 保存安装订单 和 维保订单关系
		MaintOrderInstallRelatedEntity insertEntity = new MaintOrderInstallRelatedEntity();
		insertEntity.setOrderInstallId(order.getOrderInstallId());
		insertEntity.setOrderMaintId(order.getId());
		maintOrderInstallRelatedService.save(insertEntity);
		return orderNumber;
	}

}

package org.skyworth.ess.cocmaintenanceinfo.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName MaintenanceCocCreationController
 * @Description 运维coc和维护信息控制器
 * @Date 2024/4/17 10:55
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("maintenanceCocCreation")
@Api(value = "21-运维coc生成", tags = "运维管理流程相关接口")
public class MaintenanceCocCreationController extends BladeController {

    private final ICocMaintenanceInfoService cocMaintenanceInfoService;


    /**
     * COC详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "21-运维coc生成-coc详情", notes = "传入orderId")
    public R<CocMaintenanceInfoDTO> cocDetail(@RequestParam Long orderId) {
        return R.data(cocMaintenanceInfoService.temporaryCocDetail(orderId));
    }

    /**
     * COC提交
     */
    @PostMapping("/audit")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "21-运维coc生成-coc审核", notes = "CocMaintenanceInfoDTO")
    public R cocAudit(@Valid @RequestBody CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
        try {
//            if (ValidationUtil.isEmpty(cocMaintenanceInfoDTO.getTemporaryCocStartDate())) {
//                return R.fail("please select startDate");
//            }
            return R.status(cocMaintenanceInfoService.temporaryCocAudit(cocMaintenanceInfoDTO));
        } catch (Exception e) {
            log.error(e.getMessage());
            return R.status(false);
        }
    }

}

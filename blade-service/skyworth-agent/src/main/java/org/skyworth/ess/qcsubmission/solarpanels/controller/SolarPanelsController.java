/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.solarpanels.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.qcsubmission.solarpanels.entity.SolarPanelsEntity;
import org.skyworth.ess.qcsubmission.solarpanels.vo.SolarPanelsVO;
import org.skyworth.ess.qcsubmission.solarpanels.excel.SolarPanelsExcel;
import org.skyworth.ess.qcsubmission.solarpanels.wrapper.SolarPanelsWrapper;
import org.skyworth.ess.qcsubmission.solarpanels.service.ISolarPanelsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 施工-光伏板信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-solarpanels/solarpanels")
@Api(value = "施工-光伏板信息", tags = "施工-光伏板信息接口")
public class SolarPanelsController extends BladeController {

	private final ISolarPanelsService solarpanelsService;

	/**
	 * 施工-光伏板信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入solarpanels")
	public R<SolarPanelsVO> detail(SolarPanelsEntity solarpanels) {
		SolarPanelsEntity detail = solarpanelsService.getOne(Condition.getQueryWrapper(solarpanels));
		return R.data(SolarPanelsWrapper.build().entityVO(detail));
	}
	/**
	 * 施工-光伏板信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入solarpanels")
	public R<IPage<SolarPanelsVO>> list(@ApiIgnore @RequestParam Map<String, Object> solarpanels, Query query) {
		IPage<SolarPanelsEntity> pages = solarpanelsService.page(Condition.getPage(query), Condition.getQueryWrapper(solarpanels, SolarPanelsEntity.class));
		return R.data(SolarPanelsWrapper.build().pageVO(pages));
	}

	/**
	 * 施工-光伏板信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入solarpanels")
	public R<IPage<SolarPanelsVO>> page(SolarPanelsVO solarpanels, Query query) {
		IPage<SolarPanelsVO> pages = solarpanelsService.selectSolarPanelsPage(Condition.getPage(query), solarpanels);
		return R.data(pages);
	}

	/**
	 * 施工-光伏板信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入solarpanels")
	public R save(@Valid @RequestBody SolarPanelsEntity solarpanels) {
		return R.status(solarpanelsService.save(solarpanels));
	}

	/**
	 * 施工-光伏板信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入solarpanels")
	public R update(@Valid @RequestBody SolarPanelsEntity solarpanels) {
		return R.status(solarpanelsService.updateById(solarpanels));
	}

	/**
	 * 施工-光伏板信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入solarpanels")
	public R submit(@Valid @RequestBody SolarPanelsEntity solarpanels) {
		return R.status(solarpanelsService.saveOrUpdate(solarpanels));
	}

	/**
	 * 施工-光伏板信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(solarpanelsService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-solarpanels")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入solarpanels")
	public void exportSolarPanels(@ApiIgnore @RequestParam Map<String, Object> solarpanels, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<SolarPanelsEntity> queryWrapper = Condition.getQueryWrapper(solarpanels, SolarPanelsEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(SolarPanels::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(SolarPanelsEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<SolarPanelsExcel> list = solarpanelsService.exportSolarPanels(queryWrapper);
		ExcelUtil.export(response, "施工-光伏板信息数据" + DateUtil.time(), "施工-光伏板信息数据表", list, SolarPanelsExcel.class);
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.createorder.order.mapper.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderResultMap" type="org.skyworth.ess.createorder.order.entity.OrderEntity">
        <result column="id" property="id"/>
        <result column="order_number" property="orderNumber"/>
        <result column="distributor_id" property="distributorId"/>
        <result column="rollout_manager_id" property="rolloutManagerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
<!--        <result column="customer_email" property="customerEmail"/>-->
<!--        <result column="customer_company_name" property="customerCompanyName"/>-->
        <result column="site_country_code" property="siteCountryCode"/>
        <result column="site_province_code" property="siteProvinceCode"/>
        <result column="site_city_code" property="siteCityCode"/>
        <result column="site_county_code" property="siteCountyCode"/>
        <result column="site_address" property="siteAddress"/>
<!--        <result column="install_reason" property="installReason"/>-->
        <result column="install_budgets" property="installBudgets"/>
<!--        <result column="tentative_install_start_date" property="tentativeInstallStartDate"/>-->
<!--        <result column="tentative_install_end_date" property="tentativeInstallEndDate"/>-->
        <result column="survey_date" property="surveyDate"/>
<!--        <result column="additional_doc_biz_key" property="additionalDocBizKey"/>-->
<!--        <result column="second_contact_name" property="secondContactName"/>-->
<!--        <result column="second_contact_phone" property="secondContactPhone"/>-->
        <result column="project_type" property="projectType"/>
        <result column="project_type_other_remark" property="projectTypeOtherRemark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
<!--        <result column="distance_restriction" property="distanceRestriction"/>-->
        <result column="order_type" property="orderType"/>
<!--        <result column="maint_problem_description_remark" property="maintProblemDescriptionRemark"/>-->
<!--        <result column="maint_problem_description_img_biz_key" property="maintProblemDescriptionImgBizKey"/>-->
<!--        <result column="maint_solution_remark" property="maintSolutionRemark"/>-->
<!--        <result column="maint_solution_img_biz_key" property="maintSolutionImgBizKey"/>-->
<!--        <result column="maint_call_in_times" property="maintCallInTimes"/>-->
<!--        <result column="maint_fee_type" property="maintFeeType"/>-->
    </resultMap>


    <select id="selectOrderPage" resultMap="orderResultVOMap">
        select o.*,f.wf_instance_id,f.task_id, f.audit_status,f.wf_current_status,f.wf_current_type,
        f.wf_current_role,f.wf_current_role_name ,round(TIMESTAMPDIFF(SECOND,f.update_time,now())/(3600*24)) as statusLastingTime,f.update_time as wf_update_time,
        a.company_name as agent_company_name
        from business_order o inner join business_order_work_flow f  on o.id = f.order_id and f.is_deleted = 0
        left join agent_company_info a on o.distributor_id = a.id and a.is_deleted = 0
        where o.is_deleted = 0 and (order_type is null or order_type = #{param2.orderEntity.orderType})
        <if test="param2.roleType == 'inner'">
            and 1=1
        </if>
        <if test="param2.roleType == 'out'">
            <if test="param2.agentOrInstallType == 'agent'">
                and ( o.belong_dept_id = #{param2.userDeptId} or o.id in
                (select oru.order_id  from business_order_related_user oru where oru.user_id = #{param2.installUserId} ) )
            </if>
            <if test="param2.agentOrInstallType == 'install' ">
                and o.id in
                (select oru.order_id  from business_order_related_user oru where oru.user_id = #{param2.installUserId} )
            </if>
        </if>
        <if test="param2.queryOrderNumber != null and param2.queryOrderNumber != ''">
            and o.order_number like CONCAT('%', #{param2.queryOrderNumber},'%')
        </if>
        <if test="param2.wfCurrentStatus != null and param2.wfCurrentStatus != ''">
            and f.wf_current_status = #{param2.wfCurrentStatus}
        </if>
        <if test="param2.wfCurrentStatus == null or param2.wfCurrentStatus == ''">
            and f.wf_current_status  !='cancel'
        </if>
        <if test="param2.querySiteCountryCode != null and param2.querySiteCountryCode != ''">
            and o.site_country_code = #{param2.querySiteCountryCode}
        </if>
        <if test="param2.querySiteProvinceCode != null and param2.querySiteProvinceCode != ''">
            and o.site_province_code = #{param2.querySiteProvinceCode}
        </if>
        <if test="param2.querySiteCityCode != null and param2.querySiteCityCode != ''">
            and o.site_city_code = #{param2.querySiteCityCode}
        </if>
        order by o.id desc
    </select>


    <select id="exportOrder" resultType="org.skyworth.ess.createorder.order.excel.OrderExcel">
        SELECT * FROM business_order ${ew.customSqlSegment}
    </select>

    <select id="maintQueryInstallOrder" resultMap="orderResultVOMap">
        select o.*,DATE_FORMAT(c.warranty_date, '%Y-%m-%d') as warranty_date,
        DATE_FORMAT(c.temporary_coc_start_date, '%Y-%m-%d') as temporary_coc_start_date,
        c.temporary_coc_img_biz_key from business_order o
        left join coc_maintenance_info c on o.id = c.order_id and c.is_deleted = 0
        inner join business_order_work_flow wf on wf.order_id =o.id and wf.is_deleted =0
        where o.is_deleted =0 and (order_type is null or order_type ='guardian')
        and wf.wf_current_status ='end'
        <if test="params.customerName != null and params.customerName != ''">
           and o.customer_name like CONCAT('%', #{params.customerName},'%')
        </if>
    </select>

    <select id="queryOrderWorkFlowInfo" resultMap="orderResultVOMap">
        select o.*,f.wf_current_status  from business_order o inner join business_order_work_flow f on o.id =f.order_id
        where o.is_deleted = 0 and o.id=#{orderId}
    </select>
    <resultMap id="orderResultVOMap" type="org.skyworth.ess.createorder.order.vo.OrderVO">
        <result column="id" property="id"/>
        <result column="order_number" property="orderEntity.orderNumber"/>
        <result column="distributor_id" property="orderEntity.distributorId"/>
        <result column="rollout_manager_id" property="orderEntity.rolloutManagerId"/>
        <result column="customer_name" property="orderEntity.customerName"/>
        <result column="customer_phone" property="orderEntity.customerPhone"/>
        <result column="customer_email" property="orderEntity.customerEmail"/>
        <result column="customer_company_name" property="orderEntity.customerCompanyName"/>
        <result column="site_country_code" property="orderEntity.siteCountryCode"/>
        <result column="site_province_code" property="orderEntity.siteProvinceCode"/>
        <result column="site_city_code" property="orderEntity.siteCityCode"/>
        <result column="site_county_code" property="orderEntity.siteCountyCode"/>
        <result column="site_address" property="orderEntity.siteAddress"/>
<!--        <result column="install_reason" property="orderEntity.installReason"/>-->
        <result column="install_budgets" property="orderEntity.installBudgets"/>
<!--        <result column="tentative_install_start_date" property="orderEntity.tentativeInstallStartDate"/>-->
<!--        <result column="tentative_install_end_date" property="orderEntity.tentativeInstallEndDate"/>-->
        <result column="survey_date" property="orderEntity.surveyDate"/>
<!--        <result column="additional_doc_biz_key" property="orderEntity.additionalDocBizKey"/>-->
<!--        <result column="second_contact_name" property="orderEntity.secondContactName"/>-->
<!--        <result column="second_contact_phone" property="orderEntity.secondContactPhone"/>-->
        <result column="project_type" property="orderEntity.projectType"/>
        <result column="project_type_other_remark" property="orderEntity.projectTypeOtherRemark"/>
        <result column="order_type" property="orderEntity.orderType"/>
        <result column="latitude" property="orderEntity.longitude"/>
        <result column="latitude" property="orderEntity.latitude"/>
<!--        <result column="distance_restriction" property="orderEntity.distanceRestriction"/>-->
        <result column="create_user" property="orderCreateUser"/>
        <result column="create_time" property="orderCreateTime"/>

        <result column="wf_instance_id" property="wfInstanceId"/>
        <result column="task_id" property="taskId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="wf_current_status" property="wfCurrentStatus"/>
        <result column="wf_current_type" property="wfCurrentType"/>
        <result column="wf_current_role" property="wfCurrentRole"/>
        <result column="wf_current_role_name" property="wfCurrentRoleName"/>
        <result column="wf_update_time" property="wfUpdateTime"/>
        <result column="statusLastingTime" property="statusLastingTime"/>
        <result column="agent_company_name" property="agentCompanyName"/>
        <result column="warranty_date" property="warrantyExpirationDate"/>
        <result column="temporary_coc_start_date" property="cocCreationDate"/>
        <result column="temporary_coc_img_biz_key" property="cocCreationDateImgBizKey"/>
    </resultMap>


</mapper>

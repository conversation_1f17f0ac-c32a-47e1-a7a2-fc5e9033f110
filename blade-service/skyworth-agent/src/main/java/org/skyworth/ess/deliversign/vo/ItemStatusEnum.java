package org.skyworth.ess.deliversign.vo;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ItemStatusEnum {
    UN_DELIVER("un_deliver", "未发货"),
    DELIVER("deliver", "已发货"),
    SIGN("sign", "签收"),
    LOSS("loss", "差损"),
    // 用于前端区分 差损 和 差损已发货，前端展示都是差损原因，差损展示蓝色，差损已发货展示橙色
    LOSS_DELIVER("loss_deliver", "差损已发货");

    private final String code;
    private final String desc;

    ItemStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

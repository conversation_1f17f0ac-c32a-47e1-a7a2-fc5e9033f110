/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.paymentconfirmation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.paymentconfirmation.quoteInfo.dto.QuoteInfoDTO;
import org.skyworth.ess.paymentconfirmation.quoteInfo.entity.QuoteInfoEntity;
import org.skyworth.ess.paymentconfirmation.quoteInfo.excel.QuoteInfoExcel;
import org.skyworth.ess.paymentconfirmation.quoteInfo.service.IQuoteInfoService;
import org.skyworth.ess.paymentconfirmation.quoteInfo.vo.QuoteInfoVO;
import org.skyworth.ess.paymentconfirmation.quoteInfo.wrapper.QuoteInfoWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 设备报价信息 控制器
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-quoteInfo/quoteInfo")
@Api(value = "设备报价信息", tags = "设备报价信息接口")
public class QuoteInfoController extends BladeController {

	private final IQuoteInfoService quoteInfoService;

	/**
	 * 设备报价信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入quoteInfo")
	public R<QuoteInfoVO> detail(QuoteInfoEntity quoteInfo) {
		QuoteInfoEntity detail = quoteInfoService.getOne(Condition.getQueryWrapper(quoteInfo));
		return R.data(QuoteInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 设备报价信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入quoteInfo")
	public R<IPage<QuoteInfoVO>> list(@ApiIgnore @RequestParam Map<String, Object> quoteInfo, Query query) {
		IPage<QuoteInfoEntity> pages = quoteInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(quoteInfo, QuoteInfoEntity.class));
		return R.data(QuoteInfoWrapper.build().pageVO(pages));
	}

	/**
	 * 设备报价信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入quoteInfo")
	public R<IPage<QuoteInfoVO>> page(QuoteInfoVO quoteInfo, Query query) {
		IPage<QuoteInfoVO> pages = quoteInfoService.selectQuoteInfoPage(Condition.getPage(query), quoteInfo);
		return R.data(pages);
	}

	/**
	 * 设备报价信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入quoteInfo")
	public R save(@Valid @RequestBody QuoteInfoEntity quoteInfo) {
		return R.status(quoteInfoService.save(quoteInfo));
	}

	/**
	 * 设备报价信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入quoteInfo")
	public R update(@Valid @RequestBody QuoteInfoEntity quoteInfo) {
		return R.status(quoteInfoService.updateById(quoteInfo));
	}

	/**
	 * 设备报价信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入quoteInfo")
	public R submit(@Valid @RequestBody QuoteInfoEntity quoteInfo) {
		return R.status(quoteInfoService.saveOrUpdate(quoteInfo));
	}

	/**
	 * 设备报价信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(quoteInfoService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 审核安装付款确认订单
	 */
	@PostMapping("/installPayConfirm")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "审核安装付款确认订单", notes = "传入quoteInfoDTO")
	public R<?> installPayConfirm(@RequestBody @Valid QuoteInfoDTO quoteInfoDTO) {
		return quoteInfoService.installPayConfirm(quoteInfoDTO);
	}


	/**
	 * 审核安装付款取消订单
	 */
	@PostMapping("/installPayCancel")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "审核安装付款取消订单", notes = "传入quoteInfoDTO")
	public R<?> installPayCancel(@RequestBody QuoteInfoDTO quoteInfoDTO) {
		return quoteInfoService.installPayConfirm(quoteInfoDTO);
	}


	/**
	 * 查询安装付款确认信息
	 */
	@PostMapping("/selectInstallPayConfirmInfo")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "查询安装付款确认信息", notes = "传入quoteInfoDTO")
	public R<?> selectInstallPayConfirmInfo(@RequestBody QuoteInfoDTO quoteInfoDTO) {
		return quoteInfoService.selectInstallPayConfirmInfo(quoteInfoDTO);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-quoteInfo")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导出数据", notes = "传入quoteInfo")
	public void exportQuoteInfo(@ApiIgnore @RequestParam Map<String, Object> quoteInfo, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<QuoteInfoEntity> queryWrapper = Condition.getQueryWrapper(quoteInfo, QuoteInfoEntity.class);
		queryWrapper.lambda().eq(QuoteInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<QuoteInfoExcel> list = quoteInfoService.exportQuoteInfo(queryWrapper);
		ExcelUtil.export(response, "设备报价信息数据" + DateUtil.time(), "设备报价信息数据表", list, QuoteInfoExcel.class);
	}

}

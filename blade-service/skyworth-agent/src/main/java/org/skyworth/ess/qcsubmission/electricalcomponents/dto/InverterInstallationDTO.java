package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.qcsubmission.equipmentdetails.entity.EquipmentDetailsEntity;
import org.springblade.core.tenant.mp.SkyWorthEntity;

import java.util.List;

/**
 * 智能能量变换器安装 视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InverterInstallationDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 智能能量变换器类型
	 */
	@ApiModelProperty(value = "智能能量变换器类型")
	private String inverterType;
	/**
	 * 智能能量变换器类型名称
	 */
	@ApiModelProperty(value = "智能能量变换器类型名称")
	private String inverterTypeName;
	/**
	 * 智能能量变换器选择other的描述
	 */
	@ApiModelProperty(value = "智能能量变换器选择other的描述")
	private String inverterTypeOtherRemark;

	/**
	 * 智能能量变换器规范安装图片业务主键
	 */
	@ApiModelProperty(value = "智能能量变换器规范安装图片业务主键")
	private Long verificationOfSpecificationModelImgBizKey;

	/**
	 * 智能能量变换器设备列表明细
	 */
	@ApiModelProperty(value = "智能能量变换器设备列表明细")
	private List<EquipmentDetailsEntity> equipmentDetailsList;
}

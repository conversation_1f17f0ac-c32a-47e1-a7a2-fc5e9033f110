<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.revieworder.orderworkflow.mapper.OrderWorkFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="orderWorkFlowResultMap" type="org.skyworth.ess.entity.OrderWorkFlowEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="wf_instance_id" property="wfInstanceId"/>
        <result column="task_id" property="taskId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="wf_current_status" property="wfCurrentStatus"/>
        <result column="wf_current_type" property="wfCurrentType"/>
        <result column="wf_current_role" property="wfCurrentRole"/>
        <result column="wf_current_role_name" property="wfCurrentRoleName"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectOrderWorkFlowPage" resultMap="orderWorkFlowResultMap">
        select * from business_order_work_flow where is_deleted = 0
    </select>


    <select id="exportOrderWorkFlow" resultType="org.skyworth.ess.revieworder.orderworkflow.excel.OrderWorkFlowExcel">
        SELECT * FROM business_order_work_flow ${ew.customSqlSegment}
    </select>
    <update id="updateWarehouseApproveCreateTime">
        update business_order_work_flow set  warehouse_approve_create_time = null, update_time = now()
        where id = #{params.id}
    </update>
    <select id="queryWorkFlowWithDesign" resultMap="workFlowVoMap">
        select d.order_id,d.item_base_package
        from design_info d inner join business_order_work_flow f on d.order_id =f.order_id and d.is_deleted =0 and f.is_deleted =0
        where d.order_id = #{orderId}
    </select>

    <resultMap id="workFlowVoMap" type="org.skyworth.ess.vo.OrderWorkFlowVO">
        <result column="order_id" property="orderId"/>
        <result column="item_base_package" property="itemBasePackage"/>
    </resultMap>
</mapper>

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.solarpanels.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.qcsubmission.solarpanels.entity.SolarPanelsEntity;
import org.skyworth.ess.qcsubmission.solarpanels.vo.SolarPanelsVO;
import org.skyworth.ess.qcsubmission.solarpanels.excel.SolarPanelsExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import java.util.List;

/**
 * 施工-光伏板信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
public interface ISolarPanelsService extends BaseService<SolarPanelsEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param solarpanels
	 * @return
	 */
	IPage<SolarPanelsVO> selectSolarPanelsPage(IPage<SolarPanelsVO> page, SolarPanelsVO solarpanels);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<SolarPanelsExcel> exportSolarPanels(Wrapper<SolarPanelsEntity> queryWrapper);

}

package org.skyworth.ess.deliversign.service;

import org.skyworth.ess.deliversign.vo.DeliverSignItemVO;
import org.skyworth.ess.deliversign.vo.DeliverSignVO;
import org.skyworth.ess.design.deviceItem.vo.DeviceItemStatusVO;
import org.skyworth.ess.design.vo.ProductVO;
import org.springblade.core.tool.api.R;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IDeliverDeviceItemService {

    DeviceItemStatusVO queryDeliverDeviceItemList(Long orderId);

    DeviceItemStatusVO querySignDeviceItemList(Long orderId);

    R<?> deliverAudit(DeliverSignVO deliverSignVO);

    R<?> signAudit(DeliverSignVO deliverSignVO);

    R<?> signLoss(DeliverSignItemVO deliverSignItemVO);
    R<?> signSubmit(DeliverSignItemVO deliverSignItemVO);
    Map<String, List<ProductVO>> signHistory(Long orderId);

}

package org.skyworth.ess.createorder.order.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

public class OrderUtil {


	private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
	private static final AtomicLong sequence = new AtomicLong(1);
	private static final int SEQUENCE_LENGTH = 6;
	private static String lastDate = "";

	private OrderUtil() {
	}

	public static synchronized String generateOrderNumber(String typePrefix) {
		String currentDate = dateFormat.format(new Date());
		if (!currentDate.equals(lastDate)) {
			sequence.set(1);
			lastDate = currentDate;
		}
		String sequenceStr = String.format("%0" + SEQUENCE_LENGTH + "d", sequence.getAndIncrement());
		return typePrefix + currentDate + sequenceStr;
	}

}

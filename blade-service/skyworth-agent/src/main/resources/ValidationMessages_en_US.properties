email.recipients.limit=The number of email recipients cannot exceed %s
agent.company.attributes.notNull=The distributor attribute cannot be empty
agent.company.has.intransitive.order=The distributor(%s) cannot be deleted because its order is still ongoing
agent.company.name.repeat=The distributor company name is occupied
agent.companyInfo.notNull=Company information cannot be empty
agent.companyName.notNull=Company name cannot be empty
agent.createOrder.customerEmail.notNull=Customer Email cannot be empty
agent.createOrder.customerName.notNull=Customer name cannot be empty
agent.createOrder.customerPhone.notNull=Customer phone number cannot be empty
agent.createOrder.distributorId.notNull=Distributor ID cannot be empty
agent.createOrder.insertOrder.getCompanyAttributes.fail=Cannot fine the distributor attribute
agent.createOrder.insertOrder.getOrderNumber.notNull=Cannot find the work order
agent.createOrder.insertOrder.saveOrderFail=Failed to save the order
agent.createOrder.insertOrder.startProcessFail=Failed to activate the work flow
agent.createOrder.insertOrder.workFlowSaveFail=Failed to save the work flow
agent.createOrder.orderId.notNull=Order ID cannot be empty
agent.createOrder.projectType.notNull=Project type cannot be empty
agent.createOrder.rolloutManagerId.notNull=Rollout manager ID cannot be empty
agent.createOrder.selectDeliveryManager.agentId.notNull=The distributor ID cannot be empty
agent.createOrder.selectDeliveryManager.orderDTO.notNull=Order info cannot be empty
agent.ehsSubmission.declaration.notNull=Declaration cannot be empty
agent.ehsSubmission.insertOrUpdateEhsInfo.saveOrUpdateEhsInfo.fail=Failed to save the EHS info
agent.ehsSubmission.memberSafetyChecklist.notNull=Member safety check list cannot be empty
agent.ehsSubmission.riskList.notNull=Risk list cannot be empty
agent.ehsSubmission.riskSolution.notNull=Risk solution cannot be empty
agent.ehsSubmission.selectEhsInfo.ehsInfoDTO.notNull=EHS basic info cannot be empty
agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull=EHS audit work flow info cannot be empty
agent.ehsVerification.ehsInfoFlag.notNull=EHS status type cannot be empty
agent.ehsVerification.ehsNodeName.notNull=EHS status name cannot be empty
agent.installationDate.constructionDate.notNull=Installation date cannot be empty
agent.installationDate.installRelatedInfoEntity.notNull=Installation date module cannot be empty
agent.installationDate.selectInstallDateInfo.installRelatedInfoDTO.notNull=Installation related info cannot be empty
agent.installationDate.updateInstallInfo.saveInstallRelatedInfo.fail=Failed to save the installation info
agent.installWoAssignment.getButtonStatus.orderWorkFlowEntity.notNull=Cannot fine the work flow
agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull=Order related person info cannot be empty
agent.installWoAssignment.validParamStatus.modifyStatus.notNull=Button status cannot be empty
agent.installWoAssignment.validParamStatus.orderRelatedUserDTO.modifyStatus.error=The order cannot be distributed to anyone
agent.installWoAssignment.validParamStatus.orderRelatedUserPerson.notNull=Site Technician  cannot be empty
agent.installWoAssignment.validParamStatus.orderRelatedUserPersonName.notNull=Site Technician cannot be empty
agent.materialCollection.examineMaterialCollection.deviceItemEntityList.notNull=Materials collection info cannot be empty
agent.materialCollection.setWorkFlowData.businessId.notNull=Business ID cannot be empty
agent.materialCollection.setWorkFlowData.getRelatedUser.notNull=Site Technician info is empty
agent.materialCollection.updateMaterialCollectionInfo.updateDeviceItem.fail=Failed to update the material collection info
agent.parameter.invalid=The distributor parameter is invalid
agent.parameter.notEmpty=The distributor parameter cannot be empty
agent.qcSubmission.getBasicInfo.exception=Failed to obtain the QC info data
agent.qcSubmission.getBasicInfo.orderId.notEmpty=Order ID cannot be empty
agent.qcSubmission.getHouseElectricalEntity.orderId.notExist=Electrical info of this order %s doesn't exist
agent.qcSubmission.getHouseStructureEntity.orderId.notExist=House Structure info of this ID %s doesn't exist
agent.qcSubmission.getInstallRelatedInfoEntity.orderId.notExist=Related information of this ID %s doesn't exist
agent.qcSubmission.getOrderEntity.orderId.notExist=Order information of this ID %s doesn't exist
agent.qcSubmission.getQcInfoDetail.qcShowDTO.notEmpty=QC info data display object is empty or order ID is empty
agent.qcSubmission.loadAttachmentDescVO.exception=Failed to obtain the attachment or image description
agent.qcSubmission.loadElectricalComponentsVO.exception=Failed to obtain the electrical components data
agent.qcSubmission.loadFinalInspectionVO.exception=Failed to obtain the final inspection data
agent.qcSubmission.loadGroundingEquipmentVO.exception=Failed to obtain the grounding equipment data
agent.qcSubmission.loadInverterInstallationVO.exception=Failed to obtain the inverter installation data
agent.qcSubmission.loadMountingAndRackingVO.exception=Failed to obtain the mounting and racking data
agent.qcSubmission.loadSafetyAndComplianceVO.exception=Failed to obtain the safety and compliance data
agent.qcSubmission.loadSolarPanelsVO.exception=Failed to obtain the solar panels data
agent.qcSubmission.loadSystemTestingVO.exception=Failed to obtain the system testing data
agent.qcSubmission.saveOrUpdateElectricalComponent.ElectricalComponents.notExist=The electrical component info of this ID %s doesn't exist
agent.qcSubmission.saveOrUpdateInverterInstallation.InverterInstallation.notExist=The inverter installation info of this ID %s doesn't exist
agent.qcSubmission.saveOrUpdateMountingAndRacking.MountingAndRacking.notExist=The mounting and racking info of this ID %s doesn't exist
agent.qcSubmission.saveOrUpdateQcInfo.inverterType.notEmpty=The inverter type cannot be empty when the status is completed
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.notEmpty=QC info data display object is empty
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty=Order ID cannot be empty
agent.qcSubmission.saveOrUpdateSolarPanels.SolarPanels.notExist=The solar panels info of this ID %s doesn't exist
agent.qcSubmission.submitQcInfo.clientType.notEmpty=The client type cannot be empty
agent.qcSubmission.submitQcInfo.clientType.notSupported=The client type is not supported
agent.qcSubmission.submitQcInfo.orderDTO.businessId.notEmpty=Work flow business ID cannot be empty
agent.qcSubmission.submitQcInfo.orderFlowDTO.notEmpty=Order audit work flow display object cannot be empty
agent.qcSubmission.submitQcInfo.qcInfoDtos.notEmpty=QC info data display object cannot be empty
agent.qcSubmission.submitQcInfo.subNodeSaveStatus.unFinish=The following submodule hasn't been completed:%s\u3002
agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull=Quotation info cannot be empty
agent.quoteInfo.updateQuoteInfo.updateQuoteInfo.fail=Failed to edit the order
agent.reviewOrder.examineEditOrCancelOrder.orderDTO.notNull=The order supplemented info cannot be empty
agent.reviewOrder.installBudgets.notNull=Budget for installation cannot be empty
agent.reviewOrder.installReason.notNull=Reason for installation cannot be empty
agent.reviewOrder.laterFlowTreatment.IncorrectParameter.Transmission=Failed to transfer the parameter
agent.reviewOrder.laterFlowTreatment.orderWorkFlowUpdate.fail=Failed to edit the work flow
agent.reviewOrder.preProcessTaskCheck.examineApproveType.error=Failed to transfer the audit status
agent.reviewOrder.preProcessTaskCheck.order.notNull=The work order doesn't exist
agent.reviewOrder.preProcessTaskCheck.orderFlowInfo.error=Work flow is  wrong, repeated or doesn't exist
agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail=Failed to edit the attachment
agent.reviewOrder.longitude.notNull=Longitude cannot be empty
agent.reviewOrder.latitude.notNull=latitude cannot be empty
agent.reviewOrder.siteAddress.notNull=Energy station address cannot be empty
agent.reviewOrder.siteCityCode.notNull=Energy station city code cannot be empty
agent.reviewOrder.siteCountryCode.notNull=Energy station country code cannot be empty
agent.reviewOrder.siteProvinceCode.notNull=Energy station province code cannot be empty
agent.reviewOrder.surveyDate.notNull=Survey date cannot be empty
agent.reviewOrder.tentativeInstallEndDate.notNull=Tentative installation end date cannot be empty
agent.reviewOrder.tentativeInstallStartDate.notNull=Tentative installation start date cannot be empty
agent.reviewOrder.updateOrderInfo.updateOrder.fail=Failed to edit the work order
agent.reviewOrder.workFlow.fail=Work flow failed
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.comment.notNull=The review opinion cannot be empty
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.examineApproveType.notNull=The audit status cannot be empty
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.notNull=Work order parameter cannot be empty
agent.surveyAssignment.designatedPerson.orderId.notNull=OrderId cannot be null
agent.surveyAssignment.designatedPerson.SurveyInfoEntity.notNull=The survey assignment info cannot be empty
agent.surveyAssignment.electricianId.notNull=Survey electrician ID cannot be empty
agent.surveyAssignment.electricianName.notNull=Survey electrician cannot be empty
agent.surveyAssignment.technicianId.notNull=Survey technician ID cannot be empty
agent.surveyAssignment.technicianName.notNull=Survey technician cannot be empty
design.save.designInfoAlreadySaved=designInfo is saved, please upload designInfo ID if you need to edit
design.attachmentInfo.notNull=Attachment cannot be empty
design.productList.notNull=Stock cannot be empty
design.productList.invalid=%s repeat
email.address.notNull=Email address cannot be empty
email.content.invalid=Email unavailable :%s
email.content.notNull=Email content cannot be empty
email.content.sendMail=Slow loading, please wait and try it later
email.send.fail=Email sending failed, please try again later
stock.deviceItem.notNull=Stock is empty
stock.surplusQuantity.invalid=skuCode:%s Not enough stock
survey.notNull=Survey information doesn't exist
survey.submit.finish=Survey information has been submitted,please refresh the page
survey.save.houseElectricalAlreadySaved=Electrician-House Info is saved
survey.save.houseElectricalEmpty=Electrician-House Info cannot be empty
survey.submit.allEmpty=House info and Electrical info not completed
survey.submit.electricianEmpty=Electrician-House info not completed
survey.submit.houseInfoEmpty=House info not completed
survey.submit.houseStructureAlreadySaved=Technician-House Info is saved, please upload house info ID if you need to edit
survey.submit.houseStructureEmpty=Technician-House Info cannot be empty
survey.submit.LandlordSignEmpty=Signature is not completed
survey.surveyPhoto.imgBizKey.notNull=Technician/Electrician owner photo is not completed
survey.surveySign.imgBizKey.notNull=Technician/Electrician/LL Signature is not completed
system.error=System Error
agent.sku.base.info.save.can.not.empty=the sku name , device type, company ,standards, unit ,price can not empty.
agent.sku.base.info.save.sku.exist=the sku code exist.
email.sender.limit=The number of email recipients cannot exceed %s
agent.survey.date.electrician.technician.not.both.null=electrician,technician must chose one
agent.surveyReview.submitObject.notNull=The submitted object for survey approval cannot be empty
agent.surveyReview.flowParam.notNull=The parameters for the survey approval process cannot be empty
agent.design.product.notNull=Material information cannot be empty, please select material information.
agent.record.already.exists=Record already exists, unable to submit data
agent.maint.customerName.notNull=Please enter the customer name
agent.maint.orderInstallId.notNull=Please select the installation order
agent.qcSubmission.loadBatteryInstallation.exception=Abnormal acquisition of battery installation data
agent.qcSubmission.invert.not.exist=The following inverter serial number does not exist: %s
agent.qcSubmission.battery.not.exist=The following battery serial number does not exist: %s
agent.qcSubmission.serialNumber.repeat=The following serial number is duplicated: %s
agent.workflow.lock.error=Order locking failed, please contact the administrator
agent.workflow.had.other.people.deal=The order has been processed by someone else, please handle it later
agent.deliverSign.item.empty=Deliver goods cannot be empty
agent.deliverSign.item.status.has.not.deliver=Goods status is not deliver,can not sign : %s
agent.device.item.sign.itemCode.notNull=The itemCode cannot be empty
agent.device.item.sign.remark.notNull=Loss reason cannot be empty
agent.device.item.sign.orderId.notNull=The orderId cannot be empty
agent.install.woAssignment.quoteInfo.isNull=Quote information cannot be empty
agent.installWoAssignment.constructionDate.notNull=Construction date cannot be empty
agent.reviewOrder.examineApprove.orderFlowDTO.notNull=Workflow process object data cannot be empty
agent.reviewOrder.examineApprove.taskId.notNull=Workflow parameter taskId cannot be empty.
agent.examineApproveType.cannot.be.null=Parameter exception, examineApproveType cannot be empty!
agent.station.selectedBy.another.order=The station has been selected by another order.

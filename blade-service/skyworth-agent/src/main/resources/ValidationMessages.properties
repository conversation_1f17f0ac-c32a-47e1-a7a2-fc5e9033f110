agent.company.attributes.notNull=\u516C\u53F8\u5C5E\u6027\u4E0D\u80FD\u4E3A\u7A7A
agent.company.has.intransitive.order=\u4EE3\u7406\u5546(%s)\u5B58\u5728\u5BA1\u6279\u4E2D\u7684\u7533\u8BF7\u5355\uFF0C\u4E0D\u80FD\u6267\u884C\u5220\u9664\u64CD\u4F5C
agent.company.name.repeat=\u516C\u53F8\u540D\u79F0\u91CD\u590D
agent.companyInfo.notNull=\u516C\u53F8\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.companyName.notNull=\u516C\u53F8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.customerEmail.notNull=\u53C2\u6570\u5BA2\u6237\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.customerName.notNull=\u53C2\u6570\u5BA2\u6237\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.customerPhone.notNull=\u53C2\u6570\u5BA2\u6237\u7535\u8BDD\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.distributorId.notNull=\u53C2\u6570\u7ECF\u9500\u5546\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.insertOrder.getCompanyAttributes.fail=\u83B7\u53D6\u4EE3\u7406\u5546\u5C5E\u6027\u5931\u8D25
agent.createOrder.insertOrder.getOrderNumber.notNull=\u83B7\u53D6\u8BA2\u5355\u53F7\u5931\u8D25
agent.createOrder.insertOrder.saveOrderFail=\u8BA2\u5355\u4FDD\u5B58\u5931\u8D25
agent.createOrder.insertOrder.startProcessFail=\u6D41\u7A0B\u542F\u52A8\u5931\u8D25
agent.createOrder.insertOrder.workFlowSaveFail=\u4FDD\u5B58\u6D41\u7A0B\u4FE1\u606F\u5931\u8D25
agent.createOrder.orderId.notNull=\u53C2\u6570\u8BA2\u5355\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.projectType.notNull=\u53C2\u6570\u9879\u76EE\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.rolloutManagerId.notNull=\u53C2\u6570\u63A8\u5E7F\u7ECF\u7406\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.selectDeliveryManager.agentId.notNull=\u53C2\u6570\u4EE3\u7406\u5546\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.createOrder.selectDeliveryManager.orderDTO.notNull=\u53C2\u6570\u8BA2\u5355\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.declaration.notNull=\u53C2\u6570\u58F0\u660E\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.insertOrUpdateEhsInfo.saveOrUpdateEhsInfo.fail=\u4FDD\u5B58\u65BD\u5DE5\u5B89\u5168\u57FA\u672C\u4FE1\u606F\u5931\u8D25
agent.ehsSubmission.memberSafetyChecklist.notNull=\u53C2\u6570\u5B89\u5168\u68C0\u67E5\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.riskList.notNull=\u53C2\u6570\u98CE\u9669\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.riskSolution.notNull=\u53C2\u6570\u98CE\u9669\u89E3\u51B3\u63AA\u65BD\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.selectEhsInfo.ehsInfoDTO.notNull=\u53C2\u6570\u65BD\u5DE5\u5B89\u5168\u57FA\u7840\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull=\u53C2\u6570\u5BA1\u6279\u6D41\u7A0B\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsVerification.ehsInfoFlag.notNull=\u53C2\u6570\u65BD\u5DE5\u5B89\u5168\u5BA1\u6838\u4FE1\u606F\u5C0F\u8282\u70B9\u6807\u5FD7\u4E0D\u80FD\u4E3A\u7A7A
agent.ehsVerification.ehsNodeName.notNull=\u53C2\u6570\u65BD\u5DE5\u5B89\u5168\u5BA1\u6838\u4FE1\u606F\u5C0F\u8282\u70B9\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
agent.installationDate.constructionDate.notNull=\u53C2\u6570\u65BD\u5DE5\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
agent.installationDate.installRelatedInfoEntity.notNull=\u53C2\u6570\u786E\u8BA4\u65BD\u5DE5\u65E5\u671F\u6A21\u5757\u4E0D\u80FD\u4E3A\u7A7A
agent.installationDate.selectInstallDateInfo.installRelatedInfoDTO.notNull=\u53C2\u6570\u5B89\u88C5\u76F8\u5173\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.installationDate.updateInstallInfo.saveInstallRelatedInfo.fail=\u4FDD\u5B58\u5B89\u88C5\u76F8\u5173\u4FE1\u606F\u5931\u8D25
agent.installWoAssignment.getButtonStatus.orderWorkFlowEntity.notNull=\u6D41\u7A0B\u4E0D\u5B58\u5728
agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull=\u53C2\u6570\u8BA2\u5355\u5173\u7CFB\u4EBA\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.installWoAssignment.validParamStatus.modifyStatus.notNull=\u53C2\u6570\u6309\u94AE\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
agent.installWoAssignment.validParamStatus.orderRelatedUserDTO.modifyStatus.error=\u8FD9\u4E2A\u8BA2\u5355\u4E0D\u80FD\u5206\u914D\u7ED9\u4EFB\u4F55\u4EBA
agent.installWoAssignment.validParamStatus.orderRelatedUserPerson.notNull=\u65BD\u5DE5\u4EBA\u5458\u4E0D\u80FD\u4E3A\u7A7A
agent.installWoAssignment.validParamStatus.orderRelatedUserPersonName.notNull=\u65BD\u5DE5\u961F\u957F\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
agent.materialCollection.examineMaterialCollection.deviceItemEntityList.notNull=\u53C2\u6570\u7269\u6599\u7B7E\u6536\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.materialCollection.setWorkFlowData.businessId.notNull=\u53C2\u6570\u4E1A\u52A1\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.materialCollection.setWorkFlowData.getRelatedUser.notNull=\u53C2\u6570\u65BD\u5DE5\u4EBA\u5458\u4FE1\u606F\u4E3A\u7A7A
agent.materialCollection.updateMaterialCollectionInfo.updateDeviceItem.fail=\u66F4\u65B0\u6536\u8D27\u4FE1\u606F\u5931\u8D25
agent.parameter.invalid=\u53C2\u6570\u65E0\u6548
agent.parameter.notEmpty=\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.getBasicInfo.exception=\u83B7\u53D6\u8D28\u68C0\u4FE1\u606F\u6570\u636E\u5F02\u5E38
agent.qcSubmission.getBasicInfo.orderId.notEmpty=\u8BA2\u5355ID\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.getHouseElectricalEntity.orderId.notExist=\u5177\u6709\u8BE5\u8BA2\u5355ID(%s)\u7684\u623F\u5C4B\u7535\u6C14\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.getHouseStructureEntity.orderId.notExist=\u5177\u6709\u8BE5\u8BA2\u5355ID(%s)\u7684\u623F\u5C4B\u7ED3\u6784\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.getInstallRelatedInfoEntity.orderId.notExist=\u5177\u6709\u8BE5\u8BA2\u5355ID(%s)\u7684\u76F8\u5173\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.getOrderEntity.orderId.notExist=\u8BE5\u8BA2\u5355ID(%s)\u7684\u8BA2\u5355\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.getQcInfoDetail.qcShowDTO.notEmpty=\u8D28\u68C0\u4FE1\u606F\u5C55\u793A\u6570\u636E\u4F20\u8F93\u5BF9\u8C61\u53C2\u6570\u4E3A\u7A7A\u6216\u8005\u8BA2\u5355ID\u53C2\u6570\u4E3A\u7A7A
agent.qcSubmission.loadAttachmentDescVO.exception=\u83B7\u53D6\u9644\u4EF6\u6216\u56FE\u7247\u63CF\u8FF0\u4FE1\u606F\u5F02\u5E38
agent.qcSubmission.loadElectricalComponentsVO.exception=\u83B7\u53D6\u7535\u6C14\u7EC4\u4EF6\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadFinalInspectionVO.exception=\u83B7\u53D6\u6700\u7EC8\u68C0\u67E5\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadGroundingEquipmentVO.exception=\u83B7\u53D6\u63A5\u5730\u8BBE\u5907\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadInverterInstallationVO.exception=\u83B7\u53D6\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u5B89\u88C5\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadMountingAndRackingVO.exception=\u83B7\u53D6\u5B89\u88C5\u548C\u652F\u67B6\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadSafetyAndComplianceVO.exception=\u83B7\u53D6\u5B89\u5168\u548C\u5408\u89C4\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadSolarPanelsVO.exception=\u83B7\u53D6\u592A\u9633\u80FD\u50A8\u80FD\u677F\u6570\u636E\u5F02\u5E38
agent.qcSubmission.loadSystemTestingVO.exception=\u83B7\u53D6\u7CFB\u7EDF\u6D4B\u8BD5\u6570\u636E\u5F02\u5E38
agent.qcSubmission.saveOrUpdateElectricalComponent.ElectricalComponents.notExist=\u5177\u6709\u8BE5ID(%s)\u7684\u7535\u6C14\u7EC4\u4EF6\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.saveOrUpdateInverterInstallation.InverterInstallation.notExist=\u5177\u6709\u8BE5ID(%s)\u7684\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u5B89\u88C5\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.saveOrUpdateMountingAndRacking.MountingAndRacking.notExist=\u5177\u6709\u8BE5ID(%s)\u7684\u5B89\u88C5\u548C\u652F\u67B6\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.saveOrUpdateQcInfo.inverterType.notEmpty=\u5F53\u72B6\u6001\u5B8C\u6210\u65F6\uFF0C\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.notEmpty=\u8D28\u68C0\u4FE1\u606F\u6570\u636E\u4F20\u8F93\u5BF9\u8C61\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty=\u8BA2\u5355ID\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.saveOrUpdateSolarPanels.SolarPanels.notExist=\u5177\u6709\u8BE5ID(%s)\u7684\u592A\u9633\u80FD\u50A8\u80FD\u677F\u4FE1\u606F\u4E0D\u5B58\u5728
agent.qcSubmission.submitQcInfo.clientType.notEmpty=\u5BA2\u6237\u7AEF\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.submitQcInfo.clientType.notSupported=\u4E0D\u652F\u6301\u8BE5\u5BA2\u6237\u7AEF\u7C7B\u578B
agent.qcSubmission.submitQcInfo.orderDTO.businessId.notEmpty=\u5DE5\u4F5C\u6D41\u7684\u4E1A\u52A1ID\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.submitQcInfo.orderFlowDTO.notEmpty=\u8BA2\u5355\u5BA1\u6279\u6D41\u7A0B\u6570\u636E\u4F20\u8F93\u5BF9\u8C61\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.submitQcInfo.qcInfoDtos.notEmpty=\u8D28\u68C0\u4FE1\u606F\u6570\u636E\u4F20\u8F93\u5BF9\u8C61\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.qcSubmission.submitQcInfo.subNodeSaveStatus.unFinish=\u4EE5\u4E0B\u5B50\u8282\u70B9\u5C1A\u672A\u5B8C\u6210:(%s)
agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull=\u53C2\u6570\u8BBE\u5907\u62A5\u4EF7\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.quoteInfo.updateQuoteInfo.updateQuoteInfo.fail=\u8BA2\u5355\u4FEE\u6539\u5931\u8D25
agent.reviewOrder.examineEditOrCancelOrder.orderDTO.notNull=\u53C2\u6570\u8BA2\u5355\u8865\u5145\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.installBudgets.notNull=\u53C2\u6570\u5B89\u88C5\u9884\u7B97\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.installReason.notNull=\u53C2\u6570\u5B89\u88C5\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.laterFlowTreatment.IncorrectParameter.Transmission=\u53C2\u6570\u4F20\u8F93\u9519\u8BEF
agent.reviewOrder.laterFlowTreatment.orderWorkFlowUpdate.fail=\u4FEE\u6539\u5DE5\u4F5C\u6D41\u4FE1\u606F\u5931\u8D25
agent.reviewOrder.preProcessTaskCheck.examineApproveType.error=\u5BA1\u6279\u72B6\u6001\u53C2\u6570\u4F20\u8F93\u9519\u8BEF
agent.reviewOrder.preProcessTaskCheck.order.notNull=\u8BA2\u5355\u4E0D\u5B58\u5728
agent.reviewOrder.preProcessTaskCheck.orderFlowInfo.error=\u5DE5\u4F5C\u6D41\u7A0B\u5F02\u5E38\u3001\u91CD\u590D\u6216\u4E0D\u5B58\u5728
agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail=\u9644\u4EF6\u5904\u7406\u5931\u8D25
agent.reviewOrder.longitude.notNull=\u7ECF\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.latitude.notNull=\u7EAC\u5EA6\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.siteAddress.notNull=\u53C2\u6570\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.siteCityCode.notNull=\u53C2\u6570\u5B89\u88C5\u7AD9\u70B9\u57CE\u5E02\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.siteCountryCode.notNull=\u53C2\u6570\u5B89\u88C5\u7AD9\u70B9\u56FD\u5BB6\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.siteProvinceCode.notNull=\u53C2\u6570\u5B89\u88C5\u7AD9\u70B9\u7701\u4EFD\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.surveyDate.notNull=\u53C2\u6570\u8E0F\u52D8\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.tentativeInstallEndDate.notNull=\u53C2\u6570\u9884\u8BA1\u5B89\u88C5\u7ED3\u675F\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.tentativeInstallStartDate.notNull=\u53C2\u6570\u9884\u8BA1\u5B89\u88C5\u5F00\u59CB\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.updateOrderInfo.updateOrder.fail=\u8BA2\u5355\u4FE1\u606F\u4FEE\u6539\u5931\u8D25
agent.reviewOrder.workFlow.fail=\u5DE5\u4F5C\u6D41\u6D41\u7A0B\u5931\u8D25
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.comment.notNull=\u53C2\u6570\u5BA1\u6279\u610F\u89C1\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.examineApproveType.notNull=\u53C2\u6570\u5BA1\u6279\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.notNull=\u53C2\u6570\u5DE5\u4F5C\u6D41\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.designatedPerson.orderId.notNull=\u8BA2\u5355ID\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.designatedPerson.SurveyInfoEntity.notNull=\u53C2\u6570\u6307\u6D3E\u8E0F\u52D8\u4EFB\u52A1\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.electricianId.notNull=\u53C2\u6570\u7535\u6C14\u4EBA\u5458\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.electricianName.notNull=\u53C2\u6570\u7535\u6C14\u4EBA\u5458\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.technicianId.notNull=\u53C2\u6570\u8E0F\u52D8\u4EBA\u5458\u5E8F\u5217\u53F7\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyAssignment.technicianName.notNull=\u53C2\u6570\u8E0F\u52D8\u4EBA\u5458\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
design.save.designInfoAlreadySaved=\u8BBE\u8BA1\u4FE1\u606F\u5DF2\u4FDD\u5B58,\u5982\u9700\u4FEE\u6539\u8BF7\u4F20\u8BBE\u8BA1\u4FE1\u606FID
design.attachmentInfo.notNull=\u9644\u4EF6\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
design.productList.notNull=\u8BBE\u5907\u5907\u6599\u6E05\u5355\u4E0D\u80FD\u4E3A\u7A7A
design.productList.invalid=%s \u91CD\u590D
email.address.notNull=\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A
email.content.invalid=\u90AE\u7BB1\u65E0\u6548\uFF1A%s
email.content.notNull=\u90AE\u4EF6\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A
email.content.sendMail=\u7F51\u7EDC\u6BD4\u8F83\u6162\u8BF7\u7A0D\u540E\u91CD\u8BD5
email.send.fail=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5
stock.deviceItem.notNull=\u8BBE\u5907\u5907\u6599\u6E05\u5355\u4E3A\u7A7A
stock.surplusQuantity.invalid=skuCode\uFF1A%s \u7ED3\u4F59\u5E93\u5B58\u4E0D\u8DB3
survey.notNull=\u8E0F\u52D8\u4FE1\u606F\u4E0D\u5B58\u5728
survey.submit.finish=\u8E0F\u52D8\u4FE1\u606F\u5DF2\u63D0\u4EA4,\u8BF7\u5237\u65B0\u9875\u9762
survey.save.houseElectricalAlreadySaved=\u7535\u5668\u4FE1\u606F\u5DF2\u4FDD\u5B58,\u5982\u9700\u4FEE\u6539\u8BF7\u4F20\u623F\u5C4B\u4FE1\u606FID
survey.save.houseElectricalEmpty=\u7535\u5668\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
survey.submit.allEmpty=\u623F\u5C4B\u4FE1\u606F\u4E0E\u7535\u6C14\u4FE1\u606F\u672A\u586B\u5199\u5B8C\u6210
survey.submit.electricianEmpty=\u7535\u6C14\u4FE1\u606F\u672A\u586B\u5199\u5B8C\u6210
survey.submit.houseInfoEmpty=\u623F\u5C4B\u4FE1\u606F\u672A\u586B\u5199\u5B8C\u6210
survey.submit.houseStructureAlreadySaved=\u623F\u5C4B\u4FE1\u606F\u5DF2\u4FDD\u5B58,\u5982\u9700\u4FEE\u6539\u8BF7\u4F20\u623F\u5C4B\u4FE1\u606FID
survey.submit.houseStructureEmpty=\u623F\u5C4B\u7ED3\u6784\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A
survey.submit.LandlordSignEmpty=\u7B7E\u540D\u672A\u5B8C\u6210
survey.surveyPhoto.imgBizKey.notNull=\u8E0F\u52D8\u4EBA\u5458\u6216\u8005\u7535\u6C14\u5DE5\u7A0B\u5E08\u62CD\u7167\u56FE\u7247key\u4E0D\u80FD\u4E3A\u7A7A
survey.surveySign.imgBizKey.notNull=\u8E0F\u52D8\u4EBA\u5458\u6216\u8005\u7535\u6C14\u5DE5\u7A0B\u5E08\u6216\u8005\u4E1A\u4E3B\u7B7E\u540D\u56FE\u7247key\u4E0D\u80FD\u4E3A\u7A7A
system.error=\u7CFB\u7EDF\u9519\u8BEF
agent.sku.base.info.save.can.not.empty=\u7269\u6599\u7F16\u7801\u3001\u540D\u79F0\u3001\u4F9B\u5E94\u5546\u3001\u89C4\u683C\u3001\u5355\u4F4D\u3001\u4EF7\u683C\u4E0D\u80FD\u4E3A\u7A7A
agent.sku.base.info.save.sku.exist=\u7269\u6599\u7F16\u7801\u5DF2\u5B58\u5728
email.sender.limit=\u90AE\u4EF6\u4E3B\u9001\u4EBA\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7%s\u4E2A.
email.recipients.limit=\u90AE\u4EF6\u5BC6\u9001\u4EBA\u6570\u91CF\u4E0D\u80FD\u8D85\u8FC7%s\u4E2A.
agent.survey.date.electrician.technician.not.both.null=\u7535\u6C14\u5DE5\u7A0B\u5E08\u3001\u5B89\u88C5\u5DE5\u7A0B\u5E08\u5FC5\u987B\u9009\u4E00\u4E2A
agent.surveyReview.submitObject.notNull=\u8E0F\u52D8\u5BA1\u6279\u63D0\u4EA4\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A
agent.surveyReview.flowParam.notNull=\u8E0F\u52D8\u5BA1\u6279\u6D41\u7A0B\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
agent.design.product.notNull=\u7269\u6599\u4FE1\u606F\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u9009\u62E9\u7269\u6599\u4FE1\u606F.
agent.record.already.exists=\u8BB0\u5F55\u5DF2\u5B58\u5728\uFF0C\u65E0\u6CD5\u63D0\u4EA4\u6570\u636E.
agent.maint.customerName.notNull=\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0.
agent.maint.orderInstallId.notNull=\u8BF7\u9009\u62E9\u5BA2\u6237\u9996\u6B21\u5B89\u88C5\u8BA2\u5355.
agent.qcSubmission.loadBatteryInstallation.exception=\u83B7\u53D6\u50A8\u80FD\u5B89\u88C5\u6570\u636E\u5F02\u5E38
agent.qcSubmission.invert.not.exist=\u5982\u4E0B\u667A\u80FD\u80FD\u91CF\u53D8\u6362\u5668\u5E8F\u5217\u53F7\u4E0D\u5B58\u5728: %s
agent.qcSubmission.battery.not.exist=\u5982\u4E0B\u50A8\u80FD\u5E8F\u5217\u53F7\u4E0D\u5B58\u5728: %s
agent.qcSubmission.serialNumber.repeat=\u5982\u4E0B\u5E8F\u5217\u53F7\u5B58\u5728\u91CD\u590D: %s
agent.workflow.lock.error=\u8BA2\u5355\u9501\u5B9A\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458
agent.workflow.had.other.people.deal=\u8BA2\u5355\u5DF2\u88AB\u5176\u4ED6\u4EBA\u5904\u7406\uFF0C\u8BF7\u7A0D\u540E\u5904\u7406
agent.deliverSign.item.empty=\u53D1\u8D27\u5217\u8868\u4E0D\u80FD\u4E3A\u7A7A
agent.deliverSign.item.status.has.not.deliver=\u8D27\u7269\u72B6\u6001\u4E0D\u662F\u5DF2\u53D1\u8D27\uFF0C\u4E0D\u80FD\u7B7E\u6536: %s
agent.device.item.sign.itemCode.notNull=\u7269\u6599\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
agent.device.item.sign.remark.notNull=\u5DEE\u635F\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A
agent.device.item.sign.orderId.notNull=\u8BA2\u5355ID\u4E0D\u80FD\u4E3A\u7A7A
agent.install.woAssignment.quoteInfo.isNull=\u62A5\u4EF7\u4FE1\u606F\u4E3A\u7A7A\uFF0C\u6D41\u7A0B\u5931\u8D25
agent.installWoAssignment.constructionDate.notNull=\u65BD\u5DE5\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
agent.reviewOrder.examineApprove.orderFlowDTO.notNull=\u5DE5\u4F5C\u6D41\u6D41\u7A0B\u5BF9\u8C61\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A.
agent.reviewOrder.examineApprove.taskId.notNull=\u5DE5\u4F5C\u6D41\u53C2\u6570taskId\u4E0D\u80FD\u4E3A\u7A7A.
agent.examineApproveType.cannot.be.null=\u53C2\u6570\u5F02\u5E38,examineApproveType\u4E0D\u80FD\u4E3A\u7A7A!
agent.station.selectedBy.another.order=\u8BE5\u7AD9\u70B9\u5DF2\u88AB\u53E6\u4E00\u4E2A\u8BA2\u5355\u9009\u62E9\u3002


version: '3.5'
services:

  ####################################################################################################
  ###===================================  以下为中间件模块  =========================================###
  ####################################################################################################

#  nacos:
#    image: nacos/nacos-server:v2.1.2
#    hostname: "nacos-standalone-mysql"
#    environment:
#      - MODE=standalone
#      - TZ=Africa/Johannesburg
#    volumes:
#      - /docker/nacos/standalone-logs/:/home/<USER>/logs
#      - /docker/nacos/conf/application.properties:/home/<USER>/conf/application.properties
#    ports:
#      - 8848:8848
#      - 9848:9848
#      - 9849:9849
#    networks:
#      blade_net:
#        ipv4_address: ***********

  sentinel:
    image: bladex/sentinel-dashboard:1.8.6
    hostname: "sentinel"
    environment:
      - TZ=Africa/Johannesburg
    restart: on-failure
    network_mode: host

  seata-server:
    image: seataio/seata-server:1.6.1
    hostname: "seata-server"
    environment:
      - TZ=Africa/Johannesburg
      - SEATA_PORT=8091
      - STORE_MODE=file
    network_mode: host

#  blade-nginx:
#    image: nginx:stable-alpine-perl
#    hostname: "blade-nginx"
#    environment:
#      - TZ=Africa/Johannesburg
#    ports:
#    - 88:88
#    - 9000:9000
#    volumes:
#    - /docker/nginx/api/nginx.conf:/etc/nginx/nginx.conf
#    privileged: true
#    restart: always
#    networks:
#    - blade_net
#
#  web-nginx:
#    image: nginx:stable-alpine-perl
#    hostname: "web-nginx"
#    environment:
#      - TZ=Africa/Johannesburg
#    ports:
#      - 8000:8000
#    volumes:
#      - /docker/nginx/web/html:/usr/share/nginx/html
#      - /docker/nginx/web/nginx.conf:/etc/nginx/nginx.conf
#    privileged: true
#    restart: always
#    networks:
#      - blade_net

#  blade-redis:
#    image: redis:6.2-alpine
#    hostname: "blade-redis"
#    environment:
#      - TZ=Africa/Johannesburg
#    ports:
#    - 6379:6379
#    volumes:
#    - /docker/redis/data:/data
#    command: "redis-server --appendonly yes"
#    privileged: true
#    restart: always
#    networks:
#    - blade_net

  ####################################################################################################
  ###=================================  以下为BladeX服务模块  =======================================###
  ####################################################################################################

  blade-admin:
    image: "${REGISTER}/blade-admin:${TAG}"
    volumes:
      - /opt/springlog/blade-admin:/blade/admin/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-swagger:
    image: "${REGISTER}/blade-swagger:${TAG}"
    volumes:
      - /opt/springlog/blade-swagger:/blade/swagger/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-gateway1:
    image: "${REGISTER}/blade-gateway:${TAG}"
    volumes:
      - /opt/springlog/blade-gateway1:/blade/gateway/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-gateway2:
    image: "${REGISTER}/blade-gateway:${TAG}"
    volumes:
      - /opt/springlog/blade-gateway2:/blade/gateway/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-auth1:
    image: "${REGISTER}/blade-auth:${TAG}"
    volumes:
      - /opt/springlog/blade-auth1:/blade/auth/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-auth2:
    image: "${REGISTER}/blade-auth:${TAG}"
    volumes:
      - /opt/springlog/blade-auth2:/blade/auth/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-report:
    image: "${REGISTER}/blade-report:${TAG}"
    volumes:
      - /opt/springlog/blade-report:/blade/report/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-log:
    image: "${REGISTER}/blade-log:${TAG}"
    volumes:
      - /opt/springlog/blade-log:/blade/log/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-desk:
    image: "${REGISTER}/blade-desk:${TAG}"
    volumes:
      - /opt/springlog/blade-desk:/blade/desk/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-system:
    image: "${REGISTER}/blade-system:${TAG}"
    volumes:
      - /opt/springlog/blade-system:/blade/system/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-flow:
    image: "${REGISTER}/blade-flow:${TAG}"
    volumes:
      - /opt/springlog/blade-flow:/blade/flow/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  blade-resource:
    image: "${REGISTER}/blade-resource:${TAG}"
    volumes:
      - /opt/springlog/blade-resource:/blade/resource/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host


  skyworth-client:
    image: "${REGISTER}/skyworth-client:${TAG}"
    volumes:
      - /opt/springlog/skyworth-client:/blade/skyworth-client/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host



  skyworth-toolkit:
    image: "${REGISTER}/skyworth-toolkit:${TAG}"
    volumes:
      - /opt/springlog/skyworth-toolkit:/blade/skyworth-toolkit/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host


  blade-xxljob-admin:
    image: "${REGISTER}/blade-xxljob-admin:${TAG}"
    volumes:
      - /opt/springlog/blade-xxljob-admin:/blade/xxljob-admin/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  skyworth-agent:
    image: "${REGISTER}/skyworth-agent:${TAG}"
    volumes:
      - /opt/springlog/skyworth-agent:/blade/skyworth-agent/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host

  skyworth-portable:
    image: "${REGISTER}/skyworth-portable:${TAG}"
    volumes:
      - /opt/springlog/skyworth-portable:/blade/skyworth-portable/target/blade/log
    environment:
      - TZ=Africa/Johannesburg
    privileged: true
    restart: always
    network_mode: host
  ####################################################################################################
  ###===============================  以下为Prometheus监控模块  =====================================###
  ####################################################################################################

  prometheus:
    image: prom/prometheus:v2.24.1
    hostname: "prometheus"
    environment:
      - TZ=Africa/Johannesburg
    ports:
      - 9090:9090
    volumes:
      - /docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /docker/prometheus/rules:/etc/prometheus/rules
    command: "--config.file=/etc/prometheus/prometheus.yml  --web.enable-lifecycle"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  node-exporter:
    image: prom/node-exporter:v1.0.1
    hostname: "node-exporter"
    environment:
      - TZ=Africa/Johannesburg
    ports:
      - 9190:9100
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  mysqld-exporter:
    image: prom/mysqld-exporter:v0.12.1
    hostname: "mysqld-exporter"
    environment:
      - TZ=Africa/Johannesburg
      # 需要先在mysql服务执行如下语句
      # =====================================================================================
      # === CREATE USER 'exporter'@'mysql服务ip' IDENTIFIED BY '密码';                     ===
      # === GRANT PROCESS, REPLICATION CLIENT, SELECT ON *.* TO 'exporter'@'mysql服务ip';  ===
      # === flush privileges;                                                             ===
      # =====================================================================================
      - DATA_SOURCE_NAME=exporter:skyworth@123@(********:3306)/
    ports:
      - 9104:9104
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  cadvisor:
    image: google/cadvisor:v0.33.0
    hostname: "cadvisor"
    environment:
      - TZ=Africa/Johannesburg
    ports:
      - 18080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    command: "detach=true"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ************

  grafana:
    image: grafana/grafana:7.3.7
    hostname: "grafana"
    environment:
      - TZ=Africa/Johannesburg
      - GF_SERVER_ROOT_URL=https://grafana.bladex.vip
      - GF_SECURITY_ADMIN_PASSWORD=1qaz@WSX
    ports:
      - 3000:3000
    volumes:
      - /docker/grafana/grafana.ini:/etc/grafana/grafana.ini
      - /docker/grafana:/var/lib/grafana
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  alertmanager:
    image: prom/alertmanager:v0.21.0
    hostname: "alertmanager"
    environment:
      - TZ=Africa/Johannesburg
    ports:
      - 9093:9093
    volumes:
      - /docker/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - /docker/alertmanager/data:/etc/alertmanager/data
      - /docker/alertmanager/templates:/etc/alertmanager/templates
    command: "--config.file=/etc/alertmanager/alertmanager.yml --storage.path=/etc/alertmanager/data"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  webhook-dingtalk:
    image: timonwong/prometheus-webhook-dingtalk:v1.4.0
    hostname: "webhook-dingtalk"
    environment:
      - TZ=Africa/Johannesburg
    ports:
      - 8060:8060
    command: "ding.profile=webhook_robot=https://oapi.dingtalk.com/robot/send?access_token=xxxxx"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

networks:
  blade_net:
    external:
      name: blade_net
